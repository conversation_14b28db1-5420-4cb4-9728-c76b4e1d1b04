基于D:\AIpreadfrood\rules\大模型审校方法.md文件的技术架构设计，开发AI智能审校系统的独立AI审校模块。

## 项目背景
参考D:\AIpreadfrood\rules\大模型Augment Code协作开发指南.md的开发流程，为AI智能审校系统创建可复用的AI审校引擎模块，支持多种业务场景调用（内容预审、批量审校、在线审校、多媒体审校等）。

## 开发要求

### 1. 模块目录结构
在src/modules/ai-proofreading-engine/下创建以下目录：
```
src/modules/ai-proofreading-engine/
├── types/           # TypeScript类型定义
├── core/            # 核心处理引擎
├── config/          # 配置管理
├── providers/       # AI服务提供商（国内模型：DeepSeek、文心一言、豆包、通义千问）
├── storage/         # 本地存储和会话管理
├── utils/           # 工具函数
├── api/             # 统一调用接口
└── index.ts         # 模块入口文件
```

### 2. 核心类型定义（types/index.ts）
定义以下接口和类型：
- `ProofreadingModuleAPI`: 审校模块主接口
- `BatchProofreadingAPI`: 批量审校专用接口
- `ModuleType`: 模块类型枚举（内容预审、批量审校、在线审校、多媒体审校）
- `UserSelections`: 用户选择配置（AI模型、提示词类型、输出格式）
- `AIModelConfig`: AI模型配置（包含国内模型配置）
- `PromptTemplate`: 提示词模板结构
- `ProofreadingSession`: 审校会话状态
- `ProofreadingResult`: 审校结果格式

### 3. 配置管理器（config/ConfigManager.ts）
实现以下功能：
- 从后端API获取AI模型配置和提示词模板
- 本地配置缓存机制（localStorage/sessionStorage）
- 支持多AI模型动态切换（DeepSeek、文心一言、豆包、通义千问）
- 配置验证和错误处理
- 默认配置回退机制

### 4. 会话管理器（storage/SessionManager.ts）
实现以下功能：
- 创建唯一会话ID和本地工作目录
- 会话状态持久化存储
- 文件分块和临时文件管理
- 审校进度跟踪
- 会话清理和垃圾回收
- 支持并发多会话处理

### 5. AI服务提供商（providers/）
为每个国内AI模型创建适配器：
- `DeepSeekProvider.ts`: DeepSeek API适配
- `WenxinProvider.ts`: 文心一言API适配
- `DoubaoProvider.ts`: 豆包API适配
- `TongyiProvider.ts`: 通义千问API适配
- `BaseProvider.ts`: 基础提供商抽象类

### 6. 模块调用接口（api/ModuleAPI.ts）
实现统一的模块调用接口：
- 支持不同模块类型的审校调用
- 实时进度回调和状态更新
- 错误处理和自动重试机制
- 结果格式化和输出管理
- 审校意见表生成（markdown格式）

## 技术规范
- **框架**: Vue3 + TypeScript严格模式 + Vite
- **UI组件**: Element Plus
- **代码规范**: ESLint + Prettier
- **注释要求**: 所有代码必须包含详细中文注释
- **模块化**: 确保模块完全独立，可被其他业务模块复用
- **类型安全**: 完整的TypeScript类型定义，无any类型
- **错误处理**: 完善的异常捕获和用户友好的错误提示

## 输出要求
1. 完整的模块目录结构和文件
2. 详细的TypeScript类型定义
3. 核心类的完整实现代码
4. 模块使用示例和API文档
5. 单元测试用例（使用Vitest）

## 开发流程
1. 首先分析现有技术架构文档
2. 创建详细的任务分解计划
3. 按模块逐步实现
4. 编写测试用例验证功能
5. 提供集成使用示例

请按照上述要求，创建完整的AI审校模块架构和实现代码。


请为AI智能审校系统实现文档处理与智能分块引擎模块，该模块将作为AI审校流程的核心组件。

## 项目背景
- 项目位置：D:\AIpreadfrood\frontend\src\modules\ai-proofreading-engine\
- 技术栈：Vue3 + TypeScript + Vite
- 目标：处理大型文档（数十万到百万字）的智能分块，为AI审校做准备

## 具体实现要求

### 1. 文档处理器（core/DocumentProcessor.ts）
```typescript
// 需要实现的核心功能：
- 支持文件格式：.txt、.docx、.pdf、.md
- 依赖库：mammoth.js（docx）、pdf-parse（PDF）、iconv-lite（编码检测）
- 文本预处理：去除多余空白、统一换行符、清理特殊字符
- 编码检测：自动检测文件编码并转换为UTF-8
- 错误处理：文件损坏、格式不支持、编码错误的处理
```

### 2. 智能分块管理器（core/ChunkManager.ts）
```typescript
// 分块策略实现：
- 语义分块：优先按章节标题（第一章、一、1.）、段落（双换行）分割
- 字数分块：当语义块超过限制时，按句号、分号等标点符号二次分割
- 混合分块：结合语义完整性和字数限制（默认2000字/块）
- 重叠机制：相邻块间重叠300字符，保持上下文连贯性
- 元数据管理：记录原始位置、分块类型、质量评分
```

### 3. 文本处理工具（utils/TextProcessor.ts）
```typescript
// 中文文本特殊处理：
- 章节标题识别：支持"第一章"、"一、"、"1."、"1.1"等格式
- 段落检测：识别双换行符、缩进、特殊标记
- 句子边界：中文句号、问号、感叹号、分号
- 语义完整性：确保不在句子中间分割
- 特殊字符处理：全角半角转换、标点符号规范化
```

### 4. TypeScript接口定义
```typescript
// 需要定义的核心接口：
interface ChunkInfo {
  id: string;
  content: string;
  startPosition: number;
  endPosition: number;
  chunkType: 'semantic' | 'fixed' | 'hybrid';
  qualityScore: number;
  metadata: ChunkMetadata;
}

interface ChunkConfig {
  maxChunkSize: number; // 默认2000字符
  overlapSize: number;  // 默认300字符
  strategy: ChunkStrategy;
  preserveSemantics: boolean;
}
```

### 5. 技术规范
- **Token限制**：分块大小控制在目标AI模型token限制的70%（如GPT-4的8192 tokens * 0.7 ≈ 5700 tokens）
- **内存优化**：使用流式处理，避免将整个大文件加载到内存
- **错误处理**：完整的try-catch机制，详细的错误日志
- **性能要求**：支持100MB+文档的快速处理
- **中文支持**：正确处理中文字符计数和分割

### 6. 文件结构要求
```
src/modules/ai-proofreading-engine/
├── core/
│   ├── DocumentProcessor.ts    # 文档解析器
│   └── ChunkManager.ts        # 分块管理器
├── utils/
│   └── TextProcessor.ts       # 文本处理工具
├── types/
│   └── chunk.ts              # 分块相关类型定义
└── __tests__/
    ├── DocumentProcessor.test.ts
    ├── ChunkManager.test.ts
    └── TextProcessor.test.ts
```

### 7. 验收标准
- [ ] 能够正确解析四种文档格式
- [ ] 智能分块保持语义完整性
- [ ] 分块重叠机制正常工作
- [ ] 中文文本处理准确
- [ ] 大文件处理性能良好
- [ ] 完整的单元测试覆盖
- [ ] 详细的中文注释和文档

请按照上述规范实现完整的文档处理与智能分块引擎，确保代码质量和可维护性。


##### ✅ AI智能审校系统文档处理引擎验收标准

**文档格式支持验收**
- [ ] 支持 .txt 文件的 UTF-8 编码解析，正确处理中文字符
- [ ] 支持 .docx 文件解析，提取纯文本内容并保持段落结构
- [ ] 支持 .pdf 文件解析，正确提取文本内容（包含中文）
- [ ] 支持 .md 文件解析，保持 Markdown 格式标记
- [ ] 对不支持的格式返回明确的错误信息

**智能分块算法验收**
- [ ] 语义分块：按章节、段落等语义单元进行分块，避免在句子中间截断
- [ ] 固定分块：严格按照 2000 字符限制进行分块
- [ ] 混合分块：优先语义完整性，超出限制时智能截断
- [ ] 中文文本处理：正确识别章节标题（第一章、第二节等）和段落边界
- [ ] 分块结果包含完整的元数据（块索引、原始位置、内容类型）

**分块大小和重叠机制验收**
- [ ] 每个分块大小控制在 2000 字符以内（中文字符按1个字符计算）
- [ ] 分块间重叠 300 字符，确保上下文连续性
- [ ] 最后一个分块如果小于 500 字符，合并到前一个分块
- [ ] 重叠内容不重复计算在新分块的字符限制内

**大文件处理性能验收**
- [ ] 支持流式处理，单次内存占用不超过 100MB
- [ ] 处理 1MB 以上文件时显示进度条
- [ ] 处理 10MB 以上文件时启用分批处理机制
- [ ] 内存使用监控：处理过程中内存增长不超过文件大小的 2 倍

**单元测试覆盖验收**
- [ ] 测试覆盖率达到 80% 以上
- [ ] 包含各种文档格式的解析测试用例
- [ ] 包含不同分块策略的功能测试
- [ ] 包含大文件处理的性能测试
- [ ] 包含错误处理和边界条件测试
- [ ] 所有测试用例使用中文测试数据

**集成验证**
- [ ] 与 AI 审校引擎的接口调用正常
- [ ] 分块结果能正确传递给 AI 模型进行处理
- [ ] 处理结果能正确合并和输出
- [ ] 支持并发处理多个文档而不冲突


# 基于已完成的AI智能审校系统文档处理引擎，现在进入第二阶段开发：AI服务集成与多模型适配。

## 📋 任务目标
在现有的 `src/modules/ai-proofreading-engine/` 基础上，扩展AI服务管理功能，集成国内主流AI服务提供商，实现统一的调用接口、智能负载均衡和完善的错误处理机制。

## 🎯 具体实现要求

### 1. AI服务管理器核心模块 (core/AIServiceManager.ts)
```typescript
// 需要实现的核心功能：
- 统一的AI调用接口，支持流式和非流式响应
- 信号量机制实现并发控制（默认最大并发数：5）
- 指数退避重试策略（最大重试3次，延迟：1s, 2s, 4s）
- 实时成本跟踪和预算控制（支持按日/月预算限制）
- 智能请求队列管理（优先级队列，支持紧急任务插队）
- 负载均衡算法（轮询、最少连接、响应时间优先）
- 健康检查机制（定期检测Provider可用性）
```

### 2. 统一Provider基础架构 (providers/BaseProvider.ts)
```typescript
// 抽象类设计要求：
- 标准化的请求/响应接口（支持文本审校、内容生成、摘要提取）
- 统一的错误分类和处理（网络错误、API错误、配额错误、认证错误）
- 配置验证机制（API密钥格式验证、端点可达性检查）
- 请求预处理和响应后处理钩子
- Token使用量统计和成本计算
- 请求/响应日志记录（支持敏感信息脱敏）
```

### 3. 国内AI提供商适配器实现
基于现有的Provider结构，完善以下适配器：

#### DeepSeek适配器 (providers/DeepSeekProvider.ts)
- API端点：https://api.deepseek.com/v1/chat/completions
- 模型：deepseek-chat (32K上下文)
- 成本：0.0014元/1K tokens
- 特殊处理：支持函数调用，优化中文处理

#### 百度文心一言适配器 (providers/BaiduProvider.ts)
- API端点：https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/
- 模型：ernie-bot-turbo, ernie-bot-4.0
- 认证：Access Token机制
- 成本：0.008元/1K tokens

#### 字节豆包适配器 (providers/DoubaoProvider.ts)
- API端点：https://ark.cn-beijing.volces.com/api/v3/chat/completions
- 模型：doubao-pro-32k
- 成本：0.005元/1K tokens
- 特殊处理：高并发优化，快速响应

#### 阿里通义千问适配器 (providers/QwenProvider.ts)
- API端点：https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
- 模型：qwen-turbo, qwen-plus
- 认证：API-Key机制
- 成本：0.006元/1K tokens

#### 专业大模型适配器 (providers/ProfessionalProvider.ts)
- 支持自定义端点配置
- 兼容OpenAI格式API
- 支持本地部署模型

### 4. 高级请求管理功能
```typescript
// 请求管理器要求：
- 智能重试策略：区分错误类型，网络错误立即重试，配额错误延迟重试
- 超时处理：请求超时30s，流式响应超时60s
- 并发限制：每个Provider独立限制，全局总并发数控制
- 详细日志：包含请求ID、耗时、Token消耗、错误信息
- 性能监控：响应时间统计、成功率监控、错误率告警
```

### 5. 成本控制和监控
```typescript
// 成本管理要求：
- 实时Token消耗统计（按Provider、按模块、按时间维度）
- 预算控制：日预算、月预算、总预算限制
- 成本预警：达到预算80%时告警，95%时限制调用
- 成本优化：自动选择性价比最高的Provider
- 使用报告：生成详细的使用统计和成本分析报告
```

## 🔧 技术规范

### 架构要求
- 基于现有的 `src/modules/ai-proofreading-engine/` 目录结构
- 保持与文档处理引擎的接口兼容性
- 使用TypeScript严格模式，包含完整的类型定义
- 遵循现有的错误处理和日志记录规范

### 性能要求
- 单个AI调用响应时间 < 30s
- 并发处理能力 ≥ 10个请求
- 内存使用 < 200MB
- 错误恢复时间 < 5s

### 安全要求
- API密钥加密存储
- 请求日志敏感信息脱敏
- 支持代理和VPN配置
- 请求签名验证

## 📋 验收标准
1. 所有Provider适配器能正常调用对应的AI服务
2. 并发控制和重试机制工作正常
3. 成本跟踪准确，预算控制有效
4. 错误处理覆盖所有异常情况
5. 单元测试覆盖率 ≥ 85%
6. 与现有文档处理引擎集成测试通过

## 🎯 交付物
1. 完整的AI服务管理器实现
2. 5个AI提供商适配器
3. 完善的单元测试和集成测试
4. API使用文档和配置指南
5. 性能测试报告和优化建议

请基于现有的AI智能审校引擎架构，实现完整的AI服务集成和多模型适配功能。


##### ✅ AI服务集成模块验收标准

**基础功能验证**
- [ ] 验证所有5个国内AI提供商适配器（DeepSeek、文心一言、豆包、通义千问、专业大模型）能够正常初始化和调用
- [ ] 测试统一调用接口AIServiceInterface的设计合理性，确保所有提供商都能通过相同接口调用
- [ ] 验证接口返回数据格式的一致性和完整性

**性能和并发控制**
- [ ] 测试并发控制机制：最大5个并发请求限制有效，超出限制时正确排队处理
- [ ] 验证速率限制功能：按照各提供商API限制进行请求频率控制
- [ ] 测试负载均衡算法：轮询、加权轮询、最少连接等策略正常工作

**错误处理和重试机制**
- [ ] 验证指数退避重试机制：最多3次重试，间隔时间递增（1s、2s、4s）
- [ ] 测试各种错误场景处理：网络超时、API限额、认证失败、服务不可用等
- [ ] 验证超时处理机制：请求超时时间设置合理（建议30-60秒），超时后正确终止请求

**成本控制和监控**
- [ ] 测试成本跟踪功能：准确记录每次API调用的token消耗和费用
- [ ] 验证预算限制机制：达到预算上限时停止调用并发出警告
- [ ] 确保API调用日志完整：包含请求时间、提供商、模型、token数、响应时间、错误信息等

**测试覆盖率要求**
- [ ] 单元测试覆盖率达到85%以上
- [ ] 集成测试覆盖所有AI提供商的真实API调用
- [ ] 性能测试验证高并发场景下的稳定性


请为AI智能审校引擎实现提示词模板管理和成本控制系统，基于现有的模块架构（frontend/src/modules/ai-proofreading-engine/）进行开发。

## 核心功能要求

### 1. 提示词模板管理器 (config/PromptTemplateManager.ts)

**模板分类系统**：
- 按8个业务模块分类：内容预审、批量审校、在线审校、图像/视频/音频审校、专业排版、专业查询
- 每个模块支持多个场景模板（如：语法检查、风格优化、专业术语校对）
- 模板继承机制：基础模板 → 模块模板 → 场景模板

**变量替换引擎**：
- 支持变量类型：{{content}}、{{focus}}、{{style}}、{{language}}、{{domain}}
- 条件变量：{{#if professional}}专业模式{{/if}}
- 循环变量：{{#each suggestions}}建议：{{this}}{{/each}}
- 嵌套变量：{{user.preferences.style}}

**模板版本管理**：
- 语义化版本控制（v1.0.0格式）
- 模板变更历史记录
- 向后兼容性检查
- A/B测试支持（同时运行多个版本）

**验证机制**：
- 语法验证：检查变量语法正确性
- 变量完整性：确保所有必需变量都有值
- 长度限制：验证生成的提示词不超过模型token限制
- 质量评分：基于提示词复杂度和清晰度评分

### 2. 成本计算工具 (utils/CostCalculator.ts)

**多模型计费规则**：
- DeepSeek：输入0.0014元/1K tokens，输出0.002元/1K tokens
- 文心一言：输入0.008元/1K tokens，输出0.016元/1K tokens
- 豆包：输入0.0008元/1K tokens，输出0.002元/1K tokens
- 通义千问：输入0.0015元/1K tokens，输出0.002元/1K tokens
- 专业模型：输入0.01元/1K tokens，输出0.02元/1K tokens

**成本估算算法**：
- 基于历史数据的token预测模型
- 考虑提示词长度、内容复杂度、输出要求的综合估算
- 批量处理的成本优化计算
- 重试和失败请求的成本计入

**实时跟踪系统**：
- 按用户、按项目、按时间维度的成本统计
- 成本趋势分析和预测
- 异常成本检测和告警
- 成本优化建议生成

### 3. 模板变量系统

**类型定义**：
```typescript
interface TemplateVariable {
  name: string
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect' | 'file'
  required: boolean
  defaultValue?: any
  validation?: ValidationRule[]
  description: string
  placeholder?: string
  options?: SelectOption[] // for select/multiselect
  dependencies?: string[] // 依赖的其他变量
}
```

**高级变量功能**：
- 动态选项：根据其他变量值动态生成选项列表
- 变量联动：一个变量改变时自动更新相关变量
- 表达式变量：支持简单的计算表达式
- 文件变量：支持上传文件作为变量值

### 4. 预算控制机制

**多级预算设置**：
- 全局预算：整个系统的总预算限制
- 用户预算：单个用户的预算限制
- 项目预算：特定项目的预算分配
- 时间预算：日/周/月预算限制

**智能监控系统**：
- 实时成本计算和预算消耗跟踪
- 预算使用率预警（50%、80%、95%阈值）
- 成本异常检测（突然增长、异常高消耗）
- 自动预算调整建议

**控制策略**：
- 软限制：超出预算时发出警告但继续执行
- 硬限制：超出预算时立即停止所有请求
- 智能限制：根据历史使用模式动态调整限制策略
- 紧急通道：关键任务的预算豁免机制

## 技术实现要求

### 架构设计
- 基于现有的AI审校引擎模块架构
- 与AIServiceManager、CostManager无缝集成
- 支持插件化扩展新的模板类型和成本规则
- 实现观察者模式用于成本变化通知

### 性能优化
- 模板预编译和缓存（LRU缓存策略）
- 成本计算结果缓存（避免重复计算）
- 批量操作优化（减少数据库查询）
- 异步处理非关键路径操作

### 数据持久化
- 模板配置存储在本地JSON文件
- 成本数据存储在IndexedDB
- 支持数据导入/导出功能
- 定期数据备份和清理

### 错误处理
- 模板解析错误的友好提示
- 成本计算失败的降级策略
- 网络异常时的离线模式
- 详细的错误日志和调试信息

### 测试要求
- 单元测试覆盖率85%以上
- 集成测试覆盖所有业务场景
- 性能测试验证大批量处理能力
- 边界条件测试（极大/极小值、异常输入）

## 验收标准

### 功能验收
- [ ] 支持所有8个业务模块的模板管理
- [ ] 变量替换准确率100%
- [ ] 成本计算误差小于5%
- [ ] 预算控制响应时间小于100ms
- [ ] 模板加载时间小于50ms

### 性能验收
- [ ] 支持1000+并发模板渲染
- [ ] 10万条成本记录查询响应时间小于1秒
- [ ] 内存使用量小于100MB
- [ ] 模板缓存命中率大于90%

### 可用性验收
- [ ] 提供完整的TypeScript类型定义
- [ ] 包含详细的API文档和使用示例
- [ ] 支持热重载配置更新
- [ ] 提供可视化的成本监控界面

请按照现有的代码风格和架构模式实现，确保与已有的AI服务集成模块完美兼容。


请为AI智能审校引擎实现本地文件管理和批次处理系统，基于现有的模块架构（frontend/src/modules/ai-proofreading-engine/storage/）进行开发。

## 核心功能要求

### 1. 本地文件管理器 (storage/FileManager.ts)

**会话工作目录管理**：
- 为每个审校会话创建独立的工作目录：`/sessions/{sessionId}/`
- 目录结构：`original/`（原始文件）、`chunks/`（分块文件）、`results/`（结果文件）、`temp/`（临时文件）
- 支持嵌套目录结构和文件路径管理
- 实现目录的创建、删除、清理和权限管理

**文件操作抽象层**：
- 统一的文件读写接口，支持File System Access API和IndexedDB两种存储方式
- 支持文件格式：.txt、.docx、.pdf、.md等文档格式
- 大文件分块存储（单块最大2MB），支持断点续传
- 文件元数据管理（大小、类型、创建时间、修改时间、校验和）

**并发会话管理**：
- 支持同时管理多个审校会话的文件
- 会话间文件隔离和资源竞争处理
- 会话优先级管理和资源分配策略
- 会话超时和自动清理机制

### 2. 会话存储管理 (storage/SessionStorage.ts)

**会话数据持久化**：
- 会话基本信息：sessionId、userId、创建时间、状态、配置参数
- 审校进度信息：已处理文件数、总文件数、当前状态、错误信息
- 用户偏好设置：AI模型选择、提示词配置、输出格式偏好
- 会话元数据：标签、描述、优先级、预计完成时间

**状态恢复机制**：
- 浏览器刷新后自动恢复会话状态
- 断点续传功能，支持从中断点继续处理
- 会话快照功能，定期保存会话状态
- 异常恢复策略，处理意外中断情况

**数据备份策略**：
- 本地备份：定期将会话数据备份到本地存储
- 增量备份：只备份变更的数据，节省存储空间
- 备份压缩：使用gzip压缩备份数据
- 备份验证：校验备份数据完整性

### 3. 缓存管理器 (storage/CacheManager.ts)

**多层缓存架构**：
- L1缓存：内存缓存（LRU策略，最大100MB）
- L2缓存：IndexedDB缓存（最大1GB）
- L3缓存：文件系统缓存（可配置大小）

**缓存策略配置**：
- 审校结果缓存：按文件内容hash缓存AI审校结果，避免重复处理
- 配置缓存：缓存用户配置、模板配置、模型配置
- 静态资源缓存：缓存常用的提示词模板、规则配置
- 临时数据缓存：缓存中间处理结果和临时文件

**缓存生命周期管理**：
- 过期策略：TTL（生存时间）+ LRU（最近最少使用）
- 缓存预热：预加载常用数据到缓存
- 缓存清理：定期清理过期和无效缓存
- 缓存统计：监控缓存命中率和使用情况

### 4. 文件系统工具集 (storage/FileSystemUtils.ts)

**存储适配器**：
- FileSystemAdapter：基于File System Access API的实现
- IndexedDBAdapter：基于IndexedDB的实现
- 自动检测浏览器支持情况并选择最佳存储方式
- 提供统一的存储接口，屏蔽底层差异

**文件处理工具**：
- 文件类型检测：基于文件头和扩展名的双重检测
- 文件压缩解压：支持gzip、deflate压缩算法
- 文件校验：MD5、SHA256校验和计算
- 文件转换：支持不同格式间的转换（如docx转txt）

**存储空间管理**：
- 存储配额检测和管理
- 存储空间使用统计和报告
- 自动清理策略：按时间、大小、使用频率清理
- 存储空间预警和用户提示

## 技术实现要求

### 架构设计
- 基于现有的AI审校引擎模块架构
- 与DataStorageManager、PromptTemplateManager等组件集成
- 实现观察者模式用于文件状态变化通知
- 支持插件化扩展新的存储适配器

### 性能优化
- 文件操作异步化，避免阻塞UI线程
- 大文件分块处理，支持进度显示
- 缓存预加载和懒加载策略
- 批量操作优化，减少I/O次数

### 错误处理
- 存储空间不足的降级策略
- 文件损坏的检测和修复
- 网络异常时的离线模式
- 详细的错误日志和用户友好的错误提示

### 安全考虑
- 文件访问权限控制
- 敏感数据加密存储
- 防止路径遍历攻击
- 文件大小和类型限制

## 验收标准

### 功能验收
- [ ] 支持多会话并发文件管理
- [ ] 文件读写操作成功率99%以上
- [ ] 会话状态恢复准确率100%
- [ ] 缓存命中率大于80%
- [ ] 支持所有指定的文件格式

### 性能验收
- [ ] 文件上传速度大于10MB/s
- [ ] 大文件（100MB+）分块处理时间小于30秒
- [ ] 缓存查询响应时间小于10ms
- [ ] 并发处理10个会话无性能问题
- [ ] 内存使用量小于200MB

### 可用性验收
- [ ] 提供完整的TypeScript类型定义
- [ ] 包含详细的API文档和使用示例
- [ ] 支持进度显示和状态监控
- [ ] 提供友好的错误处理和用户提示
- [ ] 支持数据导入导出功能

请按照现有的代码风格和架构模式实现，确保与已有的存储管理模块完美兼容，并提供完整的测试用例。


# 请基于已完成的AI智能审校引擎本地文件管理系统，实现批次处理和并发控制功能。

**背景说明**：
- 已完成FileManager.ts（文件管理器）、CacheManager.ts（缓存管理器）、SessionStorage.ts（会话存储）等核心模块
- 需要在现有架构基础上扩展批次处理能力
- 集成现有的会话管理和文件存储机制

**具体要求**：

1. **实现批次处理API（api/BatchAPI.ts）**
   - 基于现有FileManager创建批次工作文件夹
   - 集成SessionStorage进行批次会话管理
   - 实现批次状态枚举：PENDING、RUNNING、PAUSED、COMPLETED、CANCELLED、ERROR
   - 支持批次操作：start()、pause()、resume()、cancel()、getStatus()
   - 批次结果汇总和导出功能
   - 与现有CacheManager集成缓存批次结果

2. **实现工作空间管理器（storage/WorkspaceManager.ts）**
   - 扩展现有FileManager的会话管理功能
   - 按AI审校模块类型组织工作空间（内容预审、批量审校、在线审校等）
   - 利用现有SessionStorage进行批次信息持久化
   - 集成现有并发会话管理机制
   - 实现Semaphore信号量控制（默认3个并发）
   - 基于现有存储清理机制实现资源管理

3. **实现进度跟踪工具（utils/ProgressTracker.ts）**
   - 集成现有FileProcessingStatus状态管理
   - 实时进度计算：已完成文件数/总文件数 * 100%
   - 基于历史处理时间的剩余时间估算算法
   - 错误统计：成功率、失败率、重试次数
   - 性能指标：平均处理时间、吞吐量、内存使用
   - 支持进度回调函数和事件发射

4. **实现并发控制机制**
   - 基于现有SessionPriority扩展任务优先级管理
   - 实现任务队列：PriorityQueue<BatchTask>
   - 错误重试策略：指数退避算法，最大重试3次
   - 集成现有存储配额监控机制
   - 实现优雅降级：并发数动态调整

**技术规范**：
- TypeScript严格模式，完整类型定义
- 继承现有架构模式（适配器模式、观察者模式）
- 集成现有错误处理机制
- 支持现有的内存存储、IndexedDB存储适配器
- 遵循现有的模块化设计原则

**性能要求**：
- 默认支持3个文件并发处理（可配置1-10个）
- 批次操作响应时间 < 100ms
- 进度更新频率：每秒至少1次
- 内存使用增长 < 50MB（在现有基础上）
- 支持1000+文件的大批次处理

**验收标准**：
- 所有批次操作状态转换正确
- 并发控制机制稳定可靠
- 进度跟踪准确实时
- 错误处理和恢复完整
- 与现有文件管理系统无缝集成
- 包含完整的单元测试和集成测试

**交付文件**：
```
api/
├── BatchAPI.ts                 # 批次处理API
└── types/BatchTypes.ts         # 批次相关类型定义

storage/
├── WorkspaceManager.ts         # 工作空间管理器
└── BatchStorage.ts             # 批次数据存储

utils/
├── ProgressTracker.ts          # 进度跟踪工具
├── ConcurrencyController.ts    # 并发控制器
└── TaskQueue.ts                # 任务队列管理

__tests__/
├── BatchAPI.test.ts            # 批次API测试
├── WorkspaceManager.test.ts    # 工作空间测试
└── ConcurrencyControl.test.ts  # 并发控制测试
```

请基于现有的文件管理系统架构，提供完整的批次处理和并发控制实现，确保与已有模块的完美集成。


# 请基于已完成的AI智能审校引擎批次处理系统，实现第四阶段的结果处理与报告生成功能。

**背景说明**：
- 已完成批次处理API、工作空间管理器、并发控制器等核心模块
- 需要在现有架构基础上扩展结果处理和报告生成能力
- 集成现有的文件管理和缓存机制

**具体要求**：

1. **实现结果处理器（core/ResultProcessor.ts）**
   - 基于现有BatchAPI集成智能结果合并功能
   - 利用现有FileManager保持原文格式和结构
   - 处理分块边界的连接问题（基于现有文档分块引擎）
   - 生成最终的校对文本并保存到指定路径
   - 集成现有错误处理机制进行结果质量验证

2. **实现结果合并算法（utils/MergeAlgorithm.ts）**
   - 基于现有2000字符分块策略的边界智能处理
   - 利用300字符重叠区域进行冲突解决
   - 继承现有中文文本处理能力的格式保持策略
   - 实现语义连贯性检查（集成AI服务接口）

3. **实现结果验证机制（utils/ResultValidator.ts）**
   - 集成现有数据完整性检查机制
   - 基于原始文件大小进行文本长度验证
   - 利用现有文档格式支持进行格式一致性验证
   - 实现质量评估指标（准确率、完整率、一致性）

4. **实现结果优化功能（utils/ResultOptimizer.ts）**
   - 基于现有任务优先级机制进行重复修改去除
   - 利用现有并发控制策略处理冲突修改
   - 集成现有进度跟踪进行修改建议排序
   - 实现置信度计算（基于AI模型响应）

5. **实现报告生成器（api/ReportGenerator.ts）**
   - 生成标准化的"(文件标题)-AI预审意见表.md"格式报告
   - 集成现有批次统计信息生成处理摘要
   - 支持批次级别的汇总报告
   - 利用现有存储机制保存报告到本地临时文件夹

**技术规范**：
- TypeScript严格模式，完整类型定义和JSDoc注释
- 继承现有架构模式（适配器模式、观察者模式）
- 集成现有错误处理和重试机制
- 支持现有的.txt/.docx/.pdf/.md文档格式
- 遵循现有的模块化设计原则

**性能要求**：
- 结果合并处理时间 < 文档大小(MB) * 100ms
- 支持单文档最大10MB的结果处理
- 内存使用控制在现有基础上增长 < 30MB
- 报告生成时间 < 500ms

**验收标准**：
- 结果合并保持原文格式完整性（99%+准确率）
- 支持中文文本的特殊处理（标点、分词、语义）
- 包含完整的质量检查机制（完整性、一致性、准确性）
- 实现高效的合并算法（时间复杂度O(n)）
- 与现有批次处理系统无缝集成
- 包含完整的单元测试和集成测试

**交付文件**：
```
core/
├── ResultProcessor.ts              # 结果处理器主类
└── types/ResultTypes.ts            # 结果处理类型定义

utils/
├── MergeAlgorithm.ts               # 结果合并算法
├── ResultValidator.ts              # 结果验证机制
└── ResultOptimizer.ts              # 结果优化功能

api/
├── ReportGenerator.ts              # 报告生成器
└── types/ReportTypes.ts            # 报告相关类型

__tests__/
├── ResultProcessor.test.ts         # 结果处理器测试
├── MergeAlgorithm.test.ts          # 合并算法测试
└── ReportGenerator.test.ts         # 报告生成器测试
```

请基于现有的批次处理系统架构，提供完整的结果处理和报告生成实现，确保与已有模块的完美集成。

# 基于已完成的AI智能审校引擎结果处理与报告生成功能验收，现需要实现标准化报告生成和导出系统的完整功能。

**背景说明**：
- 当前已有ReportGenerator.ts基础实现，支持单文件和批次报告生成
- 已实现5个维度质量评分和性能监控
- 需要在现有基础上扩展导出功能和统计分析

**具体实现要求**：

1. **完善ReportGenerator.ts报告生成器**
   - 基于现有实现，增强标准化Markdown审校意见表生成
   - 确保报告格式符合"(文件标题)-AI预审意见表.md"命名规范
   - 优化报告内容结构，包含以下标准化部分：
     * 文档基本信息：标题、校对时间、AI模型类型、文件大小、处理耗时
     * 校对统计摘要：总修改数、按ModificationType分类统计、平均置信度、质量评分
     * 详细修改意见表格：原文片段、建议修改、修改原因、置信度、优先级
     * 5个维度质量评估：语法、拼写、标点、用词、流畅度评分
     * 总体改进建议：基于质量评估生成的综合性建议
   - 支持自定义报告模板配置（ReportConfig扩展）

2. **新增ExportManager.ts导出管理器**
   - 实现ZIP格式批量导出功能
   - 支持以下导出格式：Markdown、HTML、JSON、PDF
   - 分类导出结构：
     * /reports/ - 审校意见表文件
     * /processed/ - 处理后的文档文件
     * /summary/ - 批次处理摘要报告
   - 生成批次处理摘要，包含整体统计和处理效率分析
   - 集成现有FileManager和CacheManager进行文件操作

3. **扩展统计分析功能**
   - 基于现有ValidationResult和MergeResult数据实现：
     * 修改类型分布统计（按ModificationType分类）
     * 质量评估趋势分析（5个维度评分变化）
     * 处理效率指标（处理时间、内存使用、成功率）
     * 成本统计报告（API调用次数、处理文档数量、时间成本）
   - 生成可视化数据结构，支持前端图表展示

4. **技术实现规范**
   - 遵循现有模块化架构，与FileManager、CacheManager、BatchAPI集成
   - 使用TypeScript严格模式，完整类型定义
   - 性能要求：单文件导出<1秒，批量导出<5秒（50文件内）
   - 错误处理：完整的异常捕获和恢复机制
   - 中文内容处理：正确的编码和格式化
   - 测试覆盖：单元测试覆盖率>85%

5. **验收标准**
   - 报告格式100%符合标准化要求
   - 导出功能支持所有指定格式，成功率>99%
   - 统计分析数据准确性>95%
   - 批量操作性能满足指标要求
   - 与现有系统完美集成，API调用成功率>99%

**交付物**：
- 完整的ExportManager.ts实现
- 增强的ReportGenerator.ts功能
- 相应的类型定义文件更新
- 完整的测试用例
- 使用示例和API文档

请基于现有的AI智能审校引擎架构，实现完整的标准化报告生成和导出系统。


# 请为AI智能审校系统的批量审校功能实现完整的状态管理和数据持久化系统。

## 📋 核心任务目标
基于已完成的批量审校功能组件（ProgressMonitor、ResourceMonitor、OperationController等），实现统一的状态管理系统，确保数据一致性、会话持久化和跨组件状态同步。

## 🎯 具体实现要求

### 1. 批量审校状态管理 (stores/batchProofreadingStore.ts)
- **状态结构设计**：
  - 会话状态：当前批次ID、处理阶段、开始时间、暂停时间
  - 文件状态：文件列表、上传进度、处理状态、结果数据
  - 处理状态：整体进度、并发设置、错误信息、性能指标
  - 配置状态：AI模型选择、提示模板、处理选项、预算设置
- **状态管理功能**：
  - 使用Pinia实现响应式状态管理
  - 支持多个批次的并发状态管理
  - 实现状态变更的撤销/重做功能
  - 提供状态快照和恢复机制

### 2. 类型定义系统 (types/store-types.ts)
- **核心接口定义**：
  ```typescript
  interface BatchProofreadingState {
    sessions: Map<string, BatchSession>
    currentSessionId: string | null
    globalSettings: GlobalSettings
    persistenceConfig: PersistenceConfig
  }
  ```
- **状态变更Action类型**：定义所有可能的状态变更操作
- **持久化配置**：指定哪些状态需要持久化，存储策略
- **同步机制接口**：跨组件和跨标签页的状态同步协议

### 3. 状态持久化插件 (plugins/persistencePlugin.ts)
- **自动持久化**：
  - 监听关键状态变更，自动保存到localStorage/IndexedDB
  - 实现增量保存，避免频繁的完整状态序列化
  - 支持数据压缩，减少存储空间占用
- **恢复机制**：
  - 页面刷新后自动恢复会话状态
  - 处理数据版本兼容性问题
  - 提供数据迁移和清理功能

### 4. 状态同步机制
- **组件间同步**：
  - 集成已有的StateSyncMonitor工具
  - 确保ProgressMonitor、ResourceMonitor等组件状态一致
  - 实现状态变更的发布-订阅模式
- **跨标签页同步**：
  - 使用BroadcastChannel API实现标签页间通信
  - 处理多标签页的状态冲突和合并
  - 实现主从标签页的协调机制

## 🔧 技术实现标准
- **框架要求**：使用Pinia 2.x作为状态管理工具，与Vue 3组合式API深度集成
- **类型安全**：100% TypeScript覆盖，严格类型检查，完整的接口定义
- **性能优化**：实现状态的懒加载、选择性更新、内存优化
- **错误处理**：完善的错误边界、状态回滚、异常恢复机制
- **测试覆盖**：单元测试覆盖率>85%，包含状态变更、持久化、同步的测试用例

## 🎯 集成要求
- **与现有组件集成**：确保与已实现的ProgressMonitor、ResourceMonitor、OperationController等组件无缝集成
- **响应时间监控集成**：结合ResponseTimeMonitor，监控状态操作的性能
- **错误处理集成**：与ErrorHandler组件协同，提供统一的错误状态管理

## ✅ 验收标准
- [ ] 状态管理系统完整实现，支持所有批量审校业务场景
- [ ] 状态持久化机制稳定，页面刷新后100%恢复会话数据
- [ ] 跨组件状态同步实时有效，状态一致性>99%
- [ ] 状态变更历史记录完整，支持撤销/重做操作
- [ ] 错误状态处理完善，异常情况下状态自动恢复
- [ ] 性能指标达标：状态更新响应时间<50ms，内存使用增长<30MB
- [ ] 跨标签页同步正常，多标签页状态冲突自动解决

请提供完整的状态管理系统实现，包括所有必要的类型定义、插件配置和集成代码。


#### 6.2 AI智能审校系统 - 本地存储与缓存管理增强

##### 📋 任务目标
基于已完成的批量审校状态管理系统，实现企业级的本地数据存储和智能缓存管理，支持大规模文档处理的离线操作和数据持久化。

##### 💬 Augment Code对话模板
```
请为AI智能审校系统实现高级本地存储和缓存管理功能，扩展现有的状态管理系统。

## 📋 核心实现要求

### 1. 增强本地存储管理 (utils/advancedStorage.ts)
- **IndexedDB存储引擎优化**：
  - 扩展现有persistencePlugin.ts的IndexedDB实现
  - 支持大文档存储（>100MB单文件）
  - 实现分片存储和流式读写
  - 添加数据完整性校验（CRC32/MD5）
  - 支持存储配额管理和空间预警

- **配置信息本地缓存**：
  - AI模型配置的本地缓存
  - 用户偏好设置持久化
  - 审校模板和规则缓存
  - 支持配置版本控制和回滚

- **历史记录存储优化**：
  - 扩展现有stateHistory功能
  - 支持审校历史的长期存储
  - 实现历史记录的分页查询
  - 添加历史数据的压缩存储
  - 支持历史记录的导出和导入

### 2. 智能缓存策略管理 (utils/intelligentCache.ts)
- **多层缓存架构**：
  - L1缓存：内存缓存（热数据）
  - L2缓存：localStorage缓存（温数据）
  - L3缓存：IndexedDB缓存（冷数据）
  - 实现缓存数据的自动分层

- **高级缓存算法**：
  - LRU-K算法实现（考虑访问频率和时间）
  - 自适应缓存大小调整
  - 基于文件类型的差异化缓存策略
  - 预测性缓存（基于用户行为模式）

- **缓存性能监控**：
  - 实时缓存命中率统计
  - 缓存空间使用监控
  - 缓存性能分析报告
  - 缓存策略自动优化建议

### 3. 状态管理UI集成增强
- **响应式状态绑定**：
  - 扩展现有组件的状态管理集成
  - 实现状态变化的细粒度更新
  - 添加状态变化的动画效果
  - 支持状态的乐观更新

- **错误状态统一处理**：
  - 扩展现有错误处理机制
  - 实现错误状态的可视化展示
  - 添加错误恢复的用户引导
  - 支持错误状态的自动重试

### 4. 高级数据同步机制
- **离线优先架构**：
  - 扩展现有同步机制支持离线模式
  - 实现离线队列管理
  - 支持网络恢复后的自动同步
  - 添加冲突解决的用户界面

- **增量同步优化**：
  - 基于时间戳的增量更新
  - 支持二进制差异同步
  - 实现同步进度的可视化
  - 添加同步失败的重试机制

- **多标签页协调增强**：
  - 扩展现有CrossTabSyncManager
  - 实现标签页间的资源协调
  - 支持主标签页的智能选举
  - 添加标签页状态的可视化监控

## 🔧 技术实现标准

### 架构要求
- **基于现有系统扩展**：在已完成的状态管理系统基础上增强
- **向后兼容**：确保与现有batchProofreadingStore.ts完全兼容
- **模块化设计**：新功能以插件形式集成到现有架构
- **性能优化**：存储操作响应时间<100ms，缓存命中率>90%

### 数据安全要求
- **数据加密**：敏感数据的本地加密存储
- **访问控制**：基于用户权限的数据访问控制
- **数据备份**：自动数据备份和恢复机制
- **隐私保护**：用户数据的匿名化处理

### 性能指标
- **存储性能**：大文件存储速度>10MB/s
- **缓存性能**：缓存查询响应时间<10ms
- **同步性能**：增量同步延迟<500ms
- **内存使用**：缓存内存占用<200MB

### 兼容性要求
- **浏览器支持**：Chrome 90+, Firefox 88+, Safari 14+
- **设备支持**：桌面端和移动端自适应
- **存储限制**：优雅处理存储配额限制
- **降级策略**：不支持IndexedDB时的降级方案

## 📊 集成验证要求

### 功能验证
- 与现有批量审校流程的完整集成测试
- 大文档（>50MB）的存储和检索测试
- 离线模式下的完整功能验证
- 多标签页并发操作的稳定性测试

### 性能验证
- 1000+文档的批量存储性能测试
- 长时间运行的内存泄漏检测
- 网络中断和恢复的鲁棒性测试
- 存储空间不足时的优雅降级测试

请基于现有的状态管理系统架构，提供完整的本地存储和缓存管理增强实现，确保与已有功能的无缝集成。
```

##### ✅ 验收
- [ ] 本地存储系统与现有状态管理完全集成
- [ ] 支持大文档（>50MB）的高效存储和检索
- [ ] 智能缓存系统缓存命中率>90%
- [ ] 离线模式下所有核心功能正常工作
- [ ] 多标签页状态同步延迟<500ms
- [ ] 存储空间使用优化，自动清理过期数据
- [ ] 通过完整的集成测试和性能基准测试
- [ ] 数据安全和隐私保护机制有效
- [ ] 向后兼容现有批量审校功能
- [ ] 提供详细的使用文档和API参考


请基于已完成的AI智能审校系统测试与性能优化项目，进一步优化系统性能并提升用户体验质量。

## 📋 任务目标
在现有性能监控和优化基础上，深化系统性能优化，构建更完善的用户体验体系。

## 🎯 具体要求

### 1. 深度性能优化
**大文件处理优化**：
- 基于现有DocumentProcessor，实现Web Workers多线程处理
- 优化分块策略，支持>100MB文件流式处理
- 实现内存使用<200MB的约束下处理大文件
- 集成现有CacheManager实现智能缓存预加载

**AI API并发优化**：
- 扩展现有AI服务集成，支持智能负载均衡
- 实现基于响应时间的动态并发调整（当前固定5个）
- 添加请求队列优化和批处理机制
- 集成现有重试策略，优化错误恢复时间

**UI渲染性能优化**：
- 实现虚拟滚动处理长文档列表（>1000项）
- 优化Vue3组件渲染，减少不必要的重渲染
- 实现代码分割，首屏加载时间<2秒
- 添加骨架屏和渐进式加载

### 2. 内存管理增强
**基于现有存储系统优化**：
- 扩展现有FileManager，实现流式文件读写
- 优化多层缓存（L1/L2/L3）的内存分配策略
- 实现智能垃圾回收，内存使用峰值控制在300MB内
- 集成现有性能监控，添加内存泄漏检测

### 3. 用户体验体系化提升
**交互体验优化**：
- 基于现有UX优化器，添加智能进度预测
- 实现全局加载状态管理，避免界面卡顿感知
- 添加操作撤销/重做栈（支持10步历史）
- 实现快捷键系统（Ctrl+Z/Y, Ctrl+S等）

**错误处理和用户引导**：
- 扩展现有错误处理，实现分级错误提示系统
- 添加新手引导和功能发现机制
- 实现上下文相关的帮助系统
- 添加操作确认对话框（删除、覆盖等危险操作）

**响应式设计优化**：
- 基于现有设备检测，优化移动端体验
- 实现自适应布局，支持320px-2560px屏幕
- 添加触摸手势支持（移动端文档浏览）
- 实现暗色模式和高对比度模式

### 4. 性能监控体系完善
**扩展现有监控系统**：
- 基于现有PerformanceMonitor，添加用户行为分析
- 实现实时性能仪表板，可视化关键指标
- 添加性能回归检测和自动告警
- 集成现有报告生成器，支持性能趋势分析

**用户体验指标监控**：
- 实现Core Web Vitals监控（LCP, FID, CLS）
- 添加用户满意度评分系统
- 监控功能使用频率和用户路径
- 实现A/B测试框架支持

### 5. 智能化用户反馈
**基于现有用户行为分析**：
- 实现智能操作建议系统
- 添加个性化界面布局记忆
- 实现使用习惯学习和优化建议
- 添加用户反馈收集和处理机制

## 🛠️ 技术实现要求

### 核心技术栈
- **多线程处理**：Web Workers + SharedArrayBuffer
- **虚拟化**：Vue3 Virtual Scroller
- **性能优化**：防抖节流、RequestIdleCallback
- **监控集成**：扩展现有PerformanceMonitor和UXOptimizer
- **存储优化**：基于现有FileManager和CacheManager

### 架构要求
- 保持现有模块化设计，扩展而非重构
- 集成现有测试体系，新功能测试覆盖率≥85%
- 兼容现有AI服务集成和批量处理系统
- 遵循现有TypeScript严格模式和代码规范

### 性能基准
- **文件处理**：100MB文件<30秒完成处理
- **内存使用**：峰值<300MB，平均<200MB
- **UI响应**：交互响应时间<100ms
- **首屏加载**：<2秒完成核心功能加载
- **缓存命中率**：>85%（当前>80%）

## ✅ 验收标准

### 功能验收
- [ ] 支持>100MB大文件流畅处理，内存使用<300MB
- [ ] UI交互响应时间<100ms，长列表滚动流畅
- [ ] 首屏加载时间<2秒，代码分割有效
- [ ] 错误处理完善，用户引导清晰
- [ ] 快捷键和批量操作功能完整

### 性能验收
- [ ] 集成现有性能监控，新增用户体验指标
- [ ] 内存泄漏检测有效，垃圾回收优化
- [ ] 缓存命中率提升至85%以上
- [ ] Core Web Vitals指标达到"Good"级别
- [ ] 性能回归检测机制工作正常

### 测试验收
- [ ] 扩展现有测试套件，新功能测试覆盖率≥85%
- [ ] 性能基准测试通过，满足性能要求
- [ ] 跨浏览器兼容性测试通过
- [ ] 移动端响应式测试通过
- [ ] 集成测试验证与现有系统兼容性

### 文档验收
- [ ] 更新现有技术文档，包含性能优化说明
- [ ] 提供用户体验优化指南
- [ ] 补充性能监控使用文档
- [ ] 更新部署和维护指南

请基于现有的完整测试体系和性能监控基础，提供系统化的性能优化和用户体验提升实现方案。


# AI预审页面的功能
为未预审文档模块的"AI校对"按钮实现完整的AI校对功能页面，具体要求如下：

## 功能入口
- 在未预审文档表格中，点击"AI校对"按钮后，打开AI校对页面（新页面或对话框）

## AI校对页面核心功能模块

### 1. 文档信息展示区域
- 显示当前选中文档的基本信息：标题、作者、文件类型、文件大小、创建时间等
- 显示文档预览（支持文本内容预览）

### 2. AI配置选择区域
- **大模型选择**：提供下拉选择框，支持选择不同的AI模型（DeepSeek、文心一言、豆包、通义千问、专业模型等）
- **提示词选择**：提供预设的校对提示词模板选择，支持自定义提示词输入
- 显示当前选择的配置信息

### 3. 文档格式转换功能
- 使用Mock API接口将docx、PDF等格式文件转换为Markdown格式
- 显示转换进度和转换结果状态
- 支持转换失败时的重试机制

### 4. 文档智能拆分功能
- 将大文档按章节、段落或字数限制智能拆分为多个子文档
- 显示拆分策略选择（按章节、按字数、按段落等）
- 展示拆分后的文档数量和每个子文档的基本信息

### 5. 多文档任务列表
- 以表格形式展示所有拆分后的子文档
- 每行包含：序号、子文档标题、字数、状态、操作按钮
- 支持批量操作和单个文档操作

### 6. 校对操作控制
每个子文档提供以下操作按钮：
- **开始校对**：调用 `D:\AIpreadfrood\frontend\src\modules` 中的AI校对模块接口
- **实时进度条**：显示当前文档的校对进度百分比
- **停止校对**：支持中途停止校对任务
- **校对通过**：确认校对结果，文档状态更新为已通过
- **校对拒绝**：拒绝校对结果，文档状态更新为已拒绝

### 7. 状态管理和流转
- 校对通过和校对拒绝的文档自动转入"已预审文档"模块
- 支持批量状态更新
- 提供校对历史记录查看

## 技术实现要求
- 使用Vue3 + TypeScript + Element Plus开发
- 集成现有的AI校对引擎模块（src/modules/ai-proofreading-engine/）
- 使用MSW Mock API模拟文档转换和AI校对接口
- 支持并发校对控制（最多同时处理3-5个文档）
- 实现实时进度更新和状态同步
- 提供完整的错误处理和用户反馈机制

## 用户体验要求
- 界面布局清晰，操作流程直观
- 支持键盘快捷键操作
- 提供操作确认对话框
- 实时显示校对进度和状态变化
- 支持校对结果预览和编辑


# 为AI智能审校系统的"未预审文档"模块添加文档详情查看功能，具体要求如下：

**功能位置**：
- 在未预审文档列表的表格操作列中的"详情"按钮

**功能需求**：
1. 点击"详情"按钮后，打开文档详情对话框/抽屉组件
2. 以只读模式显示文档的完整信息，包括但不限于：
   - 文档基本信息（标题、作者、上传时间、文件大小等）
   - 文档内容预览（支持.txt/.docx/.pdf/.md格式）
   - 文档元数据（项目类型、分类、标签等）
   - 文件路径和存储信息

**技术要求**：
- 使用Vue3 + TypeScript + Element Plus实现
- 采用el-dialog或el-drawer组件展示详情
- 确保UI风格与现有系统保持一致
- 支持响应式设计，适配不同屏幕尺寸
- 纯查看功能，不包含任何编辑操作

**实现步骤**：
1. 创建文档详情组件（如DocumentDetailDialog.vue）
2. 在未预审文档列表组件中集成详情功能
3. 实现文档数据获取和展示逻辑
4. 确保与现有Mock API数据结构兼容


# 为AI智能审校系统的"未预审文档"模块添加文档编辑功能，具体要求如下：

**功能位置**：
- 在未预审文档列表的表格操作列中的"编辑"按钮（与现有的"详情"按钮并列）

**功能需求**：
1. 点击"编辑"按钮后，打开文档编辑对话框/抽屉组件
2. 支持编辑文档的可修改信息，包括但不限于：
   - 文档标题、内容简介
   - 文档分类、标签
   - 作者信息（姓名、单位、简介）
   - 预计完成时间
   - 文档状态（如需要）
3. 提供表单验证，确保必填字段完整性
4. 支持保存修改并更新文档信息
5. 提供取消操作，支持未保存时的确认提示

**技术要求**：
- 使用Vue3 + TypeScript + Element Plus实现
- 采用el-dialog或el-drawer组件展示编辑表单
- 复用现有的CreateDocumentDialog组件设计模式
- 确保UI风格与现有系统保持一致
- 支持响应式设计，适配不同屏幕尺寸
- 集成现有的API接口（updateDocument方法）

**实现步骤**：
1. 创建文档编辑组件（如EditDocumentDialog.vue）
2. 在未预审文档列表组件中集成编辑功能
3. 实现文档数据获取、编辑和保存逻辑
4. 确保与现有Mock API数据结构兼容
5. 添加适当的权限控制和状态管理

**验证要求**：
- 编辑功能能正确加载现有文档数据
- 表单验证正确工作
- 保存操作能成功更新文档信息
- 取消操作能正确处理未保存的更改
- 在不同设备上响应式布局正常

# 为AI智能审校系统开发"AI批量审校/未校对文档"模块页面，具体要求如下：

**功能定位**：
- 页面路径：`frontend/src/features/ai-batch-proofreading/views/UnproofreadDocuments.vue`
- 功能目标：展示需要AI校对的文档列表，支持批量校对操作

**设计参考**：
- 严格参考 `D:\AIpreadfrood\frontend\src\features\content-review\views\UnreviewedDocuments.vue` 的页面布局和CSS风格
- 保持与现有系统一致的UI/UX设计语言
- 复用相同的组件架构模式（搜索筛选、数据表格、分页等）

**核心功能需求**：
1. **文档列表展示**：显示待校对文档的基本信息（标题、类型、状态、作者、创建时间等）
2. **搜索筛选功能**：支持按文档标题、作者、状态、类型、分类等条件筛选
3. **批量操作**：支持批量选择文档进行AI校对
4. **单个文档操作**：详情、编辑、AI校对、删除等操作
5. **状态管理**：文档校对状态的实时更新和流转

**技术实现要求**：
- 使用Vue3 + TypeScript + Element Plus技术栈
- 集成现有的AI校对引擎模块（`src/modules/ai-proofreading-engine/`）
- 使用MSW Mock API模拟后端接口
- 遵循现有的代码规范和架构模式
- 支持响应式设计，适配不同屏幕尺寸

**数据结构**：
- 复用现有的Document接口定义
- 扩展校对相关的状态字段（未校对、校对中、校对完成、校对失败等）
- 支持校对进度跟踪和结果展示

**用户体验要求**：
- 页面加载时间<2秒
- 操作响应时间<500ms
- 提供清晰的操作反馈和状态提示
- 支持键盘快捷键操作
- 错误处理和用户友好的提示信息


# 修改AI批量审校模块中的"未校对文档"页面，具体要求如下：

**功能目标：**
在未校对文档列表的表格中，为每行文档添加"AI校对"操作按钮，实现单个文档的AI智能校对功能。

**详细需求：**
1. **按钮位置：** 在未校对文档表格的操作列中添加"AI校对"按钮
2. **交互流程：**
   - 点击"AI校对"按钮后，弹出AI校对对话框/抽屉组件
   - 对话框中显示当前选中文档的基本信息（标题、类型、大小等）
   - 提供AI模型选择和校对参数配置选项
   - 显示校对进度和实时状态更新
3. **校对处理：**
   - 集成现有的ai-proofreading-engine模块进行文档处理
   - 支持.txt/.docx/.pdf/.md等格式的文档校对
   - 实现智能分块处理和进度跟踪
4. **状态管理：**
   - 校对成功后，自动将文档从"未校对文档"列表移除
   - 将校对完成的文档转移到"待审校文档"页面
   - 生成标准格式的校对报告：'(文件标题)-AI预审意见表.md'
5. **用户体验：**
   - 提供校对过程中的取消操作
   - 显示详细的错误信息和重试机制
   - 保持与现有UnreviewedDocuments.vue页面的UI风格一致

**技术要求：**
- 使用Vue3 + TypeScript + Element Plus技术栈
- 复用现有的AI校对引擎和文档处理模块
- 确保响应时间<500ms，支持并发处理
- 添加完整的错误处理和用户反馈机制


# AI智能审校系统 - 待审核文档模块开发

请为AI智能审校系统实现"AI批量审校/待审核文档"模块页面，该页面用于管理已完成AI批量校对但需要人工审核的文档。

## 核心业务逻辑
- **数据来源**：显示从"未校对文档"模块经AI批量校对后自动转入的文档
- **业务流程**：待审核文档 → 人工审核 → 审核通过/驳回 → 转入相应状态
- **状态管理**：待审校、审校中、审校完成、审校驳回

## 页面功能需求

### 1. 文档列表展示
- 显示所有待审核文档的数据表格
- 严格参考 `UnreviewedDocuments.vue` 的布局、CSS样式和颜色主题保持一致性
- 使用相同的 `el-card`、`el-table` 组件结构和样式类名
- 保持相同的页面头部、搜索区域、表格区域的布局比例

### 2. 多维度搜索筛选系统
实现与UnreviewedDocuments.vue相同的搜索筛选布局：
- **标题搜索**：`el-input` 支持文档标题关键词模糊搜索
- **项目类型筛选**：`el-select` 下拉选择（学术论文、技术报告、商业文档等）
- **审核状态筛选**：`el-select` 下拉选择（待审校、审校中、审校完成、审校驳回）
- **文档分类筛选**：`el-select` 下拉选择（按业务分类）
- **时间范围筛选**：`el-date-picker` 支持创建时间、更新时间、预计完成时间的日期范围选择
- **搜索按钮**：`el-button type="primary"` 执行搜索
- **重置按钮**：`el-button` 一键清空所有筛选条件

### 3. 批量操作功能
- **批量选择**：表格支持多选checkbox
- **批量审核**：将选中的待审核文档批量提交人工审核流程
- **批量导出**：批量导出选中文档的AI校对报告
- **批量删除**：批量删除选中文档（需二次确认对话框）

### 4. 数据表格字段定义
使用 `el-table` 组件，包含以下列：
- **选择列**：`el-table-column type="selection"` 支持批量操作
- **序号列**：`el-table-column type="index"` 显示行号
- **文档标题**：`el-table-column prop="title"` 可点击查看详情，支持排序
- **AI校对报告**：`el-table-column prop="aiReport"` 显示报告摘要，支持预览
- **审核状态**：`el-table-column prop="status"` 使用 `el-tag` 显示状态标签
- **文档分类**：`el-table-column prop="category"` 显示分类标签
- **作者信息**：`el-table-column prop="author"` 显示作者姓名
- **作者单位**：`el-table-column prop="organization"` 显示所属机构
- **创建者**：`el-table-column prop="creator"` 显示文档创建人
- **创建时间**：`el-table-column prop="createTime"` 支持排序
- **更新时间**：`el-table-column prop="updateTime"` 支持排序
- **预计完成时间**：`el-table-column prop="estimatedTime"` 支持排序
- **操作列**：`el-table-column` 包含操作按钮组

### 5. 单行操作按钮
每行文档的操作列包含：
- **开始审核**：`el-button type="primary" size="small"` 将文档提交人工审核流程
- **查看详情**：`el-button type="info" size="small"` 打开文档详情对话框
- **导出报告**：`el-button type="success" size="small"` 导出AI校对报告（PDF/Word/Markdown）
- **删除文档**：`el-button type="danger" size="small"` 删除文档记录（需确认对话框）

## 技术实现规范

### 1. 文件结构要求
- **文件路径**：`src/views/ai-batch-proofreading/PendingReviewDocuments.vue`
- **组件命名**：`PendingReviewDocuments`
- **路由配置**：添加到 `src/router/index.ts` 中AI批量审校模块下

### 2. 技术栈要求
- **框架**：Vue3 Composition API + TypeScript
- **UI组件库**：Element Plus（版本与项目保持一致）
- **状态管理**：Pinia stores
- **HTTP请求**：集成现有的API服务层
- **虚拟滚动**：集成现有的虚拟滚动组件处理大数据量

### 3. 数据接口集成
- **Mock API**：集成现有MSW mock服务，添加待审核文档相关接口
- **数据类型**：使用现有的Document接口类型定义
- **API调用**：复用现有的文档管理API服务模块

### 4. 性能优化要求
- **虚拟滚动**：表格支持1000+文档的流畅滚动
- **分页加载**：支持分页和懒加载
- **搜索防抖**：搜索输入防抖处理（300ms）
- **缓存策略**：实现搜索结果缓存

## 用户体验规范

### 1. 性能指标
- **首屏加载**：页面初始加载时间 < 2秒
- **搜索响应**：搜索操作响应时间 < 500ms
- **表格渲染**：1000条数据渲染时间 < 1秒
- **操作反馈**：所有用户操作提供即时反馈

### 2. 交互体验
- **加载状态**：所有异步操作显示loading状态
- **操作确认**：删除等危险操作需要二次确认
- **错误处理**：网络错误、数据错误的友好提示
- **键盘支持**：支持常用快捷键操作

### 3. 响应式设计
- **桌面端**：≥1200px 显示完整表格
- **平板端**：768px-1199px 隐藏部分非关键列
- **移动端**：<768px 使用卡片式布局

## 验收标准

### 1. 功能完整性验收
- [ ] 页面布局与UnreviewedDocuments.vue保持一致
- [ ] 所有搜索筛选功能正常工作
- [ ] 批量操作功能完整实现
- [ ] 单行操作按钮功能正确
- [ ] 数据表格显示完整字段

### 2. 技术质量验收
- [ ] TypeScript类型定义完整
- [ ] 组件代码结构清晰
- [ ] 错误处理机制完善
- [ ] 性能指标达标
- [ ] 代码注释详细（中文）

### 3. 用户体验验收
- [ ] 页面加载流畅
- [ ] 操作响应及时
- [ ] 错误提示友好
- [ ] 响应式设计正确
- [ ] 无明显UI缺陷

## 开发优先级
1. **P0（核心功能）**：基础页面布局、数据表格显示、基本搜索功能
2. **P1（重要功能）**：批量操作、单行操作、状态管理
3. **P2（优化功能）**：性能优化、响应式设计、用户体验提升

请严格按照以上规范实现该模块，确保与现有系统的一致性和可维护性。


# 开发在线AI审校系统，基于Vue3 + TypeScript + Element Plus技术栈。系统需要实现多文档段落对比校对功能，支持PDF原稿预览和实时AI校对。

## 技术架构要求
- **前端框架**：Vue3 + TypeScript + Element Plus
- **富文本编辑器**：wangEditor v5（需验证与Vue3兼容性）
- **AI引擎集成**：集成ai-proofreading-engine模块，提供校对API接口
- **开发工具**：使用MSW（Mock Service Worker）进行API模拟和测试
- **性能指标**：页面初始加载时间<2秒，AI校对响应时间<500毫秒
- **代码规范**：遵循Vue3 Composition API + TypeScript严格模式

## 核心功能需求

### 1. 三栏布局设计
- **左侧栏（40%宽度）**：PDF原稿预览窗口，支持滚动同步
-
- **右侧栏（60%宽度）**：原文显示区（可编辑器）和校对结果区（可编辑编辑器）一组上下布局，有多组上下布局
- **布局特性**：支持按文档段落分组显示，每组包含对应的原文和校对结果编辑器对

### 2. PDF原稿预览功能
- **显示要求**：在左侧固定区域显示PDF文档
- **同步滚动**：与上下两栏编辑器内容位置保持同步
- **交互功能**：支持缩放、页面跳转，点击PDF区域可定位到对应编辑器段落

### 3. 原文显示区（中间栏）
- **功能定位**：展示原始文档内容，并标记AI建议的修改
- **视觉标记系统**：
  - 建议删除内容：`text-decoration: line-through; color: #ff4d4f; background-color: #fff2f0;`
  - 建议修改内容：`background-color: #fff7e6; border-left: 3px solid #faad14;`
  - 保持不变内容：维持原始格式样式
- **交互限制**：完全只读模式，禁止任何编辑操作
- **编辑器配置**：使用wangEditor v5，禁用所有编辑工具栏

### 4. 校对结果区（右侧栏）
- **功能定位**：显示AI校对后的完整内容，支持用户手动调整
- **视觉标记系统**：
  - 新增内容：`background-color: #e6f7ff; color: #1890ff; border-left: 3px solid #1890ff;`
  - 修改内容：`background-color: #f6ffed; border-left: 3px solid #52c41a;`
  - 普通内容：保持标准编辑器样式
- **交互功能**：完整编辑功能，支持富文本格式调整
- **编辑器配置**：使用wangEditor v5，提供完整工具栏

### 5. AI实时校对引擎
- **触发机制**：
  - 自动触发：用户停止输入500毫秒后自动发起校对请求
  - 手动触发：提供"重新校对"和"校对当前段落"按钮
  - 批量触发：支持"校对全文"功能
- **校对能力范围**：
  - 语法错误检测与智能修正
  - 错别字识别与上下文纠正
  - 标点符号规范化（中英文混排处理）
  - 语句通顺性和逻辑性优化
  - 专业术语一致性检查
- **处理流程**：
  1. 提取当前段落或全文内容
  2. 调用ai-proofreading-engine API接口
  3. 解析返回的校对建议（包含位置、类型、原文、建议文本）
  4. 生成差异对比数据结构
  5. 同步更新原文显示区和校对结果区的内容和标记
  6. 记录校对历史和用户接受/拒绝操作

### 6. 用户交互功能
- **校对建议处理**：
  - 逐条接受/拒绝校对建议
  - 批量接受当前段落所有建议
  - 撤销最近的接受/拒绝操作
- **导航功能**：
  - 段落间快速跳转
  - 校对问题定位和高亮
  - 进度指示器显示校对完成度

## 技术实现要求

### 1. 组件架构设计
- **主组件**：`AIProofreadingSystem.vue`
- **子组件**：
  - `PDFPreview.vue`：PDF预览组件
  - `OriginalTextEditor.vue`：原文显示编辑器
  - `ProofreadResultEditor.vue`：校对结果编辑器
  - `ProofreadingToolbar.vue`：校对操作工具栏
- **参考设计**：基于UnreviewedDocuments.vue的UI模式和组件结构

### 2. 状态管理
- 使用Pinia或Vuex管理校对状态、文档内容、用户操作历史
- 实现校对建议的数据结构：`{id, type, position, original, suggestion, status}`

### 3. 性能优化
- 实现虚拟滚动处理大文档
- 使用防抖机制控制AI校对请求频率
- 缓存校对结果避免重复请求

### 4. 错误处理与用户体验
- 完善的网络错误、AI服务异常处理机制
- 加载状态指示器和进度提示
- 用户操作确认和撤销功能
- 响应式设计适配不同屏幕尺寸

## 开发实施流程
1. **技术调研与架构设计**：分析现有代码结构，设计组件架构和数据流
2. **任务分解**：创建详细的开发任务列表，包含优先级和依赖关系
3. **基础组件开发**：先实现PDF预览和双编辑器基础布局
4. **AI集成开发**：集成ai-proofreading-engine，实现校对逻辑
5. **交互功能完善**：实现用户操作、状态管理和视觉反馈
6. **测试与优化**：使用MSW进行模拟测试，性能调优
7. **文档与部署**：提供技术文档和使用说明

## 质量保证要求
- **代码注释**：所有新增代码必须包含详细的中文注释，说明功能和实现逻辑
- **类型安全**：充分利用TypeScript类型系统，避免运行时错误
- **测试覆盖**：编写单元测试和集成测试，确保功能稳定性
- **代码审查**：遵循项目既有的代码规范和最佳实践
