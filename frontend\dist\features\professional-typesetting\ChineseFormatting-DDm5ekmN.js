import{d as a,c as s,a as e,Q as n,I as l,ag as r,o as t}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as d}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const c={class:"chinese-formatting"},o={class:"content"},p=d(a({__name:"ChineseFormatting",setup:a=>(a,d)=>{const p=r("el-card");return t(),s("div",c,[d[2]||(d[2]=e("div",{class:"page-header"},[e("h1",null,"中文排版文档"),e("p",{class:"page-description"},"处理中文文档的专业排版")],-1)),e("div",o,[n(p,null,{header:l(()=>d[0]||(d[0]=[e("div",{class:"card-header"},[e("span",null,"中文排版工具")],-1)])),default:l(()=>[d[1]||(d[1]=e("p",null,"中文排版功能开发中...",-1))]),_:1,__:[1]})])])}}),[["__scopeId","data-v-d3b45f63"]]);export{p as default};
