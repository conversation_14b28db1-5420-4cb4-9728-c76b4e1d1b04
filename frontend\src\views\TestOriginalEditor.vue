<template>
  <div class="test-original-editor">
    <div class="test-header">
      <h2>OriginalTextEditor 下拉菜单收起功能测试</h2>
      <p>测试 OriginalTextEditor 的下拉菜单收起行为，作为参考对比：</p>
      <ul>
        <li>点击工具栏下拉菜单（字体、颜色、字号等）</li>
        <li>选择菜单项后是否自动收起</li>
        <li>点击外部区域是否自动收起</li>
        <li>按 ESC 键是否自动收起</li>
      </ul>
    </div>

    <div class="editor-container">
      <OriginalTextEditor
        :content="testContent"
        :suggestions="testSuggestions"
        :readonly="false"
        :show-toolbar="true"
        height="400px"
        @content-change="handleContentChange"
        @suggestion-accept="handleSuggestionAccept"
        @suggestion-reject="handleSuggestionReject"
      />
    </div>

    <div class="comparison-container">
      <h3>对比测试 - ProofreadResultEditor</h3>
      <ProofreadResultEditor
        :content="testContent"
        :changes="testChanges"
        :editable="true"
        edit-mode="edit"
        :show-toolbar="true"
        height="400px"
        @content-change="handleContentChange2"
        @change-accept="handleChangeAccept"
        @change-reject="handleChangeReject"
        @save="handleSave"
      />
    </div>

    <div class="test-instructions">
      <h3>对比测试步骤：</h3>
      <ol>
        <li><strong>测试 OriginalTextEditor（上方）：</strong>
          <ul>
            <li>点击字体选择按钮，观察下拉菜单行为</li>
            <li>选择一个字体，观察是否自动收起</li>
            <li>点击颜色按钮，然后点击外部区域</li>
            <li>点击字号按钮，然后按 ESC 键</li>
          </ul>
        </li>
        <li><strong>测试 ProofreadResultEditor（下方）：</strong>
          <ul>
            <li>执行相同的操作</li>
            <li>对比两者的行为差异</li>
            <li>记录哪个组件的下拉菜单收起功能正常</li>
          </ul>
        </li>
      </ol>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import OriginalTextEditor from '@/features/online-proofreading/components/OriginalTextEditor.vue'
import ProofreadResultEditor from '@/features/online-proofreading/components/ProofreadResultEditor.vue'
import type { ProofreadingSuggestion, ProofreadingChange } from '@/features/online-proofreading/types'

// 测试内容
const testContent = ref(`
<h1>对比测试标题</h1>
<p>这是一段测试文本，用于对比 <strong>OriginalTextEditor</strong> 和 <strong>ProofreadResultEditor</strong> 的下拉菜单收起行为。</p>
<p>请分别测试两个编辑器的工具栏下拉菜单：</p>
<ul>
  <li>字体选择下拉菜单</li>
  <li>字号选择下拉菜单</li>
  <li>文字颜色选择器</li>
  <li>背景颜色选择器</li>
  <li>标题选择下拉菜单</li>
</ul>
<p>观察哪个编辑器的下拉菜单能够正确自动收起。</p>
`)

// 测试建议数据
const testSuggestions = ref<ProofreadingSuggestion[]>([
  {
    id: 'suggestion-1',
    type: 'grammar',
    position: { start: 50, end: 60 },
    original: '测试文本',
    suggestion: '示例文本',
    reason: '用词更准确',
    confidence: 0.9,
    status: 'pending',
    timestamp: new Date(),
  },
])

// 测试修改数据
const testChanges = ref<ProofreadingChange[]>([
  {
    id: 'change-1',
    type: 'replace',
    position: { start: 50, end: 60 },
    original: '测试文本',
    modified: '示例文本',
    reason: '用词更准确',
    timestamp: new Date(),
    confidence: 0.9,
  },
])

// 事件处理函数
const handleContentChange = (content: string) => {
  console.log('OriginalTextEditor 内容变化:', content)
}

const handleContentChange2 = (content: string) => {
  console.log('ProofreadResultEditor 内容变化:', content)
}

const handleSuggestionAccept = (suggestion: ProofreadingSuggestion) => {
  console.log('接受建议:', suggestion)
}

const handleSuggestionReject = (suggestion: ProofreadingSuggestion) => {
  console.log('拒绝建议:', suggestion)
}

const handleChangeAccept = (change: ProofreadingChange) => {
  console.log('接受修改:', change)
}

const handleChangeReject = (change: ProofreadingChange) => {
  console.log('拒绝修改:', change)
}

const handleSave = (content: string) => {
  console.log('保存内容:', content)
}
</script>

<style scoped>
.test-original-editor {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 20px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #0ea5e9;
}

.test-header h2 {
  color: #0ea5e9;
  margin-bottom: 10px;
}

.test-header ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-header li {
  margin: 5px 0;
}

.editor-container {
  margin: 20px 0;
  border: 2px solid #0ea5e9;
  border-radius: 8px;
  overflow: hidden;
}

.comparison-container {
  margin: 30px 0;
  border: 2px solid #f59e0b;
  border-radius: 8px;
  overflow: hidden;
}

.comparison-container h3 {
  background: #fef3c7;
  color: #d97706;
  margin: 0;
  padding: 10px 15px;
  border-bottom: 1px solid #f59e0b;
}

.test-instructions {
  margin-top: 20px;
  padding: 20px;
  background: #f0fdf4;
  border-radius: 8px;
  border-left: 4px solid #22c55e;
}

.test-instructions h3 {
  color: #16a34a;
  margin-bottom: 10px;
}

.test-instructions ol {
  margin: 10px 0;
  padding-left: 20px;
}

.test-instructions li {
  margin: 8px 0;
  line-height: 1.5;
}

.test-instructions ul {
  margin: 8px 0;
  padding-left: 20px;
}

.test-instructions ul li {
  margin: 4px 0;
}
</style>
