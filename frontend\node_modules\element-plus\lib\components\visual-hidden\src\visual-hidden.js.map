{"version": 3, "file": "visual-hidden.js", "sources": ["../../../../../../packages/components/visual-hidden/src/visual-hidden.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { StyleValue } from 'vue'\n\nexport const visualHiddenProps = buildProps({\n  style: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n    default: () => ({}),\n  },\n} as const)\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,iBAAiB,GAAGA,kBAAU,CAAC;AAC5C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,CAAC;;;;"}