import{d as a,c as s,a as e,Q as l,I as r,ag as t,o as c}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as u}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const n={class:"classical-literature-query"},d={class:"content"},o=u(a({__name:"ClassicalLiteratureQuery",setup:a=>(a,u)=>{const o=t("el-card");return c(),s("div",n,[u[1]||(u[1]=e("div",{class:"page-header"},[e("h1",null,"古诗文查询"),e("p",{class:"page-description"},"查询古典文献和诗词典故")],-1)),e("div",d,[l(o,null,{default:r(()=>u[0]||(u[0]=[e("p",null,"古诗文查询功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-94e4d7fc"]]);export{o as default};
