{"name": "proofreading-frontend", "version": "1.0.0", "description": "AI智能审校系统前端应用", "type": "module", "scripts": {"dev": "vite", "dev:mock": "cross-env VITE_ENABLE_MOCK=true vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:coverage": "vitest --coverage", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "prepare": "husky install", "clean": "rimraf dist node_modules/.vite", "analyze": "vite build --mode analyze", "verify-toolchain": "node scripts/verify-toolchain.cjs", "mock:init": "msw init public --save"}, "dependencies": {"@element-plus/icons-vue": "^2.2.0", "@wangeditor/editor": "^5.1.0", "@wangeditor/editor-for-vue": "^5.1.0", "axios": "^1.6.0", "element-plus": "^2.4.0", "pinia": "^2.1.0", "vue": "^3.4.0", "vue-router": "^4.2.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.3", "@types/node": "^20.8.10", "@vitejs/plugin-vue": "^4.4.0", "@vitest/coverage-v8": "^0.34.6", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "husky": "^8.0.0", "jsdom": "^22.1.0", "lint-staged": "^15.0.0", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "rimraf": "^5.0.5", "typescript": "~5.2.0", "unplugin-auto-import": "^0.16.0", "unplugin-vue-components": "^0.25.0", "vite": "^5.0.0", "vitest": "^0.34.6", "vue-tsc": "^1.8.19"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"], "*.{css,scss,less,html,json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}