<template>
  <div class="ai-proofreading-system">
    <!-- 顶部工具栏 -->
    <div class="toolbar-section">
      <ProofreadingToolbar
        :loading="loading.proofreading"
        :progress="progress"
        @start-proofreading="handleStartProofreading"
        @pause-proofreading="handlePauseProofreading"
        @reset-proofreading="handleResetProofreading"
        @upload-document="handleUploadDocument"
        @load-mock-data="handleLoadMockData"
        @export-document="handleExportDocument"
        @settings-change="handleSettingsChange"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：PDF预览区域 -->
      <div class="pdf-preview-section" :class="{ collapsed: isPdfCollapsed }">
        <!-- PDF 预览内容 -->
        <div class="pdf-content" v-show="!isPdfCollapsed">
          <PDFPreview
            :pdf-url="documentData?.pdfUrl || ''"
            :current-page="currentPage"
            :scroll-sync="scrollSync"
            @page-change="handlePageChange"
            @position-change="handlePDFPositionChange"
            @pdf-upload="handlePDFUpload"
            @pdf-upload-success="handlePDFUploadSuccess"
            @pdf-upload-error="handlePDFUploadError"
          />
        </div>

        <!-- 折叠/展开切换按钮 -->
        <div class="pdf-toggle-button" @click="togglePdfPreview">
          <el-icon :size="16">
            <ArrowLeft v-if="!isPdfCollapsed" />
            <ArrowRight v-if="isPdfCollapsed" />
          </el-icon>
        </div>
      </div>

      <!-- 右侧：编辑器区域 (60%) -->
      <div class="editors-section">
        <!-- 段落组循环 -->
        <div
          v-for="(paragraph, index) in paragraphs"
          :key="`paragraph-${index}`"
          class="paragraph-group"
          :class="{ active: currentParagraphIndex === index }"
        >
          <!-- 段落标题 -->
          <div class="paragraph-header">
            <span class="paragraph-title">段落 {{ index + 1 }}</span>
            <div class="paragraph-actions">
              <el-button
                size="small"
                type="primary"
                :loading="paragraph.proofreading"
                @click="proofreadParagraph(index)"
              >
                校对此段落
              </el-button>
              <el-button
                size="small"
                type="success"
                :disabled="!paragraph.proofreadResult"
                @click="acceptChanges(index)"
              >
                接受修改
              </el-button>
              <el-button
                size="small"
                type="warning"
                :disabled="!paragraph.proofreadResult"
                @click="rejectChanges(index)"
              >
                拒绝修改
              </el-button>
            </div>
          </div>

          <!-- 原文显示编辑器 -->
          <div
            class="original-editor-container"
            :style="{ minHeight: originalEditorHeight + 'px' }"
          >
            <OriginalTextEditor
              :content="paragraph.originalText"
              :suggestions="paragraph.suggestions"
              :readonly="false"
              :height="'200px'"
              @suggestion-click="handleSuggestionClick"
              @ai-proofreading-request="handleAIProofreadingFromEditor"
              @suggestion-accept="handleSuggestionAccept"
              @suggestion-reject="handleSuggestionReject"
              @suggestion-ignore="handleSuggestionIgnore"
            />
            <!-- 拖拽手柄 -->
            <div
              class="resize-handle resize-handle-original"
              @mousedown="startResize($event, 'original')"
            >
              <div class="resize-handle-line"></div>
            </div>
          </div>

          <!-- 校对结果编辑器 -->
          <div class="result-editor-container" :style="{ minHeight: resultEditorHeight + 'px' }">
            <ProofreadResultEditor
              v-model:content="paragraph.proofreadResult"
              :changes="paragraph.changes"
              :editable="true"
              :edit-mode="'edit'"
              :height="'200px'"
              :show-toolbar="true"
              :show-changes-history="true"
              :show-statistics="true"
              @content-change="handleResultChange(index, $event)"
              @change-accept="handleChangeAccept"
              @change-reject="handleChangeReject"
              @save="handleResultSave(index, $event)"
            />
            <!-- 拖拽手柄 -->
            <div
              class="resize-handle resize-handle-result"
              @mousedown="startResize($event, 'result')"
            >
              <div class="resize-handle-line"></div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="paragraphs.length === 0" class="empty-state">
          <el-empty description="请上传文档开始AI审校">
            <el-button type="primary" @click="handleUploadDocument"> 上传文档 </el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 进度指示器 -->
    <div class="progress-section">
      <ProofreadingProgress
        :total="paragraphs.length"
        :completed="completedParagraphs"
        :current="currentParagraphIndex"
        @jump-to="handleJumpToParagraph"
      />
    </div>

    <!-- 性能监控组件 -->
    <PerformanceMonitor
      ref="performanceMonitorRef"
      :enabled="true"
      :auto-optimize="false"
      :thresholds="{
        loadTime: 2000,
        responseTime: 500,
        memory: 100 * 1024 * 1024,
      }"
    />

    <!-- 文档上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传文档"
      width="600px"
      :close-on-click-modal="false"
    >
      <DocumentUpload @upload-success="handleUploadSuccess" @upload-error="handleUploadError" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * AI审校系统主组件
 *
 * 功能特性：
 * - 三栏布局：PDF预览 + 原文显示 + 校对结果
 * - 段落级别的AI校对处理
 * - 实时滚动同步
 * - 校对建议的接受/拒绝操作
 * - 进度跟踪和导航
 */

import { ref, computed, onMounted, onUnmounted, watch, h } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'

// 导入组件
import PDFPreview from '../components/PDFPreview.vue'
import OriginalTextEditor from '../components/OriginalTextEditor.vue'
import ProofreadResultEditor from '../components/ProofreadResultEditor.vue'
import ProofreadingToolbar from '../components/ProofreadingToolbar.vue'
import ProofreadingProgress from '../components/ProofreadingProgress.vue'
import DocumentUpload from '../components/DocumentUpload.vue'
import PerformanceMonitor from '../components/PerformanceMonitor.vue'

// 导入类型定义
import type { DocumentData, ProofreadingSuggestion, ProofreadingConfig } from '../types'
import { ParagraphStatus } from '../types'

// 导入状态管理
import { useAIProofreadingStore } from '../stores/aiProofreadingStore'

// 导入AI引擎
import { AIProofreadingEngine } from '@/modules/ai-proofreading-engine'

// 状态管理
const aiProofreadingStore = useAIProofreadingStore()
const { documentData, paragraphs, currentParagraphIndex, progress, loading } =
  storeToRefs(aiProofreadingStore)

// 本地响应式数据
const uploadDialogVisible = ref(false)
const currentPage = ref(1)
const scrollSync = ref(true)
const isPdfCollapsed = ref(false) // PDF 预览框折叠状态

// 编辑器高度调整相关
const originalEditorHeight = ref(250)
const resultEditorHeight = ref(250)
const isResizing = ref(false)
const resizeType = ref<'original' | 'result' | null>(null)
const startY = ref(0)
const startHeight = ref(0)

// 组件引用
const performanceMonitorRef = ref<InstanceType<typeof PerformanceMonitor>>()

// 计算属性
const completedParagraphs = computed(() => {
  return paragraphs.value.filter((p) => p.proofreadResult).length
})

// 生命周期钩子
onMounted(() => {
  console.log('AI审校系统已初始化')
  // 初始化AI引擎
  initializeAIEngine()
  // 绑定键盘快捷键
  bindKeyboardShortcuts()
})

// 组件卸载时清理
onUnmounted(() => {
  unbindKeyboardShortcuts()
})

// 监听段落变化，自动滚动同步
watch(currentParagraphIndex, (newIndex) => {
  if (scrollSync.value && newIndex >= 0) {
    scrollToCurrentParagraph()
  }
})

/**
 * 初始化AI引擎
 */
const initializeAIEngine = async () => {
  try {
    // 在开发环境下等待Mock服务启动
    if (import.meta.env.DEV) {
      console.log('🔄 等待Mock服务启动...')
      // 等待Mock服务启动完成（最多等待5秒）
      let retries = 0
      const maxRetries = 10
      while (retries < maxRetries) {
        try {
          // 测试Mock服务是否可用
          const testResponse = await fetch('/api/test', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
          })
          if (testResponse.ok) {
            console.log('✅ Mock服务已就绪')
            break
          }
        } catch (error) {
          console.log(`⏳ Mock服务未就绪，重试 ${retries + 1}/${maxRetries}...`)
        }
        retries++
        await new Promise((resolve) => setTimeout(resolve, 500)) // 等待500ms后重试
      }

      if (retries >= maxRetries) {
        console.warn('⚠️ Mock服务启动超时，继续尝试初始化AI引擎')
      }
    }

    // 导入必要的类型和枚举
    const { ModuleType } = await import('@/modules/ai-proofreading-engine/types/module-types')

    // 使用正确的UserSelections配置初始化AI引擎
    const defaultUserSelections = {
      aiModel: {
        id: 'deepseek-chat',
        name: 'DeepSeek Chat',
        provider: 'deepseek',
        maxTokens: 32000,
        costPerToken: 0.0014,
        capabilities: ['text'],
        supportedLanguages: ['zh-CN'],
        available: true,
      },
      promptTemplate: {
        id: 'default_online_proofreading',
        name: '默认在线审校模板',
        moduleType: ModuleType.ONLINE_PROOFREADING,
        category: 'general',
        template:
          '你是一个专业的文本校对助手。请仔细校对以下文本，纠正语法错误、拼写错误，并提供改进建议。',
        variables: [],
        description: '适用于在线实时审校的模板',
      },
      costBudget: 50, // 预算50元
    }

    await AIProofreadingEngine.initialize(ModuleType.ONLINE_PROOFREADING, defaultUserSelections)
    ElMessage.success('AI引擎初始化成功')
  } catch (error) {
    console.error('AI引擎初始化失败:', error)
    ElMessage.error('AI引擎初始化失败，请刷新页面重试')
  }
}

/**
 * 处理来自编辑器的AI校对请求
 */
const handleAIProofreadingFromEditor = async (data: { content: string; source: string }) => {
  console.log('收到来自编辑器的AI校对请求:', data)

  if (!data.content || data.content.trim().length === 0) {
    ElMessage.warning('请先输入需要校对的内容')
    return
  }

  // 如果当前没有段落，创建一个新段落
  if (paragraphs.value.length === 0) {
    const newParagraph = {
      id: `para-${Date.now()}`,
      index: 0,
      originalText: data.content,
      proofreadResult: undefined,
      suggestions: [],
      changes: [],
      proofreading: false,
      status: ParagraphStatus.PENDING,
      pdfPosition: {
        page: 1,
        y: 0,
        height: 100,
        relativePosition: 0,
      },
    }
    paragraphs.value = [newParagraph]
    currentParagraphIndex.value = 0
  } else {
    // 更新当前段落的原文内容
    const currentParagraph = paragraphs.value[currentParagraphIndex.value]
    if (currentParagraph) {
      currentParagraph.originalText = data.content
      currentParagraph.status = ParagraphStatus.PENDING
      currentParagraph.suggestions = []
      currentParagraph.changes = []
      currentParagraph.proofreadResult = undefined
    }
  }

  // 开始校对当前段落
  await handleStartProofreading()
}

/**
 * 开始校对
 */
const handleStartProofreading = async () => {
  if (paragraphs.value.length === 0) {
    ElMessage.warning('请先上传文档')
    return
  }

  try {
    await aiProofreadingStore.startBatchProofreading()
    ElMessage.success('开始批量校对')
  } catch (error) {
    console.error('开始校对失败:', error)
    ElMessage.error('开始校对失败，请重试')
  }
}

/**
 * 暂停校对
 */
const handlePauseProofreading = () => {
  aiProofreadingStore.pauseProofreading()
  ElMessage.info('校对已暂停')
}

/**
 * 重置校对
 */
const handleResetProofreading = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有校对结果吗？此操作不可撤销。', '确认重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    aiProofreadingStore.resetProofreading()
    ElMessage.success('校对结果已重置')
  } catch {
    // 用户取消操作
  }
}

/**
 * 导出文档
 */
const handleExportDocument = () => {
  // TODO: 实现文档导出功能
  ElMessage.info('导出功能开发中...')
}

/**
 * 校对单个段落
 */
const proofreadParagraph = async (index: number) => {
  const startTime = Date.now()

  try {
    await aiProofreadingStore.proofreadSingleParagraph(index)

    // 记录响应时间
    const responseTime = Date.now() - startTime
    performanceMonitorRef.value?.recordResponseTime(responseTime)

    ElMessage.success(`段落 ${index + 1} 校对完成`)
  } catch (error) {
    console.error('段落校对失败:', error)
    ElMessage.error('段落校对失败，请重试')
  }
}

/**
 * 接受修改
 */
const acceptChanges = (index: number) => {
  aiProofreadingStore.acceptParagraphChanges(index)
  ElMessage.success('修改已接受')
}

/**
 * 拒绝修改
 */
const rejectChanges = (index: number) => {
  aiProofreadingStore.rejectParagraphChanges(index)
  ElMessage.success('修改已拒绝')
}

/**
 * 处理建议点击
 */
const handleSuggestionClick = (suggestion: ProofreadingSuggestion) => {
  // 显示建议详情对话框
  showSuggestionDetail(suggestion)
}

/**
 * 显示建议详情
 */
const showSuggestionDetail = (suggestion: ProofreadingSuggestion) => {
  ElMessageBox({
    title: '校对建议详情',
    message: h('div', { class: 'suggestion-detail' }, [
      h('div', { class: 'suggestion-info' }, [
        h('p', [h('strong', '类型: '), getSuggestionTypeLabel(suggestion.type)]),
        h('p', [h('strong', '置信度: '), `${Math.round(suggestion.confidence * 100)}%`]),
        h('p', [h('strong', '位置: '), `第${suggestion.startIndex}-${suggestion.endIndex}字符`]),
      ]),
      h('div', { class: 'text-comparison' }, [
        h('div', { class: 'original-text' }, [
          h('strong', '原文: '),
          h(
            'span',
            { style: 'background: #fef0f0; padding: 2px 4px; border-radius: 3px;' },
            suggestion.originalText,
          ),
        ]),
        h('div', { class: 'suggested-text' }, [
          h('strong', '建议: '),
          h(
            'span',
            { style: 'background: #f0f9ff; padding: 2px 4px; border-radius: 3px;' },
            suggestion.suggestedText,
          ),
        ]),
      ]),
      h('div', { class: 'suggestion-reason' }, [
        h('strong', '修改原因: '),
        h('p', { style: 'margin: 8px 0; color: #666;' }, suggestion.reason),
      ]),
    ]),
    showCancelButton: true,
    confirmButtonText: '接受建议',
    cancelButtonText: '拒绝建议',
    distinguishCancelAndClose: true,
    type: 'info',
  })
    .then(() => {
      // 接受建议
      acceptSuggestion(suggestion)
    })
    .catch((action) => {
      if (action === 'cancel') {
        // 拒绝建议
        rejectSuggestion(suggestion)
      }
    })
}

/**
 * 接受建议
 */
const acceptSuggestion = (suggestion: ProofreadingSuggestion) => {
  aiProofreadingStore.acceptSuggestion(suggestion.id)
  ElMessage.success('建议已接受')
}

/**
 * 拒绝建议
 */
const rejectSuggestion = (suggestion: ProofreadingSuggestion) => {
  aiProofreadingStore.rejectSuggestion(suggestion.id)
  ElMessage.info('建议已拒绝')
}

/**
 * 获取建议类型标签
 */
const getSuggestionTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    grammar: '语法错误',
    spelling: '拼写错误',
    punctuation: '标点符号',
    word_choice: '用词建议',
    fluency: '语句通顺',
    formatting: '格式规范',
    terminology: '术语统一',
  }
  return labels[type] || type
}

/**
 * 处理建议接受
 */
const handleSuggestionAccept = (suggestion: ProofreadingSuggestion) => {
  acceptSuggestion(suggestion)
}

/**
 * 处理建议拒绝
 */
const handleSuggestionReject = (suggestion: ProofreadingSuggestion) => {
  rejectSuggestion(suggestion)
}

/**
 * 处理建议忽略
 */
const handleSuggestionIgnore = (suggestion: ProofreadingSuggestion) => {
  aiProofreadingStore.ignoreSuggestion(suggestion.id)
  ElMessage.info('建议已忽略')
}

/**
 * 处理结果内容变化
 */
const handleResultChange = (index: number, content: string) => {
  aiProofreadingStore.updateParagraphResult(index, content)
}

/**
 * 处理修改接受
 */
const handleChangeAccept = (change: any) => {
  console.log('接受修改:', change)
  ElMessage.success(`已接受修改: ${change.type}`)
  // TODO: 实现修改接受逻辑
}

/**
 * 处理修改拒绝
 */
const handleChangeReject = (change: any) => {
  console.log('拒绝修改:', change)
  ElMessage.info(`已拒绝修改: ${change.type}`)
  // TODO: 实现修改拒绝逻辑
}

/**
 * 处理结果保存
 */
const handleResultSave = (index: number, content: string) => {
  console.log('保存校对结果:', { index, content })
  aiProofreadingStore.updateParagraphResult(index, content)
  ElMessage.success('校对结果已保存')
}

/**
 * 处理页面变化
 */
const handlePageChange = (page: number) => {
  currentPage.value = page
}

/**
 * 处理PDF位置变化
 */
const handlePDFPositionChange = (position: number) => {
  // 根据PDF位置同步到对应段落
  const paragraphIndex = calculateParagraphFromPosition(position)
  if (paragraphIndex !== currentParagraphIndex.value) {
    aiProofreadingStore.setCurrentParagraph(paragraphIndex)
  }
}

/**
 * 跳转到指定段落
 */
const handleJumpToParagraph = (index: number) => {
  aiProofreadingStore.setCurrentParagraph(index)
  scrollToCurrentParagraph()
}

/**
 * 滚动到当前段落
 */
const scrollToCurrentParagraph = () => {
  const element = document.querySelector(
    `.paragraph-group:nth-child(${currentParagraphIndex.value + 1})`,
  )
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

/**
 * 根据PDF位置计算段落索引
 */
const calculateParagraphFromPosition = (position: number): number => {
  // TODO: 实现PDF位置到段落的映射逻辑
  return Math.floor(position * paragraphs.value.length)
}

/**
 * 上传文档
 */
const handleUploadDocument = () => {
  uploadDialogVisible.value = true
}

/**
 * 处理PDF上传
 */
const handlePDFUpload = (file: File) => {
  console.log('PDF文件上传:', file.name)
  // 这里可以添加额外的处理逻辑，比如提取文本内容
}

/**
 * 处理PDF上传成功
 */
const handlePDFUploadSuccess = (url: string) => {
  console.log('PDF上传成功，URL:', url)

  // 更新文档数据中的PDF URL
  if (documentData.value) {
    documentData.value.pdfUrl = url
  } else {
    documentData.value = {
      id: 'uploaded-pdf-' + Date.now(),
      title: '上传的PDF文档',
      type: 'PDF文档',
      createdAt: new Date().toISOString(),
      pdfUrl: url,
    }
  }

  ElMessage.success('PDF文档已成功加载到预览区域')
}

/**
 * 处理PDF上传错误
 */
const handlePDFUploadError = (error: string) => {
  console.error('PDF上传失败:', error)
  ElMessage.error('PDF上传失败: ' + error)
}

/**
 * 切换PDF预览框的折叠状态
 */
const togglePdfPreview = () => {
  isPdfCollapsed.value = !isPdfCollapsed.value
  ElMessage.info(isPdfCollapsed.value ? 'PDF预览已收起' : 'PDF预览已展开')
}

/**
 * 加载Mock示例数据
 */
const handleLoadMockData = () => {
  // 创建示例文档数据
  const mockDocument = {
    id: 'mock-doc-001',
    title: '示例文档 - 学术论文摘要',
    type: '学术论文',
    createdAt: new Date().toISOString(),
    pdfUrl: '', // 暂时不需要PDF
  }

  // 创建示例段落数据
  const mockParagraphs = [
    {
      id: 'para-1',
      index: 0,
      originalText:
        '人工智能技术在近年来得到了飞速的发展，特别是在自然语言处理领域取得了重大突破。深度学习模型如GPT、BERT等的出现，使得机器能够更好地理解和生成人类语言，为各种应用场景提供了强大的技术支撑。',
      proofreadResult: undefined,
      suggestions: [],
      changes: [],
      proofreading: false,
      status: ParagraphStatus.PENDING,
      pdfPosition: {
        page: 1,
        y: 100,
        height: 80,
        relativePosition: 0.33,
      },
    },
    {
      id: 'para-2',
      index: 1,
      originalText:
        '然而，人工智能技术的发展也带来了一些挑战和问题。首先，数据隐私和安全问题日益突出，如何在保护用户隐私的前提下充分利用数据成为了一个重要课题。其次，算法的公平性和透明度也受到了广泛关注，避免算法偏见和歧视成为了技术发展的重要考量。',
      proofreadResult: undefined,
      suggestions: [],
      changes: [],
      proofreading: false,
      status: ParagraphStatus.PENDING,
      pdfPosition: {
        page: 1,
        y: 200,
        height: 80,
        relativePosition: 0.66,
      },
    },
    {
      id: 'para-3',
      index: 2,
      originalText:
        '未来，人工智能技术将继续向更加智能化、人性化的方向发展。我们期待看到更多创新的应用场景，同时也需要建立完善的法律法规和伦理规范，确保人工智能技术能够真正造福人类社会。',
      proofreadResult: undefined,
      suggestions: [],
      changes: [],
      proofreading: false,
      status: ParagraphStatus.PENDING,
      pdfPosition: {
        page: 1,
        y: 300,
        height: 80,
        relativePosition: 1.0,
      },
    },
  ]

  // 更新数据
  documentData.value = mockDocument
  paragraphs.value = mockParagraphs
  currentParagraphIndex.value = 0

  ElMessage.success('示例文档加载成功！可以开始校对了。')
}

/**
 * 上传成功处理
 */
const handleUploadSuccess = (data: DocumentData) => {
  aiProofreadingStore.setDocumentData(data)
  uploadDialogVisible.value = false
  ElMessage.success('文档上传成功')
}

/**
 * 上传错误处理
 */
const handleUploadError = (error: Error) => {
  console.error('文档上传失败:', error)
  ElMessage.error('文档上传失败，请重试')
}

// ==================== 键盘快捷键处理 ====================

/**
 * 绑定键盘快捷键
 */
const bindKeyboardShortcuts = () => {
  document.addEventListener('keydown', handleKeyboardShortcut)
}

/**
 * 解绑键盘快捷键
 */
const unbindKeyboardShortcuts = () => {
  document.removeEventListener('keydown', handleKeyboardShortcut)
}

/**
 * 处理键盘快捷键
 */
const handleKeyboardShortcut = (event: KeyboardEvent) => {
  // Ctrl/Cmd + S: 保存当前内容
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    saveCurrentContent()
    return
  }

  // Ctrl/Cmd + Enter: 开始/暂停校对
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    if (loading.proofreading) {
      handlePauseProofreading()
    } else {
      handleStartProofreading()
    }
    return
  }

  // F11: 全屏预览
  if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
    return
  }

  // Escape: 退出全屏
  if (event.key === 'Escape') {
    event.preventDefault()
    exitFullscreen()
    return
  }

  // 上下箭头: 段落导航
  if (event.key === 'ArrowUp' && event.ctrlKey) {
    event.preventDefault()
    navigateToPreviousParagraph()
    return
  }

  if (event.key === 'ArrowDown' && event.ctrlKey) {
    event.preventDefault()
    navigateToNextParagraph()
    return
  }
}

/**
 * 保存当前内容
 */
const saveCurrentContent = () => {
  // TODO: 实现保存功能
  ElMessage.success('内容已保存')
}

/**
 * 切换全屏模式
 */
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    ElMessage.info('已进入全屏模式，按 ESC 退出')
  } else {
    document.exitFullscreen()
  }
}

/**
 * 退出全屏模式
 */
const exitFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen()
  }
}

/**
 * 导航到上一段落
 */
const navigateToPreviousParagraph = () => {
  if (currentParagraphIndex.value > 0) {
    aiProofreadingStore.setCurrentParagraph(currentParagraphIndex.value - 1)
    ElMessage.info(`已切换到段落 ${currentParagraphIndex.value + 1}`)
  }
}

/**
 * 导航到下一段落
 */
const navigateToNextParagraph = () => {
  if (currentParagraphIndex.value < paragraphs.value.length - 1) {
    aiProofreadingStore.setCurrentParagraph(currentParagraphIndex.value + 1)
    ElMessage.info(`已切换到段落 ${currentParagraphIndex.value + 1}`)
  }
}

/**
 * 处理设置变更
 */
const handleSettingsChange = (settings: ProofreadingConfig) => {
  aiProofreadingStore.updateSettings(settings)
  ElMessage.success('设置已更新')
}

// ==================== 编辑器高度调整功能 ====================

/**
 * 开始拖拽调整高度
 */
const startResize = (event: MouseEvent, type: 'original' | 'result') => {
  event.preventDefault()
  isResizing.value = true
  resizeType.value = type
  startY.value = event.clientY
  startHeight.value = type === 'original' ? originalEditorHeight.value : resultEditorHeight.value

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)

  // 添加拖拽时的样式
  document.body.style.cursor = 'ns-resize'
  document.body.style.userSelect = 'none'
}

/**
 * 处理拖拽调整
 */
const handleResize = (event: MouseEvent) => {
  if (!isResizing.value || !resizeType.value) return

  const deltaY = event.clientY - startY.value
  const newHeight = Math.max(200, startHeight.value + deltaY) // 最小高度200px

  if (resizeType.value === 'original') {
    originalEditorHeight.value = newHeight
  } else {
    resultEditorHeight.value = newHeight
  }
}

/**
 * 停止拖拽调整
 */
const stopResize = () => {
  isResizing.value = false
  resizeType.value = null

  // 移除全局鼠标事件监听
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)

  // 恢复默认样式
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}
</script>

<style scoped>
.ai-proofreading-system {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.toolbar-section {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 20px;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 6px;
  padding: 6px;
  overflow: hidden;
}

.pdf-preview-section {
  width: 40%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: width 0.3s ease-in-out;
  overflow: hidden;
}

.pdf-preview-section.collapsed {
  width: 48px; /* 只显示切换按钮的宽度 */
  min-width: 48px;
}

.pdf-content {
  width: 100%;
  height: 100%;
}

.pdf-toggle-button {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 10;
}

.pdf-toggle-button:hover {
  background: #337ecc;
  transform: translateY(-50%) scale(1.1);
}

.pdf-preview-section.collapsed .pdf-toggle-button {
  right: 8px;
  left: 8px;
  margin: auto;
  background: #67c23a;
}

.pdf-preview-section.collapsed .pdf-toggle-button:hover {
  background: #529b2e;
}

.editors-section {
  flex: 1; /* 改为 flex: 1 以自动占满剩余空间 */
  overflow-y: auto;
  padding-right: 8px;
  transition: all 0.3s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    gap: 6px;
  }

  .pdf-preview-section {
    width: 100%;
    height: 300px;
  }

  .pdf-preview-section.collapsed {
    height: 48px;
    width: 100%;
  }

  .editors-section {
    width: 100%;
  }

  .pdf-toggle-button {
    right: 16px;
  }

  .pdf-preview-section.collapsed .pdf-toggle-button {
    right: 16px;
    left: auto;
    margin: 0;
  }
}

.paragraph-group {
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.paragraph-group.active {
  border: 2px solid #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.paragraph-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
  border-radius: 8px 8px 0 0;
}

.paragraph-title {
  font-weight: 600;
  color: #303133;
}

.paragraph-actions {
  display: flex;
  gap: 8px;
}

.original-editor-container,
.result-editor-container {
  padding: 6px;
  min-height: 250px;
}

.original-editor-container {
  border-bottom: 1px solid #e4e7ed;
}

.progress-section {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 12px 20px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式 */
.editors-section::-webkit-scrollbar {
  width: 6px;
}

.editors-section::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.editors-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.editors-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 全局z-index层级管理 */
.ai-proofreading-system {
  /* 确保编辑器工具栏弹出框在最顶层 */
  position: relative;
  z-index: 1;
}

/* 编辑器容器z-index设置 */
.original-editor-container,
.result-editor-container {
  position: relative;
  z-index: 10;
}

/* 确保wangEditor工具栏弹出框不被遮挡 */
:deep(.w-e-toolbar) {
  position: relative !important;
  z-index: 100 !important;
}

/* 工具栏弹出面板 */
:deep(.w-e-panel-container) {
  z-index: 99999 !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 颜色选择器 */
:deep(.w-e-color-panel) {
  z-index: 99999 !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 字体选择器 */
:deep(.w-e-select-list) {
  z-index: 99999 !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  max-height: 200px !important;
  overflow-y: auto !important;
}

/* 表格编辑器 */
:deep(.w-e-table-panel) {
  z-index: 99999 !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 链接编辑器 */
:deep(.w-e-link-panel) {
  z-index: 99999 !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 12px !important;
}

/* 所有工具栏弹出元素 */
:deep([data-w-e-type='panel']) {
  z-index: 99999 !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 全屏模式下的z-index调整 */
:deep(.w-e-full-screen-container) {
  z-index: 10000 !important;
}

/* ==================== 编辑器高度调整样式 ==================== */

/* 拖拽手柄样式 */
.resize-handle {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 8px;
  cursor: ns-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.resize-handle-line {
  width: 40px;
  height: 2px;
  background-color: #dcdfe6;
  border-radius: 1px;
  transition: all 0.2s ease;
}

.resize-handle:hover .resize-handle-line {
  background-color: #409eff;
  width: 60px;
  height: 3px;
}

/* 拖拽时的样式 */
.resize-handle:active {
  background-color: rgba(64, 158, 255, 0.2);
}

.resize-handle:active .resize-handle-line {
  background-color: #409eff;
  width: 80px;
  height: 4px;
}

/* 编辑器容器调整 */
.original-editor-container,
.result-editor-container {
  position: relative;
  z-index: 10;
  transition: min-height 0.1s ease;
}

/* 确保编辑器内容不会被拖拽手柄遮挡 */
.original-editor-container .wang-editor,
.result-editor-container .wang-editor {
  margin-bottom: 8px;
}
</style>
