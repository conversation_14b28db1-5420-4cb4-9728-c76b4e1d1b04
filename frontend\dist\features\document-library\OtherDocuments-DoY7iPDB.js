import{d as s,c as a,a as e,Q as t,I as n,ag as o,o as c}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as l}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const r={class:"other-documents"},d={class:"content"},u=l(s({__name:"OtherDocuments",setup:s=>(s,l)=>{const u=o("el-card");return c(),a("div",r,[l[1]||(l[1]=e("div",{class:"page-header"},[e("h1",null,"其他文档"),e("p",{class:"page-description"},"管理其他类型的文档资料")],-1)),e("div",d,[t(u,null,{default:n(()=>l[0]||(l[0]=[e("p",null,"其他文档管理功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-df2a5097"]]);export{u as default};
