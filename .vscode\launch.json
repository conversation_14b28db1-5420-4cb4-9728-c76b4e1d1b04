{"version": "0.2.0", "configurations": [{"name": "🚀 启动开发服务器", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "🌐 Chrome 调试", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/frontend/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "runtimeArgs": ["--disable-web-security", "--disable-features=VizDisplayCompositor"]}, {"name": "🔗 Chrome 附加调试", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}/frontend/src", "sourceMaps": true}, {"name": "🧪 运行当前测试文件", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "program": "${workspaceFolder}/frontend/node_modules/vitest/vitest.mjs", "args": ["run", "${relativeFile}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "🧪 监听模式运行测试", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "program": "${workspaceFolder}/frontend/node_modules/vitest/vitest.mjs", "args": ["--watch"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "📊 运行测试覆盖率", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "program": "${workspaceFolder}/frontend/node_modules/vitest/vitest.mjs", "args": ["run", "--coverage"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "🔍 ESLint 检查", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "program": "${workspaceFolder}/frontend/node_modules/eslint/bin/eslint.js", "args": [".", "--ext", ".vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "--fix"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "💅 Prettier 格式化", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "program": "${workspaceFolder}/frontend/node_modules/prettier/bin-prettier.js", "args": ["--write", "src/"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "🏗️ 构建项目", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "runtimeExecutable": "npm", "runtimeArgs": ["run", "build"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "🔍 类型检查", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "runtimeExecutable": "npm", "runtimeArgs": ["run", "type-check"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}], "compounds": [{"name": "🚀 开发环境（服务器 + Chrome）", "configurations": ["🚀 启动开发服务器", "🌐 Chrome 调试"], "stopAll": true, "presentation": {"hidden": false, "group": "development", "order": 1}}, {"name": "🧪 完整测试套件", "configurations": ["🧪 监听模式运行测试", "📊 运行测试覆盖率"], "stopAll": true, "presentation": {"hidden": false, "group": "testing", "order": 2}}, {"name": "🔧 代码质量检查", "configurations": ["🔍 ESLint 检查", "💅 Prettier 格式化", "🔍 类型检查"], "stopAll": true, "presentation": {"hidden": false, "group": "quality", "order": 3}}]}