{"version": 3, "file": "kk.js", "sources": ["../../../../../packages/locale/lang/kk.ts"], "sourcesContent": ["export default {\n  name: 'kk',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Қабылдау',\n      clear: 'Тазалау',\n    },\n    datepicker: {\n      now: 'Қазір',\n      today: 'Бүгін',\n      cancel: 'Болдырмау',\n      clear: 'Тазалау',\n      confirm: 'Қабылдау',\n      selectDate: 'Күнді таңдаңыз',\n      selectTime: 'Сағатты таңдаңыз',\n      startDate: 'Басталу күні',\n      startTime: 'Басталу сағаты',\n      endDate: 'Аяқталу күні',\n      endTime: 'Аяқталу сағаты',\n      prevYear: 'Алдыңғы жыл',\n      nextYear: 'Келесі жыл',\n      prevMonth: 'Алдыңғы ай',\n      nextMonth: 'Келесі ай',\n      year: 'Жыл',\n      month1: 'Қаңтар',\n      month2: 'Ақпан',\n      month3: 'Наурыз',\n      month4: 'Сәуір',\n      month5: 'Мамыр',\n      month6: 'Маусым',\n      month7: 'Шілде',\n      month8: 'Тамыз',\n      month9: 'Қыркүйек',\n      month10: 'Қазан',\n      month11: 'Қараша',\n      month12: 'Желтоқсан',\n      week: 'Апта',\n      weeks: {\n        sun: 'Жек',\n        mon: 'Дүй',\n        tue: 'Сей',\n        wed: 'Сәр',\n        thu: 'Бей',\n        fri: 'Жұм',\n        sat: 'Сен',\n      },\n      months: {\n        jan: 'Қаң',\n        feb: 'Ақп',\n        mar: 'Нау',\n        apr: 'Сәу',\n        may: 'Мам',\n        jun: 'Мау',\n        jul: 'Шіл',\n        aug: 'Там',\n        sep: 'Қыр',\n        oct: 'Қаз',\n        nov: 'Қар',\n        dec: 'Жел',\n      },\n    },\n    select: {\n      loading: 'Жүктелуде',\n      noMatch: 'Сәйкес деректер жоқ',\n      noData: 'Деректер жоқ',\n      placeholder: 'Таңдаңыз',\n    },\n    mention: {\n      loading: 'Жүктелуде',\n    },\n    cascader: {\n      noMatch: 'Сәйкес деректер жоқ',\n      loading: 'Жүктелуде',\n      placeholder: 'Таңдаңыз',\n      noData: 'Деректер жоқ',\n    },\n    pagination: {\n      goto: 'Бару',\n      pagesize: '/page',\n      total: 'Барлығы {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Хабар',\n      confirm: 'Қабылдау',\n      cancel: 'Болдырмау',\n      error: 'Жарамсыз енгізулер',\n    },\n    upload: {\n      deleteTip: 'Өшіруді басып өшіріңіз',\n      delete: 'Өшіру',\n      preview: 'Алдын ала қарау',\n      continue: 'Жалғастыру',\n    },\n    table: {\n      emptyText: 'Деректер жоқ',\n      confirmFilter: 'Қабылдау',\n      resetFilter: 'Қалпына келтіру',\n      clearFilter: 'Барлығы',\n      sumText: 'Сомасы',\n    },\n    tree: {\n      emptyText: 'Деректер жоқ',\n    },\n    transfer: {\n      noMatch: 'Сәйкес деректер жоқ',\n      noData: 'Деректер жоқ',\n      titles: ['List 1', 'List 2'],\n      filterPlaceholder: 'Кілт сөзді енгізіңіз',\n      noCheckedFormat: '{total} элэмэнт',\n      hasCheckedFormat: '{checked}/{total} құсбелгісі қойылды',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,KAAK,EAAE,4CAA4C;AACzD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,gCAAgC;AAC3C,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,MAAM,EAAE,wDAAwD;AACtE,MAAM,KAAK,EAAE,4CAA4C;AACzD,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,UAAU,EAAE,iFAAiF;AACnG,MAAM,UAAU,EAAE,6FAA6F;AAC/G,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,SAAS,EAAE,iFAAiF;AAClG,MAAM,OAAO,EAAE,qEAAqE;AACpF,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,QAAQ,EAAE,+DAA+D;AAC/E,MAAM,QAAQ,EAAE,yDAAyD;AACzE,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,SAAS,EAAE,mDAAmD;AACpE,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,0GAA0G;AACzH,MAAM,MAAM,EAAE,qEAAqE;AACnF,MAAM,WAAW,EAAE,kDAAkD;AACrE,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,wDAAwD;AACvE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0GAA0G;AACzH,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,WAAW,EAAE,kDAAkD;AACrE,MAAM,MAAM,EAAE,qEAAqE;AACnF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,oDAAoD;AACjE,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,MAAM,EAAE,wDAAwD;AACtE,MAAM,KAAK,EAAE,yGAAyG;AACtH,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,4HAA4H;AAC7I,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,OAAO,EAAE,kFAAkF;AACjG,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,aAAa,EAAE,kDAAkD;AACvE,MAAM,WAAW,EAAE,uFAAuF;AAC1G,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,OAAO,EAAE,sCAAsC;AACrD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,qEAAqE;AACtF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0GAA0G;AACzH,MAAM,MAAM,EAAE,qEAAqE;AACnF,MAAM,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAClC,MAAM,iBAAiB,EAAE,gHAAgH;AACzI,MAAM,eAAe,EAAE,oDAAoD;AAC3E,MAAM,gBAAgB,EAAE,2HAA2H;AACnJ,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}