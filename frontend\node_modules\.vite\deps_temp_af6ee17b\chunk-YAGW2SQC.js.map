{"version": 3, "sources": ["../../vue-demi/lib/index.mjs"], "sourcesContent": ["import * as Vue from 'vue'\r\n\r\nvar isVue2 = false\r\nvar isVue3 = true\r\nvar Vue2 = undefined\r\n\r\nfunction install() {}\r\n\r\nexport function set(target, key, val) {\r\n  if (Array.isArray(target)) {\r\n    target.length = Math.max(target.length, key)\r\n    target.splice(key, 1, val)\r\n    return val\r\n  }\r\n  target[key] = val\r\n  return val\r\n}\r\n\r\nexport function del(target, key) {\r\n  if (Array.isArray(target)) {\r\n    target.splice(key, 1)\r\n    return\r\n  }\r\n  delete target[key]\r\n}\r\n\r\nexport * from 'vue'\r\nexport {\r\n  Vue,\r\n  Vue2,\r\n  isVue2,\r\n  isVue3,\r\n  install,\r\n}\r\n"], "mappings": ";AAEA,IAAI,SAAS;AAMN,SAAS,IAAI,QAAQ,KAAK,KAAK;AACpC,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,WAAO,SAAS,KAAK,IAAI,OAAO,QAAQ,GAAG;AAC3C,WAAO,OAAO,KAAK,GAAG,GAAG;AACzB,WAAO;AAAA,EACT;AACA,SAAO,GAAG,IAAI;AACd,SAAO;AACT;AAEO,SAAS,IAAI,QAAQ,KAAK;AAC/B,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,WAAO,OAAO,KAAK,CAAC;AACpB;AAAA,EACF;AACA,SAAO,OAAO,GAAG;AACnB;", "names": []}