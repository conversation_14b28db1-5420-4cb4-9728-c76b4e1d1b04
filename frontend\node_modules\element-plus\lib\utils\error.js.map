{"version": 3, "file": "error.js", "sources": ["../../../../packages/utils/error.ts"], "sourcesContent": ["import { isString } from './types'\n\nclass ElementPlusError extends Error {\n  constructor(m: string) {\n    super(m)\n    this.name = 'ElementPlusError'\n  }\n}\n\nexport function throwError(scope: string, m: string): never {\n  throw new ElementPlusError(`[${scope}] ${m}`)\n}\n\nexport function debugWarn(err: Error): void\nexport function debugWarn(scope: string, message: string): void\nexport function debugWarn(scope: string | Error, message?: string): void {\n  if (process.env.NODE_ENV !== 'production') {\n    const error: Error = isString(scope)\n      ? new ElementPlusError(`[${scope}] ${message}`)\n      : scope\n    // eslint-disable-next-line no-console\n    console.warn(error)\n  }\n}\n"], "names": ["isString"], "mappings": ";;;;;;AACA,MAAM,gBAAgB,SAAS,KAAK,CAAC;AACrC,EAAE,WAAW,CAAC,CAAC,EAAE;AACjB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACb,IAAI,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC;AACnC,GAAG;AACH,CAAC;AACM,SAAS,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE;AACrC,EAAE,MAAM,IAAI,gBAAgB,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC;AACM,SAAS,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC7C,IAAI,MAAM,KAAK,GAAGA,eAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,gBAAgB,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC1F,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,GAAG;AACH;;;;;"}