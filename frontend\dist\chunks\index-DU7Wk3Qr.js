import{w as e}from"./utils-vendor-DYQz1-BF.js";import{M as t,aa as r}from"./ui-vendor-DZ6owSRu.js";const o=(()=>{const r=e.create({baseURL:"https://api.proofreading.com",timeout:Number("30000")||3e4,headers:{"Content-Type":"application/json"}});return r.interceptors.request.use(async e=>{const t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e.headers["X-Request-ID"]=`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,e},e=>Promise.reject(e)),r.interceptors.response.use(e=>{const{data:r}=e;if(200!==r.code&&!0!==r.success){const e=r.message||"请求失败";return t.error(e),Promise.reject(new Error(e))}return e},e=>{if(e.__isMockResponse)return Promise.resolve(e.response);if(e.response){const{status:r,data:o}=e.response;let s="请求失败";switch(r){case 401:s="未授权，请重新登录",localStorage.removeItem("access_token"),localStorage.removeItem("user_info"),window.location.href="/login";break;case 403:s="权限不足";break;case 404:s="请求的资源不存在";break;case 500:s="服务器内部错误";break;default:s=o?.message||`请求失败 (${r})`}t.error(s)}else e.request?t.error("网络连接失败，请检查网络设置"):t.error("请求配置错误");return Promise.reject(e)}),r})();const s=new class{loadingInstance=null;async get(e,t){return this.request("GET",e,void 0,t)}async post(e,t,r){return this.request("POST",e,t,r)}async put(e,t,r){return this.request("PUT",e,t,r)}async delete(e,t){return this.request("DELETE",e,void 0,t)}async request(e,t,r,s){const{showLoading:a=!1,showError:n=!0,...c}=s||{};try{a&&this.showLoading();return(await o.request({method:e,url:t,data:r,...c})).data.data}catch(i){throw i}finally{a&&this.hideLoading()}}showLoading(){this.loadingInstance=r.service({lock:!0,text:"加载中...",background:"rgba(0, 0, 0, 0.7)"})}hideLoading(){this.loadingInstance&&(this.loadingInstance.close(),this.loadingInstance=null)}async upload(e,t,r){const o=new FormData;return o.append("file",t),this.request("POST",e,o,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:e=>{if(r&&e.total){const t=Math.round(100*e.loaded/e.total);r(t)}}})}async download(e,r){try{const t=await o.get(e,{responseType:"blob"}),s=new Blob([t.data]),a=window.URL.createObjectURL(s),n=document.createElement("a");n.href=a,n.download=r||"download",document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(a)}catch(s){t.error("文件下载失败")}}};export{s as a};
