<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简单Mock API测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .result {
      background: #f5f5f5;
      padding: 10px;
      margin: 10px 0;
      border-radius: 3px;
      white-space: pre-wrap;
    }
    .success { background: #d4edda; color: #155724; }
    .error { background: #f8d7da; color: #721c24; }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover { background: #0056b3; }
  </style>
</head>
<body>
  <h1>简单Mock API测试</h1>
  
  <button onclick="testBasicFetch()">测试基础Fetch</button>
  <button onclick="testMockApi()">测试Mock API</button>
  <button onclick="checkServiceWorker()">检查Service Worker</button>
  <button onclick="clearResults()">清除结果</button>
  
  <div id="results"></div>

  <script>
    function log(message, type = 'info') {
      const results = document.getElementById('results');
      const div = document.createElement('div');
      div.className = `result ${type}`;
      div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      results.appendChild(div);
      console.log(message);
    }

    function clearResults() {
      document.getElementById('results').innerHTML = '';
    }

    async function testBasicFetch() {
      try {
        log('开始测试基础Fetch...');
        
        // 测试一个肯定存在的文件
        const response = await fetch('/favicon.ico');
        log(`Favicon测试: ${response.status} ${response.ok}`, response.ok ? 'success' : 'error');
        
        // 测试一个不存在的文件
        const notFoundResponse = await fetch('/not-found-file.txt');
        log(`不存在文件测试: ${notFoundResponse.status} ${notFoundResponse.ok}`, 'info');
        
      } catch (error) {
        log(`基础Fetch测试失败: ${error.message}`, 'error');
      }
    }

    async function testMockApi() {
      try {
        log('开始测试Mock API...');
        
        // 测试简单的API端点
        const testUrls = [
          '/api/test',
          '/api/pre-review/pending',
          '/api/dashboard/statistics'
        ];

        for (const url of testUrls) {
          try {
            log(`测试 ${url}...`);
            
            const response = await fetch(url, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer dev-mock-token-12345'
              }
            });
            
            log(`${url}: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
            
            if (response.ok) {
              const data = await response.json();
              log(`${url} 数据: ${JSON.stringify(data, null, 2).substring(0, 200)}...`, 'success');
            } else {
              const text = await response.text();
              log(`${url} 错误: ${text}`, 'error');
            }
            
          } catch (error) {
            log(`${url} 异常: ${error.message}`, 'error');
          }
        }
        
      } catch (error) {
        log(`Mock API测试失败: ${error.message}`, 'error');
      }
    }

    async function checkServiceWorker() {
      try {
        log('检查Service Worker状态...');
        
        if (!('serviceWorker' in navigator)) {
          log('浏览器不支持Service Worker', 'error');
          return;
        }
        
        const registrations = await navigator.serviceWorker.getRegistrations();
        log(`Service Worker注册数量: ${registrations.length}`, 'info');
        
        registrations.forEach((reg, index) => {
          log(`注册 ${index + 1}: ${reg.scope}`, 'info');
          log(`状态: ${reg.active?.state || '未激活'}`, 'info');
          log(`脚本URL: ${reg.active?.scriptURL || '无'}`, 'info');
        });
        
        // 检查mockServiceWorker.js文件
        try {
          const swResponse = await fetch('/mockServiceWorker.js');
          log(`mockServiceWorker.js: ${swResponse.status} ${swResponse.ok}`, swResponse.ok ? 'success' : 'error');
        } catch (error) {
          log(`mockServiceWorker.js检查失败: ${error.message}`, 'error');
        }
        
      } catch (error) {
        log(`Service Worker检查失败: ${error.message}`, 'error');
      }
    }

    // 页面加载时自动运行基础测试
    window.addEventListener('load', () => {
      log('页面已加载，开始自动测试...');
      setTimeout(() => {
        testBasicFetch();
        setTimeout(() => {
          checkServiceWorker();
          setTimeout(() => {
            testMockApi();
          }, 1000);
        }, 1000);
      }, 1000);
    });
  </script>
</body>
</html>
