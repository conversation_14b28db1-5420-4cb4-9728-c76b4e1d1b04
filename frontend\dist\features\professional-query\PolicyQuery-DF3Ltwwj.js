import{d as a,c as s,a as e,Q as l,I as c,ag as o,o as r}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as n}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const p={class:"policy-query"},t={class:"content"},u=n(a({__name:"PolicyQuery",setup:a=>(a,n)=>{const u=o("el-card");return r(),s("div",p,[n[1]||(n[1]=e("div",{class:"page-header"},[e("h1",null,"政策查询"),e("p",{class:"page-description"},"查询政策文件和解读分析")],-1)),e("div",t,[l(u,null,{default:c(()=>n[0]||(n[0]=[e("p",null,"政策查询功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-da26a483"]]);export{u as default};
