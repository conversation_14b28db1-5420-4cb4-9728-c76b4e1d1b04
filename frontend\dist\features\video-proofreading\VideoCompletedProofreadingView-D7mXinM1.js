import{M as e,x as l,a0 as a,R as t}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as u,r,b as o,m as i,c as n,a as s,Q as d,I as c,ag as v,o as p,u as f,M as m,J as _,aq as g,H as h,O as b,K as w}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as y}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const x={class:"video-completed-proofreading"},C={class:"content"},T={class:"card-header"},V={class:"header-actions"},k={class:"file-name"},U={class:"pagination-container"},z={key:0,class:"details-content"},S={class:"video-preview"},q=["src"],Y={class:"video-info"},j={class:"proofreading-result"},B={class:"result-actions",style:{"margin-top":"15px"}},M={class:"dialog-footer"},R=y(u({__name:"VideoCompletedProofreadingView",setup(u){const y=r(!1),R=r([]),I=r([]),L=r(""),D=r(""),$=r(""),O=r([]),F=r(1),P=r(20),K=r(0),E=r(0),G=r(0),H=r(0),J=r(!1),Q=r(null),A=r(!1),N=r(""),W=o(()=>{let e=R.value;return L.value&&(e=e.filter(e=>e.name.toLowerCase().includes(L.value.toLowerCase()))),D.value&&(e=e.filter(e=>e.quality===D.value)),$.value&&(e=e.filter(e=>{const l=ee(e.duration);switch($.value){case"short":return l<=300;case"medium":return l>300&&l<=1800;case"long":return l>1800;default:return!0}})),O.value&&2===O.value.length&&(e=e.filter(e=>{const l=new Date(e.completedTime).toISOString().split("T")[0];return l>=O.value[0]&&l<=O.value[1]})),e});i(()=>{X(),Z()});const X=async()=>{y.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),R.value=[{id:1,name:"training_video_001.mp4",duration:"25:30",originalSize:256901120,completedTime:"2024-01-15 14:30:00",proofreader:"张三",quality:"excellent",subtitleCount:45,accuracy:98.5,processingTime:"8分30秒",videoUrl:"/api/files/preview/video/1",subtitleText:"这是培训视频的字幕内容...\n\n[00:00:10] 欢迎参加本次培训\n[00:00:15] 今天我们学习系统操作..."},{id:2,name:"meeting_record_20240115.avi",duration:"45:18",originalSize:387420160,completedTime:"2024-01-15 11:20:00",proofreader:"李四",quality:"good",subtitleCount:78,accuracy:95.2,processingTime:"12分45秒",videoUrl:"/api/files/preview/video/2",subtitleText:"这是会议录音的字幕内容..."},{id:3,name:"product_demo_final.mov",duration:"18:45",originalSize:198765432,completedTime:"2024-01-14 16:45:00",proofreader:"王五",quality:"excellent",subtitleCount:32,accuracy:99.1,processingTime:"6分15秒",videoUrl:"/api/files/preview/video/3",subtitleText:"这是产品演示的字幕内容..."}],K.value=R.value.length}catch{e.error("加载文件列表失败")}finally{y.value=!1}},Z=async()=>{try{K.value=89,E.value=23,G.value=28.5,H.value=72.1}catch{e.error("加载统计数据失败")}},ee=e=>{const l=e.split(":");return 60*parseInt(l[0])+parseInt(l[1])},le=()=>{F.value=1},ae=()=>{F.value=1},te=()=>{X(),Z()},ue=()=>{e.success("正在生成审校报告...")},re=e=>{I.value=e},oe=()=>{0!==I.value.length?e.success(`开始下载 ${I.value.length} 个文件`):e.warning("请选择要下载的文件")},ie=async()=>{if(0!==I.value.length)try{await t.confirm(`确定要归档选中的 ${I.value.length} 个文件吗？`,"批量归档确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.success("归档成功"),I.value=[]}catch{}else e.warning("请选择要归档的文件")},ne=async()=>{if(Q.value?.subtitleText)try{await navigator.clipboard.writeText(Q.value.subtitleText),e.success("字幕已复制到剪贴板")}catch{e.error("复制失败")}else e.warning("没有可复制的字幕")},se=()=>{if(!Q.value?.subtitleText)return void e.warning("没有可下载的字幕");const l=new Blob([Q.value.subtitleText],{type:"text/plain"}),a=URL.createObjectURL(l),t=document.createElement("a");t.href=a,t.download=`${Q.value.name}_subtitle.txt`,t.click(),URL.revokeObjectURL(a),e.success("下载成功")},de=()=>{e.success("SRT文件下载功能开发中...")},ce=()=>{N.value=Q.value?.subtitleText||"",A.value=!0},ve=()=>{Q.value&&(Q.value.subtitleText=N.value,e.success("保存成功")),A.value=!1},pe=e=>{P.value=e,F.value=1},fe=e=>{F.value=e},me=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":e<1073741824?(e/1048576).toFixed(1)+" MB":(e/1073741824).toFixed(1)+" GB",_e=e=>{switch(e){case"excellent":return"success";case"good":return"primary";case"average":return"warning";case"poor":return"danger";default:return"info"}},ge=e=>{switch(e){case"excellent":return"优秀";case"good":return"良好";case"average":return"一般";case"poor":return"需改进";default:return"未知"}};return(t,u)=>{const r=v("el-icon"),o=v("el-input"),i=v("el-col"),R=v("el-option"),X=v("el-select"),Z=v("el-date-picker"),ee=v("el-button"),he=v("el-row"),be=v("el-card"),we=v("el-statistic"),ye=v("el-table-column"),xe=v("el-tag"),Ce=v("el-progress"),Te=v("el-table"),Ve=v("el-pagination"),ke=v("el-descriptions-item"),Ue=v("el-descriptions"),ze=v("el-dialog"),Se=g("loading");return p(),n("div",x,[u[30]||(u[30]=s("div",{class:"page-header"},[s("h1",null,"已审校视频"),s("p",{class:"page-description"},"显示已完成审校的视频文件列表和审校结果")],-1)),s("div",C,[d(be,{class:"filter-card"},{default:c(()=>[d(he,{gutter:20},{default:c(()=>[d(i,{span:5},{default:c(()=>[d(o,{modelValue:L.value,"onUpdate:modelValue":u[0]||(u[0]=e=>L.value=e),placeholder:"搜索文件名...",clearable:"",onInput:le},{prefix:c(()=>[d(r,null,{default:c(()=>[d(f(l))]),_:1})]),_:1},8,["modelValue"])]),_:1}),d(i,{span:4},{default:c(()=>[d(X,{modelValue:D.value,"onUpdate:modelValue":u[1]||(u[1]=e=>D.value=e),placeholder:"审校质量",clearable:"",onChange:ae},{default:c(()=>[d(R,{label:"全部",value:""}),d(R,{label:"优秀",value:"excellent"}),d(R,{label:"良好",value:"good"}),d(R,{label:"一般",value:"average"}),d(R,{label:"需改进",value:"poor"})]),_:1},8,["modelValue"])]),_:1}),d(i,{span:4},{default:c(()=>[d(X,{modelValue:$.value,"onUpdate:modelValue":u[2]||(u[2]=e=>$.value=e),placeholder:"时长筛选",clearable:"",onChange:ae},{default:c(()=>[d(R,{label:"全部",value:""}),d(R,{label:"5分钟以内",value:"short"}),d(R,{label:"5-30分钟",value:"medium"}),d(R,{label:"30分钟以上",value:"long"})]),_:1},8,["modelValue"])]),_:1}),d(i,{span:5},{default:c(()=>[d(Z,{modelValue:O.value,"onUpdate:modelValue":u[3]||(u[3]=e=>O.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:ae},null,8,["modelValue"])]),_:1}),d(i,{span:3},{default:c(()=>[d(ee,{type:"primary",onClick:te},{default:c(()=>u[11]||(u[11]=[m("刷新列表")])),_:1,__:[11]})]),_:1}),d(i,{span:3},{default:c(()=>[d(ee,{onClick:ue},{default:c(()=>u[12]||(u[12]=[m("导出报告")])),_:1,__:[12]})]),_:1})]),_:1})]),_:1}),d(be,{class:"stats-card"},{default:c(()=>[d(he,{gutter:20},{default:c(()=>[d(i,{span:6},{default:c(()=>[d(we,{title:"总审校数量",value:K.value},null,8,["value"])]),_:1}),d(i,{span:6},{default:c(()=>[d(we,{title:"本月审校",value:E.value},null,8,["value"])]),_:1}),d(i,{span:6},{default:c(()=>[d(we,{title:"平均时长",value:G.value,suffix:"分钟",precision:1},null,8,["value"])]),_:1}),d(i,{span:6},{default:c(()=>[d(we,{title:"优秀率",value:H.value,suffix:"%",precision:1},null,8,["value"])]),_:1})]),_:1})]),_:1}),d(be,{title:"已审校视频列表",class:"list-card"},{header:c(()=>[s("div",T,[u[14]||(u[14]=s("span",null,"已审校视频列表",-1)),s("div",V,[d(ee,{size:"small",onClick:oe,disabled:0===I.value.length},{default:c(()=>[m(" 批量下载 ("+b(I.value.length)+") ",1)]),_:1},8,["disabled"]),d(ee,{size:"small",onClick:ie,disabled:0===I.value.length},{default:c(()=>u[13]||(u[13]=[m(" 批量归档 ")])),_:1,__:[13]},8,["disabled"])])])]),default:c(()=>[_((p(),h(Te,{data:W.value,style:{width:"100%"},onSelectionChange:re},{default:c(()=>[d(ye,{type:"selection",width:"55"}),d(ye,{prop:"name",label:"文件名",width:"180"},{default:c(e=>[s("div",k,[d(r,null,{default:c(()=>[d(f(a))]),_:1}),s("span",null,b(e.row.name),1)])]),_:1}),d(ye,{prop:"duration",label:"时长",width:"100"}),d(ye,{prop:"originalSize",label:"原始大小",width:"100"},{default:c(e=>[m(b(me(e.row.originalSize)),1)]),_:1}),d(ye,{prop:"completedTime",label:"完成时间",width:"140"}),d(ye,{prop:"proofreader",label:"审校人员",width:"100"}),d(ye,{prop:"quality",label:"审校质量",width:"100"},{default:c(e=>[d(xe,{type:_e(e.row.quality)},{default:c(()=>[m(b(ge(e.row.quality)),1)]),_:2},1032,["type"])]),_:1}),d(ye,{prop:"subtitleCount",label:"字幕段数",width:"100"}),d(ye,{prop:"accuracy",label:"识别准确率",width:"120"},{default:c(e=>[d(Ce,{percentage:e.row.accuracy,"stroke-width":8,"show-text":!0,format:e=>e+"%"},null,8,["percentage","format"])]),_:1}),d(ye,{label:"操作",width:"220"},{default:c(l=>[d(ee,{size:"small",onClick:e=>{return a=l.row,Q.value=a,void(J.value=!0);var a}},{default:c(()=>u[15]||(u[15]=[m("查看详情")])),_:2,__:[15]},1032,["onClick"]),d(ee,{size:"small",onClick:a=>{return t=l.row,void e.success(`开始下载文件：${t.name}`);var t}},{default:c(()=>u[16]||(u[16]=[m("下载结果")])),_:2,__:[16]},1032,["onClick"]),d(ee,{size:"small",onClick:a=>{return t=l.row,void e.success(`重新审校文件：${t.name}`);var t}},{default:c(()=>u[17]||(u[17]=[m("重新审校")])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Se,y.value]]),s("div",U,[d(Ve,{"current-page":F.value,"onUpdate:currentPage":u[4]||(u[4]=e=>F.value=e),"page-size":P.value,"onUpdate:pageSize":u[5]||(u[5]=e=>P.value=e),"page-sizes":[10,20,50,100],total:K.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:pe,onCurrentChange:fe},null,8,["current-page","page-size","total"])])]),_:1})]),d(ze,{modelValue:J.value,"onUpdate:modelValue":u[7]||(u[7]=e=>J.value=e),title:"审校详情",width:"80%"},{default:c(()=>[Q.value?(p(),n("div",z,[d(he,{gutter:20},{default:c(()=>[d(i,{span:12},{default:c(()=>[s("div",S,[u[21]||(u[21]=s("h4",null,"原始视频",-1)),s("video",{src:Q.value.videoUrl,controls:"",class:"preview-video"},null,8,q),s("div",Y,[s("p",null,[u[18]||(u[18]=s("strong",null,"文件名：",-1)),m(b(Q.value.name),1)]),s("p",null,[u[19]||(u[19]=s("strong",null,"时长：",-1)),m(b(Q.value.duration),1)]),s("p",null,[u[20]||(u[20]=s("strong",null,"文件大小：",-1)),m(b(me(Q.value.originalSize)),1)])])])]),_:1}),d(i,{span:12},{default:c(()=>[s("div",j,[u[26]||(u[26]=s("h4",null,"审校结果",-1)),d(Ue,{column:2,border:""},{default:c(()=>[d(ke,{label:"审校人员"},{default:c(()=>[m(b(Q.value.proofreader),1)]),_:1}),d(ke,{label:"完成时间"},{default:c(()=>[m(b(Q.value.completedTime),1)]),_:1}),d(ke,{label:"审校质量"},{default:c(()=>[d(xe,{type:_e(Q.value.quality)},{default:c(()=>[m(b(ge(Q.value.quality)),1)]),_:1},8,["type"])]),_:1}),d(ke,{label:"识别准确率"},{default:c(()=>[m(b(Q.value.accuracy)+"%",1)]),_:1}),d(ke,{label:"字幕段数"},{default:c(()=>[m(b(Q.value.subtitleCount),1)]),_:1}),d(ke,{label:"处理时长"},{default:c(()=>[m(b(Q.value.processingTime),1)]),_:1})]),_:1}),u[27]||(u[27]=s("h4",{style:{"margin-top":"20px"}},"字幕内容",-1)),d(o,{modelValue:Q.value.subtitleText,"onUpdate:modelValue":u[6]||(u[6]=e=>Q.value.subtitleText=e),type:"textarea",rows:12,readonly:"",class:"subtitle-text"},null,8,["modelValue"]),s("div",B,[d(ee,{onClick:ne},{default:c(()=>u[22]||(u[22]=[m("复制字幕")])),_:1,__:[22]}),d(ee,{onClick:se},{default:c(()=>u[23]||(u[23]=[m("下载字幕")])),_:1,__:[23]}),d(ee,{onClick:de},{default:c(()=>u[24]||(u[24]=[m("下载SRT")])),_:1,__:[24]}),d(ee,{type:"primary",onClick:ce},{default:c(()=>u[25]||(u[25]=[m("编辑字幕")])),_:1,__:[25]})])])]),_:1})]),_:1})])):w("",!0)]),_:1},8,["modelValue"]),d(ze,{modelValue:A.value,"onUpdate:modelValue":u[10]||(u[10]=e=>A.value=e),title:"编辑字幕内容",width:"60%"},{footer:c(()=>[s("span",M,[d(ee,{onClick:u[9]||(u[9]=e=>A.value=!1)},{default:c(()=>u[28]||(u[28]=[m("取消")])),_:1,__:[28]}),d(ee,{type:"primary",onClick:ve},{default:c(()=>u[29]||(u[29]=[m("保存修改")])),_:1,__:[29]})])]),default:c(()=>[d(o,{modelValue:N.value,"onUpdate:modelValue":u[8]||(u[8]=e=>N.value=e),type:"textarea",rows:15,placeholder:"请编辑字幕内容..."},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-3a3488f5"]]);export{R as default};
