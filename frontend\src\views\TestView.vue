<template>
  <div class="test-view">
    <el-container>
      <el-header>
        <h1>AI智能审校系统</h1>
        <p>系统启动成功！</p>
      </el-header>
      <el-main>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>系统状态</span>
                </div>
              </template>
              <div class="status-item">
                <el-icon><CircleCheck /></el-icon>
                <span>前端服务：运行中</span>
              </div>
              <div class="status-item">
                <el-icon><Warning /></el-icon>
                <span>后端服务：未启动</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>快速导航</span>
                </div>
              </template>
              <el-button-group>
                <el-button type="primary" @click="goToTest('/test-proofread-editor')">
                  审校编辑器测试
                </el-button>
                <el-button type="success" @click="goToTest('/test-original-editor')">
                  原文编辑器测试
                </el-button>
                <el-button type="info" @click="goToTest('/test-pdf-upload')">
                  PDF上传测试
                </el-button>
              </el-button-group>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>技术栈</span>
                </div>
              </template>
              <ul class="tech-list">
                <li>Vue 3.5.17</li>
                <li>Element Plus 2.10.2</li>
                <li>TypeScript</li>
                <li>Vite 5.4.0</li>
                <li>Pinia</li>
                <li>Vue Router</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { CircleCheck, Warning } from '@element-plus/icons-vue'

const router = useRouter()

const goToTest = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.test-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.el-header {
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  text-align: center;
  line-height: 60px;
  backdrop-filter: blur(10px);
}

.el-header h1 {
  margin: 0;
  font-size: 2.5em;
  font-weight: bold;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.el-header p {
  margin: 5px 0 0 0;
  font-size: 1.2em;
  color: #666;
}

.el-main {
  padding: 40px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #333;
}

.status-item {
  display: flex;
  align-items: center;
  margin: 10px 0;
  font-size: 14px;
}

.status-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.tech-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tech-list li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  color: #666;
}

.tech-list li:last-child {
  border-bottom: none;
}

.el-button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.el-button-group .el-button {
  width: 100%;
}
</style>
