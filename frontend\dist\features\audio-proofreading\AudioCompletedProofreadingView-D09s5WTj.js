import{M as e,x as a,d as l,R as t}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as u,r,b as n,m as o,c as i,a as s,Q as d,I as c,ag as p,o as v,u as f,M as m,J as _,aq as g,H as h,O as w,K as y}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as b}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const x={class:"audio-completed-proofreading"},C={class:"content"},V={class:"card-header"},k={class:"header-actions"},T={class:"file-name"},U={class:"pagination-container"},z={key:0,class:"details-content"},S={class:"audio-preview"},q=["src"],Y={class:"audio-info"},j={class:"proofreading-result"},B={class:"result-actions",style:{"margin-top":"15px"}},M={class:"dialog-footer"},I=b(u({__name:"AudioCompletedProofreadingView",setup(u){const b=r(!1),I=r([]),L=r([]),D=r(""),R=r(""),$=r(""),O=r([]),F=r(1),P=r(20),K=r(0),A=r(0),E=r(0),G=r(0),H=r(!1),J=r(null),Q=r(!1),N=r(""),W=n(()=>{let e=I.value;return D.value&&(e=e.filter(e=>e.name.toLowerCase().includes(D.value.toLowerCase()))),R.value&&(e=e.filter(e=>e.quality===R.value)),$.value&&(e=e.filter(e=>{const a=ee(e.duration);switch($.value){case"short":return a<=300;case"medium":return a>300&&a<=1800;case"long":return a>1800;default:return!0}})),O.value&&2===O.value.length&&(e=e.filter(e=>{const a=new Date(e.completedTime).toISOString().split("T")[0];return a>=O.value[0]&&a<=O.value[1]})),e});o(()=>{X(),Z()});const X=async()=>{b.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),I.value=[{id:1,name:"meeting_audio_001.mp3",duration:"25:30",originalSize:15728640,completedTime:"2024-01-15 14:30:00",proofreader:"张三",quality:"excellent",wordCount:3250,accuracy:97.8,processingTime:"8分30秒",audioUrl:"/api/files/preview/audio/1",transcriptText:"这是会议音频的转录内容...\n\n[00:00:10] 主持人：各位同事大家好\n[00:00:15] 张经理：今天我们讨论项目进展..."},{id:2,name:"interview_session.wav",duration:"18:45",originalSize:45678912,completedTime:"2024-01-15 11:20:00",proofreader:"李四",quality:"good",wordCount:2890,accuracy:94.5,processingTime:"6分15秒",audioUrl:"/api/files/preview/audio/2",transcriptText:"这是面试音频的转录内容..."}],K.value=I.value.length}catch(a){e.error("加载文件列表失败")}finally{b.value=!1}},Z=async()=>{try{K.value=67,A.value=18,E.value=22.3,G.value=75.8}catch(a){e.error("加载统计数据失败")}},ee=e=>{const a=e.split(":");return 60*parseInt(a[0])+parseInt(a[1])},ae=()=>{F.value=1},le=()=>{F.value=1},te=()=>{X(),Z()},ue=()=>{e.success("正在生成审校报告...")},re=e=>{L.value=e},ne=()=>{0!==L.value.length?e.success(`开始下载 ${L.value.length} 个文件`):e.warning("请选择要下载的文件")},oe=async()=>{if(0!==L.value.length)try{await t.confirm(`确定要归档选中的 ${L.value.length} 个文件吗？`,"批量归档确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.success("归档成功"),L.value=[]}catch{}else e.warning("请选择要归档的文件")},ie=async()=>{if(J.value?.transcriptText)try{await navigator.clipboard.writeText(J.value.transcriptText),e.success("转录已复制到剪贴板")}catch(a){e.error("复制失败")}else e.warning("没有可复制的转录")},se=()=>{if(!J.value?.transcriptText)return void e.warning("没有可下载的转录");const a=new Blob([J.value.transcriptText],{type:"text/plain"}),l=URL.createObjectURL(a),t=document.createElement("a");t.href=l,t.download=`${J.value.name}_transcript.txt`,t.click(),URL.revokeObjectURL(l),e.success("下载成功")},de=()=>{N.value=J.value?.transcriptText||"",Q.value=!0},ce=()=>{J.value&&(J.value.transcriptText=N.value,e.success("保存成功")),Q.value=!1},pe=e=>{P.value=e,F.value=1},ve=e=>{F.value=e},fe=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":e<1073741824?(e/1048576).toFixed(1)+" MB":(e/1073741824).toFixed(1)+" GB",me=e=>{switch(e){case"excellent":return"success";case"good":return"primary";case"average":return"warning";case"poor":return"danger";default:return"info"}},_e=e=>{switch(e){case"excellent":return"优秀";case"good":return"良好";case"average":return"一般";case"poor":return"需改进";default:return"未知"}};return(t,u)=>{const r=p("el-icon"),n=p("el-input"),o=p("el-col"),I=p("el-option"),X=p("el-select"),Z=p("el-date-picker"),ee=p("el-button"),ge=p("el-row"),he=p("el-card"),we=p("el-statistic"),ye=p("el-table-column"),be=p("el-tag"),xe=p("el-progress"),Ce=p("el-table"),Ve=p("el-pagination"),ke=p("el-descriptions-item"),Te=p("el-descriptions"),Ue=p("el-dialog"),ze=g("loading");return v(),i("div",x,[u[29]||(u[29]=s("div",{class:"page-header"},[s("h1",null,"已审校音频"),s("p",{class:"page-description"},"显示已完成审校的音频文件列表和审校结果")],-1)),s("div",C,[d(he,{class:"filter-card"},{default:c(()=>[d(ge,{gutter:20},{default:c(()=>[d(o,{span:5},{default:c(()=>[d(n,{modelValue:D.value,"onUpdate:modelValue":u[0]||(u[0]=e=>D.value=e),placeholder:"搜索文件名...",clearable:"",onInput:ae},{prefix:c(()=>[d(r,null,{default:c(()=>[d(f(a))]),_:1})]),_:1},8,["modelValue"])]),_:1}),d(o,{span:4},{default:c(()=>[d(X,{modelValue:R.value,"onUpdate:modelValue":u[1]||(u[1]=e=>R.value=e),placeholder:"审校质量",clearable:"",onChange:le},{default:c(()=>[d(I,{label:"全部",value:""}),d(I,{label:"优秀",value:"excellent"}),d(I,{label:"良好",value:"good"}),d(I,{label:"一般",value:"average"}),d(I,{label:"需改进",value:"poor"})]),_:1},8,["modelValue"])]),_:1}),d(o,{span:4},{default:c(()=>[d(X,{modelValue:$.value,"onUpdate:modelValue":u[2]||(u[2]=e=>$.value=e),placeholder:"时长筛选",clearable:"",onChange:le},{default:c(()=>[d(I,{label:"全部",value:""}),d(I,{label:"5分钟以内",value:"short"}),d(I,{label:"5-30分钟",value:"medium"}),d(I,{label:"30分钟以上",value:"long"})]),_:1},8,["modelValue"])]),_:1}),d(o,{span:5},{default:c(()=>[d(Z,{modelValue:O.value,"onUpdate:modelValue":u[3]||(u[3]=e=>O.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:le},null,8,["modelValue"])]),_:1}),d(o,{span:3},{default:c(()=>[d(ee,{type:"primary",onClick:te},{default:c(()=>u[11]||(u[11]=[m("刷新列表")])),_:1,__:[11]})]),_:1}),d(o,{span:3},{default:c(()=>[d(ee,{onClick:ue},{default:c(()=>u[12]||(u[12]=[m("导出报告")])),_:1,__:[12]})]),_:1})]),_:1})]),_:1}),d(he,{class:"stats-card"},{default:c(()=>[d(ge,{gutter:20},{default:c(()=>[d(o,{span:6},{default:c(()=>[d(we,{title:"总审校数量",value:K.value},null,8,["value"])]),_:1}),d(o,{span:6},{default:c(()=>[d(we,{title:"本月审校",value:A.value},null,8,["value"])]),_:1}),d(o,{span:6},{default:c(()=>[d(we,{title:"平均时长",value:E.value,suffix:"分钟",precision:1},null,8,["value"])]),_:1}),d(o,{span:6},{default:c(()=>[d(we,{title:"优秀率",value:G.value,suffix:"%",precision:1},null,8,["value"])]),_:1})]),_:1})]),_:1}),d(he,{title:"已审校音频列表",class:"list-card"},{header:c(()=>[s("div",V,[u[14]||(u[14]=s("span",null,"已审校音频列表",-1)),s("div",k,[d(ee,{size:"small",onClick:ne,disabled:0===L.value.length},{default:c(()=>[m(" 批量下载 ("+w(L.value.length)+") ",1)]),_:1},8,["disabled"]),d(ee,{size:"small",onClick:oe,disabled:0===L.value.length},{default:c(()=>u[13]||(u[13]=[m(" 批量归档 ")])),_:1,__:[13]},8,["disabled"])])])]),default:c(()=>[_((v(),h(Ce,{data:W.value,style:{width:"100%"},onSelectionChange:re},{default:c(()=>[d(ye,{type:"selection",width:"55"}),d(ye,{prop:"name",label:"文件名",width:"180"},{default:c(e=>[s("div",T,[d(r,null,{default:c(()=>[d(f(l))]),_:1}),s("span",null,w(e.row.name),1)])]),_:1}),d(ye,{prop:"duration",label:"时长",width:"100"}),d(ye,{prop:"originalSize",label:"原始大小",width:"100"},{default:c(e=>[m(w(fe(e.row.originalSize)),1)]),_:1}),d(ye,{prop:"completedTime",label:"完成时间",width:"140"}),d(ye,{prop:"proofreader",label:"审校人员",width:"100"}),d(ye,{prop:"quality",label:"审校质量",width:"100"},{default:c(e=>[d(be,{type:me(e.row.quality)},{default:c(()=>[m(w(_e(e.row.quality)),1)]),_:2},1032,["type"])]),_:1}),d(ye,{prop:"wordCount",label:"转录字数",width:"100"}),d(ye,{prop:"accuracy",label:"识别准确率",width:"120"},{default:c(e=>[d(xe,{percentage:e.row.accuracy,"stroke-width":8,"show-text":!0,format:e=>e+"%"},null,8,["percentage","format"])]),_:1}),d(ye,{label:"操作",width:"220"},{default:c(a=>[d(ee,{size:"small",onClick:e=>{return l=a.row,J.value=l,void(H.value=!0);var l}},{default:c(()=>u[15]||(u[15]=[m("查看详情")])),_:2,__:[15]},1032,["onClick"]),d(ee,{size:"small",onClick:l=>{return t=a.row,void e.success(`开始下载文件：${t.name}`);var t}},{default:c(()=>u[16]||(u[16]=[m("下载结果")])),_:2,__:[16]},1032,["onClick"]),d(ee,{size:"small",onClick:l=>{return t=a.row,void e.success(`重新审校文件：${t.name}`);var t}},{default:c(()=>u[17]||(u[17]=[m("重新审校")])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ze,b.value]]),s("div",U,[d(Ve,{"current-page":F.value,"onUpdate:currentPage":u[4]||(u[4]=e=>F.value=e),"page-size":P.value,"onUpdate:pageSize":u[5]||(u[5]=e=>P.value=e),"page-sizes":[10,20,50,100],total:K.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:pe,onCurrentChange:ve},null,8,["current-page","page-size","total"])])]),_:1})]),d(Ue,{modelValue:H.value,"onUpdate:modelValue":u[7]||(u[7]=e=>H.value=e),title:"审校详情",width:"70%"},{default:c(()=>[J.value?(v(),i("div",z,[d(ge,{gutter:20},{default:c(()=>[d(o,{span:12},{default:c(()=>[s("div",S,[u[21]||(u[21]=s("h4",null,"原始音频",-1)),s("audio",{src:J.value.audioUrl,controls:"",class:"preview-audio"},null,8,q),s("div",Y,[s("p",null,[u[18]||(u[18]=s("strong",null,"文件名：",-1)),m(w(J.value.name),1)]),s("p",null,[u[19]||(u[19]=s("strong",null,"时长：",-1)),m(w(J.value.duration),1)]),s("p",null,[u[20]||(u[20]=s("strong",null,"文件大小：",-1)),m(w(fe(J.value.originalSize)),1)])])])]),_:1}),d(o,{span:12},{default:c(()=>[s("div",j,[u[25]||(u[25]=s("h4",null,"审校结果",-1)),d(Te,{column:2,border:""},{default:c(()=>[d(ke,{label:"审校人员"},{default:c(()=>[m(w(J.value.proofreader),1)]),_:1}),d(ke,{label:"完成时间"},{default:c(()=>[m(w(J.value.completedTime),1)]),_:1}),d(ke,{label:"审校质量"},{default:c(()=>[d(be,{type:me(J.value.quality)},{default:c(()=>[m(w(_e(J.value.quality)),1)]),_:1},8,["type"])]),_:1}),d(ke,{label:"识别准确率"},{default:c(()=>[m(w(J.value.accuracy)+"%",1)]),_:1}),d(ke,{label:"转录字数"},{default:c(()=>[m(w(J.value.wordCount),1)]),_:1}),d(ke,{label:"处理时长"},{default:c(()=>[m(w(J.value.processingTime),1)]),_:1})]),_:1}),u[26]||(u[26]=s("h4",{style:{"margin-top":"20px"}},"转录内容",-1)),d(n,{modelValue:J.value.transcriptText,"onUpdate:modelValue":u[6]||(u[6]=e=>J.value.transcriptText=e),type:"textarea",rows:12,readonly:"",class:"transcript-text"},null,8,["modelValue"]),s("div",B,[d(ee,{onClick:ie},{default:c(()=>u[22]||(u[22]=[m("复制转录")])),_:1,__:[22]}),d(ee,{onClick:se},{default:c(()=>u[23]||(u[23]=[m("下载转录")])),_:1,__:[23]}),d(ee,{type:"primary",onClick:de},{default:c(()=>u[24]||(u[24]=[m("编辑转录")])),_:1,__:[24]})])])]),_:1})]),_:1})])):y("",!0)]),_:1},8,["modelValue"]),d(Ue,{modelValue:Q.value,"onUpdate:modelValue":u[10]||(u[10]=e=>Q.value=e),title:"编辑转录内容",width:"60%"},{footer:c(()=>[s("span",M,[d(ee,{onClick:u[9]||(u[9]=e=>Q.value=!1)},{default:c(()=>u[27]||(u[27]=[m("取消")])),_:1,__:[27]}),d(ee,{type:"primary",onClick:ce},{default:c(()=>u[28]||(u[28]=[m("保存修改")])),_:1,__:[28]})])]),default:c(()=>[d(n,{modelValue:N.value,"onUpdate:modelValue":u[8]||(u[8]=e=>N.value=e),type:"textarea",rows:15,placeholder:"请编辑转录内容..."},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-ddf8688d"]]);export{I as default};
