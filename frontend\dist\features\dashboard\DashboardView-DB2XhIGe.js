import{d as s,N as a,b as t,I as e,O as l,P as c}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d,r as i,m as n,c as o,a as r,Q as u,I as v,ag as p,o as m,u as f,O as _,P as h,a6 as y,H as b,M as g}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as D}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const z={class:"dashboard-container"},k={class:"stats-grid"},j={class:"stats-content"},I={class:"stats-icon"},M={class:"stats-info"},O={class:"stats-value"},U={class:"stats-content"},w={class:"stats-icon"},x={class:"stats-info"},P={class:"stats-value"},C={class:"stats-content"},H={class:"stats-icon"},N={class:"stats-info"},Q={class:"stats-value"},R={class:"stats-content"},V={class:"stats-icon"},q={class:"stats-info"},A={class:"stats-value"},B={class:"charts-grid"},E={class:"chart-placeholder"},F={class:"chart-placeholder"},G=D(d({__name:"DashboardView",setup(d){const D=i({totalDocuments:1234,completedDocuments:856,pendingDocuments:378,onlineUsers:23}),G=i([{id:1,description:"用户张三完成了文档《年度报告》的审校",timestamp:"2025-06-26 14:30",type:"success"},{id:2,description:"系统自动处理了50个文档的批量审校",timestamp:"2025-06-26 14:15",type:"primary"},{id:3,description:"用户李四上传了新文档《项目计划书》",timestamp:"2025-06-26 14:00",type:"info"},{id:4,description:"检测到异常：OCR识别服务响应缓慢",timestamp:"2025-06-26 13:45",type:"warning"}]);return n(()=>{setInterval(()=>{D.value.onlineUsers=Math.floor(50*Math.random())+10},5e3)}),(d,i)=>{const n=p("el-icon"),J=p("el-card"),K=p("el-timeline-item"),L=p("el-timeline");return m(),o("div",z,[i[9]||(i[9]=r("div",{class:"dashboard-header"},[r("h1",null,"校对大屏"),r("p",null,"系统运行状态总览")],-1)),r("div",k,[u(J,{class:"stats-card"},{default:v(()=>[r("div",j,[r("div",I,[u(n,{size:"32",color:"#409eff"},{default:v(()=>[u(f(s))]),_:1})]),r("div",M,[r("div",O,_(D.value.totalDocuments),1),i[0]||(i[0]=r("div",{class:"stats-label"},"总文档数",-1))])])]),_:1}),u(J,{class:"stats-card"},{default:v(()=>[r("div",U,[r("div",w,[u(n,{size:"32",color:"#67c23a"},{default:v(()=>[u(f(a))]),_:1})]),r("div",x,[r("div",P,_(D.value.completedDocuments),1),i[1]||(i[1]=r("div",{class:"stats-label"},"已完成审校",-1))])])]),_:1}),u(J,{class:"stats-card"},{default:v(()=>[r("div",C,[r("div",H,[u(n,{size:"32",color:"#e6a23c"},{default:v(()=>[u(f(t))]),_:1})]),r("div",N,[r("div",Q,_(D.value.pendingDocuments),1),i[2]||(i[2]=r("div",{class:"stats-label"},"待处理文档",-1))])])]),_:1}),u(J,{class:"stats-card"},{default:v(()=>[r("div",R,[r("div",V,[u(n,{size:"32",color:"#f56c6c"},{default:v(()=>[u(f(e))]),_:1})]),r("div",q,[r("div",A,_(D.value.onlineUsers),1),i[3]||(i[3]=r("div",{class:"stats-label"},"在线用户",-1))])])]),_:1})]),r("div",B,[u(J,{class:"chart-card"},{header:v(()=>i[4]||(i[4]=[r("span",null,"审校趋势",-1)])),default:v(()=>[r("div",E,[u(n,{size:"64",color:"#ddd"},{default:v(()=>[u(f(l))]),_:1}),i[5]||(i[5]=r("p",null,"图表组件待集成",-1))])]),_:1}),u(J,{class:"chart-card"},{header:v(()=>i[6]||(i[6]=[r("span",null,"文档类型分布",-1)])),default:v(()=>[r("div",F,[u(n,{size:"64",color:"#ddd"},{default:v(()=>[u(f(c))]),_:1}),i[7]||(i[7]=r("p",null,"图表组件待集成",-1))])]),_:1})]),u(J,{class:"activity-card"},{header:v(()=>i[8]||(i[8]=[r("span",null,"最近活动",-1)])),default:v(()=>[u(L,null,{default:v(()=>[(m(!0),o(h,null,y(G.value,s=>(m(),b(K,{key:s.id,timestamp:s.timestamp,type:s.type},{default:v(()=>[g(_(s.description),1)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1})])}}}),[["__scopeId","data-v-734e13a8"]]);export{G as default};
