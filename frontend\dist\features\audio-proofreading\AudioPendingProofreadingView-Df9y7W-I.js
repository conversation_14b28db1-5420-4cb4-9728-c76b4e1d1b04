import{M as e,x as a,d as l,a0 as u,R as t}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as n,r as s,b as o,m as r,c as i,a as d,Q as p,I as c,ag as v,o as m,u as g,M as f,O as _,J as h,aq as w,H as b,K as y}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as C}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const k={class:"audio-pending-proofreading"},z={class:"content"},V={class:"file-name"},x={class:"pagination-container"},T={key:0,class:"play-content"},B=["src"],Y={class:"audio-info"},I=C(n({__name:"AudioPendingProofreadingView",setup(n){const C=s(!1),I=s([]),M=s([]),U=s(""),j=s(""),D=s(""),S=s([]),P=s(1),$=s(20),F=s(0),K=s(!1),L=s(null),O=o(()=>{let e=I.value;return U.value&&(e=e.filter(e=>e.name.toLowerCase().includes(U.value.toLowerCase()))),j.value&&(e=e.filter(e=>e.status===j.value)),D.value&&(e=e.filter(e=>{const a=A(e.duration);switch(D.value){case"short":return a<=300;case"medium":return a>300&&a<=1800;case"long":return a>1800;default:return!0}})),S.value&&2===S.value.length&&(e=e.filter(e=>{const a=new Date(e.uploadTime).toISOString().split("T")[0];return a>=S.value[0]&&a<=S.value[1]})),e});r(()=>{q()});const q=async()=>{C.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),I.value=[{id:1,name:"meeting_audio_001.mp3",duration:"25:30",size:15728640,uploadTime:"2024-01-15 10:30:00",status:"pending",uploader:"张三",url:"/api/files/preview/audio/1"},{id:2,name:"interview_recording.wav",duration:"18:45",size:45678912,uploadTime:"2024-01-15 09:15:00",status:"processing",uploader:"李四",url:"/api/files/preview/audio/2"},{id:3,name:"podcast_episode_03.m4a",duration:"42:18",size:32505856,uploadTime:"2024-01-14 16:45:00",status:"uploaded",uploader:"王五",url:"/api/files/preview/audio/3"}],F.value=I.value.length}catch(a){e.error("加载文件列表失败")}finally{C.value=!1}},A=e=>{const a=e.split(":");return 60*parseInt(a[0])+parseInt(a[1])},E=()=>{P.value=1},G=()=>{P.value=1},H=()=>{q()},J=e=>{M.value=e},Q=async()=>{if(0!==M.value.length)try{await t.confirm(`确定要批量处理选中的 ${M.value.length} 个文件吗？`,"批量处理确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.success("批量处理已开始，请稍后查看处理结果"),M.value.forEach(e=>{e.status="processing"}),M.value=[]}catch{}else e.warning("请选择要处理的文件")},R=e=>{$.value=e,P.value=1},N=e=>{P.value=e},W=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":e<1073741824?(e/1048576).toFixed(1)+" MB":(e/1073741824).toFixed(1)+" GB",X=e=>{switch(e){case"pending":return"warning";case"processing":return"primary";case"uploaded":return"success";default:return"info"}},Z=e=>{switch(e){case"pending":return"待处理";case"processing":return"处理中";case"uploaded":return"已上传";default:return"未知"}};return(n,s)=>{const o=v("el-icon"),r=v("el-input"),q=v("el-col"),A=v("el-option"),ee=v("el-select"),ae=v("el-date-picker"),le=v("el-button"),ue=v("el-row"),te=v("el-card"),ne=v("el-table-column"),se=v("el-tag"),oe=v("el-table"),re=v("el-pagination"),ie=v("el-dialog"),de=w("loading");return m(),i("div",k,[s[16]||(s[16]=d("div",{class:"page-header"},[d("h1",null,"待审查音频"),d("p",{class:"page-description"},"显示等待审校的音频文件列表")],-1)),d("div",z,[p(te,{class:"filter-card"},{default:c(()=>[p(ue,{gutter:20},{default:c(()=>[p(q,{span:6},{default:c(()=>[p(r,{modelValue:U.value,"onUpdate:modelValue":s[0]||(s[0]=e=>U.value=e),placeholder:"搜索文件名...",clearable:"",onInput:E},{prefix:c(()=>[p(o,null,{default:c(()=>[p(g(a))]),_:1})]),_:1},8,["modelValue"])]),_:1}),p(q,{span:4},{default:c(()=>[p(ee,{modelValue:j.value,"onUpdate:modelValue":s[1]||(s[1]=e=>j.value=e),placeholder:"状态筛选",clearable:"",onChange:G},{default:c(()=>[p(A,{label:"全部",value:""}),p(A,{label:"待处理",value:"pending"}),p(A,{label:"处理中",value:"processing"}),p(A,{label:"已上传",value:"uploaded"})]),_:1},8,["modelValue"])]),_:1}),p(q,{span:4},{default:c(()=>[p(ee,{modelValue:D.value,"onUpdate:modelValue":s[2]||(s[2]=e=>D.value=e),placeholder:"时长筛选",clearable:"",onChange:G},{default:c(()=>[p(A,{label:"全部",value:""}),p(A,{label:"5分钟以内",value:"short"}),p(A,{label:"5-30分钟",value:"medium"}),p(A,{label:"30分钟以上",value:"long"})]),_:1},8,["modelValue"])]),_:1}),p(q,{span:4},{default:c(()=>[p(ae,{modelValue:S.value,"onUpdate:modelValue":s[3]||(s[3]=e=>S.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:G},null,8,["modelValue"])]),_:1}),p(q,{span:3},{default:c(()=>[p(le,{type:"primary",onClick:H},{default:c(()=>s[7]||(s[7]=[f("刷新列表")])),_:1,__:[7]})]),_:1}),p(q,{span:3},{default:c(()=>[p(le,{onClick:Q,disabled:0===M.value.length},{default:c(()=>[f(" 批量处理 ("+_(M.value.length)+") ",1)]),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1}),p(te,{title:"待审查音频列表",class:"list-card"},{default:c(()=>[h((m(),b(oe,{data:O.value,style:{width:"100%"},onSelectionChange:J},{default:c(()=>[p(ne,{type:"selection",width:"55"}),p(ne,{prop:"name",label:"文件名",width:"200"},{default:c(e=>[d("div",V,[p(o,null,{default:c(()=>[p(g(l))]),_:1}),d("span",null,_(e.row.name),1)])]),_:1}),p(ne,{prop:"duration",label:"时长",width:"100"}),p(ne,{prop:"size",label:"文件大小",width:"100"},{default:c(e=>[f(_(W(e.row.size)),1)]),_:1}),p(ne,{prop:"uploadTime",label:"上传时间",width:"160"}),p(ne,{prop:"status",label:"状态",width:"100"},{default:c(e=>[p(se,{type:X(e.row.status)},{default:c(()=>[f(_(Z(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),p(ne,{prop:"uploader",label:"上传者",width:"120"}),p(ne,{label:"预览",width:"80"},{default:c(e=>[p(le,{size:"small",onClick:a=>{return l=e.row,L.value=l,void(K.value=!0);var l}},{default:c(()=>[p(o,null,{default:c(()=>[p(g(u))]),_:1})]),_:2},1032,["onClick"])]),_:1}),p(ne,{label:"操作",width:"200"},{default:c(a=>[p(le,{size:"small",type:"primary",onClick:l=>{return u=a.row,void e.success(`开始审校文件：${u.name}`);var u}},{default:c(()=>s[8]||(s[8]=[f(" 开始审校 ")])),_:2,__:[8]},1032,["onClick"]),p(le,{size:"small",onClick:l=>{return u=a.row,void e.success(`开始下载文件：${u.name}`);var u}},{default:c(()=>s[9]||(s[9]=[f("下载")])),_:2,__:[9]},1032,["onClick"]),p(le,{size:"small",type:"danger",onClick:l=>(async a=>{try{await t.confirm(`确定要删除文件 "${a.name}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=I.value.findIndex(e=>e.id===a.id);l>-1&&(I.value.splice(l,1),F.value=I.value.length,e.success("删除成功"))}catch{}})(a.row)},{default:c(()=>s[10]||(s[10]=[f("删除")])),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[de,C.value]]),d("div",x,[p(re,{"current-page":P.value,"onUpdate:currentPage":s[4]||(s[4]=e=>P.value=e),"page-size":$.value,"onUpdate:pageSize":s[5]||(s[5]=e=>$.value=e),"page-sizes":[10,20,50,100],total:F.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:R,onCurrentChange:N},null,8,["current-page","page-size","total"])])]),_:1})]),p(ie,{modelValue:K.value,"onUpdate:modelValue":s[6]||(s[6]=e=>K.value=e),title:"音频播放",width:"60%"},{default:c(()=>[L.value?(m(),i("div",T,[d("audio",{src:L.value.url,controls:"",class:"audio-player"},null,8,B),d("div",Y,[d("p",null,[s[11]||(s[11]=d("strong",null,"文件名：",-1)),f(_(L.value.name),1)]),d("p",null,[s[12]||(s[12]=d("strong",null,"时长：",-1)),f(_(L.value.duration),1)]),d("p",null,[s[13]||(s[13]=d("strong",null,"文件大小：",-1)),f(_(W(L.value.size)),1)]),d("p",null,[s[14]||(s[14]=d("strong",null,"上传时间：",-1)),f(_(L.value.uploadTime),1)]),d("p",null,[s[15]||(s[15]=d("strong",null,"上传者：",-1)),f(_(L.value.uploader),1)])])])):y("",!0)]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-d621d1fd"]]);export{I as default};
