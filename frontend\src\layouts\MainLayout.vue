<!--
  主布局组件
  包含侧边栏导航、顶部导航栏、面包屑和主内容区域
-->
<template>
  <el-container class="main-layout">
    <!-- 侧边栏 -->
    <el-aside :width="sidebarCollapsed ? '64px' : '200px'" class="sidebar">
      <div class="logo-container">
        <el-icon class="logo" size="32" style="margin-right: 10px"><Document /></el-icon>
        <span
          v-show="!sidebarCollapsed"
          class="logo-text"
          style="font-size: 22px; font-weight: bold"
        >
          AI智能审校</span
        >
      </div>

      <!-- 导航菜单 -->
      <el-menu
        :default-active="activeMenu"
        :collapse="sidebarCollapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <!-- 校对大屏 -->
        <el-menu-item index="/dashboard">
          <el-icon><Monitor /></el-icon>
          <template #title>校对情况</template>
        </el-menu-item>

        <!-- 内容预审管理 -->
        <el-sub-menu index="/content-review">
          <template #title>
            <el-icon><DocumentChecked /></el-icon>
            <span>内容预审管理</span>
          </template>
          <el-menu-item index="/content-review/unreviewed">
            <el-icon><Files /></el-icon>
            <span>未预审文档</span>
          </el-menu-item>
          <el-menu-item index="/content-review/reviewed">
            <el-icon><Checked /></el-icon>
            <span>已预审文档</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- AI批量审校 -->
        <el-sub-menu index="/ai-batch-proofreading">
          <template #title>
            <el-icon><Cpu /></el-icon>
            <span>AI批量审校</span>
          </template>
          <el-menu-item index="/ai-batch-proofreading/unproofread">
            <el-icon><Files /></el-icon>
            <span>未校对文档</span>
          </el-menu-item>
          <el-menu-item index="/ai-batch-proofreading/pending">
            <el-icon><Clock /></el-icon>
            <span>待审校文档</span>
          </el-menu-item>
          <el-menu-item index="/ai-batch-proofreading/completed">
            <el-icon><Finished /></el-icon>
            <span>完成校对文档</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 在线AI审校 -->
        <el-sub-menu index="/online-proofreading">
          <template #title>
            <el-icon><Edit /></el-icon>
            <span>在线AI审校</span>
          </template>
          <el-menu-item index="/online-proofreading/editor">
            <el-icon><EditPen /></el-icon>
            <span>在线审校</span>
          </el-menu-item>
          <el-menu-item index="/online-proofreading/history">
            <el-icon><Finished /></el-icon>
            <span>已在线审校文档</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 多媒体审校 -->
        <el-sub-menu index="/multimedia">
          <template #title>
            <el-icon><Picture /></el-icon>
            <span>多媒体审校</span>
          </template>

          <!-- 图片审校子菜单 -->
          <el-sub-menu index="/multimedia/image">
            <template #title>
              <el-icon><Picture /></el-icon>
              <span>图片审校</span>
            </template>
            <el-menu-item index="/multimedia/image/batch">
              <el-icon><Operation /></el-icon>
              <span>批量审校</span>
            </el-menu-item>
            <el-menu-item index="/multimedia/image/pending">
              <el-icon><Clock /></el-icon>
              <span>待审查图片</span>
            </el-menu-item>
            <el-menu-item index="/multimedia/image/online">
              <el-icon><EditPen /></el-icon>
              <span>在线审校</span>
            </el-menu-item>
            <el-menu-item index="/multimedia/image/completed">
              <el-icon><Finished /></el-icon>
              <span>已审校图片</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 视频审校子菜单 -->
          <el-sub-menu index="/multimedia/video">
            <template #title>
              <el-icon><VideoCamera /></el-icon>
              <span>视频审校</span>
            </template>
            <el-menu-item index="/multimedia/video/batch">
              <el-icon><Operation /></el-icon>
              <span>批量审校</span>
            </el-menu-item>
            <el-menu-item index="/multimedia/video/pending">
              <el-icon><Clock /></el-icon>
              <span>待审查视频</span>
            </el-menu-item>
            <el-menu-item index="/multimedia/video/online">
              <el-icon><EditPen /></el-icon>
              <span>在线审校</span>
            </el-menu-item>
            <el-menu-item index="/multimedia/video/completed">
              <el-icon><Finished /></el-icon>
              <span>已审校视频</span>
            </el-menu-item>
          </el-sub-menu>

          <!-- 音频审校子菜单 -->
          <el-sub-menu index="/multimedia/audio">
            <template #title>
              <el-icon><Microphone /></el-icon>
              <span>音频审校</span>
            </template>
            <el-menu-item index="/multimedia/audio/batch">
              <el-icon><Operation /></el-icon>
              <span>批量审校</span>
            </el-menu-item>
            <el-menu-item index="/multimedia/audio/pending">
              <el-icon><Clock /></el-icon>
              <span>待审查音频</span>
            </el-menu-item>
            <el-menu-item index="/multimedia/audio/online">
              <el-icon><EditPen /></el-icon>
              <span>在线审校</span>
            </el-menu-item>
            <el-menu-item index="/multimedia/audio/completed">
              <el-icon><Finished /></el-icon>
              <span>已审校音频</span>
            </el-menu-item>
          </el-sub-menu>
        </el-sub-menu>

        <!-- 专业排版 -->
        <el-sub-menu index="/professional-typesetting">
          <template #title>
            <el-icon><Document /></el-icon>
            <span>专业排版</span>
          </template>
          <el-menu-item index="/professional-typesetting/unformatted">
            <el-icon><Files /></el-icon>
            <span>未排版文档</span>
          </el-menu-item>
          <el-menu-item index="/professional-typesetting/chinese-formatting">
            <el-icon><SetUp /></el-icon>
            <span>排版中的文档</span>
          </el-menu-item>
          <el-menu-item index="/professional-typesetting/formatted">
            <el-icon><Finished /></el-icon>
            <span>已排版文档</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 专业查询 -->
        <el-sub-menu index="/professional-query">
          <template #title>
            <el-icon><Search /></el-icon>
            <span>专业查询</span>
          </template>
          <el-menu-item index="/professional-query/terminology">
            <el-icon><Notebook /></el-icon>
            <span>术语查询</span>
          </el-menu-item>
          <el-menu-item index="/professional-query/standards">
            <el-icon><Management /></el-icon>
            <span>标准查询</span>
          </el-menu-item>
          <el-menu-item index="/professional-query/classical-literature">
            <el-icon><Reading /></el-icon>
            <span>古诗文查询</span>
          </el-menu-item>
          <el-menu-item index="/professional-query/legal-regulations">
            <el-icon><Document /></el-icon>
            <span>法律法规查询</span>
          </el-menu-item>
          <el-menu-item index="/professional-query/important-speeches">
            <el-icon><Microphone /></el-icon>
            <span>重要讲话查询</span>
          </el-menu-item>
          <el-menu-item index="/professional-query/official-reports">
            <el-icon><Memo /></el-icon>
            <span>官方报道查询</span>
          </el-menu-item>
          <el-menu-item index="/professional-query/policy">
            <el-icon><DataBoard /></el-icon>
            <span>政策查询</span>
          </el-menu-item>
          <el-menu-item index="/professional-query/dictionary">
            <el-icon><Collection /></el-icon>
            <span>词典查询</span>
          </el-menu-item>
          <el-menu-item index="/professional-query/other">
            <el-icon><More /></el-icon>
            <span>其他查询</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 编辑文档库 -->
        <el-sub-menu index="/document-library">
          <template #title>
            <el-icon><Folder /></el-icon>
            <span>编辑文档库</span>
          </template>
          <el-menu-item index="/document-library/new-proofreading-comments">
            <el-icon><FolderAdd /></el-icon>
            <span>新建校对意见</span>
          </el-menu-item>
          <el-menu-item index="/document-library/existing-proofreading-comments">
            <el-icon><CopyDocument /></el-icon>
            <span>已校对意见</span>
          </el-menu-item>
          <el-menu-item index="/document-library/new-review-comments">
            <el-icon><FolderAdd /></el-icon>
            <span>新建审查意见</span>
          </el-menu-item>
          <el-menu-item index="/document-library/existing-review-comments">
            <el-icon><CopyDocument /></el-icon>
            <span>已审查意见</span>
          </el-menu-item>
          <el-menu-item index="/document-library/other-documents">
            <el-icon><Files /></el-icon>
            <span>其他文档</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 我的修改积累 -->
        <el-sub-menu index="/modification-accumulation">
          <template #title>
            <el-icon><Collection /></el-icon>
            <span>我的修改积累</span>
          </template>
          <el-menu-item index="/modification-accumulation/add-case-set">
            <el-icon><Plus /></el-icon>
            <span>添加案例集</span>
          </el-menu-item>
          <el-menu-item index="/modification-accumulation/review-case-set">
            <el-icon><DocumentChecked /></el-icon>
            <span>审查案例集</span>
          </el-menu-item>
          <el-menu-item index="/modification-accumulation/sync-case-set">
            <el-icon><Refresh /></el-icon>
            <span>同步案例集</span>
          </el-menu-item>
          <el-menu-item index="/modification-accumulation/query-proofreading-cases">
            <el-icon><Search /></el-icon>
            <span>查询校对案例集</span>
          </el-menu-item>
          <el-menu-item index="/modification-accumulation/query-review-cases">
            <el-icon><Search /></el-icon>
            <span>查询审查案例集</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 个人中心 -->
        <el-sub-menu index="/user-center">
          <template #title>
            <el-icon><User /></el-icon>
            <span>个人中心</span>
          </template>
          <el-menu-item index="/user-center/profile">
            <el-icon><Avatar /></el-icon>
            <span>个人资料</span>
          </el-menu-item>
          <el-menu-item index="/user-center/settings">
            <el-icon><Setting /></el-icon>
            <span>账户设置</span>
          </el-menu-item>
          <el-menu-item index="/user-center/operation-logs">
            <el-icon><List /></el-icon>
            <span>操作日志</span>
          </el-menu-item>
          <el-menu-item index="/user-center/preferences">
            <el-icon><Tools /></el-icon>
            <span>系统偏好</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 响应式测试（开发用） -->
        <el-menu-item index="/responsive-test">
          <el-icon><Monitor /></el-icon>
          <template #title>响应式测试</template>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主内容区域 -->
    <el-container class="main-container">
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <div class="header-left">
          <!-- 折叠按钮 -->
          <el-button link @click="toggleSidebar" class="collapse-btn">
            <el-icon><Fold v-if="!sidebarCollapsed" /><Expand v-else /></el-icon>
          </el-button>

          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item v-for="item in breadcrumbList" :key="item.path" :to="item.path">
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 用户信息 -->
          <el-dropdown @command="handleUserCommand">
            <span class="user-info">
              <el-avatar :size="32" :src="userInfo.avatar" />
              <span class="username">{{ userInfo.username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Monitor,
  DocumentChecked,
  Cpu,
  Edit,
  Picture,
  Document,
  Search,
  Folder,
  Collection,
  User,
  Fold,
  Expand,
  ArrowDown,
  // 二级和三级菜单图标
  Files,
  Checked,
  Clock,
  EditPen,
  Finished,
  VideoCamera,
  Microphone,
  Operation,
  Management,
  Reading,
  SetUp,
  DataBoard,
  Notebook,
  Memo,
  CopyDocument,
  FolderAdd,
  Avatar,
  Setting,
  List,
  Tools,
  More,
  Plus,
  Refresh,
} from '@element-plus/icons-vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 侧边栏状态
const sidebarCollapsed = ref(false)

// 当前激活的菜单项
const activeMenu = computed(() => route.path)

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter((item) => item.meta?.title)
  return matched.map((item) => ({
    title: item.meta?.title as string,
    path: item.path,
  }))
})

// 用户信息
const userInfo = ref({
  username: '管理员',
  avatar: '/avatar.png',
})

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 处理用户下拉菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/user-center/profile')
      break
    case 'settings':
      router.push('/user-center/settings')
      break
    case 'logout':
      // 清除登录状态
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_info')
      router.push('/login')
      break
  }
}
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --el-menu-item-height: 45px;
}

.main-layout {
  width: 100%;
  height: 100vh;
  min-height: 100vh;
  overflow: hidden; /* 防止整体滚动 */
}

.sidebar {
  background-color: #cde6ff;
  transition: width 0.3s;
  height: 100vh;
  overflow-y: auto; /* 侧边栏内容可滚动 */
}

.logo-container {
  display: flex;
  align-items: center;
  padding: 16px;
  color: #0056ac;
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
}

.sidebar-menu {
  border: none;
  background-color: transparent;
}

/* 一级菜单项高度设置 */
.sidebar-menu .el-menu-item,
.sidebar-menu .el-sub-menu__title {
  height: 40px !important;
  line-height: 40px !important;
  color: #0056ac;
  background-color: transparent;
  margin: 2px;
}

/* 菜单图标样式 */
.sidebar-menu .el-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  vertical-align: middle;
}

.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-sub-menu__title:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #409eff;
  color: white;
}

/* 二级菜单项高度设置 */
.sidebar-menu .el-sub-menu .el-menu-item {
  height: var(--el-menu-item-height);
  line-height: var(--el-menu-item-height);
  background-color: rgba(255, 255, 255, 0.1);
  color: #0056ac;
}

.sidebar-menu .el-sub-menu .el-menu-item:hover {
  background-color: rgba(64, 158, 255, 0.2);
}

.sidebar-menu .el-sub-menu .el-menu-item.is-active {
  background-color: #409eff;
  color: white;
}

/* 三级菜单标题高度设置 */
.sidebar-menu .el-sub-menu .el-sub-menu__title {
  height: var(--el-menu-item-height);
  line-height: var(--el-menu-item-height);
  background-color: rgba(255, 255, 255, 0.08);
  color: #0056ac;
  padding-left: 40px;
}

.sidebar-menu .el-sub-menu .el-sub-menu__title:hover {
  background-color: rgba(64, 158, 255, 0.18);
}

/* 三级菜单项高度设置 */
.sidebar-menu .el-sub-menu .el-sub-menu .el-menu-item {
  height: var(--el-menu-item-height);
  line-height: var(--el-menu-item-height);
  background-color: rgba(255, 255, 255, 0.05);
  color: #0056ac;
  padding-left: 60px;
}

.sidebar-menu .el-sub-menu .el-sub-menu .el-menu-item:hover {
  background-color: rgba(64, 158, 255, 0.15);
}

.sidebar-menu .el-sub-menu .el-sub-menu .el-menu-item.is-active {
  background-color: #409eff;
  color: white;
}

.main-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  width: 100%;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  margin-right: 16px;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 8px;
}

.main-content {
  background-color: #f5f5f5;
  padding: 6px;
  width: 100%;
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 内容可滚动 */
  overflow-x: hidden; /* 防止水平滚动 */
}

/* 卡片内容区域样式调整 */
:deep(.el-card__body) {
  padding: 5px 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100% !important;
    height: auto;
    position: relative;
  }

  .logo-container {
    padding: 12px 16px;
  }

  .logo-text {
    font-size: 16px;
  }

  .header {
    padding: 0 12px;
  }

  .main-content {
    padding: 2px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .sidebar {
    width: 200px !important;
  }

  .logo-text {
    font-size: 16px;
  }
}

@media (min-width: 1025px) {
  .main-layout {
    width: 100%;
  }
}
</style>
