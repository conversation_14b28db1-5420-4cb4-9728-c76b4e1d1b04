import{d as s,c as a,a as e,Q as r,I as l,ag as c,o as n}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as t}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const u={class:"query-review-cases"},o={class:"content"},p=t(s({__name:"QueryReviewCases",setup:s=>(s,t)=>{const p=c("el-card");return n(),a("div",u,[t[1]||(t[1]=e("div",{class:"page-header"},[e("h1",null,"查询审查案例集"),e("p",{class:"page-description"},"检索和查看审查案例")],-1)),e("div",o,[r(p,null,{default:l(()=>t[0]||(t[0]=[e("p",null,"查询审查案例集功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-687baa7b"]]);export{p as default};
