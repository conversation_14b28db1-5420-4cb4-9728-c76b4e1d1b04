{"version": 3, "file": "transfer-panel2.js", "sources": ["../../../../../../packages/components/transfer/src/transfer-panel.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { transferCheckedChangeFn, transferProps } from './transfer'\n\nimport type { ExtractPropTypes, VNode } from 'vue'\nimport type { TransferDataItem, TransferKey } from './transfer'\nimport type TransferPanel from './transfer-panel.vue'\n\nexport interface TransferPanelState {\n  checked: TransferKey[]\n  allChecked: boolean\n  query: string\n  checkChangeByUser: boolean\n}\n\nexport const CHECKED_CHANGE_EVENT = 'checked-change'\n\nexport const transferPanelProps = buildProps({\n  data: transferProps.data,\n  optionRender: {\n    type: definePropType<(option: TransferDataItem) => VNode | VNode[]>(\n      Function\n    ),\n  },\n  placeholder: String,\n  title: String,\n  filterable: Boolean,\n  format: transferProps.format,\n  filterMethod: transferProps.filterMethod,\n  defaultChecked: transferProps.leftDefaultChecked,\n  props: transferProps.props,\n} as const)\nexport type TransferPanelProps = ExtractPropTypes<typeof transferPanelProps>\n\nexport const transferPanelEmits = {\n  [CHECKED_CHANGE_EVENT]: transferCheckedChangeFn,\n}\nexport type TransferPanelEmits = typeof transferPanelEmits\n\nexport type TransferPanelInstance = InstanceType<typeof TransferPanel> & unknown\n"], "names": ["buildProps", "transferProps", "definePropType", "transferCheckedChangeFn"], "mappings": ";;;;;;;AAEY,MAAC,oBAAoB,GAAG,iBAAiB;AACzC,MAAC,kBAAkB,GAAGA,kBAAU,CAAC;AAC7C,EAAE,IAAI,EAAEC,sBAAa,CAAC,IAAI;AAC1B,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEC,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,MAAM,EAAED,sBAAa,CAAC,MAAM;AAC9B,EAAE,YAAY,EAAEA,sBAAa,CAAC,YAAY;AAC1C,EAAE,cAAc,EAAEA,sBAAa,CAAC,kBAAkB;AAClD,EAAE,KAAK,EAAEA,sBAAa,CAAC,KAAK;AAC5B,CAAC,EAAE;AACS,MAAC,kBAAkB,GAAG;AAClC,EAAE,CAAC,oBAAoB,GAAGE,gCAAuB;AACjD;;;;;;"}