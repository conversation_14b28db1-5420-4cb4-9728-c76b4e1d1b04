<template>
  <div class="original-text-editor">
    <!-- 编辑器工具栏 -->
    <div v-if="showToolbar" class="editor-toolbar">
      <div class="toolbar-left">
        <span class="editor-title">原文内容</span>
        <el-tag v-if="suggestions.length > 0" type="warning" size="small">
          {{ suggestions.length }} 个建议
        </el-tag>
      </div>

      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="toggleMarkup" :type="showMarkup ? 'primary' : 'default'">
            <el-icon><View /></el-icon>
            {{ showMarkup ? '隐藏标记' : '显示标记' }}
          </el-button>
          <el-button @click="copyContent">
            <el-icon><CopyDocument /></el-icon>
            复制
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 编辑器容器 -->
    <div
      ref="editorContainer"
      class="editor-container"
      :class="{ readonly: readonly, 'with-markup': showMarkup }"
    >
      <!-- WangEditor Vue3 组件 -->
      <div style="border: 1px solid #ccc">
        <Toolbar
          style="border-bottom: 1px solid #ccc"
          :editor="editorRef"
          :defaultConfig="toolbarConfig"
          mode="default"
        />
        <Editor
          :style="{ height: height, overflowY: 'hidden' }"
          v-model="valueHtml"
          :defaultConfig="editorConfig"
          mode="default"
          @onCreated="handleCreated"
          @onChange="handleChange"
          @onFocus="handleFocus"
          @onBlur="handleBlur"
        />
      </div>

      <!-- 建议悬浮提示 -->
      <div
        v-if="hoveredSuggestion"
        ref="tooltipRef"
        class="suggestion-tooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-header">
          <span class="suggestion-type">{{ getSuggestionTypeLabel(hoveredSuggestion.type) }}</span>
          <span class="confidence-score"
            >置信度: {{ Math.round(hoveredSuggestion.confidence * 100) }}%</span
          >
        </div>
        <div class="tooltip-content">
          <div class="original-text"><strong>原文:</strong> {{ hoveredSuggestion.original }}</div>
          <div class="suggested-text">
            <strong>建议:</strong> {{ hoveredSuggestion.suggestion }}
          </div>
          <div class="suggestion-reason"><strong>原因:</strong> {{ hoveredSuggestion.reason }}</div>
        </div>
        <div class="tooltip-actions">
          <el-button size="small" type="primary" @click="acceptSuggestion(hoveredSuggestion)">
            接受
          </el-button>
          <el-button size="small" @click="rejectSuggestion(hoveredSuggestion)"> 拒绝 </el-button>
          <el-button size="small" @click="ignoreSuggestion(hoveredSuggestion)"> 忽略 </el-button>
        </div>
      </div>
    </div>

    <!-- 建议列表面板 -->
    <div v-if="showSuggestionsList && suggestions.length > 0" class="suggestions-panel">
      <div class="panel-header">
        <span>校对建议 ({{ suggestions.length }})</span>
        <el-button size="small" text @click="toggleSuggestionsList">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>

      <div class="suggestions-list">
        <div
          v-for="suggestion in suggestions"
          :key="suggestion.id"
          class="suggestion-item"
          :class="{
            active: hoveredSuggestion?.id === suggestion.id,
            [suggestion.status]: true,
          }"
          @mouseenter="highlightSuggestion(suggestion)"
          @mouseleave="clearHighlight"
          @click="scrollToSuggestion(suggestion)"
        >
          <div class="suggestion-header">
            <el-tag :type="getSuggestionTagType(suggestion.type)" size="small">
              {{ getSuggestionTypeLabel(suggestion.type) }}
            </el-tag>
            <span class="confidence">{{ Math.round(suggestion.confidence * 100) }}%</span>
          </div>

          <div class="suggestion-content">
            <div class="text-change">
              <span class="original">{{ suggestion.original }}</span>
              <el-icon class="arrow"><ArrowRight /></el-icon>
              <span class="suggested">{{ suggestion.suggestion }}</span>
            </div>
            <div class="reason">{{ suggestion.reason }}</div>
          </div>

          <div class="suggestion-actions">
            <el-button-group size="small">
              <el-button
                type="primary"
                :disabled="suggestion.status !== 'pending'"
                @click.stop="acceptSuggestion(suggestion)"
              >
                接受
              </el-button>
              <el-button
                :disabled="suggestion.status !== 'pending'"
                @click.stop="rejectSuggestion(suggestion)"
              >
                拒绝
              </el-button>
              <el-button
                :disabled="suggestion.status !== 'pending'"
                @click.stop="ignoreSuggestion(suggestion)"
              >
                忽略
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </div>

    <!-- 建议统计 -->
    <div v-if="showStatistics" class="suggestions-statistics">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="总建议数" :value="suggestions.length" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已接受" :value="acceptedCount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已拒绝" :value="rejectedCount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="待处理" :value="pendingCount" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 原文显示编辑器组件
 *
 * 功能特性：
 * - 基于WangEditor的只读文本显示
 * - AI校对建议的可视化标记
 * - 建议悬浮提示和详情显示
 * - 建议接受/拒绝/忽略操作
 * - 建议列表面板和统计信息
 */

import {
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
  shallowRef,
  onBeforeUnmount,
} from 'vue'
import { ElMessage } from 'element-plus'
import { View, CopyDocument, Close, ArrowRight } from '@element-plus/icons-vue'

// 导入WangEditor Vue3组件
import '@wangeditor/editor/dist/css/style.css'
import type { IDomEditor, IEditorConfig } from '@wangeditor/editor'

// 导入自定义编辑器扩展（暂时不使用）
// import { registerAIProofreadingButton, getToolbarWithAIProofreading } from './editor-extensions'

// 导入类型定义
import type { ProofreadingSuggestion, SuggestionType, SuggestionStatus } from '../types'

// 组件属性
interface Props {
  /** 文本内容 */
  content: string
  /** 校对建议列表 */
  suggestions: ProofreadingSuggestion[]
  /** 是否只读 */
  readonly?: boolean
  /** 是否显示工具栏 */
  showToolbar?: boolean
  /** 是否显示建议列表 */
  showSuggestionsList?: boolean
  /** 是否显示统计信息 */
  showStatistics?: boolean
  /** 编辑器高度 */
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showToolbar: true,
  showSuggestionsList: false,
  showStatistics: false,
  height: '500px',
})

// 组件事件
interface Emits {
  (e: 'suggestion-click', suggestion: ProofreadingSuggestion): void
  (e: 'suggestion-accept', suggestion: ProofreadingSuggestion): void
  (e: 'suggestion-reject', suggestion: ProofreadingSuggestion): void
  (e: 'suggestion-ignore', suggestion: ProofreadingSuggestion): void
  (e: 'content-change', content: string): void
  (e: 'ai-proofreading-request', data: { content: string; source: string }): void
}

const emit = defineEmits<Emits>()

// 响应式数据 - 使用官方推荐的方式
const editorRef = shallowRef<IDomEditor>() // 编辑器实例，必须用 shallowRef
const valueHtml = ref(props.content || '<p></p>') // 编辑器内容
const editorContainer = ref<HTMLElement>()
const tooltipRef = ref<HTMLElement>()
const showMarkup = ref(true)
const hoveredSuggestion = ref<ProofreadingSuggestion | null>(null)
const tooltipStyle = reactive({
  left: '0px',
  top: '0px',
  display: 'none',
})

// 编辑器配置
const toolbarConfig = computed(() => ({
  toolbarKeys: [
    'headerSelect',
    'bold',
    'italic',
    'underline',
    'through',
    '|',
    'fontSize',
    'fontFamily',
    'lineHeight',
    'color',
    'bgColor',
    '|',
    'bulletedList',
    'numberedList',
    '|',
    'justifyLeft',
    'justifyCenter',
    'justifyRight',
    '|',
    'insertLink',
    'insertTable',
    '|',
    'undo',
    'redo',
    '|',
    'fullScreen',
  ],
}))

const editorConfig = computed(() => ({
  placeholder: '原文内容将在这里显示...',
  readOnly: props.readonly,
  scroll: true,
  maxLength: 100000,
  MENU_CONF: {
    // 字体配置
    fontFamily: {
      fontFamilyList: [
        'PingFang SC',
        'Microsoft YaHei',
        'Helvetica Neue',
        'Arial',
        'sans-serif',
        '宋体',
        '黑体',
        '楷体',
        '仿宋',
      ],
    },
    fontSize: {
      fontSizeList: ['12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px'],
    },
  },
}))

// 计算属性
const acceptedCount = computed(() => {
  return props.suggestions.filter((s) => s.status === 'accepted').length
})

const rejectedCount = computed(() => {
  return props.suggestions.filter((s) => s.status === 'rejected').length
})

const pendingCount = computed(() => {
  return props.suggestions.filter((s) => s.status === 'pending').length
})

// 监听内容变化
watch(
  () => props.content,
  (newContent) => {
    if (editor.value && newContent !== editor.value.getHtml()) {
      editor.value.setHtml(newContent)
      applyMarkup()
    }
  },
)

// 监听建议变化
watch(
  () => props.suggestions,
  () => {
    nextTick(() => {
      applyMarkup()
    })
  },
  { deep: true },
)

// 组件挂载
onMounted(() => {
  // 暂时不注册AI校对按钮
  // registerAIProofreadingButton()
})

/**
 * 设置AI校对按钮事件监听器
 */
const setupAIProofreadingListener = () => {
  if (!editorRef.value) return

  const editorContainer = editorRef.value.getEditableContainer()
  if (!editorContainer) return

  // 监听AI校对请求事件
  editorContainer.addEventListener('ai-proofreading-request', handleAIProofreadingRequest)
}

/**
 * 处理AI校对请求
 */
const handleAIProofreadingRequest = (event: CustomEvent) => {
  const { content, source } = event.detail

  console.log('收到AI校对请求:', {
    contentLength: content.length,
    source,
  })

  // 触发父组件的AI校对事件
  emit('ai-proofreading-request', {
    content,
    source: 'original-editor',
  })
}

/**
 * 应用校对标记
 */
const applyMarkup = () => {
  if (!editorRef.value || !showMarkup.value) return

  try {
    let html = props.content

    // 按位置倒序排序，避免位置偏移
    const sortedSuggestions = [...props.suggestions].sort(
      (a, b) => b.position.start - a.position.start,
    )

    // 为每个建议添加标记
    sortedSuggestions.forEach((suggestion) => {
      const { start, end } = suggestion.position
      const originalText = html.substring(start, end)

      // 根据建议类型和状态选择样式
      const className = getSuggestionClassName(suggestion)
      const markedText = `<span class="${className}" data-suggestion-id="${suggestion.id}" data-original="${originalText}" data-suggestion="${suggestion.suggestion}">${originalText}</span>`

      html = html.substring(0, start) + markedText + html.substring(end)
    })

    editorRef.value.setHtml(html)

    // 绑定建议点击事件
    bindSuggestionEvents()
  } catch (error) {
    console.error('应用标记失败:', error)
  }
}

/**
 * 获取建议样式类名
 */
const getSuggestionClassName = (suggestion: ProofreadingSuggestion): string => {
  const baseClass = 'suggestion-mark'
  const typeClass = `suggestion-${suggestion.type}`
  const statusClass = `suggestion-${suggestion.status}`

  return `${baseClass} ${typeClass} ${statusClass}`
}

/**
 * 绑定建议事件
 */
const bindSuggestionEvents = () => {
  if (!editorRef.value) return

  const editorContainer = editorRef.value.getEditableContainer()
  if (!editorContainer) return

  const suggestionElements = editorContainer.querySelectorAll('.suggestion-mark')

  suggestionElements.forEach((element) => {
    element.addEventListener('mouseenter', handleSuggestionHover)
    element.addEventListener('mouseleave', handleSuggestionLeave)
    element.addEventListener('click', handleSuggestionClick)
  })
}

/**
 * 处理建议悬停
 */
const handleSuggestionHover = (event: Event) => {
  const element = event.target as HTMLElement
  const suggestionId = element.getAttribute('data-suggestion-id')

  if (suggestionId) {
    const suggestion = props.suggestions.find((s) => s.id === suggestionId)
    if (suggestion) {
      hoveredSuggestion.value = suggestion
      showTooltip(event as MouseEvent)
    }
  }
}

/**
 * 处理建议离开
 */
const handleSuggestionLeave = () => {
  hideTooltip()
}

/**
 * 处理建议点击
 */
const handleSuggestionClick = (event: Event) => {
  event.preventDefault()
  const element = event.target as HTMLElement
  const suggestionId = element.getAttribute('data-suggestion-id')

  if (suggestionId) {
    const suggestion = props.suggestions.find((s) => s.id === suggestionId)
    if (suggestion) {
      emit('suggestion-click', suggestion)
    }
  }
}

/**
 * 显示提示框
 */
const showTooltip = (event: MouseEvent) => {
  if (!tooltipRef.value) return

  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const containerRect = editorContainer.value?.getBoundingClientRect()

  if (containerRect) {
    tooltipStyle.left = `${rect.left - containerRect.left}px`
    tooltipStyle.top = `${rect.bottom - containerRect.top + 5}px`
    tooltipStyle.display = 'block'
  }
}

/**
 * 隐藏提示框
 */
const hideTooltip = () => {
  tooltipStyle.display = 'none'
  hoveredSuggestion.value = null
}

/**
 * 切换标记显示
 */
const toggleMarkup = () => {
  showMarkup.value = !showMarkup.value
  if (showMarkup.value) {
    applyMarkup()
  } else {
    editorRef.value?.setHtml(props.content)
  }
}

/**
 * 复制内容
 */
const copyContent = async () => {
  try {
    const text = editorRef.value?.getText() || ''
    await navigator.clipboard.writeText(text)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

/**
 * 接受建议
 */
const acceptSuggestion = (suggestion: ProofreadingSuggestion) => {
  // 更新建议状态
  suggestion.status = 'accepted'
  emit('suggestion-accept', suggestion)
  hideTooltip()

  // 显示成功提示
  ElMessage.success('建议已接受')
}

/**
 * 拒绝建议
 */
const rejectSuggestion = (suggestion: ProofreadingSuggestion) => {
  // 更新建议状态
  suggestion.status = 'rejected'
  emit('suggestion-reject', suggestion)
  hideTooltip()

  // 显示提示
  ElMessage.info('建议已拒绝')
}

/**
 * 忽略建议
 */
const ignoreSuggestion = (suggestion: ProofreadingSuggestion) => {
  // 更新建议状态
  suggestion.status = 'ignored'
  emit('suggestion-ignore', suggestion)
  hideTooltip()

  // 显示提示
  ElMessage.info('建议已忽略')
}

/**
 * 高亮建议
 */
const highlightSuggestion = (suggestion: ProofreadingSuggestion) => {
  hoveredSuggestion.value = suggestion
  // TODO: 滚动到建议位置并高亮
}

/**
 * 清除高亮
 */
const clearHighlight = () => {
  hoveredSuggestion.value = null
}

/**
 * 滚动到建议
 */
const scrollToSuggestion = (suggestion: ProofreadingSuggestion) => {
  // TODO: 实现滚动到建议位置
  emit('suggestion-click', suggestion)
}

/**
 * 切换建议列表
 */
const toggleSuggestionsList = () => {
  // 这里应该通过emit通知父组件
}

/**
 * 获取建议类型标签
 */
const getSuggestionTypeLabel = (type: SuggestionType): string => {
  const labels = {
    grammar: '语法',
    spelling: '拼写',
    punctuation: '标点',
    word_choice: '用词',
    fluency: '通顺',
    formatting: '格式',
    terminology: '术语',
  }
  return labels[type] || type
}

/**
 * 获取建议标签类型
 */
const getSuggestionTagType = (type: SuggestionType): string => {
  const types = {
    grammar: 'danger',
    spelling: 'warning',
    punctuation: 'info',
    word_choice: 'primary',
    fluency: 'success',
    formatting: 'info',
    terminology: 'warning',
  }
  return types[type] || 'default'
}

// Vue3 官方组件事件处理
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor // 记录 editor 实例，重要！
  console.log('原文编辑器创建成功', editor)

  // 设置初始内容
  if (props.content) {
    editor.setHtml(props.content)
  }

  // 应用标记
  nextTick(() => {
    applyMarkup()
  })

  // 添加AI校对按钮事件监听器
  setupAIProofreadingListener()
}

const handleChange = (editor: IDomEditor) => {
  if (!props.readonly) {
    const html = editor.getHtml()
    valueHtml.value = html
    emit('content-change', html)
  }
}

const handleFocus = (editor: IDomEditor) => {
  console.log('编辑器获得焦点', editor)
}

const handleBlur = (editor: IDomEditor) => {
  console.log('编辑器失去焦点', editor)
}

// 组件销毁时，及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return

  // 清理AI校对事件监听器
  const editorContainer = editor.getEditableContainer()
  if (editorContainer) {
    editorContainer.removeEventListener('ai-proofreading-request', handleAIProofreadingRequest)
  }

  editor.destroy()
})
</script>

<script lang="ts">
import { defineComponent } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

export default defineComponent({
  name: 'OriginalTextEditor',
  components: {
    Editor,
    Toolbar,
  },
})
</script>

<style scoped>
.original-text-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-title {
  font-weight: 500;
  color: #303133;
}

.editor-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.wang-editor {
  height: 100%;
}

/* WangEditor 内容区域字体优化 */
.wang-editor :deep(.w-e-text-container) {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #303133;
  background-color: #ffffff;
}

.wang-editor :deep(.w-e-text-container p) {
  margin: 8px 0;
  font-size: 16px;
  line-height: 1.6;
  color: #303133;
}

.wang-editor :deep(.w-e-text-container [data-slate-editor]) {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #303133;
}

/* 工具栏字体优化 */
.wang-editor :deep(.w-e-toolbar) {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
  position: relative;
  z-index: 100;
}

/* 确保工具栏弹出框在最顶层 */
.wang-editor :deep(.w-e-toolbar .w-e-menu) {
  position: relative;
  z-index: 9999;
}

/* 工具栏下拉菜单和弹出框 */
.wang-editor :deep(.w-e-toolbar .w-e-menu .w-e-panel-container) {
  z-index: 9999 !important;
  position: absolute !important;
}

/* 颜色选择器弹出框 */
.wang-editor :deep(.w-e-toolbar .w-e-color-panel) {
  z-index: 9999 !important;
  position: absolute !important;
}

/* 字体选择器弹出框 */
.wang-editor :deep(.w-e-toolbar .w-e-select-list) {
  z-index: 9999 !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #e4e7ed !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 所有工具栏弹出元素 */
.wang-editor :deep(.w-e-toolbar [data-w-e-type='panel']) {
  z-index: 9999 !important;
  position: absolute !important;
}

/* 工具栏按钮悬浮状态 */
.wang-editor :deep(.w-e-toolbar .w-e-menu:hover) {
  z-index: 9999;
}

.editor-container.readonly .wang-editor {
  background: #f9f9f9;
}

.suggestion-tooltip {
  position: absolute;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  max-width: 300px;
  z-index: 1000;
  font-size: 14px;
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.suggestion-type {
  font-weight: 500;
  color: #409eff;
}

.confidence-score {
  font-size: 12px;
  color: #909399;
}

.tooltip-content {
  margin-bottom: 12px;
}

.original-text,
.suggested-text,
.suggestion-reason {
  margin-bottom: 6px;
  line-height: 1.4;
}

.tooltip-actions {
  display: flex;
  gap: 6px;
}

.suggestions-panel {
  border-top: 1px solid #e4e7ed;
  background: #fafafa;
  max-height: 300px;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
}

.suggestions-list {
  padding: 8px;
}

.suggestion-item {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.suggestion-item.active {
  border-color: #409eff;
  background: #e6f7ff;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.confidence {
  font-size: 12px;
  color: #909399;
}

.suggestion-content {
  margin-bottom: 12px;
}

.text-change {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.original {
  color: #f56c6c;
  text-decoration: line-through;
}

.suggested {
  color: #67c23a;
  font-weight: 500;
}

.arrow {
  color: #909399;
}

.reason {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.suggestion-actions {
  display: flex;
  justify-content: flex-end;
}

.suggestions-statistics {
  padding: 16px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}
</style>

<!-- 全局样式 - 建议标记 -->
<style>
.suggestion-mark {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-mark:hover {
  background-color: rgba(64, 158, 255, 0.1) !important;
}

/* 语法错误 - 红色下划线 */
.suggestion-grammar {
  border-bottom: 2px wavy #f56c6c;
  background-color: #fef0f0;
}

/* 拼写错误 - 橙色下划线 */
.suggestion-spelling {
  border-bottom: 2px wavy #e6a23c;
  background-color: #fdf6ec;
}

/* 标点符号 - 蓝色下划线 */
.suggestion-punctuation {
  border-bottom: 2px wavy #409eff;
  background-color: #ecf5ff;
}

/* 用词建议 - 紫色下划线 */
.suggestion-word_choice {
  border-bottom: 2px wavy #909399;
  background-color: #f4f4f5;
}

/* 通顺性 - 绿色下划线 */
.suggestion-fluency {
  border-bottom: 2px wavy #67c23a;
  background-color: #f0f9ff;
}

/* 格式规范 - 青色下划线 */
.suggestion-formatting {
  border-bottom: 2px wavy #17a2b8;
  background-color: #e8f4f8;
}

/* 术语一致性 - 黄色下划线 */
.suggestion-terminology {
  border-bottom: 2px wavy #e6a23c;
  background-color: #fdf6ec;
}

/* 建议状态样式 */
.suggestion-accepted {
  background-color: #f0f9ff !important;
  border-bottom-color: #67c23a !important;
}

.suggestion-rejected {
  background-color: #fef0f0 !important;
  border-bottom-color: #f56c6c !important;
  opacity: 0.6;
}

.suggestion-ignored {
  background-color: #f4f4f5 !important;
  border-bottom-color: #909399 !important;
  opacity: 0.4;
}
</style>
