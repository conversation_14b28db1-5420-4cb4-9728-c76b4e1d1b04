<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台过滤器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .filtered {
            background: #28a745;
        }
        .normal {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 控制台过滤器测试</h1>
        
        <div class="test-section">
            <h3>测试被过滤的日志（这些不应该在控制台显示）</h3>
            <button class="filtered" onclick="testFiltered()">测试过滤日志</button>
            <p>点击按钮后，检查浏览器控制台，这些日志应该被过滤掉：</p>
            <ul>
                <li>InstallTrigger 相关警告</li>
                <li>MSW 相关信息</li>
                <li>Pinia store 信息</li>
                <li>已弃用警告</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>测试正常日志（这些应该在控制台显示）</h3>
            <button class="normal" onclick="testNormal()">测试正常日志</button>
            <p>点击按钮后，这些日志应该正常显示在控制台中。</p>
        </div>

        <div class="test-section">
            <h3>检查当前页面状态</h3>
            <button onclick="checkPageStatus()">检查页面状态</button>
            <p>检查当前页面的控制台过滤器状态和Mock服务状态。</p>
        </div>
    </div>

    <script>
        function testFiltered() {
            console.log('🧪 开始测试被过滤的日志...');
            
            // 这些日志应该被过滤掉
            console.warn('InstallTrigger 已弃用，未来将被移除。');
            console.log('[MSW] Mocking enabled.');
            console.log('Documentation: https://mswjs.io/docs');
            console.log('Found an issue? https://github.com/mswjs/msw/issues');
            console.log('Worker script URL: http://localhost:5173/mockServiceWorker.js');
            console.log('Worker scope: http://localhost:5173/');
            console.log('Client ID: 96b5006c-f9e5-4ff5-971b-4b70a7b5890a (auxiliary)');
            console.log('🍍 "preReview" store installed 🆕');
            console.warn('某个功能已弃用，请使用新的API');
            console.error('commons.js:2:589607 某个错误信息');
            
            console.log('✅ 过滤测试完成 - 如果上面的日志没有显示，说明过滤器工作正常');
        }

        function testNormal() {
            console.log('🧪 开始测试正常日志...');
            
            // 这些日志应该正常显示
            console.log('✅ 这是一条正常的日志信息');
            console.warn('⚠️ 这是一条正常的警告信息');
            console.error('❌ 这是一条正常的错误信息');
            console.info('ℹ️ 这是一条信息日志');
            
            console.log('✅ 正常日志测试完成 - 上面的日志应该都能看到');
        }

        function checkPageStatus() {
            console.log('🔍 检查页面状态...');
            
            // 检查控制台方法是否被重写
            const isConsoleModified = console.log.toString().includes('originalLog') || 
                                    console.log.toString().length > 50;
            
            console.log('📊 页面状态报告:');
            console.log('  - 控制台是否被修改:', isConsoleModified ? '是' : '否');
            console.log('  - 当前URL:', window.location.href);
            console.log('  - 用户代理:', navigator.userAgent.substring(0, 50) + '...');
            
            // 检查全局Mock对象
            const mockObjects = [
                '__mockWorker',
                '__mockReset', 
                '__mockConfig'
            ];
            
            console.log('  - Mock对象状态:');
            mockObjects.forEach(obj => {
                const exists = window[obj] !== undefined;
                console.log(`    - ${obj}: ${exists ? '存在' : '不存在'}`);
            });
            
            console.log('✅ 状态检查完成');
        }

        // 页面加载时自动运行一次检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🚀 控制台过滤器测试页面已加载');
                console.log('💡 使用按钮测试不同类型的日志输出');
            }, 1000);
        });
    </script>
</body>
</html>
