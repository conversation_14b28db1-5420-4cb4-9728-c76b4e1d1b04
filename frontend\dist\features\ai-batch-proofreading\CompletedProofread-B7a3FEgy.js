import{d as e,r as s,m as a,c as l,Q as t,I as o,ag as r,o as d,a as c,M as n}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as p}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const u={class:"completed-proofread"},_={class:"card-header"},m={class:"content"},i=p(e({__name:"CompletedProofread",setup:e=>(s(!1),a(()=>{}),(e,s)=>{const a=r("el-button"),p=r("el-empty"),i=r("el-card");return d(),l("div",u,[t(i,null,{header:o(()=>[c("div",_,[s[1]||(s[1]=c("span",null,"已完成校对",-1)),t(a,{type:"success",size:"small"},{default:o(()=>s[0]||(s[0]=[n(" 导出结果 ")])),_:1,__:[0]})])]),default:o(()=>[c("div",m,[s[2]||(s[2]=c("p",null,"已完成校对页面 - 开发中...",-1)),t(p,{description:"暂无数据"})])]),_:1})])})}),[["__scopeId","data-v-3e20558b"]]);export{i as default};
