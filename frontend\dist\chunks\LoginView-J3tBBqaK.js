import{d as e,r as a,X as s,c as l,a as r,Q as o,I as t,V as n,ag as i,o as u,aB as m,a4 as d,M as p,O as c}from"./vue-vendor-BCsylZgc.js";import{M as g}from"./ui-vendor-DZ6owSRu.js";import{_ as f}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./utils-vendor-DYQz1-BF.js";const _={class:"login-container"},v={class:"login-box"},b=f(e({__name:"LoginView",setup(e){const f=m(),b=a(),w=a(!1),h=s({username:"",password:"",remember:!1}),V={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]},x=async()=>{if(b.value)try{await b.value.validate(),w.value=!0,await new Promise(e=>setTimeout(e,1e3));const e="mock_access_token_"+Date.now(),a={id:1,username:h.username,email:"<EMAIL>",avatar:"/avatar.png",roles:["admin"]};localStorage.setItem("access_token",e),localStorage.setItem("user_info",JSON.stringify(a)),g.success("登录成功"),f.push("/dashboard")}catch(e){g.error("登录失败，请检查用户名和密码")}finally{w.value=!1}};return(e,a)=>{const s=i("el-input"),m=i("el-form-item"),g=i("el-checkbox"),f=i("el-button"),y=i("el-form");return u(),l("div",_,[r("div",v,[a[4]||(a[4]=r("div",{class:"login-header"},[r("h1",{class:"login-title"},"AI智能审校系统"),r("p",{class:"login-subtitle"},"基于AI技术的智能文档审校平台")],-1)),o(y,{ref_key:"loginFormRef",ref:b,model:h,rules:V,class:"login-form",onSubmit:n(x,["prevent"])},{default:t(()=>[o(m,{prop:"username"},{default:t(()=>[o(s,{modelValue:h.username,"onUpdate:modelValue":a[0]||(a[0]=e=>h.username=e),placeholder:"请输入用户名",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),o(m,{prop:"password"},{default:t(()=>[o(s,{modelValue:h.password,"onUpdate:modelValue":a[1]||(a[1]=e=>h.password=e),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":"",onKeyup:d(x,["enter"])},null,8,["modelValue"])]),_:1}),o(m,null,{default:t(()=>[o(g,{modelValue:h.remember,"onUpdate:modelValue":a[2]||(a[2]=e=>h.remember=e)},{default:t(()=>a[3]||(a[3]=[p("记住我")])),_:1,__:[3]},8,["modelValue"])]),_:1}),o(m,null,{default:t(()=>[o(f,{type:"primary",size:"large",class:"login-btn",loading:w.value,onClick:x},{default:t(()=>[p(c(w.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),a[5]||(a[5]=r("div",{class:"login-footer"},[r("p",null,"© 2025 AI智能审校系统. All rights reserved.")],-1))])])}}}),[["__scopeId","data-v-b9179343"]]);export{b as default};
