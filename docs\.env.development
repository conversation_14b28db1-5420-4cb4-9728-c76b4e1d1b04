# 开发环境配置
NODE_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_USE_MOCK_API=true

# 应用配置
VITE_APP_TITLE=AI智能审校系统
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=基于AI的智能文档审校平台

# 功能开关
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_MOCK=true
VITE_ENABLE_CONSOLE_LOG=true

# 文件上传配置
VITE_UPLOAD_MAX_SIZE=50
VITE_UPLOAD_ALLOWED_TYPES=.doc,.docx,.pdf,.txt,.jpg,.jpeg,.png,.mp4,.mp3,.wav

# AI服务配置
VITE_AI_SERVICE_TIMEOUT=30000
VITE_AI_MAX_TEXT_LENGTH=100000

# Mock服务网络延迟配置 (毫秒)
VITE_MOCK_DELAY_MIN=100
VITE_MOCK_DELAY_MAX=2000

# Mock服务错误率配置 (0-1之间的小数)
VITE_MOCK_ERROR_RATE_NETWORK=0.02
VITE_MOCK_ERROR_RATE_SERVER=0.01
VITE_MOCK_ERROR_RATE_VALIDATION=0.05
VITE_MOCK_ERROR_RATE_TIMEOUT=0.01

# Mock服务超时配置 (毫秒)
VITE_MOCK_TIMEOUT_SHORT=5000
VITE_MOCK_TIMEOUT_MEDIUM=15000
VITE_MOCK_TIMEOUT_LONG=30000

# Mock服务分页配置
VITE_MOCK_PAGE_SIZE=20
VITE_MOCK_MAX_PAGE_SIZE=100

# Mock服务JWT配置
VITE_MOCK_ACCESS_TOKEN_EXPIRY=3600
VITE_MOCK_REFRESH_TOKEN_EXPIRY=86400
VITE_MOCK_JWT_SECRET=mock-jwt-secret-key

# Mock服务日志配置
VITE_MOCK_QUIET=false
