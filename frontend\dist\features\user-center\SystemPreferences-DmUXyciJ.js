import{d as s,c as e,a,Q as r,I as n,ag as t,o as c}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as l}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const p={class:"system-preferences"},o={class:"content"},d=l(s({__name:"SystemPreferences",setup:s=>(s,l)=>{const d=t("el-card");return c(),e("div",p,[l[1]||(l[1]=a("div",{class:"page-header"},[a("h1",null,"系统偏好"),a("p",{class:"page-description"},"配置系统界面和功能偏好")],-1)),a("div",o,[r(d,null,{default:n(()=>l[0]||(l[0]=[a("p",null,"系统偏好设置功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-18f460f3"]]);export{d as default};
