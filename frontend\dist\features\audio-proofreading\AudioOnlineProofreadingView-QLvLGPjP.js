import{a7 as e,g as a,Y as l,ab as u,ac as t,d as s,z as n,S as d,M as i}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as o,r,X as c,b as v,c as p,a as f,Q as _,H as m,K as g,I as h,ag as b,o as y,M as k,u as w,O as V}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as x}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const z={class:"audio-online-proofreading"},R={class:"content"},U={class:"toolbar"},C={class:"toolbar-left"},j={class:"toolbar-right"},L={key:0,class:"audio-container"},O=["src"],A={class:"audio-info"},M={class:"waveform-container"},P={class:"waveform-header"},S={key:0,class:"waveform-display"},B={key:1,class:"empty-audio"},I={class:"panel-header"},T={class:"header-actions"},$={class:"text-editor"},E={key:0,class:"text-stats"},H=x(o({__name:"AudioOnlineProofreadingView",setup(o){const x=r(null),H=r(),K=r("00:00"),Q=r("00:00"),W=r(!1),X=r(""),Y=r(!0),q=r(!1),D=c({language:"zh",accuracy:"standard",speakerRecognition:!1}),F=r(!1),G=v(()=>{if(!X.value)return{characters:0,words:0,lines:0};const e=X.value;return{characters:e.length,words:e.split(/\s+/).filter(e=>e.length>0).length,lines:e.split("\n").length}}),J=e=>e.type.startsWith("audio/")?e.size>209715200?(i.error("文件大小不能超过 200MB"),!1):(x.value={name:e.name,size:e.size,url:URL.createObjectURL(e),file:e},X.value="",F.value=!1,i.success("音频上传成功"),!1):(i.error("只能上传音频文件"),!1),N=()=>{if(H.value){const e=H.value.duration;K.value=ue(e)}},Z=()=>{H.value&&(Q.value=ue(H.value.currentTime))},ee=()=>{},ae=()=>{},le=async()=>{if(x.value){W.value=!0;try{await new Promise(e=>setTimeout(e,3e3)),X.value=`这是音频 ${x.value.name} 的语音转文字结果示例...\n\n[00:00:10] 说话人A：大家好，欢迎收听本期音频节目\n[00:00:15] 说话人B：今天我们来讨论AI智能审校系统的应用\n[00:00:20] 说话人A：这个系统在多媒体处理方面有什么特色？\n[00:00:25] 说话人B：主要包括图片OCR、视频字幕提取、音频转录等功能\n[00:00:30] 说话人A：听起来很实用，能详细介绍一下吗？`,i.success("语音转录完成")}catch(e){i.error("语音转录失败")}finally{W.value=!1}}else i.warning("请先上传音频")},ue=e=>{const a=Math.floor(e/60),l=Math.floor(e%60);return`${a.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}`},te=()=>{F.value=!0},se=()=>{Y.value=!Y.value},ne=async()=>{if(X.value)try{await navigator.clipboard.writeText(X.value),i.success("文字已复制到剪贴板")}catch(e){i.error("复制失败")}else i.warning("没有可复制的文字")},de=()=>{if(!X.value)return void i.warning("没有可导出的文字");const e=new Blob([X.value],{type:"text/plain"}),a=URL.createObjectURL(e),l=document.createElement("a");l.href=a,l.download=`${x.value?.name||"transcript"}_text.txt`,l.click(),URL.revokeObjectURL(a),i.success("导出成功")},ie=()=>{x.value=null,X.value="",F.value=!1,K.value="00:00",Q.value="00:00"},oe=()=>{i.success("设置已应用"),q.value=!1},re=()=>{x.value&&X.value?(i.success("保存成功"),F.value=!1):i.warning("没有可保存的内容")},ce=()=>{x.value&&X.value?i.success("审校结果已提交"):i.warning("请先完成语音转录")};return(i,o)=>{const r=b("el-icon"),c=b("el-button"),v=b("el-upload"),ue=b("el-card"),ve=b("el-col"),pe=b("el-row"),fe=b("el-input"),_e=b("el-statistic"),me=b("el-option"),ge=b("el-select"),he=b("el-form-item"),be=b("el-switch");return y(),p("div",z,[o[19]||(o[19]=f("div",{class:"page-header"},[f("h1",null,"音频在线审校"),f("p",{class:"page-description"},"在线实时进行音频语音转文字和文字校对")],-1)),f("div",R,[_(ue,{class:"toolbar-card"},{default:h(()=>[f("div",U,[f("div",C,[_(v,{action:"#","show-file-list":!1,"before-upload":J,accept:"audio/*"},{default:h(()=>[_(c,{type:"primary"},{default:h(()=>[_(r,null,{default:h(()=>[_(w(e))]),_:1}),o[4]||(o[4]=k(" 上传音频 "))]),_:1,__:[4]})]),_:1}),_(c,{onClick:le,disabled:!x.value,loading:W.value},{default:h(()=>[_(r,null,{default:h(()=>[_(w(a))]),_:1}),o[5]||(o[5]=k(" 开始转录 "))]),_:1,__:[5]},8,["disabled","loading"]),_(c,{onClick:ie},{default:h(()=>[_(r,null,{default:h(()=>[_(w(l))]),_:1}),o[6]||(o[6]=k(" 清空 "))]),_:1,__:[6]})]),f("div",j,[_(c,{onClick:re,disabled:!F.value},{default:h(()=>[_(r,null,{default:h(()=>[_(w(u))]),_:1}),o[7]||(o[7]=k(" 保存 "))]),_:1,__:[7]},8,["disabled"]),_(c,{type:"success",onClick:ce,disabled:!x.value},{default:h(()=>[_(r,null,{default:h(()=>[_(w(t))]),_:1}),o[8]||(o[8]=k(" 提交审校 "))]),_:1,__:[8]},8,["disabled"])])])]),_:1}),_(pe,{gutter:20,class:"workspace"},{default:h(()=>[_(ve,{span:12},{default:h(()=>[_(ue,{title:"音频播放器",class:"audio-panel"},{default:h(()=>[x.value?(y(),p("div",L,[f("audio",{ref_key:"audioPlayer",ref:H,src:x.value.url,controls:"",class:"audio-player",onLoadedmetadata:N,onTimeupdate:Z,onPlay:ee,onPause:ae},null,40,O),f("div",A,[_(pe,{gutter:10},{default:h(()=>[_(ve,{span:8},{default:h(()=>[f("p",null,[o[9]||(o[9]=f("strong",null,"文件名：",-1)),k(V(x.value.name),1)])]),_:1}),_(ve,{span:8},{default:h(()=>[f("p",null,[o[10]||(o[10]=f("strong",null,"时长：",-1)),k(V(K.value),1)])]),_:1}),_(ve,{span:8},{default:h(()=>[f("p",null,[o[11]||(o[11]=f("strong",null,"当前时间：",-1)),k(V(Q.value),1)])]),_:1})]),_:1})]),f("div",M,[f("div",P,[o[12]||(o[12]=f("span",null,"音频波形",-1)),_(c,{size:"small",onClick:se},{default:h(()=>[k(V(Y.value?"隐藏波形":"显示波形"),1)]),_:1})]),Y.value?(y(),p("div",S,o[13]||(o[13]=[f("div",{class:"waveform-placeholder"},[f("p",null,"音频波形显示区域（开发中）")],-1)]))):g("",!0)])])):(y(),p("div",B,[_(r,{size:"64"},{default:h(()=>[_(w(s))]),_:1}),o[14]||(o[14]=f("p",null,"请上传音频开始审校",-1))]))]),_:1})]),_:1}),_(ve,{span:12},{default:h(()=>[_(ue,{title:"转录文字编辑",class:"transcript-panel"},{header:h(()=>[f("div",I,[o[17]||(o[17]=f("span",null,"转录文字编辑",-1)),f("div",T,[_(c,{size:"small",onClick:ne,disabled:!X.value},{default:h(()=>[_(r,null,{default:h(()=>[_(w(n))]),_:1}),o[15]||(o[15]=k(" 复制 "))]),_:1,__:[15]},8,["disabled"]),_(c,{size:"small",onClick:de,disabled:!X.value},{default:h(()=>[_(r,null,{default:h(()=>[_(w(d))]),_:1}),o[16]||(o[16]=k(" 导出 "))]),_:1,__:[16]},8,["disabled"])])])]),default:h(()=>[f("div",$,[_(fe,{modelValue:X.value,"onUpdate:modelValue":o[0]||(o[0]=e=>X.value=e),type:"textarea",rows:20,placeholder:"语音转文字结果将显示在这里，您可以直接编辑...",onInput:te},null,8,["modelValue"])]),X.value?(y(),p("div",E,[_(pe,{gutter:10},{default:h(()=>[_(ve,{span:8},{default:h(()=>[_(_e,{title:"字符数",value:G.value.characters},null,8,["value"])]),_:1}),_(ve,{span:8},{default:h(()=>[_(_e,{title:"单词数",value:G.value.words},null,8,["value"])]),_:1}),_(ve,{span:8},{default:h(()=>[_(_e,{title:"行数",value:G.value.lines},null,8,["value"])]),_:1})]),_:1})])):g("",!0)]),_:1})]),_:1})]),_:1}),q.value?(y(),m(ue,{key:0,title:"转录设置",class:"settings-panel"},{default:h(()=>[_(pe,{gutter:20},{default:h(()=>[_(ve,{span:6},{default:h(()=>[_(he,{label:"识别语言"},{default:h(()=>[_(ge,{modelValue:D.language,"onUpdate:modelValue":o[1]||(o[1]=e=>D.language=e),placeholder:"选择语言"},{default:h(()=>[_(me,{label:"中文",value:"zh"}),_(me,{label:"英文",value:"en"}),_(me,{label:"中英混合",value:"zh-en"}),_(me,{label:"粤语",value:"yue"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),_(ve,{span:6},{default:h(()=>[_(he,{label:"识别精度"},{default:h(()=>[_(ge,{modelValue:D.accuracy,"onUpdate:modelValue":o[2]||(o[2]=e=>D.accuracy=e),placeholder:"选择精度"},{default:h(()=>[_(me,{label:"快速",value:"fast"}),_(me,{label:"标准",value:"standard"}),_(me,{label:"高精度",value:"high"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),_(ve,{span:6},{default:h(()=>[_(he,{label:"说话人识别"},{default:h(()=>[_(be,{modelValue:D.speakerRecognition,"onUpdate:modelValue":o[3]||(o[3]=e=>D.speakerRecognition=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),_(ve,{span:6},{default:h(()=>[_(he,null,{default:h(()=>[_(c,{type:"primary",onClick:oe},{default:h(()=>o[18]||(o[18]=[k("应用设置")])),_:1,__:[18]})]),_:1})]),_:1})]),_:1})]),_:1})):g("",!0)])])}}}),[["__scopeId","data-v-3de46de7"]]);export{H as default};
