import{d as s,c as a,a as e,Q as d,I as t,ag as l,o as c}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as n}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const o={class:"add-case-set"},p={class:"content"},r=n(s({__name:"AddCaseSet",setup:s=>(s,n)=>{const r=l("el-card");return c(),a("div",o,[n[1]||(n[1]=e("div",{class:"page-header"},[e("h1",null,"添加案例集"),e("p",{class:"page-description"},"录入新的修改案例和经验")],-1)),e("div",p,[d(r,null,{default:t(()=>n[0]||(n[0]=[e("p",null,"添加案例集功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-fd8528e6"]]);export{r as default};
