<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>批量审校API测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 3px;
      cursor: pointer;
      margin: 5px;
    }

    button:hover {
      background: #0056b3;
    }

    .result {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 3px;
      padding: 10px;
      margin: 10px 0;
      white-space: pre-wrap;
      font-family: monospace;
      max-height: 300px;
      overflow-y: auto;
    }

    .success {
      border-color: #28a745;
      background: #d4edda;
    }

    .error {
      border-color: #dc3545;
      background: #f8d7da;
    }
  </style>
</head>

<body>
  <h1>批量审校API测试页面</h1>

  <div class="test-section">
    <h3>基础API测试</h3>
    <button onclick="testBasicApi()">测试基础API</button>
    <div id="basic-result" class="result"></div>
  </div>

  <div class="test-section">
    <h3>未校对文档列表API</h3>
    <button onclick="testDocumentsApi()">测试文档列表API</button>
    <div id="documents-result" class="result"></div>
  </div>

  <div class="test-section">
    <h3>统计信息API</h3>
    <button onclick="testStatisticsApi()">测试统计信息API</button>
    <div id="statistics-result" class="result"></div>
  </div>

  <div class="test-section">
    <h3>Service Worker状态</h3>
    <button onclick="checkServiceWorker()">检查Service Worker</button>
    <div id="sw-result" class="result"></div>
  </div>

  <script>
    // 设置默认token
    const defaultToken = 'dev-mock-token-12345';

    // 简单的MSW状态检查和初始化
    async function initializeMSW () {
      try {
        console.log('🚀 检查MSW状态...')

        // 检查是否已经有MSW worker
        if (window.__mockWorker) {
          console.log('✅ MSW已经启动')
          return
        }

        // 尝试通过Service Worker检查MSW
        if ('serviceWorker' in navigator) {
          const registrations = await navigator.serviceWorker.getRegistrations()
          const mswRegistration = registrations.find(reg =>
            reg.scope.includes('/') && reg.active?.scriptURL.includes('mockServiceWorker')
          )

          if (mswRegistration) {
            console.log('✅ 发现MSW Service Worker')
            // 等待一段时间让MSW完全初始化
            await new Promise(resolve => setTimeout(resolve, 2000))
          } else {
            console.log('⚠️ 未发现MSW Service Worker，请确保在开发环境中运行')
          }
        }
      } catch (error) {
        console.error('❌ MSW初始化检查失败:', error)
      }
    }

    function log (elementId, message, isError = false) {
      const element = document.getElementById(elementId);
      element.textContent = message;
      element.className = `result ${isError ? 'error' : 'success'}`;
    }

    async function testBasicApi () {
      try {
        console.log('🧪 测试基础API...');
        const response = await fetch('/api/test', {
          headers: {
            'Authorization': `Bearer ${defaultToken}`,
            'Content-Type': 'application/json'
          }
        });

        const data = await response.json();
        log('basic-result', `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`);
        console.log('✅ 基础API测试成功:', data);
      } catch (error) {
        log('basic-result', `错误: ${error.message}`, true);
        console.error('❌ 基础API测试失败:', error);
      }
    }

    async function testDocumentsApi () {
      try {
        console.log('🧪 测试文档列表API...');

        // 检查请求前的状态
        console.log('📋 请求前状态检查:');
        console.log('  - MSW Worker:', !!window.__mockWorker);
        console.log('  - Service Worker注册数:', (await navigator.serviceWorker.getRegistrations()).length);

        const response = await fetch('/api/batch-proofreading/unproofread-documents?page=1&pageSize=10', {
          headers: {
            'Authorization': `Bearer ${defaultToken}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('📡 响应状态:', response.status, response.statusText);
        console.log('📡 响应头:', Object.fromEntries(response.headers.entries()));

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        console.log('📄 内容类型:', contentType);

        if (!contentType || !contentType.includes('application/json')) {
          const text = await response.text();
          console.error('❌ 响应不是JSON格式:', text.substring(0, 200));
          log('documents-result', `错误: 响应不是JSON格式\n内容: ${text.substring(0, 200)}...`, true);
          return;
        }

        const data = await response.json();
        log('documents-result', `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`);
        console.log('✅ 文档列表API测试成功:', data);
      } catch (error) {
        console.error('❌ 文档列表API测试失败:', error);
        log('documents-result', `错误: ${error.message}\n详情: ${error.stack || '无堆栈信息'}`, true);
      }
    }

    async function testStatisticsApi () {
      try {
        console.log('🧪 测试统计信息API...');

        const response = await fetch('/api/batch-proofreading/unproofread-documents/statistics', {
          headers: {
            'Authorization': `Bearer ${defaultToken}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('📈 统计API响应状态:', response.status, response.statusText);

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        console.log('📄 统计API内容类型:', contentType);

        if (!contentType || !contentType.includes('application/json')) {
          const text = await response.text();
          console.error('❌ 统计API响应不是JSON格式:', text.substring(0, 200));
          log('statistics-result', `错误: 响应不是JSON格式\n内容: ${text.substring(0, 200)}...`, true);
          return;
        }

        const data = await response.json();
        log('statistics-result', `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`);
        console.log('✅ 统计信息API测试成功:', data);
      } catch (error) {
        console.error('❌ 统计信息API测试失败:', error);
        log('statistics-result', `错误: ${error.message}\n详情: ${error.stack || '无堆栈信息'}`, true);
      }
    }

    async function checkServiceWorker () {
      try {
        console.log('🧪 检查Service Worker状态...');

        let result = 'Service Worker状态检查:\n\n';

        // 检查浏览器支持
        if ('serviceWorker' in navigator) {
          result += '✅ 浏览器支持Service Worker\n';

          // 检查注册状态
          const registrations = await navigator.serviceWorker.getRegistrations();
          result += `📋 已注册的Service Worker数量: ${registrations.length}\n`;

          registrations.forEach((reg, index) => {
            result += `  ${index + 1}. 作用域: ${reg.scope}\n`;
            result += `     状态: ${reg.active ? '活跃' : '非活跃'}\n`;
            if (reg.active) {
              result += `     脚本URL: ${reg.active.scriptURL}\n`;
              result += `     Service Worker状态: ${reg.active.state}\n`;
            }
          });

          // 检查MSW worker
          if (window.__mockWorker) {
            result += '\n✅ MSW Worker已加载\n';
            if (window.__mockWorker.listHandlers) {
              const handlers = window.__mockWorker.listHandlers();
              result += `📋 已注册的Handler数量: ${handlers.length}\n`;

              // 列出前几个handlers
              handlers.slice(0, 5).forEach((handler, index) => {
                const method = handler.info.method || 'ALL';
                const path = handler.info.path || handler.info.header || 'unknown';
                result += `  ${index + 1}. ${method} ${path}\n`;
              });

              if (handlers.length > 5) {
                result += `  ... 还有 ${handlers.length - 5} 个handlers\n`;
              }
            }

            // 测试MSW拦截功能
            result += '\n🔍 测试MSW拦截功能:\n';
            try {
              const testResponse = await fetch('/api/test-msw-intercept', {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${defaultToken}`,
                  'X-Test-Request': 'msw-intercept-test'
                }
              });

              const responseText = await testResponse.text();
              console.log('MSW拦截测试响应:', responseText);

              if (responseText.includes('<!DOCTYPE html>')) {
                result += '❌ MSW未拦截请求 (返回HTML页面)\n';
                result += `   响应内容: ${responseText.substring(0, 100)}...\n`;
              } else {
                result += '✅ MSW正在拦截请求\n';
                result += `   响应内容: ${responseText.substring(0, 200)}\n`;
              }
            } catch (interceptError) {
              result += `❌ MSW拦截测试失败: ${interceptError.message}\n`;
            }

          } else {
            result += '\n❌ MSW Worker未找到\n';
            result += '   这可能意味着MSW未正确初始化\n';
          }

        } else {
          result += '❌ 浏览器不支持Service Worker\n';
        }

        log('sw-result', result);
        console.log('✅ Service Worker状态检查完成');
      } catch (error) {
        log('sw-result', `错误: ${error.message}`, true);
        console.error('❌ Service Worker状态检查失败:', error);
      }
    }

    // 页面加载时先初始化MSW，然后检查状态
    window.addEventListener('load', async () => {
      console.log('🚀 批量审校API测试页面已加载');
      await initializeMSW();
      setTimeout(checkServiceWorker, 1000);
    });
  </script>
</body>

</html>
