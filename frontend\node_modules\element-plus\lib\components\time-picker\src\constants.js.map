{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/time-picker/src/constants.ts"], "sourcesContent": ["export const timeUnits = ['hours', 'minutes', 'seconds'] as const\n\nexport const PICKER_BASE_INJECTION_KEY = 'EP_PICKER_BASE'\n\nexport const PICKER_POPPER_OPTIONS_INJECTION_KEY = 'ElPopperOptions'\n\nexport const DEFAULT_FORMATS_TIME = 'HH:mm:ss'\n\nexport const DEFAULT_FORMATS_DATE = 'YYYY-MM-DD'\n\nexport const DEFAULT_FORMATS_DATEPICKER = {\n  date: DEFAULT_FORMATS_DATE,\n  dates: DEFAULT_FORMATS_DATE,\n  week: 'gggg[w]ww',\n  year: 'YYYY',\n  years: 'YYYY',\n  month: 'YYYY-MM',\n  months: 'YYYY-MM',\n  datetime: `${DEFAULT_FORMATS_DATE} ${DEFAULT_FORMATS_TIME}`,\n  monthrange: 'YYYY-MM',\n  yearrange: 'YYYY',\n  daterange: DEFAULT_FORMATS_DATE,\n  datetimerange: `${DEFAULT_FORMATS_DATE} ${DEFAULT_FORMATS_TIME}`,\n}\n\nexport type TimeUnit = typeof timeUnits[number]\n"], "names": [], "mappings": ";;;;AAAY,MAAC,SAAS,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE;AAC7C,MAAC,yBAAyB,GAAG,iBAAiB;AAC9C,MAAC,mCAAmC,GAAG,kBAAkB;AACzD,MAAC,oBAAoB,GAAG,WAAW;AACnC,MAAC,oBAAoB,GAAG,aAAa;AACrC,MAAC,0BAA0B,GAAG;AAC1C,EAAE,IAAI,EAAE,oBAAoB;AAC5B,EAAE,KAAK,EAAE,oBAAoB;AAC7B,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,QAAQ,EAAE,CAAC,EAAE,oBAAoB,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC;AAC7D,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,SAAS,EAAE,oBAAoB;AACjC,EAAE,aAAa,EAAE,CAAC,EAAE,oBAAoB,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC;AAClE;;;;;;;;;"}