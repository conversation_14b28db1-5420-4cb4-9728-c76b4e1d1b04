# AI智能审校系统环境变量配置模板
# 复制此文件为 .env 并根据实际情况修改配置

# ===========================================
# Django后端配置
# ===========================================

# 基础配置
DJANGO_ENVIRONMENT=development
DEBUG=True
SECRET_KEY=your-secret-key-here-change-in-production

# 允许的主机（生产环境必须设置）
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 双数据库配置
# 远程MySQL数据库配置 (用户管理、系统设置)
REMOTE_DB_ENGINE=django.db.backends.mysql
REMOTE_DB_NAME=book-proofread
REMOTE_DB_USER=book-proofread
REMOTE_DB_PASSWORD=AdwpJ364J2FcsBP6
REMOTE_DB_HOST=***********
REMOTE_DB_PORT=3306

# 本地SQLite数据库配置 (图书数据)
LOCAL_DB_ENGINE=django.db.backends.sqlite3
LOCAL_DB_NAME=book.db

# 兼容性配置 (向后兼容)
DB_ENGINE=django.db.backends.mysql
DB_NAME=book-proofread
DB_USER=book-proofread
DB_PASSWORD=AdwpJ364J2FcsBP6
DB_HOST=***********
DB_PORT=3306

# Redis配置
REDIS_URL=redis://localhost:6379/0

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1

# CORS配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CSRF_TRUSTED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# 文件存储配置
USE_S3=False
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1

# 错误监控配置
SENTRY_DSN=your-sentry-dsn-here

# AI服务配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_API_BASE=https://api.openai.com/v1
HUGGINGFACE_API_KEY=your-huggingface-api-key

# ===========================================
# Vue3前端配置
# ===========================================

# 应用基础配置
VITE_APP_TITLE=ProofreadAI
VITE_APP_DESCRIPTION=AI智能审校系统
VITE_APP_VERSION=1.0.0

# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=10000

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_ANALYTICS=false

# 第三方服务配置
VITE_SENTRY_DSN=your-frontend-sentry-dsn
VITE_GOOGLE_ANALYTICS_ID=your-ga-id

# 开发配置
VITE_DROP_CONSOLE=false
VITE_SOURCE_MAP=true

# ===========================================
# Docker配置
# ===========================================

# 容器端口配置
DJANGO_PORT=8000
VUE_PORT=3000
MYSQL_PORT=3306
REDIS_PORT=6379

# MySQL Docker配置
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DATABASE=proofread_ai
MYSQL_USER=proofread_user
MYSQL_PASSWORD=proofread_password

# Redis Docker配置
REDIS_PASSWORD=redis_password

# ===========================================
# 生产环境额外配置
# ===========================================

# 安全配置（仅生产环境）
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# 性能配置
GUNICORN_WORKERS=4
GUNICORN_TIMEOUT=30
GUNICORN_KEEPALIVE=2

# 监控配置
ENABLE_HEALTH_CHECK=True
ENABLE_METRICS=True

# 备份配置
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ===========================================
# 开发工具配置
# ===========================================

# 调试工具
ENABLE_DEBUG_TOOLBAR=True
ENABLE_SILK_PROFILER=True

# 测试配置
TEST_DATABASE_NAME=test_proofread_ai
COVERAGE_THRESHOLD=90

# 代码质量
ENABLE_LINTING=True
ENABLE_TYPE_CHECKING=True

# ===========================================
# 注意事项
# ===========================================

# 1. 生产环境必须修改所有密钥和密码
# 2. 确保数据库和Redis服务正常运行
# 3. 根据实际部署环境调整主机和端口配置
# 4. 生产环境建议使用环境变量而非.env文件
# 5. 定期更新和轮换密钥
