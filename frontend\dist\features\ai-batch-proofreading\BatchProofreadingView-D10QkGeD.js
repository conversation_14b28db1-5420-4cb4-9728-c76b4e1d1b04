import{d as e,r as a,b as t,c as s,o as l,a as i,K as n,D as o,Q as r,ag as u,I as d,u as c,O as p,P as v,a6 as m,M as g,H as f,L as h,p as b,m as y,aC as w,S as _,J as k,R as x}from"../../chunks/vue-vendor-BCsylZgc.js";import{v as C}from"../../chunks/utils-vendor-DYQz1-BF.js";import{Q as T,M as S,Y as D,d as F,Z as M,_ as P,$ as z,X as A,S as R,C as U,p as E,a0 as O,a1 as I,N as V,b as B,x as $,R as L,T as j,a2 as N,g as H,a3 as G,F as W,a4 as q,a5 as K,a6 as J,v as Q,a7 as Y,a8 as Z}from"../../chunks/ui-vendor-DZ6owSRu.js";import{_ as X}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import{M as ee}from"../../chunks/ModuleAPI-p-N7PV56.js";import"../../chunks/ai-engine-storage-DTARYHrG.js";import"../../chunks/ai-engine-core-wyUSRaHZ.js";const ae={class:"file-upload-area"},te=["accept"],se={class:"upload-content"},le={class:"upload-text"},ie={class:"upload-title"},ne={class:"upload-hint"},oe={key:0,class:"file-preview-list"},re={class:"preview-title"},ue={class:"file-items"},de={class:"file-icon"},ce={class:"file-info"},pe=["title"],ve={class:"file-meta"},me={key:0},ge={class:"file-actions"},fe={key:1,class:"batch-actions"},he=X(e({__name:"FileUploadArea",props:{files:{},maxFiles:{default:10},maxFileSize:{default:52428800},acceptedTypes:{default:()=>[".pdf",".doc",".docx",".txt"]},disabled:{type:Boolean,default:!1},showTips:{type:Boolean,default:!0}},emits:["update:files","file-added","file-removed","files-cleared"],setup(e,{emit:b}){const y=e,w=b,_=a(),k=a(!1),x=t(()=>y.files.some(e=>"uploading"===e.status)),C=e=>{e.preventDefault(),e.stopPropagation()},M=e=>{e.preventDefault(),e.stopPropagation(),!y.disabled&&y.files.length<y.maxFiles&&(k.value=!0)},P=e=>{e.preventDefault(),e.stopPropagation(),e.currentTarget?.contains(e.relatedTarget)||(k.value=!1)},z=e=>{if(e.preventDefault(),e.stopPropagation(),k.value=!1,y.disabled||y.files.length>=y.maxFiles)return;const a=Array.from(e.dataTransfer?.files||[]);U(a)},A=()=>{!y.disabled&&y.files.length<y.maxFiles&&_.value?.click()},R=e=>{const a=e.target,t=Array.from(a.files||[]);U(t),a.value=""},U=e=>{const a=[],t=y.maxFiles-y.files.length;for(let s=0;s<Math.min(e.length,t);s++){const t=e[s],l=E(t);if(l.valid){const e={id:O(),name:t.name,size:t.size,type:t.type,extension:I(t.name),file:t,status:"pending",estimatedChars:B(t)};a.push(e)}else S.error(l.message)}if(a.length>0){const e=[...y.files,...a];w("update:files",e),a.forEach(e=>{w("file-added",e)})}},E=e=>{if(e.size>y.maxFileSize)return{valid:!1,message:`文件 "${e.name}" 大小超过限制`};const a=I(e.name);return y.acceptedTypes.includes(a)?{valid:!0}:{valid:!1,message:`文件 "${e.name}" 类型不支持`}},O=()=>`file-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,I=e=>"."+e.split(".").pop()?.toLowerCase()||"",V=e=>{if(0===e)return"0 B";const a=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB"][a]},B=e=>Math.round(.5*e.size),$=()=>{x.value?S.warning("有文件正在上传中，无法清空"):(w("update:files",[]),w("files-cleared"),S.success("已清空所有文件"))};return(e,a)=>{const t=u("el-icon"),b=u("el-button");return l(),s("div",ae,[i("div",{class:o(["upload-dragger",{"is-dragover":k.value,"is-disabled":e.disabled||e.files.length>=e.maxFiles}]),onDrop:z,onDragover:C,onDragenter:M,onDragleave:P,onClick:A},[i("input",{ref_key:"fileInputRef",ref:_,type:"file",multiple:"",accept:e.acceptedTypes.join(","),onChange:R,style:{display:"none"}},null,40,te),i("div",se,[r(t,{class:"upload-icon",size:"48"},{default:d(()=>[r(c(T))]),_:1}),i("div",le,[i("p",ie,p(e.files.length>=e.maxFiles?"已达到文件数量上限":"点击或拖拽文件到此区域上传"),1),i("p",ne," 支持 "+p(e.acceptedTypes.join(", "))+" 格式，单个文件不超过 "+p(V(e.maxFileSize))+"， 最多上传 "+p(e.maxFiles)+" 个文件 ",1)])])],34),e.files.length>0?(l(),s("div",oe,[i("h4",re,"已选择文件 ("+p(e.files.length)+"/"+p(e.maxFiles)+")",1),i("div",ue,[(l(!0),s(v,null,m(e.files,e=>{return l(),s("div",{key:e.id,class:o(["file-item",{"is-error":"error"===e.status}])},[i("div",de,[r(t,{size:"24",color:(e.extension,"#409eff")},{default:d(()=>[(l(),f(h((e.extension,F))))]),_:2},1032,["color"])]),i("div",ce,[i("div",{class:"file-name",title:e.name},p(e.name),9,pe),i("div",ve,[i("span",null,p(V(e.size)),1),e.estimatedChars?(l(),s("span",me," · "+p(e.estimatedChars.toLocaleString())+" 字 ",1)):n("",!0),i("span",{class:o(["file-status",`status-${e.status}`])}," · "+p((a=e.status,{pending:"等待上传",uploading:"上传中",success:"上传成功",error:"上传失败"}[a]||a)),3)])]),i("div",ge,[r(b,{text:"",type:"danger",size:"small",onClick:a=>(e=>{const a=y.files.filter(a=>a.id!==e);w("update:files",a),w("file-removed",e)})(e.id),disabled:"uploading"===e.status},{default:d(()=>[r(t,null,{default:d(()=>[r(c(D))]),_:1})]),_:2},1032,["onClick","disabled"])])],2);var a}),128))])])):n("",!0),e.files.length>1?(l(),s("div",fe,[r(b,{size:"small",onClick:$,disabled:x.value},{default:d(()=>a[0]||(a[0]=[g(" 清空所有 ")])),_:1,__:[0]},8,["disabled"])])):n("",!0)])}}}),[["__scopeId","data-v-532f3d20"]]),be={key:0,class:"file-details"},ye={class:"card-header"},we={class:"file-name-info"},_e={key:0},ke={key:1,class:"text-muted"},xe={class:"card-header"},Ce={class:"progress-content"},Te={class:"progress-text"},Se={class:"progress-info"},De={class:"card-header"},Fe={class:"card-header"},Me={class:"result-content"},Pe={class:"result-stats"},ze={class:"stat-item"},Ae={class:"stat-value"},Re={class:"stat-item"},Ue={class:"stat-value"},Ee={class:"stat-item"},Oe={class:"stat-value"},Ie={class:"result-preview"},Ve={class:"suggestions-list"},Be={class:"suggestion-header"},$e={class:"suggestion-position"},Le={class:"suggestion-content"},je={class:"original-text"},Ne={class:"text"},He={class:"suggested-text"},Ge={class:"text"},We={key:0,class:"suggestion-reason"},qe={class:"text"},Ke={class:"dialog-footer"},Je=X(e({__name:"FileDetailsDialog",props:{visible:{type:Boolean},file:{}},emits:["update:visible","retry","download","remove"],setup(e,{emit:t}){const o=e,y=t,w=a(!1),_=a("suggestions");b(()=>o.visible,e=>{w.value=e}),b(w,e=>{y("update:visible",e)});const k=e=>{if(0===e)return"0 B";const a=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB"][a]},x=e=>{const a=e.toLowerCase();return[".jpg",".jpeg",".png",".gif",".bmp",".webp"].includes(a)?E:[".mp4",".avi",".mov",".wmv",".flv"].includes(a)?O:([".mp3",".wav",".flac",".aac"].includes(a),F)},C=e=>({pending:B,uploading:P,processing:P,completed:V,error:I}[e]||B),T=e=>({pending:"等待处理",uploading:"上传中",processing:"处理中",completed:"已完成",error:"处理失败"}[e]||e),S=()=>{w.value=!1},$=()=>{o.file&&y("retry",o.file.id)},L=()=>{o.file&&y("download",o.file)},j=()=>{o.file&&y("remove",o.file.id)};return(e,a)=>{const t=u("el-icon"),b=u("el-descriptions-item"),y=u("el-tag"),F=u("el-descriptions"),E=u("el-card"),O=u("el-progress"),I=u("el-alert"),V=u("el-button"),B=u("el-tab-pane"),N=u("el-input"),H=u("el-tabs"),G=u("el-dialog");return l(),f(G,{modelValue:w.value,"onUpdate:modelValue":a[1]||(a[1]=e=>w.value=e),title:"文件详情",width:"800px","before-close":S,"destroy-on-close":""},{footer:d(()=>[i("div",Ke,[r(V,{onClick:S},{default:d(()=>a[14]||(a[14]=[g("关闭")])),_:1,__:[14]}),"error"===e.file?.status?(l(),f(V,{key:0,type:"warning",onClick:$},{default:d(()=>[r(t,null,{default:d(()=>[r(c(U))]),_:1}),a[15]||(a[15]=g(" 重试 "))]),_:1,__:[15]})):n("",!0),"completed"===e.file?.status?(l(),f(V,{key:1,type:"success",onClick:L},{default:d(()=>[r(t,null,{default:d(()=>[r(c(R))]),_:1}),a[16]||(a[16]=g(" 下载结果 "))]),_:1,__:[16]})):n("",!0),r(V,{type:"danger",onClick:j,disabled:"processing"===e.file?.status},{default:d(()=>[r(t,null,{default:d(()=>[r(c(D))]),_:1}),a[17]||(a[17]=g(" 删除文件 "))]),_:1,__:[17]},8,["disabled"])])]),default:d(()=>[e.file?(l(),s("div",be,[r(E,{class:"info-card",shadow:"never"},{header:d(()=>[i("div",ye,[r(t,null,{default:d(()=>[r(c(M))]),_:1}),a[2]||(a[2]=i("span",null,"基本信息",-1))])]),default:d(()=>[r(F,{column:2,border:""},{default:d(()=>[r(b,{label:"文件名"},{default:d(()=>[i("div",we,[r(t,{color:(e.file.extension,"#409eff")},{default:d(()=>[(l(),f(h(x(e.file.extension))))]),_:1},8,["color"]),i("span",null,p(e.file.name),1)])]),_:1}),r(b,{label:"文件大小"},{default:d(()=>[g(p(k(e.file.size)),1)]),_:1}),r(b,{label:"文件类型"},{default:d(()=>[g(p(e.file.type||e.file.extension),1)]),_:1}),r(b,{label:"字符数"},{default:d(()=>[e.file.estimatedChars?(l(),s("span",_e,p(e.file.estimatedChars.toLocaleString())+" 字 ",1)):(l(),s("span",ke,"未统计"))]),_:1}),r(b,{label:"上传时间"},{default:d(()=>{return[g(p((a=e.file.uploadTime,new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(new Date(a)))),1)];var a}),_:1}),r(b,{label:"当前状态"},{default:d(()=>{return[r(y,{type:(a=e.file.status,{pending:"info",uploading:"warning",processing:"warning",completed:"success",error:"danger"}[a]||"info")},{default:d(()=>[r(t,null,{default:d(()=>[(l(),f(h(C(e.file.status))))]),_:1}),g(" "+p(T(e.file.status)),1)]),_:1},8,["type"])];var a}),_:1})]),_:1})]),_:1}),"processing"===e.file.status||"uploading"===e.file.status?(l(),f(E,{key:0,class:"progress-card",shadow:"never"},{header:d(()=>[i("div",xe,[r(t,null,{default:d(()=>[r(c(P))]),_:1}),a[3]||(a[3]=i("span",null,"处理进度",-1))])]),default:d(()=>[i("div",Ce,[r(O,{percentage:o.file&&o.file.progress||0,"stroke-width":12,status:o.file?"error"===o.file.status?"exception":"completed"===o.file.status?"success":"":""},{default:d(({percentage:e})=>[i("span",Te,p(e)+"%",1)]),_:1},8,["percentage","status"]),i("div",Se,[i("span",null,p(o.file?"uploading"===o.file.status?"正在上传文件...":"processing"===o.file.status?"正在处理文件...":"":""),1)])])]),_:1})):n("",!0),"error"===e.file.status&&e.file.error?(l(),f(E,{key:1,class:"error-card",shadow:"never"},{header:d(()=>[i("div",De,[r(t,null,{default:d(()=>[r(c(z))]),_:1}),a[4]||(a[4]=i("span",null,"错误信息",-1))])]),default:d(()=>[r(I,{title:e.file.error,type:"error",closable:!1,"show-icon":""},{default:d(()=>a[5]||(a[5]=[i("div",{class:"error-details"},[i("p",null,"处理失败，请检查文件格式或网络连接后重试。"),i("p",null,"如果问题持续存在，请联系技术支持。")],-1)])),_:1},8,["title"])]),_:1})):n("",!0),"completed"===e.file.status&&e.file.result?(l(),f(E,{key:2,class:"result-card",shadow:"never"},{header:d(()=>[i("div",Fe,[r(t,null,{default:d(()=>[r(c(A))]),_:1}),a[7]||(a[7]=i("span",null,"处理结果",-1)),r(V,{size:"small",type:"primary",onClick:L,class:"download-btn"},{default:d(()=>[r(t,null,{default:d(()=>[r(c(R))]),_:1}),a[6]||(a[6]=g(" 下载结果 "))]),_:1,__:[6]})])]),default:d(()=>[i("div",Me,[i("div",Pe,[i("div",ze,[a[8]||(a[8]=i("span",{class:"stat-label"},"处理时间:",-1)),i("span",Ae,p(o.file?.result?.processingTime?`${o.file.result.processingTime}秒`:"未知"),1)]),i("div",Re,[a[9]||(a[9]=i("span",{class:"stat-label"},"修改建议:",-1)),i("span",Ue,p(o.file?.result?.suggestions?o.file.result.suggestions.length:0)+" 处",1)]),i("div",Ee,[a[10]||(a[10]=i("span",{class:"stat-label"},"质量评分:",-1)),i("span",Oe,p(o.file?.result?.qualityScore?`${o.file.result.qualityScore}/100`:"未评分"),1)])]),i("div",Ie,[r(H,{modelValue:_.value,"onUpdate:modelValue":a[0]||(a[0]=e=>_.value=e)},{default:d(()=>[r(B,{label:"修改建议",name:"suggestions"},{default:d(()=>[i("div",Ve,[(l(!0),s(v,null,m(o.file?.result?.suggestions?o.file.result.suggestions:[],(e,t)=>{return l(),s("div",{key:t,class:"suggestion-item"},[i("div",Be,[r(y,{size:"small",type:(o=e.type,{grammar:"warning",spelling:"danger",style:"info",punctuation:"warning"}[o]||"info")},{default:d(()=>[g(p(e.type),1)]),_:2},1032,["type"]),i("span",$e,"第 "+p(e.position)+" 行",1)]),i("div",Le,[i("div",je,[a[11]||(a[11]=i("span",{class:"label"},"原文:",-1)),i("span",Ne,p(e.original),1)]),i("div",He,[a[12]||(a[12]=i("span",{class:"label"},"建议:",-1)),i("span",Ge,p(e.suggested),1)]),e.reason?(l(),s("div",We,[a[13]||(a[13]=i("span",{class:"label"},"理由:",-1)),i("span",qe,p(e.reason),1)])):n("",!0)])]);var o}),128))])]),_:1}),r(B,{label:"原始数据",name:"raw"},{default:d(()=>[r(N,{type:"textarea","model-value":JSON.stringify(e.file.result,null,2),rows:10,readonly:"",class:"result-json"},null,8,["model-value"])]),_:1})]),_:1},8,["modelValue"])])])]),_:1})):n("",!0)])):n("",!0)]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-2b48b6fd"]]),Qe={class:"file-manager"},Ye={class:"toolbar"},Ze={class:"search-box"},Xe={class:"filters"},ea={key:0,class:"batch-operations"},aa={class:"file-table-container"},ta={class:"status-indicator"},sa={class:"file-name-cell"},la={class:"file-name"},ia={key:0},na={key:1,class:"text-muted"},oa={class:"progress-cell"},ra={key:1,class:"progress-text"},ua={class:"action-buttons"},da={key:0,class:"statistics"},ca={class:"stat-item"},pa={class:"stat-value"},va={class:"stat-item"},ma={class:"stat-value"},ga={class:"stat-item"},fa={class:"stat-value"},ha={class:"stat-item"},ba={class:"stat-value"},ya=X(e({__name:"FileManager",props:{files:{},processingStatus:{}},emits:["file-remove","file-retry","batch-operation"],setup(e,{emit:v}){const m=e,y=v,w=a(),_=a(""),k=a(""),x=a([]),C=a({prop:"uploadTime",order:"descending"}),T=a(!1),M=a(null),z=t(()=>{let e=[...m.files];if(_.value){const a=_.value.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(a))}return k.value&&(e=e.filter(e=>e.status===k.value)),C.value.prop&&e.sort((e,a)=>{const t=C.value.prop,s="ascending"===C.value.order?1:-1;let l=e[t],i=a[t];return"uploadTime"===t&&(l=new Date(l).getTime(),i=new Date(i).getTime()),l<i?-1*s:l>i?1*s:0}),e}),A=t(()=>m.files.filter(e=>"completed"===e.status).length),H=t(()=>m.files.filter(e=>"processing"===e.status).length),G=t(()=>m.files.filter(e=>"error"===e.status).length),W=t(()=>x.value.some(e=>"error"===e.status)),q=t(()=>x.value.some(e=>"completed"===e.status)),K=e=>{if(0===e)return"0 B";const a=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB"][a]},J=e=>{const a=e.toLowerCase();return[".jpg",".jpeg",".png",".gif",".bmp",".webp"].includes(a)?E:[".mp4",".avi",".mov",".wmv",".flv"].includes(a)?O:([".mp3",".wav",".flac",".aac"].includes(a),F)},Q=e=>{const a=e.toLowerCase();return[".jpg",".jpeg",".png",".gif",".bmp",".webp"].includes(a)?"#67c23a":[".mp4",".avi",".mov",".wmv",".flv"].includes(a)?"#409eff":[".mp3",".wav",".flac",".aac"].includes(a)?"#e6a23c":"#909399"},Y=e=>({pending:B,uploading:P,processing:P,completed:V,error:I,cancelled:N}[e]||B),Z=e=>({pending:"待处理",uploading:"上传中",processing:"处理中",completed:"已完成",error:"失败",cancelled:"已取消"}[e]||"未知"),X=e=>{if("uploading"===e.status)return e.uploadProgress||0;if("processing"===e.status){const a=m.processingStatus[e.id];return a?.progress||0}return 0},ee=()=>{},ae=()=>{},te=e=>{x.value=e},se=({prop:e,order:a})=>{C.value={prop:e,order:a}},le=e=>{y("file-retry",e)},ie=e=>{if(!e.result)return void S.warning("文件处理结果不存在");const a=new Blob([JSON.stringify(e.result,null,2)],{type:"application/json"}),t=URL.createObjectURL(a),s=document.createElement("a");s.href=t,s.download=`${e.name}-审校结果.json`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(t),S.success("结果下载成功")},ne=async e=>{try{await L.confirm("确认删除此文件？删除后无法恢复。","确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}),y("file-remove",e)}catch{}},oe=()=>{const e=x.value.filter(e=>"error"===e.status);if(0===e.length)return void S.warning("没有可重试的文件");const a=e.map(e=>e.id);y("batch-operation","retry",a)},re=()=>{const e=x.value.filter(e=>"completed"===e.status);if(0===e.length)return void S.warning("没有可导出的文件");const a=e.map(e=>e.id);y("batch-operation","export",a)},ue=async()=>{if(0!==x.value.length)try{await L.confirm(`确认删除选中的 ${x.value.length} 个文件？删除后无法恢复。`,"确认批量删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"});const e=x.value.map(e=>e.id);y("batch-operation","remove",e),x.value=[]}catch{}else S.warning("请先选择要删除的文件")};return b(()=>m.files.length,()=>{x.value=[]}),(e,a)=>{const t=u("el-input"),v=u("el-option"),m=u("el-select"),b=u("el-icon"),y=u("el-button"),C=u("el-button-group"),S=u("el-table-column"),F=u("el-progress"),P=u("el-table");return l(),s("div",Qe,[i("div",Ye,[i("div",Ze,[r(t,{modelValue:_.value,"onUpdate:modelValue":a[0]||(a[0]=e=>_.value=e),placeholder:"搜索文件名...","prefix-icon":c($),size:"small",clearable:"",onInput:ee},null,8,["modelValue","prefix-icon"])]),i("div",Xe,[r(m,{modelValue:k.value,"onUpdate:modelValue":a[1]||(a[1]=e=>k.value=e),placeholder:"状态筛选",size:"small",clearable:"",onChange:ae},{default:d(()=>[r(v,{label:"全部",value:""}),r(v,{label:"待处理",value:"pending"}),r(v,{label:"处理中",value:"processing"}),r(v,{label:"已完成",value:"completed"}),r(v,{label:"失败",value:"error"})]),_:1},8,["modelValue"])]),x.value.length>0?(l(),s("div",ea,[r(C,{size:"small"},{default:d(()=>[r(y,{onClick:oe,disabled:!W.value},{default:d(()=>[r(b,null,{default:d(()=>[r(c(U))]),_:1}),g(" 重试 ("+p(x.value.length)+") ",1)]),_:1},8,["disabled"]),r(y,{onClick:re,disabled:!q.value},{default:d(()=>[r(b,null,{default:d(()=>[r(c(R))]),_:1}),g(" 导出 ("+p(x.value.length)+") ",1)]),_:1},8,["disabled"]),r(y,{type:"danger",onClick:ue},{default:d(()=>[r(b,null,{default:d(()=>[r(c(D))]),_:1}),g(" 删除 ("+p(x.value.length)+") ",1)]),_:1})]),_:1})])):n("",!0)]),i("div",aa,[r(P,{ref_key:"tableRef",ref:w,data:z.value,onSelectionChange:te,onSortChange:se,stripe:"",size:"small",height:"100%","empty-text":"暂无文件"},{default:d(()=>[r(S,{type:"selection",width:"50"}),r(S,{label:"状态",width:"80",align:"center"},{default:d(({row:e})=>{return[i("div",ta,[r(b,{size:16,color:(a=e.status,{pending:"#909399",uploading:"#409eff",processing:"#e6a23c",completed:"#67c23a",error:"#f56c6c",cancelled:"#909399"}[a]||"#909399"),class:o(`status-${e.status}`)},{default:d(()=>[(l(),f(h(Y(e.status))))]),_:2},1032,["color","class"])])];var a}),_:1}),r(S,{label:"文件名",prop:"name",sortable:"custom","min-width":"200","show-overflow-tooltip":""},{default:d(({row:e})=>[i("div",sa,[r(b,{size:16,color:Q(e.extension)},{default:d(()=>[(l(),f(h(J(e.extension))))]),_:2},1032,["color"]),i("span",la,p(e.name),1)])]),_:1}),r(S,{label:"大小",prop:"size",sortable:"custom",width:"100",align:"right"},{default:d(({row:e})=>[g(p(K(e.size)),1)]),_:1}),r(S,{label:"字符数",prop:"estimatedChars",sortable:"custom",width:"100",align:"right"},{default:d(({row:e})=>[e.estimatedChars?(l(),s("span",ia,p(e.estimatedChars.toLocaleString()),1)):(l(),s("span",na,"--"))]),_:1}),r(S,{label:"进度",width:"120"},{default:d(({row:e})=>{return[i("div",oa,["processing"===e.status||"uploading"===e.status?(l(),f(F,{key:0,percentage:X(e),"stroke-width":6,"show-text":!1,status:(a=e.status,"processing"===a||"uploading"===a?"active":"normal")},null,8,["percentage","status"])):(l(),s("span",ra,p(Z(e.status)),1))])];var a}),_:1}),r(S,{label:"上传时间",prop:"uploadTime",sortable:"custom",width:"140"},{default:d(({row:e})=>{return[g(p((a=e.uploadTime,new Intl.DateTimeFormat("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}).format(new Date(a)))),1)];var a}),_:1}),r(S,{label:"操作",width:"120",fixed:"right"},{default:d(({row:e})=>[i("div",ua,[r(y,{text:"",size:"small",onClick:a=>{return t=e,M.value=t,void(T.value=!0);var t},title:"查看详情"},{default:d(()=>[r(b,null,{default:d(()=>[r(c(j))]),_:1})]),_:2},1032,["onClick"]),"error"===e.status?(l(),f(y,{key:0,text:"",size:"small",type:"warning",onClick:a=>le(e.id),title:"重试"},{default:d(()=>[r(b,null,{default:d(()=>[r(c(U))]),_:1})]),_:2},1032,["onClick"])):n("",!0),"completed"===e.status?(l(),f(y,{key:1,text:"",size:"small",type:"success",onClick:a=>ie(e),title:"下载结果"},{default:d(()=>[r(b,null,{default:d(()=>[r(c(R))]),_:1})]),_:2},1032,["onClick"])):n("",!0),r(y,{text:"",size:"small",type:"danger",onClick:a=>ne(e.id),disabled:"processing"===e.status,title:"删除"},{default:d(()=>[r(b,null,{default:d(()=>[r(c(D))]),_:1})]),_:2},1032,["onClick","disabled"])])]),_:1})]),_:1},8,["data"])]),e.files.length>0?(l(),s("div",da,[i("div",ca,[a[3]||(a[3]=i("span",{class:"stat-label"},"总计:",-1)),i("span",pa,p(e.files.length)+" 个文件",1)]),i("div",va,[a[4]||(a[4]=i("span",{class:"stat-label"},"已完成:",-1)),i("span",ma,p(A.value)+" 个",1)]),i("div",ga,[a[5]||(a[5]=i("span",{class:"stat-label"},"处理中:",-1)),i("span",fa,p(H.value)+" 个",1)]),i("div",ha,[a[6]||(a[6]=i("span",{class:"stat-label"},"失败:",-1)),i("span",ba,p(G.value)+" 个",1)])])):n("",!0),r(Je,{visible:T.value,"onUpdate:visible":a[2]||(a[2]=e=>T.value=e),file:M.value,onRetry:le,onDownload:ie,onRemove:ne},null,8,["visible","file"])])}}}),[["__scopeId","data-v-75e7b7c2"]]),wa={class:"config-panel"},_a={class:"card-header"},ka={class:"config-section"},xa={class:"model-option"},Ca={class:"model-info"},Ta={class:"model-name"},Sa={class:"model-meta"},Da={class:"model-price"},Fa={class:"model-provider"},Ma={key:0,class:"model-details"},Pa={key:1,class:"model-params"},za={class:"card-header"},Aa={class:"config-section"},Ra={class:"template-option"},Ua={class:"template-info"},Ea={class:"template-name"},Oa={class:"template-description"},Ia={key:0,class:"template-editor"},Va={key:0,class:"variable-preview"},Ba={class:"variables-list"},$a={class:"card-header"},La={class:"config-section"},ja={class:"card-header"},Na={class:"config-section"},Ha={key:0,class:"budget-warning"},Ga=X(e({__name:"ConfigPanel",props:{aiModel:{},promptTemplate:{},processingOptions:{},budgetLimit:{}},emits:["update:ai-model","update:prompt-template","update:processing-options","update:budget-limit","cost-estimation","config-change"],setup(e,{emit:o}){const h=e,w=o,_=a(""),k=a(""),x=a(""),C=a(""),T=a({temperature:.7,maxTokens:2e3}),D=a([{id:"deepseek-chat",name:"DeepSeek Chat",provider:"DeepSeek",description:"高性能的中文对话模型，适合文本审校任务",pricePerThousand:.14,maxTokens:4e3,available:!0},{id:"wenxin-4",name:"文心一言 4.0",provider:"百度",description:"百度自研的大语言模型，中文理解能力强",pricePerThousand:.12,maxTokens:8e3,available:!0},{id:"doubao-pro",name:"豆包 Pro",provider:"字节跳动",description:"字节跳动的AI助手，擅长内容创作和审校",pricePerThousand:.1,maxTokens:4e3,available:!0},{id:"qwen-max",name:"通义千问 Max",provider:"阿里云",description:"阿里云的大语言模型，支持多种任务类型",pricePerThousand:.16,maxTokens:6e3,available:!0}]),F=a([{id:"content-review",name:"内容预审"},{id:"batch-proofreading",name:"批量审校"},{id:"online-proofreading",name:"在线审校"},{id:"multimedia-proofreading",name:"多媒体审校"},{id:"professional-typesetting",name:"专业排版"},{id:"professional-query",name:"专业查询"},{id:"document-library",name:"文档库管理"},{id:"modification-accumulation",name:"修改积累"}]),M=a([{id:"batch-basic",name:"基础批量审校",category:"batch-proofreading",description:"适用于一般文档的批量审校",content:"请对以下文本进行审校，重点检查语法、用词、标点符号等问题：\n\n{{content}}",version:"1.0",variables:[{name:"{{content}}",description:"待审校的文本内容"},{name:"{{filename}}",description:"文件名称"}]},{id:"batch-academic",name:"学术论文审校",category:"batch-proofreading",description:"专门用于学术论文的审校",content:"请对以下学术文本进行专业审校，注意学术用词的准确性和表达的严谨性：\n\n{{content}}",version:"1.1",variables:[{name:"{{content}}",description:"待审校的文本内容"},{name:"{{subject}}",description:"学科领域"}]}]),P=a([{name:"{{content}}",description:"待审校的文本内容"},{name:"{{filename}}",description:"文件名称"},{name:"{{filesize}}",description:"文件大小"},{name:"{{timestamp}}",description:"处理时间戳"}]),z=t(()=>D.value.find(e=>e.id===_.value)||null),A=t(()=>M.value.find(e=>e.id===k.value)||null),R=t(()=>x.value?M.value.filter(e=>e.category===x.value):M.value),U=t(()=>!1),E=()=>{w("update:ai-model",z.value),w("config-change")},O=()=>{k.value="",w("update:prompt-template",null),w("config-change")},I=()=>{A.value&&(C.value=A.value.content,P.value=A.value.variables||[]),w("update:prompt-template",A.value),w("config-change")},V=()=>{if(A.value){const e={...A.value,content:C.value};w("update:prompt-template",e),w("config-change")}},B=e=>{const a={...h.processingOptions,concurrency:e};w("update:processing-options",a),w("config-change")},$=e=>{const a={...h.processingOptions,retryAttempts:e};w("update:processing-options",a),w("config-change")},L=e=>{const a={...h.processingOptions,retryDelay:e};w("update:processing-options",a),w("config-change")},j=e=>{const a={...h.processingOptions,chunkStrategy:e};w("update:processing-options",a),w("config-change")},N=e=>{const a={...h.processingOptions,exportFormat:e};w("update:processing-options",a),w("config-change")},K=e=>{w("update:budget-limit",e),w("config-change")};return b(()=>h.aiModel,e=>{e&&e.id!==_.value&&(_.value=e.id)}),b(()=>h.promptTemplate,e=>{e&&e.id!==k.value&&(k.value=e.id,x.value=e.category,C.value=e.content)}),y(()=>{(()=>{try{const e=localStorage.getItem("batch-proofreading-config");if(e){const a=JSON.parse(e);a.model&&(_.value=a.model.id),a.template&&(k.value=a.template.id,x.value=a.template.category,C.value=a.templateContent||a.template.content),a.modelParams&&(T.value={...T.value,...a.modelParams}),S.success("配置已加载")}}catch(e){S.error("配置加载失败")}})()}),(e,a)=>{const t=u("el-icon"),o=u("el-tag"),h=u("el-option"),b=u("el-select"),y=u("el-form-item"),w=u("el-descriptions-item"),S=u("el-descriptions"),M=u("el-slider"),J=u("el-input-number"),Q=u("el-card"),Y=u("el-input"),Z=u("el-radio"),X=u("el-radio-group"),ee=u("el-alert");return l(),s("div",wa,[r(Q,{class:"config-card",shadow:"never"},{header:d(()=>[i("div",_a,[r(t,null,{default:d(()=>[r(c(H))]),_:1}),a[6]||(a[6]=i("span",null,"AI模型配置",-1))])]),default:d(()=>[i("div",ka,[r(y,{label:"选择模型"},{default:d(()=>[r(b,{modelValue:_.value,"onUpdate:modelValue":a[0]||(a[0]=e=>_.value=e),placeholder:"请选择AI模型",onChange:E,style:{width:"100%"}},{default:d(()=>[(l(!0),s(v,null,m(D.value,e=>(l(),f(h,{key:e.id,label:e.name,value:e.id},{default:d(()=>[i("div",xa,[i("div",Ca,[i("span",Ta,p(e.name),1),r(o,{type:e.available?"success":"danger",size:"small"},{default:d(()=>[g(p(e.available?"可用":"不可用"),1)]),_:2},1032,["type"])]),i("div",Sa,[i("span",Da,"¥"+p(e.pricePerThousand)+"/千字",1),i("span",Fa,p(e.provider),1)])])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),z.value?(l(),s("div",Ma,[r(S,{column:1,size:"small",border:""},{default:d(()=>[r(w,{label:"模型描述"},{default:d(()=>[g(p(z.value.description),1)]),_:1}),r(w,{label:"服务商"},{default:d(()=>[g(p(z.value.provider),1)]),_:1}),r(w,{label:"定价"},{default:d(()=>[g(" ¥"+p(z.value.pricePerThousand)+"/千字 ",1)]),_:1}),r(w,{label:"最大上下文"},{default:d(()=>[g(p(z.value.maxTokens?.toLocaleString())+" tokens ",1)]),_:1})]),_:1})])):n("",!0),z.value?(l(),s("div",Pa,[r(y,{label:"温度参数"},{default:d(()=>[r(M,{modelValue:T.value.temperature,"onUpdate:modelValue":a[1]||(a[1]=e=>T.value.temperature=e),min:0,max:1,step:.1,"show-input":"","input-size":"small"},null,8,["modelValue"]),a[7]||(a[7]=i("div",{class:"param-hint"},"控制输出的随机性，值越高越有创意，值越低越稳定",-1))]),_:1,__:[7]}),r(y,{label:"最大令牌数"},{default:d(()=>[r(J,{modelValue:T.value.maxTokens,"onUpdate:modelValue":a[2]||(a[2]=e=>T.value.maxTokens=e),min:100,max:z.value.maxTokens||4e3,step:100,size:"small",style:{width:"100%"}},null,8,["modelValue","max"]),a[8]||(a[8]=i("div",{class:"param-hint"},"单次请求的最大令牌数限制",-1))]),_:1,__:[8]})])):n("",!0)])]),_:1}),r(Q,{class:"config-card",shadow:"never"},{header:d(()=>[i("div",za,[r(t,null,{default:d(()=>[r(c(G))]),_:1}),a[9]||(a[9]=i("span",null,"提示词配置",-1))])]),default:d(()=>[i("div",Aa,[r(y,{label:"业务模块"},{default:d(()=>[r(b,{modelValue:x.value,"onUpdate:modelValue":a[3]||(a[3]=e=>x.value=e),placeholder:"请选择业务模块",onChange:O,style:{width:"100%"}},{default:d(()=>[(l(!0),s(v,null,m(F.value,e=>(l(),f(h,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(y,{label:"提示词模板"},{default:d(()=>[r(b,{modelValue:k.value,"onUpdate:modelValue":a[4]||(a[4]=e=>k.value=e),placeholder:"请选择提示词模板",onChange:I,style:{width:"100%"}},{default:d(()=>[(l(!0),s(v,null,m(R.value,e=>(l(),f(h,{key:e.id,label:e.name,value:e.id},{default:d(()=>[i("div",Ra,[i("div",Ua,[i("span",Ea,p(e.name),1),r(o,{size:"small",type:"info"},{default:d(()=>[g("v"+p(e.version),1)]),_:2},1024)]),i("div",Oa,p(e.description),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),A.value?(l(),s("div",Ia,[r(y,{label:"模板内容"},{default:d(()=>[r(Y,{modelValue:C.value,"onUpdate:modelValue":a[5]||(a[5]=e=>C.value=e),type:"textarea",rows:6,placeholder:"请输入提示词模板内容...",onInput:V},null,8,["modelValue"])]),_:1}),P.value.length>0?(l(),s("div",Va,[a[10]||(a[10]=i("h4",null,"可用变量",-1)),i("div",Ba,[(l(!0),s(v,null,m(P.value,e=>(l(),f(o,{key:e.name,size:"small",class:"variable-tag",onClick:a=>(e=>{const a=document.querySelector(".template-editor textarea");if(a){const t=a.selectionStart,s=a.selectionEnd,l=C.value;C.value=l.substring(0,t)+e+l.substring(s),setTimeout(()=>{a.focus(),a.setSelectionRange(t+e.length,t+e.length)},0),V()}})(e.name)},{default:d(()=>[g(p(e.name),1)]),_:2},1032,["onClick"]))),128))]),a[11]||(a[11]=i("div",{class:"variable-hint"},"点击变量名可插入到模板中",-1))])):n("",!0)])):n("",!0)])]),_:1}),r(Q,{class:"config-card",shadow:"never"},{header:d(()=>[i("div",$a,[r(t,null,{default:d(()=>[r(c(W))]),_:1}),a[12]||(a[12]=i("span",null,"处理参数",-1))])]),default:d(()=>[i("div",La,[r(y,{label:"并发数量"},{default:d(()=>[r(M,{"model-value":e.processingOptions.concurrency,min:1,max:5,step:1,"show-input":"","input-size":"small","onUpdate:modelValue":B},null,8,["model-value"]),a[13]||(a[13]=i("div",{class:"param-hint"},"同时处理的文件数量，建议设置为3-5个",-1))]),_:1,__:[13]}),r(y,{label:"重试次数"},{default:d(()=>[r(J,{"model-value":e.processingOptions.retryAttempts,min:0,max:5,size:"small","onUpdate:modelValue":$,style:{width:"100%"}},null,8,["model-value"])]),_:1}),r(y,{label:"重试延迟"},{default:d(()=>[r(J,{"model-value":e.processingOptions.retryDelay,min:500,max:1e4,step:500,size:"small","onUpdate:modelValue":L,style:{width:"100%"}},null,8,["model-value"]),a[14]||(a[14]=i("div",{class:"param-hint"},"重试间隔时间（毫秒）",-1))]),_:1,__:[14]}),r(y,{label:"分块策略"},{default:d(()=>[r(X,{"model-value":e.processingOptions.chunkStrategy,"onUpdate:modelValue":j},{default:d(()=>[r(Z,{value:"semantic"},{default:d(()=>a[15]||(a[15]=[g("语义分块")])),_:1,__:[15]}),r(Z,{value:"fixed"},{default:d(()=>a[16]||(a[16]=[g("固定分块")])),_:1,__:[16]}),r(Z,{value:"hybrid"},{default:d(()=>a[17]||(a[17]=[g("混合分块")])),_:1,__:[17]})]),_:1},8,["model-value"])]),_:1}),r(y,{label:"导出格式"},{default:d(()=>[r(b,{"model-value":e.processingOptions.exportFormat,"onUpdate:modelValue":N,style:{width:"100%"}},{default:d(()=>[r(h,{label:"Markdown",value:"markdown"}),r(h,{label:"HTML",value:"html"}),r(h,{label:"JSON",value:"json"}),r(h,{label:"PDF",value:"pdf"}),r(h,{label:"ZIP压缩包",value:"zip"})]),_:1},8,["model-value"])]),_:1})])]),_:1}),r(Q,{class:"config-card",shadow:"never"},{header:d(()=>[i("div",ja,[r(t,null,{default:d(()=>[r(c(q))]),_:1}),a[18]||(a[18]=i("span",null,"预算控制",-1))])]),default:d(()=>[i("div",Na,[r(y,{label:"预算限制"},{default:d(()=>[r(J,{"model-value":e.budgetLimit,min:1,max:1e3,precision:2,size:"small","onUpdate:modelValue":K,style:{width:"100%"}},null,8,["model-value"]),a[19]||(a[19]=i("div",{class:"param-hint"},"单次批量处理的最大成本限制（元）",-1))]),_:1,__:[19]}),U.value?(l(),s("div",Ha,[r(ee,{title:"预算预警",type:"warning",closable:!1,"show-icon":""},{default:d(()=>a[20]||(a[20]=[g(" 当前预估成本已接近预算限制，请注意控制文件数量或调整预算设置。 ")])),_:1})])):n("",!0)])]),_:1})])}}}),[["__scopeId","data-v-0a33d186"]]);let Wa=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((e,a)=>e+=(a&=63)<36?a.toString(36):a<62?(a-26).toString(36).toUpperCase():a>62?"-":"_","");const qa={defaultConcurrency:3,defaultBudget:100,autoSaveInterval:3e4,monitorUpdateInterval:2e3,enableRealtimeSync:!0,enableCrossTabSync:!0,theme:"light",language:"zh-CN"},Ka={engine:"localStorage",keyPrefix:"ai-batch-proofreading",enableCompression:!0,compressionThreshold:1024,version:"1.0.0",persistKeys:["sessions","globalSettings","currentSessionId"],excludeKeys:["performanceMetrics","syncStatus"],autoCleanup:{enabled:!0,retentionDays:30,maxSessions:50,strategy:"lru"}};function Ja(e){return{name:e||`批次审校-${(new Date).toLocaleString()}`,startedAt:void 0,pausedAt:void 0,completedAt:void 0,status:"idle",phase:"preparing",files:[],progress:{overallProgress:0,completedFiles:0,totalFiles:0,processingFiles:0,failedFiles:0,estimatedTimeRemaining:0,averageProcessingTime:0,startTime:new Date,fileProgress:[]},resourceMonitor:{memoryUsage:0,cpuUsage:0,networkUsage:0,concurrentConnections:0,lastUpdate:new Date},costMonitor:{consumedCost:0,estimatedTotalCost:0,budget:qa.defaultBudget,usageRate:0,isOverBudget:!1,warningThreshold:80},config:{aiModel:"deepseek-chat",promptTemplateId:"default-proofreading",concurrency:qa.defaultConcurrency,budget:qa.defaultBudget,processingOptions:{enableAutoRetry:!0,maxRetries:3,retryInterval:5e3,enableQualityCheck:!0,qualityThreshold:.8},exportOptions:{formats:["markdown"],includeOriginal:!0,includeStatistics:!0,namingPattern:"(文件标题)-AI预审意见表"}},errors:[],workingDirectory:void 0}}const Qa=w("batchProofreading",()=>{const e=a(new Map),s=a(null),l=a({...qa}),i=a({...Ka}),n=a([]),o=a({localVersion:1,remoteVersion:1,status:"synced",lastSyncTime:new Date,syncDelay:0,conflicts:[]}),r=a({stateUpdateTime:0,memoryUsage:0,cacheHitRate:0,persistenceTime:0,syncTime:0,lastUpdate:new Date}),u=t(()=>s.value&&e.value.get(s.value)||null),d=t(()=>Array.from(e.value.values()).sort((e,a)=>new Date(a.updatedAt).getTime()-new Date(e.updatedAt).getTime())),c=t(()=>Array.from(e.value.values()).filter(e=>"running"===e.status||"paused"===e.status).length),p=t(()=>Array.from(e.value.values()).reduce((e,a)=>e+a.files.length,0)),v=t(()=>{const a=Array.from(e.value.values());if(0===a.length)return 0;const t=a.reduce((e,a)=>e+a.progress.overallProgress,0);return Math.round(t/a.length)}),m=t(()=>n.value.some(e=>e.canUndo)),g=t(()=>{const a=[];return e.value.forEach((e,t)=>{e.errors.forEach(e=>{a.push({...e,sessionId:t})})}),a.sort((e,a)=>new Date(a.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,10)});function f(e,a,t,s=!0){const l={id:Wa(),action:e,timestamp:new Date,beforeState:{},afterState:{},description:t,canUndo:s};n.value.unshift(l),n.value.length>100&&(n.value=n.value.slice(0,100))}function h(a){const t=e.value.get(a);if(!t)return;const s=t.files.length,l=t.files.filter(e=>"completed"===e.status).length,i=t.files.filter(e=>"processing"===e.status).length,n=t.files.filter(e=>"error"===e.status).length,o=s>0?Math.round(l/s*100):0;t.progress={...t.progress,overallProgress:o,completedFiles:l,totalFiles:s,processingFiles:i,failedFiles:n},l+n===s&&s>0&&(t.status="completed",t.phase="completed",t.completedAt=new Date),t.updatedAt=new Date}return{sessions:e,currentSessionId:s,globalSettings:l,persistenceConfig:i,stateHistory:n,syncStatus:o,performanceMetrics:r,currentSession:u,sessionList:d,activeSessionCount:c,totalFileCount:p,overallProgress:v,canUndo:m,recentErrors:g,createSession:function(a){const t=Wa(),s=new Date,l={id:t,createdAt:s,updatedAt:s,...Ja(a)};return e.value.set(t,l),f("CREATE_SESSION",{session:{...Ja(a)}},`创建新会话: ${l.name}`),t},deleteSession:function(a){const t=e.value.get(a);return!!t&&(s.value===a&&(s.value=null),e.value.delete(a),f("DELETE_SESSION",{},`删除会话: ${t.name}`),!0)},setCurrentSession:function(a){return!(a&&!e.value.has(a))&&(s.value=a,!0)},updateSession:function(a,t){const s=e.value.get(a);if(!s)return!1;const l={...s,...t,updatedAt:new Date};return e.value.set(a,l),f("UPDATE_SESSION",{},`更新会话: ${s.name}`),!0},addStateHistory:f,clearStateHistory:function(e,a){e&&(n.value=n.value.filter(a=>a.timestamp>e)),a&&n.value.length>a&&(n.value=n.value.slice(0,a)),f("CLEAR_HISTORY",0,"清理状态历史",!1)},addFile:function(a,t){const s=e.value.get(a);return!!s&&(!s.files.find(e=>e.id===t.id)&&(s.files.push(t),s.progress.totalFiles=s.files.length,s.updatedAt=new Date,f("ADD_FILE",0,`添加文件: ${t.name}`),!0))},removeFile:function(a,t){const s=e.value.get(a);if(!s)return!1;const l=s.files.findIndex(e=>e.id===t);if(-1===l)return!1;const i=s.files[l];return s.files.splice(l,1),s.progress.totalFiles=s.files.length,s.updatedAt=new Date,f("REMOVE_FILE",0,`移除文件: ${i.name}`),!0},updateFileStatus:function(a,t,s,l){const i=e.value.get(a);if(!i)return!1;const n=i.files.find(e=>e.id===t);if(!n)return!1;const o=n.status;return n.status=s,void 0!==l&&(n.progress=l),n.updatedAt=new Date,i.updatedAt=new Date,h(a),f("UPDATE_FILE_STATUS",0,`文件状态变更: ${n.name} (${o} -> ${s})`),!0},batchUpdateFileStatus:function(a,t){const s=e.value.get(a);if(!s)return!1;let l=!1;return t.forEach(({fileId:e,status:a,progress:t})=>{const i=s.files.find(a=>a.id===e);i&&(i.status=a,void 0!==t&&(i.progress=t),i.updatedAt=new Date,l=!0)}),l&&(s.updatedAt=new Date,h(a)),l},startProcessing:function(a,t){const s=e.value.get(a);return!!s&&("running"!==s.status&&(t&&(s.config={...s.config,...t}),s.status="running",s.phase="processing",s.startedAt=new Date,s.pausedAt=void 0,s.updatedAt=new Date,f("START_PROCESSING",0,`开始处理: ${s.name}`),!0))},pauseProcessing:function(a,t){const s=e.value.get(a);return!!s&&("running"===s.status&&(s.status="paused",s.pausedAt=new Date,s.updatedAt=new Date,f("PAUSE_PROCESSING",0,`暂停处理: ${s.name}`),!0))},resumeProcessing:function(a){const t=e.value.get(a);return!!t&&("paused"===t.status&&(t.status="running",t.pausedAt=void 0,t.updatedAt=new Date,f("RESUME_PROCESSING",0,`恢复处理: ${t.name}`),!0))},cancelProcessing:function(a,t){const s=e.value.get(a);return!!s&&(("running"===s.status||"paused"===s.status)&&(s.status="cancelled",s.phase="completed",s.completedAt=new Date,s.updatedAt=new Date,f("CANCEL_PROCESSING",0,`取消处理: ${s.name}`),!0))},updateSessionProgress:h,updateResourceMonitor:function(a,t){const s=e.value.get(a);return!!s&&(s.resourceMonitor={...s.resourceMonitor,...t,lastUpdate:new Date},s.updatedAt=new Date,!0)},updateCostMonitor:function(a,t){const s=e.value.get(a);return!!s&&(s.costMonitor={...s.costMonitor,...t},s.costMonitor.estimatedTotalCost>s.costMonitor.budget&&(s.costMonitor.isOverBudget=!0),s.updatedAt=new Date,!0)},updateBatchProgress:function(a,t){const s=e.value.get(a);return!!s&&(s.progress={...s.progress,...t},s.updatedAt=new Date,f("UPDATE_PROGRESS",0,`更新进度: ${s.name}`),!0)},addError:function(a,t){const s=e.value.get(a);if(!s)return null;const l=Wa(),i={id:l,timestamp:new Date,resolved:!1,...t};return s.errors.push(i),s.updatedAt=new Date,f("ADD_ERROR",0,`添加错误: ${t.message}`),l},resolveError:function(a,t,s){const l=e.value.get(a);if(!l)return!1;const i=l.errors.find(e=>e.id===t);return!!i&&(i.resolved=!0,s&&(i.solution=s),l.updatedAt=new Date,f("RESOLVE_ERROR",0,`解决错误: ${i.message}`),!0)},clearResolvedErrors:function(a){const t=e.value.get(a);if(!t)return 0;const s=t.errors.length;t.errors=t.errors.filter(e=>!e.resolved);const l=s-t.errors.length;return l>0&&(t.updatedAt=new Date),l},updateSessionConfig:function(a,t){const s=e.value.get(a);return!!s&&(s.config={...s.config,...t},s.updatedAt=new Date,f("UPDATE_CONFIG",0,`更新配置: ${s.name}`),!0)},updateGlobalSettings:function(e){l.value={...l.value,...e}},resetGlobalSettings:function(){l.value={...qa}}}}),Ya={class:"progress-monitor"},Za={class:"overall-progress"},Xa={class:"progress-header"},et={class:"progress-title"},at={class:"progress-actions"},tt={class:"main-progress"},st={class:"progress-text"},lt={class:"progress-percentage"},it={class:"progress-detail"},nt={class:"progress-stats"},ot={class:"stat-item"},rt={class:"stat-value processing"},ut={class:"stat-item"},dt={class:"stat-value completed"},ct={class:"stat-item"},pt={class:"stat-value error"},vt={class:"stat-item"},mt={class:"stat-value time"},gt={class:"stat-item"},ft={class:"stat-value speed"},ht={key:0,class:"file-progress"},bt={class:"file-progress-header"},yt={class:"file-list"},wt={class:"file-info"},_t={class:"file-details"},kt={class:"file-name"},xt={class:"file-size"},Ct={class:"file-status"},Tt={class:"status-text"},St={key:0,class:"file-progress-bar"},Dt={class:"file-actions"},Ft=X(e({__name:"ProgressMonitor",props:{batchId:{},initialProgress:{},showFileDetails:{type:Boolean,default:!0}},emits:["pause","resume","cancel","retry-file","view-result"],setup(e,{expose:w,emit:C}){const T=e,D=C,M=Qa(),z=t(()=>M.currentSession),A=t(()=>z.value?.progress||{overallProgress:0,completedFiles:0,processingFiles:0,failedFiles:0,totalFiles:0,estimatedTimeRemaining:0,averageProcessingTime:0,startTime:new Date,fileProgress:[]}),R=t(()=>z.value?.files||[]),I=a(0),V=a(!1),B=a(T.showFileDetails);let $=null,N=null;const H=t(()=>A.value.completedFiles),G=t(()=>A.value.processingFiles),W=t(()=>A.value.failedFiles),q=t(()=>A.value.totalFiles),Y=t(()=>Math.round(60*(A.value.averageProcessingTime||0))),Z=t(()=>{const e=A.value.estimatedTimeRemaining;if(e<=0)return"--";const a=Math.floor(e/6e4),t=Math.floor(e%6e4/1e3);if(a>60){return`${Math.floor(a/60)}小时${a%60}分钟`}return a>0?`${a}分${t}秒`:`${t}秒`}),X=t(()=>{const e=A.value.status;return"error"===e?"exception":"completed"===e?"success":"paused"===e?"warning":void 0}),ee=t(()=>"processing"===A.value.status),ae=t(()=>"paused"===A.value.status),te=t(()=>["processing","paused"].includes(A.value.status)),se=e=>{if(0===e)return"0 B";const a=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB"][a]},le=e=>{const a=e.split(".").pop()?.toLowerCase();switch(a){case"jpg":case"jpeg":case"png":case"gif":return E;case"mp4":case"avi":case"mov":return Q;default:return F}},ie=e=>{switch(e){case"processing":return P;case"completed":return"Check";case"error":return"Close";case"pending":return"Clock";default:return"Document"}},ne=e=>{switch(e){case"processing":return"#409eff";case"completed":return"#67c23a";case"error":return"#f56c6c";default:return"#909399"}},oe=e=>{switch(e){case"processing":return"处理中";case"completed":return"已完成";case"error":return"处理失败";case"pending":return"等待处理";default:return"未知状态"}},re=e=>{const a=I.value,t=e-a,s=Date.now(),l=()=>{const e=Date.now()-s,i=Math.min(e/500,1),n=1-Math.pow(1-i,4);I.value=a+t*n,i<1&&(N=requestAnimationFrame(l))};N&&cancelAnimationFrame(N),l()},ue=async()=>{V.value=!0;try{z.value&&(M.pauseProcessing(z.value.id),D("pause"),S.success("批量处理已暂停"))}catch{S.error("暂停失败")}finally{V.value=!1}},de=async()=>{V.value=!0;try{z.value&&(M.resumeProcessing(z.value.id),D("resume"),S.success("批量处理已继续"))}catch{S.error("继续失败")}finally{V.value=!1}},ce=async()=>{try{await L.confirm("确认取消批量处理？已处理的文件结果将保留。","确认取消",{confirmButtonText:"确认取消",cancelButtonText:"继续处理",type:"warning"}),V.value=!0,z.value&&M.cancelProcessing(z.value.id),D("cancel"),S.success("批量处理已取消")}catch{}finally{V.value=!1}},pe=async()=>{try{re(A.value.overallProgress)}catch(e){}};return b(()=>A.value.overallProgress,e=>{re(e)}),T.initialProgress&&(A.value={...T.initialProgress},I.value=T.initialProgress.overallProgress),y(()=>{$=window.setInterval(pe,2e3),pe()}),_(()=>{$&&clearInterval($),N&&cancelAnimationFrame(N)}),w({updateProgress:e=>{A.value={...e}},updateFileProgress:e=>{R.value=[...e]}}),(e,a)=>{const t=u("el-icon"),b=u("el-button"),y=u("el-progress");return l(),s("div",Ya,[i("div",Za,[i("div",Xa,[i("h3",et,[r(t,null,{default:d(()=>[r(c(P))]),_:1}),a[1]||(a[1]=g(" 批量处理进度 "))]),i("div",at,[ee.value?(l(),f(b,{key:0,size:"small",onClick:ue,loading:V.value},{default:d(()=>[r(t,null,{default:d(()=>[r(c(K))]),_:1}),a[2]||(a[2]=g(" 暂停 "))]),_:1,__:[2]},8,["loading"])):n("",!0),ae.value?(l(),f(b,{key:1,size:"small",type:"primary",onClick:de,loading:V.value},{default:d(()=>[r(t,null,{default:d(()=>[r(c(O))]),_:1}),a[3]||(a[3]=g(" 继续 "))]),_:1,__:[3]},8,["loading"])):n("",!0),te.value?(l(),f(b,{key:2,size:"small",type:"danger",onClick:ce,loading:V.value},{default:d(()=>[r(t,null,{default:d(()=>[r(c(J))]),_:1}),a[4]||(a[4]=g(" 取消 "))]),_:1,__:[4]},8,["loading"])):n("",!0)])]),i("div",tt,[r(y,{percentage:I.value,status:X.value,"stroke-width":12,"show-text":!1,class:"main-progress-bar"},null,8,["percentage","status"]),i("div",st,[i("span",lt,p(Math.round(I.value))+"%",1),i("span",it,p(H.value)+" / "+p(q.value)+" 文件已完成 ",1)])]),i("div",nt,[i("div",ot,[a[5]||(a[5]=i("span",{class:"stat-label"},"处理中",-1)),i("span",rt,p(G.value),1)]),i("div",ut,[a[6]||(a[6]=i("span",{class:"stat-label"},"已完成",-1)),i("span",dt,p(H.value),1)]),i("div",ct,[a[7]||(a[7]=i("span",{class:"stat-label"},"失败",-1)),i("span",pt,p(W.value),1)]),i("div",vt,[a[8]||(a[8]=i("span",{class:"stat-label"},"预计剩余",-1)),i("span",mt,p(Z.value),1)]),i("div",gt,[a[9]||(a[9]=i("span",{class:"stat-label"},"处理速度",-1)),i("span",ft,p(Y.value)+" 文件/分钟",1)])])]),B.value?(l(),s("div",ht,[i("div",bt,[a[10]||(a[10]=i("h4",null,"文件处理详情",-1)),r(b,{text:"",onClick:a[0]||(a[0]=e=>B.value=!B.value),icon:B.value?"ArrowUp":"ArrowDown"},null,8,["icon"])]),k(i("div",yt,[(l(!0),s(v,null,m(R.value,e=>(l(),s("div",{key:e.id,class:o(["file-item",`status-${e.status}`])},[i("div",wt,[r(t,{class:"file-icon"},{default:d(()=>[(l(),f(h(le(e.name))))]),_:2},1024),i("div",_t,[i("span",kt,p(e.name),1),i("span",xt,p(se(e.size)),1)])]),i("div",Ct,[r(t,{color:ne(e.status),class:"status-icon"},{default:d(()=>[(l(),f(h(ie(e.status))))]),_:2},1032,["color"]),i("span",Tt,p(oe(e.status)),1)]),"processing"===e.status?(l(),s("div",St,[r(y,{percentage:e.progress,"stroke-width":4,"show-text":!1},null,8,["percentage"])])):n("",!0),i("div",Dt,["error"===e.status?(l(),f(b,{key:0,size:"small",text:"",onClick:a=>{return t=e.id,z.value&&M.updateFileStatus(z.value.id,t,"pending"),void D("retry-file",t);var t}},{default:d(()=>[r(t,null,{default:d(()=>[r(c(U))]),_:1})]),_:2},1032,["onClick"])):n("",!0),"completed"===e.status?(l(),f(b,{key:1,size:"small",text:"",onClick:a=>{return t=e.id,void D("view-result",t);var t}},{default:d(()=>[r(t,null,{default:d(()=>[r(c(j))]),_:1})]),_:2},1032,["onClick"])):n("",!0)])],2))),128))],512),[[x,B.value]])])):n("",!0)])}}}),[["__scopeId","data-v-280ee204"]]),Mt=768,Pt=1200;const zt=new class{windowWidth=a(0);windowHeight=a(0);resizeObserver=null;constructor(){this.updateDimensions(),this.setupResizeListener()}get deviceType(){const e=this.windowWidth.value;return e<Mt?"mobile":e<Pt?"tablet":"desktop"}get isMobile(){return"mobile"===this.deviceType}get isTablet(){return"tablet"===this.deviceType}get isDesktop(){return"desktop"===this.deviceType}get width(){return this.windowWidth.value}get height(){return this.windowHeight.value}getResponsiveColumns(e=4,a=2,t=1){switch(this.deviceType){case"desktop":default:return e;case"tablet":return a;case"mobile":return t}}getResponsiveSpacing(e=24,a=16,t=12){switch(this.deviceType){case"desktop":default:return e;case"tablet":return a;case"mobile":return t}}getResponsiveFontSize(e=16,a=14,t=12){switch(this.deviceType){case"desktop":default:return e;case"tablet":return a;case"mobile":return t}}getContainerWidth(){switch(this.deviceType){case"desktop":default:return"1200px";case"tablet":case"mobile":return"100%"}}getSidebarWidth(){switch(this.deviceType){case"desktop":default:return 240;case"tablet":return 200;case"mobile":return 0}}shouldShowSidebar(){return!this.isMobile}getTableConfig(){return{size:this.isMobile?"small":"default",showHeader:!this.isMobile,stripe:!0,border:!this.isMobile,fit:!0,highlightCurrentRow:!0,maxHeight:this.isMobile?400:600}}getDialogConfig(){return{width:this.isMobile?"95%":this.isTablet?"80%":"60%",fullscreen:this.isMobile,destroyOnClose:!0,closeOnClickModal:!this.isMobile,showClose:!0}}getDrawerConfig(){return{size:this.isMobile?"100%":this.isTablet?"60%":"40%",direction:this.isMobile?"btt":"rtl",withHeader:!0,modal:!0,lockScroll:!0}}getButtonConfig(){return{size:this.isMobile?"small":"default",round:this.isMobile,circle:!1}}getFormConfig(){return{labelPosition:this.isMobile?"top":"right",labelWidth:this.isMobile?"auto":"120px",size:this.isMobile?"small":"default",statusIcon:!this.isMobile}}getCardConfig(){return{shadow:this.isMobile?"never":"hover",bodyStyle:{padding:this.getResponsiveSpacing()+"px"}}}updateDimensions(){this.windowWidth.value=window.innerWidth,this.windowHeight.value=window.innerHeight}setupResizeListener(){"undefined"!=typeof ResizeObserver?(this.resizeObserver=new ResizeObserver(()=>{this.updateDimensions()}),this.resizeObserver.observe(document.documentElement)):window.addEventListener("resize",this.updateDimensions)}destroy(){this.resizeObserver?(this.resizeObserver.disconnect(),this.resizeObserver=null):window.removeEventListener("resize",this.updateDimensions)}};const At={class:"batch-proofreading-view"},Rt={class:"page-header"},Ut={class:"page-title"},Et={class:"main-content"},Ot={key:0,class:"desktop-layout"},It={class:"panel-header"},Vt={class:"panel-content"},Bt={class:"center-panel"},$t={class:"card-header"},Lt={class:"card-header"},jt={key:0,class:"cost-estimation"},Nt={class:"cost-details"},Ht={key:1,class:"progress-monitor-container"},Gt={class:"panel-header"},Wt={class:"panel-content"},qt={key:1,class:"mobile-layout"},Kt={class:"mobile-bottom-bar"},Jt={key:0,class:"cost-info"},Qt={class:"cost-value"},Yt=X(e({__name:"BatchProofreadingView",setup(e){const v=C({mobile:768,tablet:1024,desktop:1200}).smaller("tablet");!function(){const e=zt,a=t(()=>e.deviceType),s=t(()=>e.isMobile),l=t(()=>e.isTablet),i=t(()=>e.isDesktop),n=t(()=>e.width),o=t(()=>e.height);y(()=>{e.updateDimensions()}),_(()=>{})}();const m=a(!1),h=a(!1),b=a("upload"),w=a([]),T=a({}),D=a(null),M=a(null),P=a({concurrency:3,retryAttempts:3,retryDelay:1e3,chunkStrategy:"semantic",exportFormat:"markdown"}),z=a(100),A=a({total:0,totalChars:0,perFile:[]}),R=a(!1),U=a(0),E=a(0),O=a(0),I=a(0),V=a("--"),B=a(""),$=a(),j=a({overallProgress:0,completedFiles:0,processingFiles:0,errorFiles:0,totalFiles:0,estimatedTimeRemaining:0,processingSpeed:0,status:"pending"}),N=a({updateInterval:2e3,enableRealtime:!0,showFileDetails:!0,enableAnimation:!0,progressThreshold:1}),H=new ee,G=t(()=>w.value.length>0&&D.value&&M.value&&!R.value&&A.value.total<=z.value);t(()=>100===U.value?"success":U.value>0?"active":"normal");const q=(e,a)=>{const t=w.value.findIndex(e=>e.id===a.id);-1!==t&&(w.value[t].uploadProgress=e)},K=(e,a)=>{S.error(`文件 ${a.name} 上传失败：${e.message}`);const t=w.value.findIndex(e=>e.id===a.id);-1!==t&&w.value.splice(t,1)},J=e=>{const a=w.value.findIndex(a=>a.id===e);-1!==a&&(w.value.splice(a,1),se())},Q=async e=>{const a=w.value.find(a=>a.id===e);if(a)try{a.status="pending",a.error=void 0,await ge(a)}catch(t){S.error(`文件重试失败：${t instanceof Error?t.message:"未知错误"}`)}},X=async(e,a)=>{switch(e){case"retry":for(const e of a)await Q(e);break;case"remove":a.forEach(e=>J(e));break;case"export":await fe(a);break;default:S.warning(`未知操作：${e}`)}},ae=e=>{A.value=e},te=()=>{se()},se=async()=>{if(D.value&&0!==w.value.length)try{const e=await H.estimateCost({files:w.value,model:D.value,template:M.value});A.value=e}catch(e){S.error("成本预估计算失败")}else A.value={total:0,totalChars:0,perFile:[]}},le=async()=>{if(G.value){try{await L.confirm(`确认开始批量审校？预估成本：¥${A.value.total.toFixed(2)}`,"确认批量审校",{confirmButtonText:"开始审校",cancelButtonText:"取消",type:"info"})}catch{return}R.value=!0,I.value=w.value.length,E.value=0,O.value=0,U.value=0,B.value=`batch_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,j.value={overallProgress:0,completedFiles:0,processingFiles:0,errorFiles:0,totalFiles:I.value,estimatedTimeRemaining:0,processingSpeed:0,status:"processing",startTime:new Date};try{await H.batchProcess({files:w.value,model:D.value,template:M.value,options:P.value,onProgress:ie,onFileComplete:ve,onError:me}),S.success("批量审校完成！")}catch(e){S.error(`批量审校失败：${e instanceof Error?e.message:"未知错误"}`)}finally{R.value=!1}}else S.warning("请检查文件、模型选择和预算设置")},ie=e=>{U.value=e.overallProgress,E.value=e.completedFiles,O.value=e.processingFiles,V.value=e.estimatedTimeRemaining,ne(e)},ne=e=>{j.value={...j.value,...e,errorFiles:w.value.filter(e=>"error"===e.status).length,processingSpeed:oe(),status:R.value?"processing":"pending"},$.value&&$.value.updateProgress(j.value)},oe=()=>{const e=E.value,a=j.value.startTime;if(!a||0===e)return 0;const t=(Date.now()-a.getTime())/1e3;return t>0?e/t:0},re=async()=>{try{j.value.status="paused",j.value.pauseTime=new Date,S.success("批量处理已暂停")}catch(e){S.error("暂停失败")}},ue=async()=>{try{j.value.status="processing",j.value.pauseTime=void 0,S.success("批量处理已继续")}catch(e){S.error("恢复失败")}},de=async()=>{try{R.value=!1,j.value.status="cancelled",B.value="",S.success("批量处理已取消")}catch(e){S.error("取消失败")}},ce=e=>{const a=w.value.find(a=>a.id===e);a&&a.result},pe=e=>{j.value={...e},U.value=e.overallProgress,E.value=e.completedFiles,O.value=e.processingFiles},ve=(e,a)=>{const t=w.value.find(a=>a.id===e);t&&(t.status="completed",t.result=a)},me=(e,a)=>{const t=w.value.find(a=>a.id===e);t&&(t.status="error",t.error=a.message)},ge=async e=>{e.status="processing";try{const a=await H.processFile({file:e,model:D.value,template:M.value,options:P.value});e.status="completed",e.result=a}catch(a){throw e.status="error",e.error=a instanceof Error?a.message:"处理失败",a}},fe=async e=>{try{const a=w.value.filter(a=>e.includes(a.id)&&"completed"===a.status);if(0===a.length)return void S.warning("没有可导出的已完成文件");await H.exportResults({files:a,format:P.value.exportFormat}),S.success("结果导出成功！")}catch(a){S.error(`导出失败：${a instanceof Error?a.message:"未知错误"}`)}};return y(()=>{H.initialize({apiBaseUrl:"/api/ai-proofreading",timeout:3e4})}),_(()=>{R.value&&H.cancelBatchProcessing()}),(e,a)=>{const t=u("el-breadcrumb-item"),y=u("el-breadcrumb"),_=u("el-icon"),C=u("el-button"),S=u("el-tag"),U=u("el-card"),E=u("el-alert"),O=u("el-tab-pane"),I=u("el-tabs");return l(),s("div",At,[i("div",Rt,[r(y,{separator:"/"},{default:d(()=>[r(t,{to:{path:"/dashboard"}},{default:d(()=>a[13]||(a[13]=[g("校对大屏")])),_:1,__:[13]}),r(t,{to:{path:"/ai-batch-proofreading"}},{default:d(()=>a[14]||(a[14]=[g("AI批量审校")])),_:1,__:[14]}),r(t,null,{default:d(()=>a[15]||(a[15]=[g("批量审校")])),_:1,__:[15]})]),_:1}),i("h1",Ut,[r(_,null,{default:d(()=>[r(c(F))]),_:1}),a[16]||(a[16]=g(" 大模型批量审校 "))]),a[17]||(a[17]=i("p",{class:"page-description"}," 支持多文件同时上传，使用国内主流AI模型进行智能审校，提供完整的处理监控和结果导出功能 ",-1))]),i("div",Et,[c(v)?(l(),s("div",qt,[r(I,{modelValue:b.value,"onUpdate:modelValue":a[12]||(a[12]=e=>b.value=e),class:"mobile-tabs"},{default:d(()=>[r(O,{label:"文件上传",name:"upload"},{default:d(()=>[r(he,{files:w.value,"onUpdate:files":a[7]||(a[7]=e=>w.value=e),"max-files":50,"max-size":52428800,"max-total-size":524288e3,"accepted-formats":[".txt",".docx",".pdf",".md"],onUploadProgress:q,onUploadError:K},null,8,["files"])]),_:1}),r(O,{label:"文件管理",name:"files"},{default:d(()=>[r(ya,{files:w.value,"processing-status":T.value,onFileRemove:J,onFileRetry:Q,onBatchOperation:X},null,8,["files","processing-status"])]),_:1}),r(O,{label:"配置选项",name:"config"},{default:d(()=>[r(Ga,{"ai-model":D.value,"onUpdate:aiModel":a[8]||(a[8]=e=>D.value=e),"prompt-template":M.value,"onUpdate:promptTemplate":a[9]||(a[9]=e=>M.value=e),"processing-options":P.value,"onUpdate:processingOptions":a[10]||(a[10]=e=>P.value=e),"budget-limit":z.value,"onUpdate:budgetLimit":a[11]||(a[11]=e=>z.value=e),onCostEstimation:ae,onConfigChange:te},null,8,["ai-model","prompt-template","processing-options","budget-limit"])]),_:1})]),_:1},8,["modelValue"]),i("div",Kt,[A.value.total>0?(l(),s("div",Jt,[a[23]||(a[23]=i("span",{class:"cost-label"},"预估成本",-1)),i("span",Qt,"¥"+p(A.value.total.toFixed(2)),1)])):n("",!0),r(C,{type:"primary",size:"large",disabled:!G.value,loading:R.value,onClick:le,class:"start-button"},{default:d(()=>a[24]||(a[24]=[g(" 开始审校 ")])),_:1,__:[24]},8,["disabled","loading"])])])):(l(),s("div",Ot,[i("div",{class:o(["left-panel",{collapsed:m.value}])},[i("div",It,[a[18]||(a[18]=i("h3",null,"文件管理",-1)),r(C,{text:"",onClick:a[0]||(a[0]=e=>m.value=!m.value),icon:m.value?"Expand":"Fold"},null,8,["icon"])]),k(i("div",Vt,[r(ya,{files:w.value,"processing-status":T.value,onFileRemove:J,onFileRetry:Q,onBatchOperation:X},null,8,["files","processing-status"])],512),[[x,!m.value]])],2),i("div",Bt,[r(U,{class:"upload-card",shadow:"hover"},{header:d(()=>[i("div",$t,[r(_,null,{default:d(()=>[r(c(Y))]),_:1}),a[19]||(a[19]=i("span",null,"文件上传",-1)),w.value.length>0?(l(),f(S,{key:0,type:"info"},{default:d(()=>[g(" 已选择 "+p(w.value.length)+" 个文件 ",1)]),_:1})):n("",!0)])]),default:d(()=>[r(he,{files:w.value,"onUpdate:files":a[1]||(a[1]=e=>w.value=e),"max-files":50,"max-size":52428800,"max-total-size":524288e3,"accepted-formats":[".txt",".docx",".pdf",".md"],onUploadProgress:q,onUploadError:K},null,8,["files"])]),_:1}),r(U,{class:"control-card",shadow:"hover"},{header:d(()=>[i("div",Lt,[r(_,null,{default:d(()=>[r(c(W))]),_:1}),a[21]||(a[21]=i("span",null,"处理控制",-1)),r(C,{type:"primary",disabled:!G.value,loading:R.value,onClick:le},{default:d(()=>[r(_,null,{default:d(()=>[r(c(Z))]),_:1}),a[20]||(a[20]=g(" 开始批量审校 "))]),_:1,__:[20]},8,["disabled","loading"])])]),default:d(()=>[A.value.total>0?(l(),s("div",jt,[r(E,{title:`预估成本：¥${A.value.total.toFixed(2)}`,type:"info",closable:!1,"show-icon":""},{default:d(()=>[i("div",Nt,[i("span",null,"文件数量："+p(w.value.length)+" 个",1),i("span",null,"预估字符："+p(A.value.totalChars.toLocaleString())+" 字",1),i("span",null,"选择模型："+p(D.value?.name||"未选择"),1)])]),_:1},8,["title"])])):n("",!0),R.value||B.value?(l(),s("div",Ht,[r(Ft,{"batch-id":B.value,"initial-progress":j.value,"show-file-details":!0,config:N.value,onPause:re,onResume:ue,onCancel:de,onRetryFile:Q,onViewResult:ce,onProgressUpdate:pe,ref_key:"progressMonitorRef",ref:$},null,8,["batch-id","initial-progress","config"])])):n("",!0)]),_:1})]),i("div",{class:o(["right-panel",{collapsed:h.value}])},[i("div",Gt,[a[22]||(a[22]=i("h3",null,"配置选项",-1)),r(C,{text:"",onClick:a[2]||(a[2]=e=>h.value=!h.value),icon:h.value?"Expand":"Fold"},null,8,["icon"])]),k(i("div",Wt,[r(Ga,{"ai-model":D.value,"onUpdate:aiModel":a[3]||(a[3]=e=>D.value=e),"prompt-template":M.value,"onUpdate:promptTemplate":a[4]||(a[4]=e=>M.value=e),"processing-options":P.value,"onUpdate:processingOptions":a[5]||(a[5]=e=>P.value=e),"budget-limit":z.value,"onUpdate:budgetLimit":a[6]||(a[6]=e=>z.value=e),onCostEstimation:ae,onConfigChange:te},null,8,["ai-model","prompt-template","processing-options","budget-limit"])],512),[[x,!h.value]])],2)]))])])}}}),[["__scopeId","data-v-22d3f074"]]);export{Yt as default};
