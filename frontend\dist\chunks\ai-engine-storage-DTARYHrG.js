var s=(s=>(s.PRE_REVIEW="pre_review",s.BATCH_PROOFREADING="batch_proofreading",s.ONLINE_PROOFREADING="online_proofreading",s.IMAGE_PROOFREADING="image_proofreading",s.VIDEO_PROOFREADING="video_proofreading",s.AUDIO_PROOFREADING="audio_proofreading",s.TYPESETTING="typesetting",s.PROFESSIONAL_QUERY="professional_query",s))(s||{}),e=(s=>(s.PREPARING="preparing",s.PROCESSING="processing",s.COMPLETED="completed",s.ERROR="error",s.PAUSED="paused",s))(e||{});class t{activeSessions=new Map;storagePrefix="ai_proofreading_session_";workspaceBasePath="ai_proofreading_workspace";cleanupInterval=36e5;dataRetentionTime=6048e5;constructor(){this.startCleanupTask(),this.restoreActiveSessions()}async createSession(s){const t=this.generateSessionId(),r=Date.now(),o=`${this.workspaceBasePath}/${t}`,a={sessionInfo:{sessionId:t,moduleType:s.moduleType,fileName:"",fileSize:0,totalChunks:0,aiProvider:s.aiModel.provider,startTime:r,status:e.PREPARING,progress:{sessionId:t,status:"idle",overallProgress:0,currentChunkIndex:0,totalChunks:0,completedChunks:0,failedChunks:0,lastUpdated:r},workspacePath:o,userSelections:{aiModel:s.aiModel,promptTemplate:s.promptTemplate,processingOptions:{focus:"all",style:"formal",language:"zh-CN"},costBudget:s.budget}},chunks:[],chunkResults:new Map,errors:[],createdAt:r,lastUpdated:r};return this.activeSessions.set(t,a),await this.persistSession(t,a),await this.createWorkspaceStructure(o),t}async getSession(s){const e=await this.getSessionData(s);return e?.sessionInfo||null}async updateSessionStatus(s,t){const r=await this.getSessionData(s);if(!r)throw new Error(`会话 ${s} 不存在`);r.sessionInfo.status=t,r.lastUpdated=Date.now(),t===e.COMPLETED&&(r.sessionInfo.endTime=Date.now()),await this.persistSession(s,r)}async updateProgress(s,e){const t=await this.getSessionData(s);if(!t)throw new Error(`会话 ${s} 不存在`);const r=t.sessionInfo.progress;switch(e.type){case"parse":r.parseProgress=e.progress;break;case"chunk":r.overallProgress=Math.min(e.progress,100);break;case"proofread":r.proofreadProgress=e.progress,r.currentChunkIndex=Math.floor(e.progress/100*r.totalChunks);break;case"merge":r.mergeProgress=e.progress;break;case"report":r.reportProgress=e.progress}r.lastUpdated=e.timestamp,t.lastUpdated=Date.now(),await this.persistSession(s,t)}async getProgress(s){const e=await this.getSessionData(s);if(!e)throw new Error(`会话 ${s} 不存在`);return e.sessionInfo.progress}async saveChunks(s,e){const t=await this.getSessionData(s);if(!t)throw new Error(`会话 ${s} 不存在`);t.chunks=e,t.sessionInfo.totalChunks=e.length,t.sessionInfo.progress.totalChunks=e.length,t.lastUpdated=Date.now(),await this.persistSession(s,t)}async saveChunkResult(s,e,t){const r=await this.getSessionData(s);if(!r)throw new Error(`会话 ${s} 不存在`);r.chunkResults.set(e,t),r.sessionInfo.progress.completedChunks=r.chunkResults.size;const o=r.sessionInfo.totalChunks;if(o>0){const s=r.chunkResults.size;r.sessionInfo.progress.overallProgress=Math.round(s/o*100)}r.lastUpdated=Date.now(),await this.persistSession(s,r)}async saveResults(s,e){const t=await this.getSessionData(s);if(!t)throw new Error(`会话 ${s} 不存在`);t.finalResult=e.mergedResult,t.report=e.report,t.lastUpdated=Date.now(),await this.persistSession(s,t)}async getResult(s){const e=await this.getSessionData(s);if(!e)throw new Error(`会话 ${s} 不存在`);if(!e.finalResult)throw new Error(`会话 ${s} 的结果尚未生成`);return e.finalResult}async getReport(s){const e=await this.getSessionData(s);if(!e)throw new Error(`会话 ${s} 不存在`);if(!e.report)throw new Error(`会话 ${s} 的报告尚未生成`);return e.report}async addError(s,e){const t=await this.getSessionData(s);if(!t)throw new Error(`会话 ${s} 不存在`);t.errors.push(e),e.chunkId&&t.sessionInfo.progress.failedChunks++,t.lastUpdated=Date.now(),await this.persistSession(s,t)}async saveError(s,e){const t={id:this.generateErrorId(),type:"processing",message:e.message,details:e.stack,timestamp:Date.now(),retryable:!0};await this.addError(s,t)}async cleanupSession(s){this.activeSessions.delete(s),await this.removeSessionFromStorage(s)}async cleanup(){const s=Array.from(this.activeSessions.keys());for(const e of s)await this.cleanupSession(e)}async getActiveSessions(s){const e=[];for(const t of this.activeSessions.values())s&&t.sessionInfo.moduleType!==s||e.push(t.sessionInfo);return e}async getSessionData(s){let e=this.activeSessions.get(s);return e||(e=await this.loadSessionFromStorage(s),e&&this.activeSessions.set(s,e)),e||null}async persistSession(s,e){try{const t=`${this.storagePrefix}${s}`,r={...e,chunkResults:Array.from(e.chunkResults.entries())};localStorage.setItem(t,JSON.stringify(r))}catch(t){}}async loadSessionFromStorage(s){try{const e=`${this.storagePrefix}${s}`,t=localStorage.getItem(e);if(!t)return null;const r=JSON.parse(t);return{...r,chunkResults:new Map(r.chunkResults||[])}}catch(e){return null}}async removeSessionFromStorage(s){try{const e=`${this.storagePrefix}${s}`;localStorage.removeItem(e)}catch(e){}}async restoreActiveSessions(){try{for(let s=0;s<localStorage.length;s++){const t=localStorage.key(s);if(t&&t.startsWith(this.storagePrefix)){const s=t.replace(this.storagePrefix,""),r=await this.loadSessionFromStorage(s);if(r){const t=r.sessionInfo.status;t!==e.COMPLETED&&t!==e.ERROR&&this.activeSessions.set(s,r)}}}}catch(s){}}async createWorkspaceStructure(s){}startCleanupTask(){setInterval(()=>{this.performCleanup()},this.cleanupInterval)}async performCleanup(){const s=Date.now(),t=[];for(const[r,o]of this.activeSessions.entries()){const a=s-o.createdAt,n=o.sessionInfo.status===e.COMPLETED,i=a>this.dataRetentionTime;n&&i&&t.push(r)}for(const e of t)await this.cleanupSession(e)}generateSessionId(){return`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}generateErrorId(){return`error_${Date.now()}_${Math.random().toString(36).substr(2,6)}`}}export{s as M,t as S};
