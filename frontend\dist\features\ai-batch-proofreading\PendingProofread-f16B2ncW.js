import{d as a,m as e,c as s,Q as n,I as d,ag as r,o as l,a as t,M as o}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as p}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const u={class:"pending-proofread"},c={class:"card-header"},i={class:"content"},_=p(a({__name:"PendingProofread",setup:a=>(e(()=>{}),(a,e)=>{const p=r("el-button"),_=r("el-empty"),m=r("el-card");return l(),s("div",u,[n(m,null,{header:d(()=>[t("div",c,[e[1]||(e[1]=t("span",null,"校对中文档",-1)),n(p,{type:"warning",size:"small"},{default:d(()=>e[0]||(e[0]=[o(" 查看进度 ")])),_:1,__:[0]})])]),default:d(()=>[t("div",i,[e[2]||(e[2]=t("p",null,"校对中文档页面 - 开发中...",-1)),n(_,{description:"暂无数据"})])]),_:1})])})}),[["__scopeId","data-v-403995de"]]);export{_ as default};
