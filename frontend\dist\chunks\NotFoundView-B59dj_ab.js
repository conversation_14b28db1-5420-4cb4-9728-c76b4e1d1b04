import{d as s,c as a,a as o,Q as e,I as r,ag as t,o as n,M as d,u as i,aB as l}from"./vue-vendor-BCsylZgc.js";import{ad as u,ae as c}from"./ui-vendor-DZ6owSRu.js";import{_}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./utils-vendor-DYQz1-BF.js";const p={class:"not-found-container"},v={class:"not-found-content"},f={class:"error-actions"},m=_(s({__name:"NotFoundView",setup(s){const _=l(),m=()=>{_.push("/dashboard")},g=()=>{_.go(-1)};return(s,l)=>{const _=t("el-icon"),j=t("el-button");return n(),a("div",p,[o("div",v,[l[2]||(l[2]=o("div",{class:"error-code"},"404",-1)),l[3]||(l[3]=o("div",{class:"error-message"},"页面未找到",-1)),l[4]||(l[4]=o("div",{class:"error-description"},"抱歉，您访问的页面不存在或已被移除",-1)),o("div",f,[e(j,{type:"primary",onClick:m},{default:r(()=>[e(_,null,{default:r(()=>[e(i(u))]),_:1}),l[0]||(l[0]=d(" 返回首页 "))]),_:1,__:[0]}),e(j,{onClick:g},{default:r(()=>[e(_,null,{default:r(()=>[e(i(c))]),_:1}),l[1]||(l[1]=d(" 返回上页 "))]),_:1,__:[1]})])])])}}}),[["__scopeId","data-v-3715f75e"]]);export{m as default};
