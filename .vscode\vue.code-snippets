{"Vue 3 Composition API Component": {"prefix": "v3comp", "body": ["<template>", "  <div class=\"${1:component-name}\">", "    $0", "  </div>", "</template>", "", "<script setup lang=\"ts\">", "import { ref, reactive, computed, onMounted } from 'vue'", "", "// ========== 接口定义 ==========", "interface Props {", "  ${2:// 定义 props 类型}", "}", "", "interface Emits {", "  ${3:// 定义 emits 类型}", "}", "", "// ========== Props 和 Emits ==========", "const props = withDefaults(defineProps<Props>(), {", "  ${4:// 默认值}", "})", "", "const emit = defineEmits<Emits>()", "", "// ========== 响应式数据 ==========", "const ${5:data} = ref('')", "", "// ========== 计算属性 ==========", "const ${6:computed} = computed(() => {", "  return ${7:// 计算逻辑}", "})", "", "// ========== 方法 ==========", "const ${8:method} = () => {", "  ${9:// 方法实现}", "}", "", "// ========== 生命周期 ==========", "onMounted(() => {", "  ${10:// 组件挂载后执行}", "})", "</script>", "", "<style scoped>", ".${1:component-name} {", "  ${11:/* 样式 */}", "}", "</style>"], "description": "创建 Vue 3 Composition API 组件模板"}, "Vue 3 Composable": {"prefix": "v3composable", "body": ["import { ref, reactive, computed } from 'vue'", "", "/**", " * ${1:Composable 描述}", " */", "export function use${2:Name}() {", "  // ========== 响应式状态 ==========", "  const ${3:state} = ref(${4:initialValue})", "", "  // ========== 计算属性 ==========", "  const ${5:computed} = computed(() => {", "    return ${6:// 计算逻辑}", "  })", "", "  // ========== 方法 ==========", "  const ${7:method} = () => {", "    ${8:// 方法实现}", "  }", "", "  // ========== 返回 ==========", "  return {", "    ${3:state},", "    ${5:computed},", "    ${7:method}", "  }", "}", "", "export type ${2:Name}Return = ReturnType<typeof use${2:Name}>"], "description": "创建 Vue 3 Composable 函数"}, "Pinia Store": {"prefix": "pinia", "body": ["import { defineStore } from 'pinia'", "import { ref, computed } from 'vue'", "", "/**", " * ${1:Store 描述}", " */", "export const use${2:Name}Store = defineStore('${3:storeName}', () => {", "  // ========== 状态 ==========", "  const ${4:state} = ref(${5:initialValue})", "", "  // ========== 计算属性 (Getters) ==========", "  const ${6:getter} = computed(() => {", "    return ${7:// 计算逻辑}", "  })", "", "  // ========== 方法 (Actions) ==========", "  const ${8:action} = async () => {", "    try {", "      ${9:// 异步操作}", "    } catch (error) {", "      console.error('${8:action} error:', error)", "      throw error", "    }", "  }", "", "  // ========== 返回 ==========", "  return {", "    // 状态", "    ${4:state},", "    // 计算属性", "    ${6:getter},", "    // 方法", "    ${8:action}", "  }", "})"], "description": "创建 Pinia Store"}, "Vue Test": {"prefix": "vtest", "body": ["import { describe, it, expect, beforeEach, vi } from 'vitest'", "import { mount } from '@vue/test-utils'", "import ${1:ComponentName} from '@/components/${1:ComponentName}.vue'", "", "describe('${1:ComponentName}', () => {", "  let wrapper: any", "", "  beforeEach(() => {", "    wrapper = mount(${1:ComponentName}, {", "      props: {", "        ${2:// 测试 props}", "      }", "    })", "  })", "", "  it('${3:should render correctly}', () => {", "    expect(wrapper.exists()).toBe(true)", "    ${4:// 断言逻辑}", "  })", "", "  it('${5:should handle events}', async () => {", "    ${6:// 事件测试}", "    await wrapper.vm.\\$nextTick()", "    ${7:// 断言}", "  })", "})"], "description": "创建 Vue 组件测试"}, "API Service": {"prefix": "apiservice", "body": ["import { request } from '@/utils/request'", "", "/**", " * ${1:API 描述}", " */", "export interface ${2:DataType} {", "  ${3:// 数据类型定义}", "}", "", "/**", " * ${4:请求参数类型}", " */", "export interface ${5:RequestParams} {", "  ${6:// 请求参数定义}", "}", "", "/**", " * ${7:响应数据类型}", " */", "export interface ${8:ResponseData} {", "  ${9:// 响应数据定义}", "}", "", "/**", " * ${10:API 服务类}", " */", "export class ${11:ServiceName} {", "  /**", "   * ${12:方法描述}", "   */", "  static async ${13:methodName}(params: ${5:RequestParams}): Promise<${8:ResponseData}> {", "    return request({", "      url: '${14:/api/endpoint}',", "      method: '${15:GET}',", "      ${16:data: params}", "    })", "  }", "}"], "description": "创建 API 服务"}, "TypeScript Interface": {"prefix": "tsinterface", "body": ["/**", " * ${1:接口描述}", " */", "export interface ${2:InterfaceName} {", "  /**", "   * ${3:属性描述}", "   */", "  ${4:property}: ${5:type}", "", "  /**", "   * ${6:可选属性描述}", "   */", "  ${7:optionalProperty}?: ${8:type}", "", "  /**", "   * ${9:方法描述}", "   */", "  ${10:method}(${11:param}: ${12:type}): ${13:returnType}", "}"], "description": "创建 TypeScript 接口"}, "Vue Router Route": {"prefix": "vroute", "body": ["{", "  path: '${1:/path}',", "  name: '${2:RouteN<PERSON>}',", "  component: () => import('@/views/${3:ViewName}.vue'),", "  meta: {", "    title: '${4:页面标题}',", "    requiresAuth: ${5:false},", "    roles: [${6:'admin'}]", "  },", "  children: [", "    ${7:// 子路由}", "  ]", "}"], "description": "创建 Vue Router 路由配置"}, "Element Plus Form": {"prefix": "elform", "body": ["<el-form", "  ref=\"${1:formRef}\"", "  :model=\"${2:formData}\"", "  :rules=\"${3:formRules}\"", "  label-width=\"${4:120px}\"", "  @submit.prevent=\"${5:handleSubmit}\"", ">", "  <el-form-item label=\"${6:标签}\" prop=\"${7:field}\">", "    <el-input", "      v-model=\"${2:formData}.${7:field}\"", "      placeholder=\"${8:请输入${6:标签}}\"", "    />", "  </el-form-item>", "", "  <el-form-item>", "    <el-button type=\"primary\" @click=\"${5:handleSubmit}\">", "      ${9:提交}", "    </el-button>", "    <el-button @click=\"${10:handleReset}\">", "      ${11:重置}", "    </el-button>", "  </el-form-item>", "</el-form>"], "description": "创建 Element Plus 表单"}}