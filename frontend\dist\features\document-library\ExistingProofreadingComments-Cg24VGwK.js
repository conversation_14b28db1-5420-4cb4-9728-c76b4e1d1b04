import{d as s,c as a,a as e,Q as n,I as o,ag as t,o as r}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as c}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const l={class:"existing-proofreading-comments"},d={class:"content"},i=c(s({__name:"ExistingProofreadingComments",setup:s=>(s,c)=>{const i=t("el-card");return r(),a("div",l,[c[1]||(c[1]=e("div",{class:"page-header"},[e("h1",null,"已校对意见"),e("p",{class:"page-description"},"查看和管理已有的校对意见")],-1)),e("div",d,[n(i,null,{default:o(()=>c[0]||(c[0]=[e("p",null,"已校对意见管理功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-07c403c8"]]);export{i as default};
