<template>
  <div class="test-pdf-upload">
    <div class="test-header">
      <h2>PDF预览组件上传功能测试</h2>
      <p>测试PDF预览组件工具栏中的上传PDF按钮功能</p>
    </div>

    <div class="test-section">
      <h3>PDF预览组件（带上传功能）</h3>
      <div class="test-controls">
        <el-button type="primary" @click="loadSamplePDF"> 加载示例PDF </el-button>
        <el-button @click="clearPDF"> 清除PDF </el-button>
        <span class="current-url" v-if="currentPdfUrl">
          当前PDF: {{ currentPdfUrl.substring(0, 50) }}...
        </span>
      </div>
      <div class="pdf-preview-wrapper">
        <PDFPreview
          :pdf-url="currentPdfUrl"
          :current-page="currentPage"
          :scroll-sync="true"
          @page-change="handlePageChange"
          @position-change="handlePositionChange"
          @pdf-upload="handlePDFUpload"
          @pdf-upload-success="handlePDFUploadSuccess"
          @pdf-upload-error="handlePDFUploadError"
        />
      </div>
    </div>

    <div class="test-info">
      <h3>测试信息</h3>
      <el-card>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-statistic title="当前页码" :value="currentPage" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="PDF状态" :value="pdfStatus" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="上传次数" :value="uploadCount" />
          </el-col>
        </el-row>
      </el-card>

      <div class="upload-log" v-if="uploadLogs.length > 0">
        <h4>上传日志</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(log, index) in uploadLogs"
            :key="index"
            :timestamp="log.timestamp"
            :type="log.type"
          >
            {{ log.message }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <div class="test-instructions">
      <h3>测试步骤</h3>
      <ol>
        <li>点击PDF预览区域工具栏中的"上传PDF"按钮</li>
        <li>选择一个PDF文件进行上传</li>
        <li>观察PDF是否正确加载到预览区域</li>
        <li>测试PDF的缩放、翻页等功能</li>
        <li>查看下方的上传日志和统计信息</li>
      </ol>

      <el-alert title="注意事项" type="info" :closable="false" style="margin-top: 16px">
        <ul>
          <li>支持的文件格式：PDF (.pdf)</li>
          <li>文件大小限制：50MB</li>
          <li>上传后会在本地创建预览URL</li>
        </ul>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PDFPreview from '@/features/online-proofreading/components/PDFPreview.vue'

// 响应式数据
const currentPdfUrl = ref('')
const currentPage = ref(1)
const pdfStatus = ref('未加载')
const uploadCount = ref(0)
const uploadLogs = ref<
  Array<{
    timestamp: string
    type: 'success' | 'warning' | 'danger' | 'info'
    message: string
  }>
>([])

// 事件处理
const handlePageChange = (page: number) => {
  currentPage.value = page
  addLog('info', `切换到第 ${page} 页`)
}

const handlePositionChange = (position: number) => {
  // 可以在这里处理位置变化
}

const handlePDFUpload = (file: File) => {
  uploadCount.value++
  pdfStatus.value = '上传中'
  addLog('info', `开始上传PDF文件: ${file.name} (${formatFileSize(file.size)})`)
}

const handlePDFUploadSuccess = (url: string) => {
  currentPdfUrl.value = url
  pdfStatus.value = '已加载'
  currentPage.value = 1
  addLog('success', `PDF上传成功，已加载到预览区域`)
}

const handlePDFUploadError = (error: string) => {
  pdfStatus.value = '加载失败'
  addLog('danger', `PDF上传失败: ${error}`)
}

// 工具函数
const addLog = (type: 'success' | 'warning' | 'danger' | 'info', message: string) => {
  uploadLogs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    type,
    message,
  })

  // 限制日志数量
  if (uploadLogs.value.length > 10) {
    uploadLogs.value = uploadLogs.value.slice(0, 10)
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const loadSamplePDF = () => {
  const sampleUrl = '/sample.pdf'
  currentPdfUrl.value = sampleUrl
  pdfStatus.value = '已加载'
  currentPage.value = 1
  addLog('info', '加载示例PDF文件')
}

const clearPDF = () => {
  currentPdfUrl.value = ''
  pdfStatus.value = '未加载'
  currentPage.value = 1
  addLog('info', '清除PDF文件')
}
</script>

<style scoped>
.test-pdf-upload {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 24px;
  text-align: center;
}

.test-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.test-header p {
  color: #606266;
  font-size: 14px;
}

.test-section {
  margin-bottom: 32px;
}

.test-section h3 {
  color: #409eff;
  margin-bottom: 16px;
  font-size: 18px;
}

.test-controls {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-url {
  color: #606266;
  font-size: 12px;
  font-family: monospace;
}

.pdf-preview-wrapper {
  height: 600px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
}

.test-info {
  margin-bottom: 32px;
}

.upload-log {
  margin-top: 16px;
}

.upload-log h4 {
  color: #606266;
  margin-bottom: 12px;
}

.test-instructions {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.test-instructions h3 {
  color: #303133;
  margin-bottom: 16px;
}

.test-instructions ol {
  margin-bottom: 16px;
}

.test-instructions li {
  margin-bottom: 8px;
  color: #606266;
}

.test-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.test-instructions ul li {
  margin-bottom: 4px;
}
</style>
