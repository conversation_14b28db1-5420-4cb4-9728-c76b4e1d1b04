import{Q as e,M as a}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as l,r as t,X as u,b as n,c as s,a as o,Q as r,H as d,K as i,I as c,ag as p,o as v,u as m,M as _,O as f}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as h}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const g={class:"audio-batch-proofreading"},b={class:"content"},w={key:0,class:"upload-actions"},k={class:"progress-text"},x={key:0,class:"current-processing"},y={class:"batch-actions"},V={key:0,class:"play-content"},T=["src"],U={class:"audio-info"},z={class:"transcript-editor"},R={class:"speaker-editor"},B={class:"dialog-footer"},C=h(l({__name:"AudioBatchProofreadingView",setup(l){const h=t([]),C=t([]),L=t(!1),j=t(null),M=t(0),A=u({language:"zh",accuracy:"standard",speakerRecognition:!1,includeTimestamp:!0}),$=t(!1),O=t(null),I=t(!1),F=t("text"),S=t(""),E=t(null),K=t([]),P=n(()=>0===h.value.length?0:Math.round(C.value.length/h.value.length*100)),Q=n(()=>L.value?"active":100===P.value?"success":"normal"),G=e=>e.raw?.type.startsWith("audio/")?e.size&&e.size>209715200?(a.error("文件大小不能超过 200MB"),!1):void 0:(a.error("只能上传音频文件"),!1),H=e=>{const a=h.value.findIndex(a=>a.uid===e.uid);a>-1&&h.value.splice(a,1)},W=()=>{h.value=[],C.value=[]},X=async()=>{if(0!==h.value.length){L.value=!0,C.value=[];try{for(let e=0;e<h.value.length;e++){const a=h.value[e];j.value=a;for(let e=0;e<=100;e+=10)M.value=e,await new Promise(e=>setTimeout(e,200));const l={name:a.name,size:a.size,duration:"03:45",status:"处理完成",transcribedText:`这是音频 ${a.name} 的语音转文字结果示例...\n\n[00:00:10] 说话人A：大家好，欢迎收听本期节目\n[00:00:15] 说话人B：今天我们来讨论AI智能审校系统\n[00:00:20] 说话人A：这个系统有哪些主要功能呢？\n[00:00:25] 说话人B：主要包括文档审校、多媒体处理等功能...`,url:URL.createObjectURL(a.raw),originalFile:a,speakers:[{startTime:"00:00:10",endTime:"00:00:15",speaker:"说话人A",text:"大家好，欢迎收听本期节目"},{startTime:"00:00:15",endTime:"00:00:20",speaker:"说话人B",text:"今天我们来讨论AI智能审校系统"},{startTime:"00:00:20",endTime:"00:00:25",speaker:"说话人A",text:"这个系统有哪些主要功能呢？"}]};C.value.push(l),M.value=0}a.success("批量处理完成")}catch(e){a.error("批量处理失败")}finally{L.value=!1,j.value=null}}else a.warning("请先上传音频文件")},q=e=>{const a=e.target,l=Math.floor(a.duration),t=Math.floor(l/60),u=l%60;O.value&&(O.value.duration=`${t.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}`)},D=()=>{E.value&&(E.value.transcribedText=S.value,a.success("保存成功")),I.value=!1},J=()=>{if(0===C.value.length)return void a.warning("没有可导出的结果");const e=C.value.map(e=>`文件名: ${e.name}\n时长: ${e.duration}\n转录内容:\n${e.transcribedText}\n\n`).join("---\n\n"),l=new Blob([e],{type:"text/plain"}),t=URL.createObjectURL(l),u=document.createElement("a");u.href=t,u.download="batch_transcript_results.txt",u.click(),URL.revokeObjectURL(t),a.success("导出成功")},N=()=>{0!==C.value.length?a.success("已提交审查，文件将转入待审查列表"):a.warning("没有可提交的结果")},Y=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":e<1073741824?(e/1048576).toFixed(1)+" MB":(e/1073741824).toFixed(1)+" GB",Z=e=>{switch(e){case"处理完成":return"success";case"处理中":return"warning";case"处理失败":return"danger";default:return"info"}};return(a,l)=>{const t=p("el-icon"),u=p("el-upload"),n=p("el-button"),ee=p("el-card"),ae=p("el-option"),le=p("el-select"),te=p("el-form-item"),ue=p("el-col"),ne=p("el-switch"),se=p("el-row"),oe=p("el-progress"),re=p("el-table-column"),de=p("el-tag"),ie=p("el-input"),ce=p("el-table"),pe=p("el-dialog"),ve=p("el-tab-pane"),me=p("el-tabs");return v(),s("div",g,[l[25]||(l[25]=o("div",{class:"page-header"},[o("h1",null,"音频批量审校"),o("p",{class:"page-description"},"批量上传音频进行语音转文字和文字校对")],-1)),o("div",b,[r(ee,{class:"upload-card",title:"批量上传音频"},{default:c(()=>[r(u,{class:"upload-demo",drag:"",action:"#",multiple:"",accept:"audio/*","auto-upload":!1,"file-list":h.value,"on-change":G,"on-remove":H},{tip:c(()=>l[9]||(l[9]=[o("div",{class:"el-upload__tip"}," 支持 mp3/wav/m4a/aac 格式，单个文件不超过 200MB，最多可上传 30 个文件 ",-1)])),default:c(()=>[r(t,{class:"el-icon--upload"},{default:c(()=>[r(m(e))]),_:1}),l[10]||(l[10]=o("div",{class:"el-upload__text"},[_("将音频拖拽到此处，或"),o("em",null,"点击批量上传")],-1))]),_:1,__:[10]},8,["file-list"]),h.value.length>0?(v(),s("div",w,[r(n,{type:"primary",onClick:X,loading:L.value},{default:c(()=>l[11]||(l[11]=[_(" 开始批量处理 ")])),_:1,__:[11]},8,["loading"]),r(n,{onClick:W},{default:c(()=>l[12]||(l[12]=[_("清空文件")])),_:1,__:[12]})])):i("",!0)]),_:1}),h.value.length>0?(v(),d(ee,{key:0,title:"处理设置",class:"settings-card"},{default:c(()=>[r(se,{gutter:20},{default:c(()=>[r(ue,{span:6},{default:c(()=>[r(te,{label:"识别语言"},{default:c(()=>[r(le,{modelValue:A.language,"onUpdate:modelValue":l[0]||(l[0]=e=>A.language=e),placeholder:"选择语言"},{default:c(()=>[r(ae,{label:"中文",value:"zh"}),r(ae,{label:"英文",value:"en"}),r(ae,{label:"中英混合",value:"zh-en"}),r(ae,{label:"粤语",value:"yue"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),r(ue,{span:6},{default:c(()=>[r(te,{label:"识别精度"},{default:c(()=>[r(le,{modelValue:A.accuracy,"onUpdate:modelValue":l[1]||(l[1]=e=>A.accuracy=e),placeholder:"选择精度"},{default:c(()=>[r(ae,{label:"快速",value:"fast"}),r(ae,{label:"标准",value:"standard"}),r(ae,{label:"高精度",value:"high"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),r(ue,{span:6},{default:c(()=>[r(te,{label:"说话人识别"},{default:c(()=>[r(ne,{modelValue:A.speakerRecognition,"onUpdate:modelValue":l[2]||(l[2]=e=>A.speakerRecognition=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),r(ue,{span:6},{default:c(()=>[r(te,{label:"时间戳"},{default:c(()=>[r(ne,{modelValue:A.includeTimestamp,"onUpdate:modelValue":l[3]||(l[3]=e=>A.includeTimestamp=e),"active-text":"包含","inactive-text":"不包含"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})):i("",!0),L.value||C.value.length>0?(v(),d(ee,{key:1,title:"处理进度",class:"progress-card"},{default:c(()=>[r(oe,{percentage:P.value,status:Q.value,"stroke-width":8},null,8,["percentage","status"]),o("p",k," 已处理 "+f(C.value.length)+" / "+f(h.value.length)+" 个文件 ",1),j.value?(v(),s("div",x,[o("p",null,"正在处理："+f(j.value.name),1),r(oe,{percentage:M.value,"stroke-width":6,"show-text":!1},null,8,["percentage"])])):i("",!0)]),_:1})):i("",!0),C.value.length>0?(v(),d(ee,{key:2,title:"处理结果",class:"results-card"},{default:c(()=>[r(ce,{data:C.value,style:{width:"100%"}},{default:c(()=>[r(re,{prop:"name",label:"文件名",width:"200"}),r(re,{prop:"duration",label:"时长",width:"100"}),r(re,{prop:"size",label:"文件大小",width:"100"},{default:c(e=>[_(f(Y(e.row.size)),1)]),_:1}),r(re,{prop:"status",label:"处理状态",width:"120"},{default:c(e=>[r(de,{type:Z(e.row.status)},{default:c(()=>[_(f(e.row.status),1)]),_:2},1032,["type"])]),_:1}),r(re,{prop:"transcribedText",label:"转录文字","min-width":"300"},{default:c(e=>[r(ie,{modelValue:e.row.transcribedText,"onUpdate:modelValue":a=>e.row.transcribedText=a,type:"textarea",rows:3,placeholder:"语音转文字结果...",readonly:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),r(re,{label:"操作",width:"250"},{default:c(e=>[r(n,{size:"small",onClick:a=>{return l=e.row,O.value=l,void($.value=!0);var l}},{default:c(()=>l[13]||(l[13]=[_("播放")])),_:2,__:[13]},1032,["onClick"]),r(n,{size:"small",type:"primary",onClick:a=>{return l=e.row,E.value=l,S.value=l.transcribedText,K.value=l.speakers||[],void(I.value=!0);var l}},{default:c(()=>l[14]||(l[14]=[_("编辑")])),_:2,__:[14]},1032,["onClick"]),r(n,{size:"small",onClick:a=>(e=>{const a=new Blob([e.transcribedText],{type:"text/plain"}),l=URL.createObjectURL(a),t=document.createElement("a");t.href=l,t.download=`${e.name}_transcript.txt`,t.click(),URL.revokeObjectURL(l)})(e.row)},{default:c(()=>l[15]||(l[15]=[_("下载")])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),o("div",y,[r(n,{type:"primary",onClick:J},{default:c(()=>l[16]||(l[16]=[_("导出全部结果")])),_:1,__:[16]}),r(n,{onClick:N},{default:c(()=>l[17]||(l[17]=[_("提交审查")])),_:1,__:[17]})])]),_:1})):i("",!0)]),r(pe,{modelValue:$.value,"onUpdate:modelValue":l[4]||(l[4]=e=>$.value=e),title:"音频播放",width:"60%"},{default:c(()=>[O.value?(v(),s("div",V,[o("audio",{src:O.value.url,controls:"",class:"audio-player",onLoadedmetadata:q},null,40,T),o("div",U,[o("p",null,[l[18]||(l[18]=o("strong",null,"文件名：",-1)),_(f(O.value.name),1)]),o("p",null,[l[19]||(l[19]=o("strong",null,"时长：",-1)),_(f(O.value.duration),1)]),o("p",null,[l[20]||(l[20]=o("strong",null,"文件大小：",-1)),_(f(Y(O.value.size)),1)])]),l[21]||(l[21]=o("div",{class:"waveform-container"},[o("h4",null,"音频波形"),o("div",{class:"waveform-placeholder"},[o("p",null,"音频波形显示区域（开发中）")])],-1))])):i("",!0)]),_:1},8,["modelValue"]),r(pe,{modelValue:I.value,"onUpdate:modelValue":l[8]||(l[8]=e=>I.value=e),title:"编辑转录文字",width:"60%"},{footer:c(()=>[o("span",B,[r(n,{onClick:l[7]||(l[7]=e=>I.value=!1)},{default:c(()=>l[23]||(l[23]=[_("取消")])),_:1,__:[23]}),r(n,{type:"primary",onClick:D},{default:c(()=>l[24]||(l[24]=[_("保存")])),_:1,__:[24]})])]),default:c(()=>[o("div",z,[r(me,{modelValue:F.value,"onUpdate:modelValue":l[6]||(l[6]=e=>F.value=e)},{default:c(()=>[r(ve,{label:"文字编辑",name:"text"},{default:c(()=>[r(ie,{modelValue:S.value,"onUpdate:modelValue":l[5]||(l[5]=e=>S.value=e),type:"textarea",rows:15,placeholder:"请编辑转录文字..."},null,8,["modelValue"])]),_:1}),r(ve,{label:"说话人标记",name:"speaker"},{default:c(()=>[o("div",R,[l[22]||(l[22]=o("p",null,"说话人标记功能（开发中）",-1)),r(ce,{data:K.value,style:{width:"100%"}},{default:c(()=>[r(re,{prop:"startTime",label:"开始时间",width:"120"}),r(re,{prop:"endTime",label:"结束时间",width:"120"}),r(re,{prop:"speaker",label:"说话人",width:"100"}),r(re,{prop:"text",label:"内容"})]),_:1},8,["data"])])]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-64c049ad"]]);export{C as default};
