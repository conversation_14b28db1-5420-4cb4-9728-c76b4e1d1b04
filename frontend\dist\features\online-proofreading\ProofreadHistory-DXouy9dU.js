import{d as s,r as a,m as e,c as r,Q as o,I as t,ag as d,o as l,a as n,M as p}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as u}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const c={class:"proofread-history"},i={class:"card-header"},_={class:"content"},f=u(s({__name:"ProofreadHistory",setup:s=>(a(!1),e(()=>{}),(s,a)=>{const e=d("el-button"),u=d("el-empty"),f=d("el-card");return l(),r("div",c,[o(f,null,{header:t(()=>[n("div",i,[a[1]||(a[1]=n("span",null,"校对历史",-1)),o(e,{type:"info",size:"small"},{default:t(()=>a[0]||(a[0]=[p(" 清理历史 ")])),_:1,__:[0]})])]),default:t(()=>[n("div",_,[a[2]||(a[2]=n("p",null,"校对历史页面 - 开发中...",-1)),o(u,{description:"暂无历史记录"})])]),_:1})])})}),[["__scopeId","data-v-e597ded4"]]);export{f as default};
