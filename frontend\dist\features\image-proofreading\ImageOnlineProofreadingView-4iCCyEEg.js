import{a7 as e,g as l,Y as a,ab as t,ac as u,p as s,z as n,S as d,M as r}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as i,r as o,X as c,b as v,c as p,a as f,Q as _,H as m,K as g,I as h,ag as b,o as y,M as w,u as k,O as x,P as C,a6 as V,C as R,D as O}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as U}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const z={class:"image-online-proofreading"},j={class:"content"},L={class:"toolbar"},B={class:"toolbar-left"},$={class:"toolbar-right"},I={key:0,class:"image-container"},M=["src"],P={class:"image-info"},F={key:0,class:"ocr-overlay"},H=["onClick"],K={key:1,class:"empty-image"},T={class:"panel-header"},W={class:"header-actions"},D={class:"text-editor"},E={key:0,class:"text-stats"},Q={class:"dialog-footer"},S=U(i({__name:"ImageOnlineProofreadingView",setup(i){const U=o(null),S=o(""),X=o(!1),Y=o(""),q=o([]),A=o(-1),G=o(!1),J=c({language:"zh",accuracy:"standard",format:"text"}),N=o(!1),Z=o(!1),ee=c({filename:"",remarks:""}),le=v(()=>{if(!Y.value)return{characters:0,words:0,lines:0,paragraphs:0};const e=Y.value;return{characters:e.length,words:e.split(/\s+/).filter(e=>e.length>0).length,lines:e.split("\n").length,paragraphs:e.split(/\n\s*\n/).filter(e=>e.trim().length>0).length}}),ae=e=>e.type.startsWith("image/")?e.size>10485760?(r.error("文件大小不能超过 10MB"),!1):(U.value={name:e.name,size:e.size,url:URL.createObjectURL(e),file:e},Y.value="",q.value=[],A.value=-1,N.value=!1,r.success("图片上传成功"),!1):(r.error("只能上传图片文件"),!1),te=e=>{const l=e.target;S.value=`${l.naturalWidth} × ${l.naturalHeight}`},ue=async()=>{if(U.value){X.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),Y.value=`这是图片 ${U.value.name} 的OCR识别结果示例。\n\n这里是第一段文字内容，包含了图片中的主要文字信息。OCR技术能够准确识别图片中的文字，并转换为可编辑的文本格式。\n\n这是第二段内容，展示了多段落文字的识别效果。您可以直接在右侧编辑区域修改识别结果，进行人工校对和优化。\n\n识别完成后，您可以：\n1. 直接编辑文字内容\n2. 复制或导出文字\n3. 保存审校结果\n4. 提交最终版本`,q.value=[{x:10,y:10,width:200,height:30},{x:10,y:50,width:300,height:60},{x:10,y:120,width:250,height:80},{x:10,y:210,width:180,height:100}],r.success("OCR识别完成")}catch{r.error("OCR识别失败")}finally{X.value=!1}}else r.warning("请先上传图片")},se=()=>{N.value=!0},ne=e=>({left:e.x+"px",top:e.y+"px",width:e.width+"px",height:e.height+"px"}),de=async()=>{if(Y.value)try{await navigator.clipboard.writeText(Y.value),r.success("文字已复制到剪贴板")}catch{r.error("复制失败")}else r.warning("没有可复制的文字")},re=()=>{if(!Y.value)return void r.warning("没有可导出的文字");const e=new Blob([Y.value],{type:"text/plain"}),l=URL.createObjectURL(e),a=document.createElement("a");a.href=l,a.download=`${U.value?.name||"ocr_result"}_text.txt`,a.click(),URL.revokeObjectURL(l),r.success("导出成功")},ie=()=>{U.value=null,Y.value="",q.value=[],A.value=-1,N.value=!1,S.value=""},oe=()=>{r.success("设置已应用"),G.value=!1},ce=()=>{U.value&&Y.value?(ee.filename=U.value.name.replace(/\.[^/.]+$/,"")+"_ocr_result",ee.remarks="",Z.value=!0):r.warning("没有可保存的内容")},ve=()=>{ee.filename.trim()?(r.success("保存成功"),N.value=!1,Z.value=!1):r.warning("请输入文件名称")},pe=()=>{U.value&&Y.value?r.success("审校结果已提交"):r.warning("请先完成OCR识别")};return(r,i)=>{const o=b("el-icon"),c=b("el-button"),v=b("el-upload"),fe=b("el-card"),_e=b("el-col"),me=b("el-input"),ge=b("el-statistic"),he=b("el-row"),be=b("el-option"),ye=b("el-select"),we=b("el-form-item"),ke=b("el-form"),xe=b("el-dialog");return y(),p("div",z,[i[23]||(i[23]=f("div",{class:"page-header"},[f("h1",null,"图片在线审校"),f("p",{class:"page-description"},"在线实时进行图片OCR识别和文字校对")],-1)),f("div",j,[_(fe,{class:"toolbar-card"},{default:h(()=>[f("div",L,[f("div",B,[_(v,{action:"#","show-file-list":!1,"before-upload":ae,accept:"image/*"},{default:h(()=>[_(c,{type:"primary"},{default:h(()=>[_(o,null,{default:h(()=>[_(k(e))]),_:1}),i[8]||(i[8]=w(" 上传图片 "))]),_:1,__:[8]})]),_:1}),_(c,{onClick:ue,disabled:!U.value,loading:X.value},{default:h(()=>[_(o,null,{default:h(()=>[_(k(l))]),_:1}),i[9]||(i[9]=w(" 开始OCR "))]),_:1,__:[9]},8,["disabled","loading"]),_(c,{onClick:ie},{default:h(()=>[_(o,null,{default:h(()=>[_(k(a))]),_:1}),i[10]||(i[10]=w(" 清空 "))]),_:1,__:[10]})]),f("div",$,[_(c,{onClick:ce,disabled:!N.value},{default:h(()=>[_(o,null,{default:h(()=>[_(k(t))]),_:1}),i[11]||(i[11]=w(" 保存 "))]),_:1,__:[11]},8,["disabled"]),_(c,{type:"success",onClick:pe,disabled:!U.value},{default:h(()=>[_(o,null,{default:h(()=>[_(k(u))]),_:1}),i[12]||(i[12]=w(" 提交审校 "))]),_:1,__:[12]},8,["disabled"])])])]),_:1}),_(he,{gutter:20,class:"workspace"},{default:h(()=>[_(_e,{span:12},{default:h(()=>[_(fe,{title:"图片预览",class:"image-panel"},{default:h(()=>{return[U.value?(y(),p("div",I,[f("img",{src:U.value.url,alt:"当前图片",class:"preview-image",onLoad:te},null,40,M),f("div",P,[f("p",null,[i[13]||(i[13]=f("strong",null,"文件名：",-1)),w(x(U.value.name),1)]),f("p",null,[i[14]||(i[14]=f("strong",null,"文件大小：",-1)),w(x((e=U.value.size,e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":(e/1048576).toFixed(1)+" MB")),1)]),f("p",null,[i[15]||(i[15]=f("strong",null,"图片尺寸：",-1)),w(x(S.value),1)])]),q.value.length>0?(y(),p("div",F,[(y(!0),p(C,null,V(q.value,(e,l)=>(y(),p("div",{key:l,class:O(["ocr-region",{active:A.value===l}]),style:R(ne(e)),onClick:e=>(e=>{A.value=e})(l)},x(l+1),15,H))),128))])):g("",!0)])):(y(),p("div",K,[_(o,{size:"64"},{default:h(()=>[_(k(s))]),_:1}),i[16]||(i[16]=f("p",null,"请上传图片开始审校",-1))]))];var e}),_:1})]),_:1}),_(_e,{span:12},{default:h(()=>[_(fe,{title:"OCR识别结果",class:"text-panel"},{header:h(()=>[f("div",T,[i[19]||(i[19]=f("span",null,"OCR识别结果",-1)),f("div",W,[_(c,{size:"small",onClick:de,disabled:!Y.value},{default:h(()=>[_(o,null,{default:h(()=>[_(k(n))]),_:1}),i[17]||(i[17]=w(" 复制 "))]),_:1,__:[17]},8,["disabled"]),_(c,{size:"small",onClick:re,disabled:!Y.value},{default:h(()=>[_(o,null,{default:h(()=>[_(k(d))]),_:1}),i[18]||(i[18]=w(" 导出 "))]),_:1,__:[18]},8,["disabled"])])])]),default:h(()=>[f("div",D,[_(me,{modelValue:Y.value,"onUpdate:modelValue":i[0]||(i[0]=e=>Y.value=e),type:"textarea",rows:20,placeholder:"OCR识别结果将显示在这里，您可以直接编辑...",onInput:se},null,8,["modelValue"])]),Y.value?(y(),p("div",E,[_(he,{gutter:10},{default:h(()=>[_(_e,{span:6},{default:h(()=>[_(ge,{title:"字符数",value:le.value.characters},null,8,["value"])]),_:1}),_(_e,{span:6},{default:h(()=>[_(ge,{title:"单词数",value:le.value.words},null,8,["value"])]),_:1}),_(_e,{span:6},{default:h(()=>[_(ge,{title:"行数",value:le.value.lines},null,8,["value"])]),_:1}),_(_e,{span:6},{default:h(()=>[_(ge,{title:"段落数",value:le.value.paragraphs},null,8,["value"])]),_:1})]),_:1})])):g("",!0)]),_:1})]),_:1})]),_:1}),G.value?(y(),m(fe,{key:0,title:"OCR设置",class:"settings-panel"},{default:h(()=>[_(he,{gutter:20},{default:h(()=>[_(_e,{span:6},{default:h(()=>[_(we,{label:"识别语言"},{default:h(()=>[_(ye,{modelValue:J.language,"onUpdate:modelValue":i[1]||(i[1]=e=>J.language=e),placeholder:"选择语言"},{default:h(()=>[_(be,{label:"中文",value:"zh"}),_(be,{label:"英文",value:"en"}),_(be,{label:"中英混合",value:"zh-en"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),_(_e,{span:6},{default:h(()=>[_(we,{label:"识别精度"},{default:h(()=>[_(ye,{modelValue:J.accuracy,"onUpdate:modelValue":i[2]||(i[2]=e=>J.accuracy=e),placeholder:"选择精度"},{default:h(()=>[_(be,{label:"快速",value:"fast"}),_(be,{label:"标准",value:"standard"}),_(be,{label:"高精度",value:"high"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),_(_e,{span:6},{default:h(()=>[_(we,{label:"输出格式"},{default:h(()=>[_(ye,{modelValue:J.format,"onUpdate:modelValue":i[3]||(i[3]=e=>J.format=e),placeholder:"选择格式"},{default:h(()=>[_(be,{label:"纯文本",value:"text"}),_(be,{label:"保留格式",value:"formatted"}),_(be,{label:"表格识别",value:"table"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),_(_e,{span:6},{default:h(()=>[_(we,null,{default:h(()=>[_(c,{type:"primary",onClick:oe},{default:h(()=>i[20]||(i[20]=[w("应用设置")])),_:1,__:[20]})]),_:1})]),_:1})]),_:1})]),_:1})):g("",!0)]),_(xe,{modelValue:Z.value,"onUpdate:modelValue":i[7]||(i[7]=e=>Z.value=e),title:"保存审校结果",width:"40%"},{footer:h(()=>[f("span",Q,[_(c,{onClick:i[6]||(i[6]=e=>Z.value=!1)},{default:h(()=>i[21]||(i[21]=[w("取消")])),_:1,__:[21]}),_(c,{type:"primary",onClick:ve},{default:h(()=>i[22]||(i[22]=[w("确认保存")])),_:1,__:[22]})])]),default:h(()=>[_(ke,{model:ee,"label-width":"100px"},{default:h(()=>[_(we,{label:"文件名称"},{default:h(()=>[_(me,{modelValue:ee.filename,"onUpdate:modelValue":i[4]||(i[4]=e=>ee.filename=e),placeholder:"请输入文件名称"},null,8,["modelValue"])]),_:1}),_(we,{label:"备注说明"},{default:h(()=>[_(me,{modelValue:ee.remarks,"onUpdate:modelValue":i[5]||(i[5]=e=>ee.remarks=e),type:"textarea",rows:3,placeholder:"请输入备注说明（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-ba23f69c"]]);export{S as default};
