# 🚀 阶段1: 项目初始化

### 1.1 环境搭建

#### 📋 任务目标
搭建Django后端和Vue3前端的开发环境，确保所有依赖正确安装。

#### 📥 输入要求
- 确认Python 3.11+已安装
- 确认Node.js 18+已安装
- 确认Git已配置
## 第一问  环境搭建 (原始提示词)
#### 💬 Augment Code对话模板
```
我需要搭建AI智能审校系统的开发环境，请严格按照Django和Vue3最佳实践帮我：

1. 创建Django 5后端项目结构
   - 项目名称：backend
   - 应用模块：documents, diff_engine, ai_proofreading, users, annotations
   - 配置Django REST Framework (JWT认证、权限控制、分页)
   - 配置CORS (django-cors-headers)
   - 配置MySQL和Redis连接 (支持多环境配置)
   - 遵循MVT架构模式，使用自定义Manager和Signal

2. 创建Vue3前端项目结构
   - 项目名称：frontend
   - 使用TypeScript (严格模式)
   - 集成Element Plus UI库
   - 配置Vite构建工具 (代码分割、懒加载)
   - 配置路由和状态管理 (Vue Router + Pinia)
   - 采用Composition API和特性驱动目录结构

3. 开发工具配置
   - Django: Black格式化 + flake8检查 + pytest测试
   - Vue3: ESLint + Prettier + Vitest测试
   - 生成requirements.txt和package.json文件

请提供完整的项目初始化脚本和配置文件，确保符合最佳实践。

```

## 第一问  环境搭建 (提示词优化)

我需要搭建AI智能审校系统的开发环境，请严格按照Django 5和Vue3最佳实践帮我完成项目初始化：

**项目背景**：
- 项目名称：ProofreadAI (AI智能审校系统)
- 技术架构：前后端分离，Django 5 + Vue3 + TypeScript
- 开发环境：Python 3.11+, Node.js 18+
- 数据库：MySQL 8.0 + Redis 7.0
- 部署方式：Docker容器化

**1. Django 5后端项目结构创建**
项目要求：
- 项目根目录：`backend/`
- 应用模块：documents(文档管理), diff_engine(差异对比引擎), ai_proofreading(AI校对服务), users(用户管理), annotations(批注系统)
- 严格遵循MVT架构模式和DRY原则

技术配置要求：
- Django REST Framework：实现ViewSet、序列化器、JWT认证、基于角色的权限控制、分页(PageNumberPagination)
- CORS配置：使用django-cors-headers，支持前端开发环境跨域
- 数据库配置：MySQL主数据库 + Redis缓存，支持开发/测试/生产多环境配置
- 自定义组件：BaseModel基类、自定义Manager、Signal信号处理
- 安全配置：SECRET_KEY管理、CSRF保护、XSS防护

**2. Vue3前端项目结构创建**
项目要求：
- 项目根目录：`frontend/`
- 使用Vue3 Composition API + TypeScript严格模式
- UI框架：Element Plus + 自定义主题
- 构建工具：Vite 4+ (支持HMR、代码分割、Tree Shaking)

技术配置要求：
- 路由管理：Vue Router 4 (懒加载、路由守卫、动态路由)
- 状态管理：Pinia (模块化store、状态持久化、TypeScript支持)
- 目录结构：特性驱动架构 (features/document-compare/, components/, composables/, utils/, types/)
- 组件规范：PascalCase命名、props/emits验证、
```
"<script setup>语法"
```
**3. 开发工具和质量保证配置**
Django开发工具：
- 代码格式化：Black + isort
- 代码检查：flake8 + mypy (类型检查)
- 测试框架：pytest + pytest-django + Factory Boy
- 开发工具：Django Debug Toolbar + django-extensions

Vue3开发工具：
- 代码规范：ESLint + @vue/eslint-config-typescript + Prettier
- 测试框架：Vitest + Vue Test Utils + @testing-library/vue
- 类型检查：vue-tsc + TypeScript strict模式
- 开发工具：Vue DevTools + Vite插件

**4. 环境配置和依赖管理**
- 环境变量：使用.env文件管理，支持.env.development/.env.production
- Python依赖：生成requirements/base.txt, requirements/development.txt, requirements/production.txt
- Node.js依赖：package.json包含开发和生产依赖分离
- Docker配置：提供Dockerfile和docker-compose.yml用于容器化开发

**交付要求**：
请提供以下完整文件和脚本：
1. 项目初始化脚本 (setup.sh 或 setup.bat)
2. Django项目完整配置文件 (settings/, urls.py, wsgi.py等)
3. Vue3项目完整配置文件 (vite.config.ts, tsconfig.json, package.json等)
4. 开发工具配置文件 (.eslintrc.js, .prettierrc, pyproject.toml等)
5. 环境配置模板 (.env.example)
6. Docker配置文件 (Dockerfile, docker-compose.yml)
7. 项目README.md (包含安装和运行说明)

**验收标准**：
- Django项目可以正常启动 (python manage.py runserver)
- Vue3项目可以正常启动 (npm run dev)
- 前后端可以正常通信 (CORS配置正确)
- 数据库连接测试通过
- 代码格式化和检查工具正常工作
- 所有配置文件包含详细中文注释
- 遵循PEP 8和ESLint规范


## 第二问 请数据库的配置（原始提示词）：
本地图书数据使用SQLite数据库
远程用户管理、相关设置等使用MySQL数据库分离设计。
请数据库的配置：
远程数据库的配置：
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'book-proofread',  # 数据库名称
        'USER': 'book-proofread',  # 数据库用户名
        'PASSWORD': 'AdwpJ364J2FcsBP6',  # 数据库密码
        'HOST': '***********',  # 数据库主机地址
        'PORT': '3306',  # 数据库端口，默认是 '3306'
    }
}

本地数据库：
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'book.db,
    }
}

添加到配置文件中，确定现在为开发环境，不是生产环境




## 第二问 请数据库的配置（提示词优化）：

请为AI智能审校系统配置双数据库架构，实现本地和远程数据库的分离设计：

**需求说明**：
- 本地图书数据使用SQLite数据库存储
- 远程用户管理、系统设置等使用MySQL数据库
- 当前为开发环境配置，非生产环境

**具体配置要求**：

1. **修改Django数据库配置**：
   - 文件位置：`backend/config/settings/development.py`
   - 配置双数据库：'default'(MySQL远程) 和 'local'(SQLite本地)

2. **远程MySQL数据库配置**：
   ```python
   'default': {
       'ENGINE': 'django.db.backends.mysql',
       'NAME': 'book-proofread',
       'USER': 'book-proofread',
       'PASSWORD': 'AdwpJ364J2FcsBP6',
       'HOST': '***********',
       'PORT': '3306',
   }
   ```

3. **本地SQLite数据库配置**：
   ```python
   'local': {
       'ENGINE': 'django.db.backends.sqlite3',
       'NAME': BASE_DIR / 'book.db',
   }
   ```

4. **数据库路由配置**：
   - 用户管理(users)、系统设置等模型使用MySQL远程数据库
   - 图书数据相关模型使用SQLite本地数据库
   - 需要创建数据库路由器(DATABASE_ROUTERS)实现自动路由

5. **环境变量配置**：
   - 在`.env`文件中添加远程数据库连接信息
   - 确保开发环境标识正确设置

请按照Django最佳实践实现这个双数据库配置，包括必要的数据库路由器和迁移文件管理。



## 第三问 在frontend和backend目录下分别创建test文件夹/WangEditor富文本编辑器集成（原始提示词）：
1. 在frontend和backend目录下分别创建test文件夹，用于存放单元测试和集成测试代码。
2. wangeditor富文本编辑器参考：
https://github.com/wangfupeng1988/vue3-wangeditor-demo
https://www.wangeditor.com/v5/getting-started.html
https://github.com/wangeditor-team/wangEditor

## 第三问 在frontend和backend目录下分别创建test文件夹/WangEditor富文本编辑器集成（提示词优化）：

请按照以下具体要求完成测试目录创建和富文本编辑器集成任务：

## 1. 测试目录结构创建
在项目根目录下的 `frontend` 和 `backend` 目录中分别创建标准化的测试目录结构：

### Frontend测试目录 (frontend/tests/)
- `unit/` - Vue3组件单元测试，使用Vitest + Vue Test Utils
- `integration/` - 前端集成测试，测试组件间交互
- `e2e/` - 端到端测试（如使用Cypress或Playwright）
- `fixtures/` - 测试数据和模拟数据
- `utils/` - 测试工具函数和辅助方法
- `setup.ts` - 测试环境配置文件

### Backend测试目录 (backend/tests/)
- `unit/` - Django模型、视图、序列化器的单元测试
- `integration/` - API集成测试，测试完整的请求-响应流程
- `fixtures/` - 测试数据fixtures和工厂类
- `utils/` - 测试工具函数和辅助方法
- `conftest.py` - pytest配置文件
- `test_settings.py` - 测试环境的Django设置

## 2. WangEditor富文本编辑器集成
基于提供的参考资源，在Vue3前端项目中集成WangEditor v5富文本编辑器：

### 集成要求：
- 使用TypeScript进行类型安全的开发
- 与Element Plus UI框架保持设计一致性
- 支持图片上传功能，集成后端API接口
- 配置工具栏，包含常用的文本编辑功能
- 实现双向数据绑定，支持v-model
- 添加表单验证支持
- 确保编辑器内容的XSS安全防护

### 参考资源：
- Vue3集成示例：https://github.com/wangfupeng1988/vue3-wangeditor-demo
- 官方文档：https://www.wangeditor.com/v5/getting-started.html
- 源码仓库：https://github.com/wangeditor-team/wangEditor

### 实现步骤：
1. 安装WangEditor依赖包
2. 创建可复用的富文本编辑器组件
3. 配置编辑器选项和工具栏
4. 实现图片上传接口对接
5. 添加组件的单元测试
6. 编写使用文档和示例代码

请确保所有新增代码都有详细的中文注释，并遵循项目的代码规范和架构模式。


## 第四问  验证Django后端项目启动和基本功能测试 ✅ 已完成

**验证时间**: 2025年6月22日 11:31
**验证状态**: ✅ 成功完成

### 验证执行步骤：

1. **环境准备** ✅
   - 修复了用户应用配置问题
   - 启用了自定义用户模型 (`AUTH_USER_MODEL = 'users.User'`)
   - 解决了用户模型related_name冲突

2. **数据库迁移** ✅
   - 清理了冲突的数据库文件
   - 重新生成迁移文件：`python manage.py makemigrations`
   - 成功执行迁移：`python manage.py migrate`
   - 创建超级用户：<EMAIL> / admin123456

3. **启动开发服务器** ✅
   - 成功启动：`python manage.py runserver 127.0.0.1:8000`
   - 服务器运行在：http://127.0.0.1:8000
   - 无错误信息，系统检查通过

4. **基本功能验证** ✅
   - ✅ API根路径：http://127.0.0.1:8000/ (显示美观的首页)
   - ✅ 管理后台：http://127.0.0.1:8000/admin/ (可正常登录)
   - ✅ API文档：http://127.0.0.1:8000/api/docs/ (Swagger UI正常)
   - ✅ ReDoc文档：http://127.0.0.1:8000/api/redoc/ (文档正常显示)
   - ✅ 健康检查：http://127.0.0.1:8000/health/ (系统状态良好)
   - ✅ 性能分析：http://127.0.0.1:8000/silk/ (开发工具正常)

5. **测试新增功能** ✅
   - ✅ 文件上传API配置完成
   - ✅ WangEditor后端接口准备就绪
   - ✅ 测试目录结构正确创建 (backend/tests/)

6. **验证结果总结**
   - **Django服务器启动状态**: ✅ 成功
   - **API端点访问状态**: ✅ 所有端点正常响应
   - **错误信息**: 无错误
   - **新增功能验证**: ✅ 全部通过

**详细验证报告**: 参见 `DJANGO_BACKEND_VERIFICATION_COMPLETE.md`



## 第五问  验收标准 Vue3项目可以正常启动 (npm run dev)（提示词优化）

#### ✅ 验收标准 - Vue3前端项目启动验证

**主要目标：**
- [ ] Vue3前端项目能够成功启动并运行在开发环境

**具体验收要求：**
- [ ] 执行 `npm run dev` 命令后，项目能够正常启动
- [ ] 开发服务器成功运行在默认端口（通常是3000或5173）
- [ ] 浏览器能够正常访问前端应用首页
- [ ] 控制台无严重错误信息（允许警告）
- [ ] 热重载功能正常工作（修改代码后页面自动刷新）

**前置条件：**
- [ ] 已安装Node.js（版本 >= 16）
- [ ] 已安装项目依赖（npm install 或 yarn install）
- [ ] 项目配置文件正确（如vite.config.js、package.json等）

**验证步骤：**
1. 进入frontend目录
2. 执行 `npm run dev` 或 `yarn dev`
3. 确认终端显示开发服务器启动成功信息
4. 在浏览器中访问显示的本地地址
5. 验证页面正常加载和功能可用

**成功标准：**
- 命令执行无错误退出
- 浏览器能正常显示Vue3应用界面
- 开发工具和热重载功能正常




## 第六问 在processDoc文件夹中创建一个名为*_COMPLETE.md的文件


1. **文件夹位置**：`d:\AIpreadfrood\processDoc`
2. **文件命名规范**：`[任务名称]_COMPLETE.md`，例如：
   - `frontend_setup_COMPLETE.md`
   - `wangeditor_integration_COMPLETE.md`
   - `database_configuration_COMPLETE.md`
3. **文件内容要求**：每个完成报告应包含：
   - 任务概述和目标
   - 完成的具体工作内容
   - 遇到的问题及解决方案
   - 测试验证结果
   - 相关文件和代码变更清单
   - 后续建议或注意事项
4. **文件格式**：使用Markdown格式，便于版本控制和阅读

请创建该文件夹结构，并准备好用于记录各个开发任务的完成情况。

##  项目验收标准:数据库连接配置验证\前后端通信配置验证\依赖包安装验证\功能模块验证\开发环境验证


### 数据库连接配置验证
- [ ] **SQLite本地数据库**：确认 `backend/db.sqlite3` 文件存在且可正常读写
- [ ] **MySQL远程数据库**：验证 `backend/config/settings/base.py` 中的数据库路由配置正确
- [ ] **数据库迁移**：运行 `python manage.py migrate` 无错误，所有表结构创建成功
- [ ] **数据库连接测试**：执行 `python backend/scripts/verify_dual_db.py` 脚本验证双数据库连接正常

### 前后端通信配置验证
- [ ] **后端API服务**：Django开发服务器在 `http://localhost:8000` 正常启动
- [ ] **前端开发服务器**：Vue3应用在 `http://localhost:5173` 正常启动（运行 `npm run dev`）
- [ ] **CORS跨域配置**：前端可以成功调用后端API接口，无跨域错误
- [ ] **API接口测试**：至少验证一个GET和一个POST接口的正常响应
- [ ] **JWT认证**：用户登录/注册接口返回有效的JWT token

### 依赖包安装验证
- [ ] **后端Python依赖**：`pip install -r backend/requirements/development.txt` 无错误
- [ ] **前端Node.js依赖**：`npm install` 在frontend目录下无错误完成
- [ ] **版本兼容性**：Node.js >= 16, Python >= 3.8, Django 5.x
- [ ] **关键包验证**：确认Django REST Framework、Vue3、TypeScript、Element Plus等核心包正确安装

### 功能模块验证
- [ ] **用户认证模块**：注册、登录、JWT token验证功能正常
- [ ] **文档管理模块**：文档上传、存储、检索功能基础框架就绪
- [ ] **前端路由**：Vue Router配置正确，页面跳转无错误
- [ ] **状态管理**：Pinia store配置正确，数据状态管理正常

### 开发环境验证
- [ ] **热重载功能**：前端代码修改后自动刷新页面
- [ ] **错误日志**：后端Django日志记录正常，前端控制台无严重错误
- [ ] **代码质量工具**：ESLint、Black代码格式化工具配置正确
- [ ] **测试框架**：pytest（后端）和Vitest（前端）测试环境配置完成



## 1.2 项目结构创建
#### 📋 任务目标

创建标准化的项目目录结构，便于后续开发和维护。请帮我创建AI智能审校系统的标准项目结构，严格遵循Django和Vue3最佳实践：
## 第七问  项目结构创建 (原始提示词)

后端结构要求（遵循Django最佳实践）：
- backend/
  - config/ (项目配置，分环境settings)
  - apps/ (应用模块，每个应用独立)
    - documents/ (文档管理 - 遵循MVT模式)
      - models.py (使用自定义Manager)
      - views.py (基于ViewSet)
      - serializers.py (DRF序列化器)
      - urls.py (URL路由)
      - signals.py (信号处理)
      - managers.py (自定义查询管理器)
    - diff_engine/ (差异对比引擎)
    - ai_proofreading/ (AI校对服务)
    - users/ (用户管理 - 扩展AbstractUser)
    - annotations/ (批注系统)
  - requirements/ (依赖管理 - 分环境requirements)
  - tests/ (测试文件 - pytest配置)
  - static/ (静态文件)
  - media/ (媒体文件)
  - logs/ (日志文件)

  ## 第七问  项目结构创建 (提示词优化)
  请按照以下详细规范创建Django后端项目结构，严格遵循Django 5最佳实践和MVT模式：

**后端目录结构要求（backend/）：**

1. **配置层（config/）**
   - settings/ 目录包含分环境配置文件
     - __init__.py
     - base.py（基础配置）
     - development.py（开发环境）
     - production.py（生产环境）
     - testing.py（测试环境）
   - urls.py（主路由配置）
   - wsgi.py 和 asgi.py（部署配置）

2. **应用模块层（apps/）** - 每个应用严格遵循MVT模式
   
   **documents/（文档管理应用）**
   - models.py：定义Document模型，使用自定义Manager类进行查询优化
   - views.py：基于DRF ViewSet实现CRUD操作
   - serializers.py：DRF序列化器，包含字段验证和嵌套序列化
   - urls.py：应用级URL路由配置
   - signals.py：Django信号处理（如文档创建/更新后的自动处理）
   - managers.py：自定义查询管理器，封装复杂查询逻辑
   - admin.py：Django管理后台配置
   - apps.py：应用配置
   
   **diff_engine/（差异对比引擎）**
   - 实现文本差异算法和对比逻辑
   - 遵循相同的MVT结构
   
   **ai_proofreading/（AI校对服务）**
   - 集成AI校对功能
   - 遵循相同的MVT结构
   
   **users/（用户管理）**
   - 扩展Django AbstractUser模型
   - 实现JWT认证和权限管理
   - 遵循相同的MVT结构
   
   **annotations/（批注系统）**
   - 实现文档批注功能
   - 遵循相同的MVT结构

3. **依赖管理（requirements/）**
   - base.txt（基础依赖）
   - development.txt（开发环境依赖）
   - production.txt（生产环境依赖）
   - testing.txt（测试环境依赖）

4. **测试目录（tests/）**
   - pytest.ini 配置文件
   - unit/（单元测试）
   - integration/（集成测试）
   - fixtures/（测试数据）
   - conftest.py（pytest配置）

5. **静态文件和媒体文件**
   - static/（静态文件收集目录）
   - media/（用户上传文件目录）

6. **日志目录（logs/）**
   - 按环境和模块分类的日志文件

**技术栈要求：**
- Django 5 + DRF（Django REST Framework）
- JWT认证（django-rest-framework-simplejwt）
- 数据库：SQLite（本地书籍数据）+ MySQL（用户管理和系统设置）
- 代码质量：Black格式化 + pytest测试框架
- 容器化：Docker + docker-compose

**开发规范：**
- 严格遵循DRY原则，避免代码重复
- 每个应用必须包含完整的MVT组件
- 所有模型使用自定义Manager进行查询优化
- 视图层统一使用DRF ViewSet
- 序列化器包含完整的字段验证
- 所有代码必须包含详细的中文注释
- 遵循Django命名约定和PEP 8规范

请创建完整的目录结构，并为每个关键文件提供基础代码框架和详细注释。



请按照以下Vue3特性驱动的前端项目结构要求，创建和组织frontend目录：

**核心目录结构：**
```
frontend/
├── src/
│   ├── components/          # 通用可复用组件（使用PascalCase命名，如UserCard.vue）
│   ├── views/              # 页面级组件（路由对应的页面，如HomePage.vue）
│   ├── stores/             # Pinia状态管理（按模块分组，如useUserStore.ts）
│   ├── composables/        # Vue3组合式函数（use开头命名，如useDocumentCompare.ts）
│   ├── utils/              # 纯工具函数（如formatDate.ts, apiClient.ts）
│   ├── types/              # TypeScript类型定义（如User.ts, Document.ts）
│   ├── assets/             # 静态资源（图片、图标、字体等）
│   ├── styles/             # 全局样式文件（使用SCSS预处理器）
│   └── features/           # 特性模块（按业务功能分组的完整模块）
│       └── document-compare/
│           ├── components/ # 该特性专用组件
│           ├── composables/# 该特性专用组合函数
│           └── types/      # 该特性专用类型定义
├── public/                 # 公共静态资源（favicon.ico, index.html等）
└── tests/                  # 测试文件（使用Vitest测试框架）
    ├── unit/               # 单元测试
    ├── integration/        # 集成测试
    └── fixtures/           # 测试数据
```

**技术栈要求：**
- Vue3 + Composition API + TypeScript
- Pinia状态管理
- Element Plus UI组件库
- SCSS样式预处理器
- Vite构建工具
- Vitest测试框架
- ESLint代码质量检查

**命名规范：**
- 组件文件：PascalCase（如UserProfile.vue）
- 组合函数：use开头的camelCase（如useDocumentCompare.ts）
- 工具函数：camelCase（如formatDate.ts）
- 类型定义：PascalCase（如UserInfo.ts）
- 目录名：kebab-case（如document-compare/）

**特性模块组织原则：**
- 每个features子目录代表一个完整的业务功能模块
- 模块内部保持相对独立，包含该功能所需的所有组件、逻辑和类型
- 通用组件放在src/components/，特性专用组件放在对应features目录下

请确保创建的目录结构严格遵循上述规范，并在每个目录下添加适当的README.md文件说明其用途。



## 第八问  项目结构创建 (验收标准 原始提示词)
### ✅ 验收标准

- [ ] 目录结构符合Django和Vue3最佳实践
- [ ] 每个目录都有README.md说明
- [ ] Django应用包含必要的__init__.py文件
- [ ] 前端路由配置完成，支持懒加载
- [ ] Pinia状态管理结构搭建完成
- [ ] 特性驱动的前端目录结构清晰
- [ ] Django应用遵循单一职责原则
- [ ] Vue3组件采用Composition API结构
- [ ] TypeScript类型定义文件完整

## 第八问  项目结构创建 (验收标准 优化 提示词)
请按照以下详细验收标准验证Django后端和Vue3前端项目结构的完整性和规范性：

## 📋 Django后端验收标准

### 目录结构验收
- [ ] **应用模块完整性**：确认5个核心应用（common、users、documents、diff_engine、ai_proofreading、annotations）已创建
- [ ] **MVT组件完整性**：每个应用包含models.py、views.py、serializers.py、managers.py、signals.py、admin.py、urls.py、apps.py
- [ ] **Python包结构**：所有目录包含__init__.py文件，确保Python包导入正常
- [ ] **配置文件结构**：config/settings/目录包含base.py、development.py、production.py、testing.py
- [ ] **依赖管理**：requirements/目录包含base.txt、development.txt、production.txt、testing.txt

### 代码质量验收
- [ ] **Django最佳实践**：严格遵循MVT模式，使用自定义Manager类，ViewSet统一API设计
- [ ] **数据库设计**：双数据库架构配置正确（SQLite+MySQL），模型关系合理，索引优化
- [ ] **API设计规范**：DRF序列化器包含完整字段验证，支持嵌套序列化和错误处理
- [ ] **中文注释完整**：所有模型、视图、序列化器包含详细的中文注释说明

### 功能验收
- [ ] **数据库迁移**：执行`python manage.py makemigrations`和`python manage.py migrate`无错误
- [ ] **管理后台**：访问`/admin/`可正常显示所有模型管理界面
- [ ] **API文档**：访问`/api/docs/`可正常显示Swagger文档
- [ ] **开发服务器**：`python manage.py runserver`启动无错误

## 📋 Vue3前端验收标准

### 目录结构验收
- [ ] **特性驱动结构**：features/目录按业务模块组织（documents、diff-engine、ai-proofreading、annotations）
- [ ] **组件结构**：components/目录包含base/、business/、layout/子目录
- [ ] **类型定义**：types/目录包含完整的TypeScript接口定义
- [ ] **工具函数**：utils/目录包含api/、helpers/、constants/子目录

### 技术栈验收
- [ ] **Vue3 Composition API**：所有组件使用`<script setup>`语法和Composition API
- [ ] **TypeScript集成**：所有.vue文件使用TypeScript，类型定义完整
- [ ] **Pinia状态管理**：stores/目录包含模块化的状态管理文件
- [ ] **Vue Router配置**：router/index.ts配置路由懒加载和路由守卫

### 开发工具验收
- [ ] **ESLint配置**：.eslintrc.js配置Vue3+TypeScript规则
- [ ] **Vite配置**：vite.config.ts包含路径别名、代理配置、构建优化
- [ ] **Element Plus集成**：组件库正确引入，支持按需导入
- [ ] **SCSS支持**：样式预处理器配置正确

### 功能验收
- [ ] **项目启动**：`npm run dev`启动无错误，端口访问正常
- [ ] **热重载**：修改文件后浏览器自动刷新
- [ ] **类型检查**：`npm run type-check`无TypeScript错误
- [ ] **代码检查**：`npm run lint`无ESLint错误

## 📋 前后端集成验收标准

### API通信验收
- [ ] **跨域配置**：前端可正常访问后端API接口
- [ ] **认证集成**：JWT token认证流程正常
- [ ] **错误处理**：API错误响应正确处理和显示
- [ ] **数据格式**：前后端数据格式一致，类型匹配

### 开发环境验收
- [ ] **并发开发**：前后端可同时启动，端口不冲突
- [ ] **环境变量**：.env文件配置正确，敏感信息已隔离
- [ ] **依赖安装**：`pip install -r requirements/development.txt`和`npm install`无错误
- [ ] **数据库连接**：后端可正常连接配置的数据库

## 📋 文档和规范验收标准

### 文档完整性
- [ ] **README文件**：项目根目录和主要子目录包含详细的README.md
- [ ] **API文档**：Swagger/OpenAPI文档完整，接口说明清晰
- [ ] **开发指南**：包含环境搭建、运行、测试的详细说明
- [ ] **代码注释**：关键业务逻辑包含中文注释

### 代码规范
- [ ] **命名规范**：文件名、变量名、函数名遵循约定规范
- [ ] **目录组织**：按功能模块组织，职责单一，耦合度低
- [ ] **版本控制**：.gitignore文件配置正确，排除不必要文件
- [ ] **配置管理**：开发、测试、生产环境配置分离

请按照以上标准逐项检查项目结构，确保每个检查点都符合要求后再进行下一步开发工作。


## 第二问 搭建一个完整的Mock API服务

基于现有的Vue3 + TypeScript + Element Plus前端项目，需要搭建一个完整的Mock API服务来模拟后端接口。请按照以下详细要求实施：

**项目背景**：
- 前端项目已完成基础搭建（Vue3 + TypeScript + Element Plus + Vite + Vue Router + Pinia）
- 需要Mock API支持前端独立开发，无需依赖真实后端
- 项目是一个AI文档校对系统，包含用户管理、文档管理、版本控制、AI校对等功能

**技术选型要求**：
1. 优先使用MSW (Mock Service Worker)，如果不适合则使用json-server
2. 集成faker.js用于数据生成，配置中文locale
3. 使用localStorage实现数据持久化
4. 确保与现有Vite开发环境兼容

**具体实施要求**：

**1. Mock服务基础架构**
- 创建完整的MSW配置文件和handlers
- 配置开发环境自动启动Mock服务
- 实现200-800ms随机网络延迟模拟
- 支持可配置的错误率（默认5%失败率）
- 提供详细的控制台日志，显示请求方法、URL、参数和响应

**2. 数据模型和生成**
- 定义完整的TypeScript数据类型（User, Document, Version, ProofreadResult等）
- 使用faker.js生成中文测试数据：
  * 用户：中文姓名、手机号、邮箱、头像
  * 文档：中文标题、内容、创建时间、标签
  * 版本：版本号、变更说明、时间戳
- 确保数据关联关系正确（外键关联、级联删除等）
- 生成100+用户、500+文档、1000+版本的测试数据集
- 实现分页数据结构（total, page, pageSize, data）

**3. 核心API接口实现**
详细实现以下接口，包含完整的请求/响应格式：

**用户认证模块**：
- POST /api/auth/login - 登录（支持用户名/邮箱+密码）
- POST /api/auth/logout - 登出
- POST /api/auth/register - 注册
- GET /api/auth/profile - 获取用户信息
- PUT /api/auth/profile - 更新用户信息
- POST /api/auth/refresh - 刷新token

**文档管理模块**：
- GET /api/documents - 文档列表（支持搜索、分页、排序、筛选）
- POST /api/documents - 创建文档
- GET /api/documents/:id - 获取文档详情
- PUT /api/documents/:id - 更新文档
- DELETE /api/documents/:id - 删除文档
- POST /api/documents/:id/duplicate - 复制文档

**版本管理模块**：
- GET /api/documents/:id/versions - 版本列表
- POST /api/documents/:id/versions - 创建新版本
- GET /api/versions/:id - 获取版本详情
- POST /api/versions/compare - 版本对比
- PUT /api/versions/:id/restore - 恢复版本

**文件管理模块**：
- POST /api/files/upload - 文件上传（模拟FormData处理）
- GET /api/files/:id/download - 文件下载
- DELETE /api/files/:id - 删除文件

**AI校对模块**：
- POST /api/proofread/submit - 提交校对请求
- GET /api/proofread/:id/status - 获取校对状态
- GET /api/proofread/:id/result - 获取校对结果
- POST /api/proofread/:id/accept - 接受校对建议

**4. 业务逻辑模拟**
- JWT token模拟（生成、验证、过期处理）
- 权限验证中间件（admin、editor、viewer角色）
- 文档状态流转（草稿→审核→发布→归档）
- 文件上传进度模拟（分块上传、断点续传）
- AI校对异步处理模拟（提交→处理中→完成）

**5. 开发体验优化**
- 提供Mock数据管理面板（可选，通过console命令）
- 支持数据重置功能（恢复初始数据集）
- 多用户并发场景模拟
- 错误场景覆盖（网络超时、服务器错误、权限不足等）
- 与Vite HMR集成，支持热重载

**6. 配置和文档**
- 创建详细的README.md说明Mock服务使用方法
- 提供API文档（接口列表、参数说明、响应示例）
- 配置文件支持环境变量（开发/测试环境切换）
- 提供数据重置和清理脚本

**交付要求**：
- 提供完整的文件结构和代码实现
- 确保所有代码包含详细的中文注释
- 提供package.json依赖配置和安装说明
- 验证前端项目可以完全基于Mock数据正常运行
- 提供测试用例验证Mock API的正确性

请按照上述要求，提供完整的Mock API实现方案和代码。

请验证并确保前端项目的Mock API服务完全可用，具体检查以下各项：

**Mock服务启动验证：**
- [ ] 开发服务器启动时Mock Service Worker自动加载
- [ ] 浏览器控制台显示"🎭 Mock Service Worker 已启动"消息
- [ ] 访问 http://localhost:5173/mock-test 测试页面正常加载
- [ ] 在浏览器控制台中 `window.__mockReset` 等Mock控制函数可用

**API接口完整性检查：**
- [ ] 认证模块：登录、注册、获取用户信息、登出、刷新令牌等接口
- [ ] 文档管理：CRUD操作、批量操作、统计接口
- [ ] 版本管理：版本列表、创建、对比、恢复等接口
- [ ] 文件管理：上传、下载、删除接口
- [ ] AI校对：提交任务、获取状态、处理结果等接口
- [ ] 所有接口路径遵循 `/api/v1/*` 格式

**Mock数据质量验证：**
- [ ] 使用faker.js生成的中文测试数据真实可信
- [ ] 用户数据包含完整的个人信息（姓名、邮箱、角色等）
- [ ] 文档数据包含丰富的内容和元数据
- [ ] 数据关联关系正确（用户-文档、文档-版本等）
- [ ] 分页、排序、筛选功能的数据支持完整

**业务逻辑模拟准确性：**
- [ ] 用户认证流程：登录验证、JWT令牌生成和验证
- [ ] 权限控制：不同角色的访问权限正确限制
- [ ] 文档状态流转：草稿→审核→发布等状态变化
- [ ] 版本管理：创建版本、版本对比、回滚操作
- [ ] 数据持久化：localStorage保存，页面刷新后数据不丢失

**错误场景覆盖：**
- [ ] 网络错误模拟（可配置错误率）
- [ ] 认证失败场景（无效凭据、令牌过期）
- [ ] 权限不足错误（403 Forbidden）
- [ ] 资源不存在错误（404 Not Found）
- [ ] 服务器错误模拟（500 Internal Server Error）
- [ ] 参数验证错误（400 Bad Request）

**前端开发支持完整性：**
- [ ] 所有前端API调用都能被Mock服务正确拦截
- [ ] Mock控制台工具可用（mockConsole.help()、reset()、stats()等）
- [ ] 开发者可以通过控制台管理测试数据
- [ ] 支持实时数据重置和重新生成
- [ ] Mock服务日志清晰，便于调试
- [ ] 前端开发无需依赖真实后端即可完成所有功能开发

**验证方法：**
1. 启动开发服务器：`npm run dev`
2. 访问测试页面：http://localhost:5173/mock-test
3. 逐一测试各个API接口功能
4. 检查浏览器控制台的Mock服务日志
5. 使用Mock控制台工具验证数据管理功能
6. 测试错误场景的正确响应

记录提示词  




## 第一阶段验收成功    开始第二阶段-----------------------------------------------------------------------------

 












 # 阶段2: 数据模型设计
### 2.1 数据库设计

#### 📋 任务目标

设计完整的数据库模型，支持文档管理、版本控制、用户权限等功能。