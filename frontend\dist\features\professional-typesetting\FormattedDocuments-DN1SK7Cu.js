import{d as a,c as s,a as e,Q as t,I as c,ag as d,o as l}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as n}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const r={class:"formatted-documents"},o={class:"content"},u=n(a({__name:"FormattedDocuments",setup:a=>(a,n)=>{const u=d("el-card");return l(),s("div",r,[n[2]||(n[2]=e("div",{class:"page-header"},[e("h1",null,"已排版文档"),e("p",{class:"page-description"},"查看和管理已完成排版的文档")],-1)),e("div",o,[t(u,null,{header:c(()=>n[0]||(n[0]=[e("div",{class:"card-header"},[e("span",null,"已排版文档列表")],-1)])),default:c(()=>[n[1]||(n[1]=e("p",null,"已排版文档管理功能开发中...",-1))]),_:1,__:[1]})])])}}),[["__scopeId","data-v-9ee2c40c"]]);export{u as default};
