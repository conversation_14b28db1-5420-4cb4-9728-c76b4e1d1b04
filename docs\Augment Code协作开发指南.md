# AI智能审校系统 - Augment Code 前后端分离协作开发指南

**版本**: 4.0
**创建日期**: 2025-06-22
**更新日期**: 2025-06-25
**项目**: ProofreadAI
**文档类型**: Augment Code AI助手前后端分离协作开发指南
**适用范围**: 与Augment Code AI助手的前后端分离高效协作开发

---

## 📋 文档概述

本指南专门为与Augment Code AI助手协作开发AI智能审校系统而设计，采用**前后端分离开发模式**，提供详细的、可操作的分步骤流程。每个步骤都包含具体的对话模板、验收标准和质量检查点，确保开发过程高效且可控。

### 🎯 项目目标
开发一个基于Django 5和Vue3的AI智能审校系统，使用AI进行对比校对，比对使用上下两个wangeditor编辑框承载对比的内容，支持原文与修改稿的段落级对比，并提供差异高亮显示功能。系统采用B/S架构，前后端分离设计，满足编辑人员对文档修改痕迹进行对比和管理的需求。

### 🚀 前后端分离开发理念
本指南遵循**前后端分离开发工作流程**，确保：
1. **前端优先开发**：先完成前端界面和交互逻辑，使用Mock数据模拟后端接口
2. **用户体验驱动**：从用户界面和交互开始设计，确保最佳用户体验
3. **需求明确化**：通过前端开发过程明确和细化业务需求
4. **API契约先行**：在前端开发过程中定义清晰的API契约和数据结构
5. **Mock数据验证**：使用Mock API完整验证前端功能，确保前端逻辑正确
6. **后端按需开发**：根据前端API需求和数据结构，精确开发后端接口
7. **无缝对接集成**：前端切换真实API，进行前后端集成测试

### 🛠️ 技术栈
- **前端**: Vue3 + TypeScript + Element Plus + WangEditor + Vite
- **后端**: Django 5 + Django REST Framework + MySQL + Redis
- **AI集成**: OpenAI API + HuggingFace
- **部署**: Docker + Nginx + 云服务器

### 📋 开发规范参考
- **Django规范**: 参考 `rules/django.mdc` - Django最佳实践和编码标准
  - 遵循MVT架构模式，使用Django ORM进行数据库操作
  - 实施DRY原则，避免代码重复
  - 使用Django Forms进行数据验证
  - 实现信号机制进行解耦事件处理
  - 遵循PEP 8代码风格，使用Black格式化工具
- **Vue3规范**: 参考 `rules/vue3.mdc` - Vue3最佳实践和编码标准
  - 采用Composition API进行组件开发
  - 使用TypeScript提供类型安全
  - 实施特性驱动的目录结构
  - 遵循PascalCase组件命名约定
  - 使用Pinia进行状态管理
- **代码质量**: 遵循PEP 8 (Python) 和 ESLint (JavaScript/TypeScript)
- **安全标准**: 遵循OWASP安全指南和Django安全最佳实践
- **性能标准**: API响应时间<500ms，前端加载时间<2s，文本对比10万字符<2s

### 📖 使用说明
- ✅ 每个任务都有明确的**输入要求**、**对话模板**和**验收标准**
- ✅ 按照**检查清单**逐项验证完成质量
- ✅ 使用**代码片段标识**便于快速定位和复用
- ✅ 遵循**渐进式开发**原则，每步都可独立验证
- ✅ 所有代码必须包含详细的中文注释
- ✅ 严格遵循Django和Vue3最佳实践规范

---

## 📚 前后端分离开发阶段目录

### 🚀 [阶段1: 前端项目初始化](#阶段1-前端项目初始化) (预计1天)
- [1.1 前端项目搭建](#11-前端项目搭建)
- [1.2 Mock API服务搭建](#12-mock-api服务搭建)
- [1.3 前端开发环境配置](#13-前端开发环境配置)

### 🎨 [阶段2: 前端完整开发](#阶段2-前端完整开发) (预计5天)
- [2.1 基础组件开发](#21-基础组件开发)
- [2.2 编辑器组件开发](#22-编辑器组件开发)
- [2.3 对比显示组件开发](#23-对比显示组件开发)
- [2.4 页面组件集成](#24-页面组件集成)
- [2.5 前端功能完整性验证](#25-前端功能完整性验证)

### 📋 [阶段3: API需求整理](#阶段3-api需求整理) (预计1天)
- [3.1 API契约文档编写](#31-api契约文档编写)
- [3.2 数据模型定义](#32-数据模型定义)
- [3.3 Mock API完善](#33-mock-api完善)
- [3.4 前端Mock集成验证](#34-前端mock集成验证)

### 🗄️ [阶段4: 后端项目初始化](#阶段4-后端项目初始化) (预计1天)
- [4.1 后端项目搭建](#41-后端项目搭建)
- [4.2 数据库设计](#42-数据库设计)
- [4.3 Django模型实现](#43-django模型实现)
- [4.4 数据迁移](#44-数据迁移)

### 🔌 [阶段5: 后端API开发](#阶段5-后端api开发) (预计3天)
- [5.1 基础API框架](#51-基础api框架)
- [5.2 用户认证API](#52-用户认证api)
- [5.3 文档管理API](#53-文档管理api)
- [5.4 AI校对API](#54-ai校对api)

### 🧠 [阶段6: 核心算法实现](#阶段6-核心算法实现) (预计2天)
- [6.1 文本差异算法](#61-文本差异算法)
- [6.2 AI校对集成](#62-ai校对集成)
- [6.3 PDF处理](#63-pdf处理)

### 🔗 [阶段7: 前后端对接集成](#阶段7-前后端对接集成) (预计2天)
- [7.1 API接口联调](#71-api接口联调)
- [7.2 前端切换真实API](#72-前端切换真实api)
- [7.3 集成测试验证](#73-集成测试验证)
- [7.4 性能优化](#74-性能优化)

### 🚀 [阶段8: 部署优化](#阶段8-部署优化) (预计1天)
- [8.1 生产环境配置](#81-生产环境配置)
- [8.2 部署脚本](#82-部署脚本)

---

## 🚀 阶段1: 前端项目初始化

### 1.1 前端项目搭建

#### 📋 任务目标
搭建Vue3前端项目，建立完整的用户界面和交互流程，使用Mock数据实现完整的前端功能，为后续后端开发提供明确的API需求。

#### 📥 输入要求
- 确认Node.js 18+已安装
- 确认Git已配置
- 明确前端功能需求和用户交互流程

#### 💬 Augment Code对话模板
```
我需要搭建AI智能审校系统的前端项目，采用前后端分离开发策略，请严格按照Vue3最佳实践帮我：

1. 创建Vue3前端项目结构（前端独立开发）
   - 项目名称：frontend
   - 使用TypeScript (严格模式)
   - 集成Element Plus UI库
   - 配置Vite构建工具 (代码分割、懒加载)
   - 配置路由和状态管理 (Vue Router + Pinia)
   - 采用Composition API和特性驱动目录结构

2. 前端开发环境配置
   - ESLint + Prettier + Vitest测试
   - Mock API服务配置 (MSW或json-server)
   - 环境变量配置 (.env文件)
   - TypeScript严格模式配置
   - 代码质量检查工具

3. 基础UI框架搭建
   - 主布局组件 (Header, Sidebar, Main)
   - 路由配置 (文档列表、编辑器、对比页面)
   - 全局样式和主题配置
   - 响应式设计基础

4. Mock数据和API接口定义
   - 定义前端所需的完整数据结构
   - 创建Mock API服务，模拟所有后端接口
   - 建立API调用封装，支持Mock/真实API切换
   - 错误处理和加载状态管理

请提供完整的前端项目初始化脚本和配置文件，确保前端可以完全独立开发和运行。
```

#### ✅ 验收标准
- [ ] Vue3项目可以正常启动 (npm run dev)
- [ ] TypeScript严格模式配置正确
- [ ] Element Plus UI库集成成功
- [ ] 路由配置正确，页面可以正常跳转
- [ ] Mock API服务正常工作，可以完全模拟后端
- [ ] 代码格式化和检查工具配置正确
- [ ] 前端可以完全独立开发，不依赖后端

#### 🔍 质量检查清单
- [ ] 项目目录结构清晰，符合Vue3最佳实践
- [ ] TypeScript类型定义完整，严格模式启用
- [ ] 组件使用Composition API，代码结构清晰
- [ ] 路由配置支持懒加载，性能优化
- [ ] Mock API数据结构完整，覆盖所有业务场景
- [ ] 环境变量配置安全，支持多环境
- [ ] 代码包含详细中文注释，遵循ESLint规范
- [ ] 响应式设计基础搭建完成
- [ ] API调用封装支持Mock/真实API无缝切换

---

### 1.2 Mock API服务搭建

#### 📋 任务目标
搭建完整的Mock API服务，模拟所有后端接口，支持前端完全独立开发和功能验证。

#### 💬 Augment Code对话模板
```
基于前端项目搭建完成，现在需要搭建Mock API服务，完全模拟后端接口，请帮我：

1. Mock API服务搭建（使用MSW或json-server）
   - 配置Mock Service Worker (MSW) 或 json-server
   - 支持所有前端需要的API接口
   - 模拟真实的网络延迟和响应时间
   - 支持错误场景模拟（网络错误、服务器错误等）

2. 数据生成和管理
   - 使用faker.js生成真实的测试数据
   - 支持中文数据生成（姓名、地址、文本等）
   - 数据关联关系正确（用户-文档-版本等）
   - 支持分页数据生成
   - 数据持久化到localStorage

3. 业务逻辑模拟
   - 用户认证流程模拟（登录、注册、权限验证）
   - 文档CRUD操作模拟
   - 文档版本管理模拟
   - 文档对比逻辑模拟
   - 文件上传下载模拟
   - AI校对结果模拟

4. API接口覆盖
   - 用户认证API (login, logout, profile)
   - 文档管理API (CRUD, 搜索, 分页)
   - 文档版本API (版本列表, 创建版本, 对比)
   - 文件管理API (上传, 下载, 删除)
   - AI校对API (校对请求, 结果获取)
   - 系统配置API (设置, 统计)

5. 开发体验优化
   - 支持热重载，修改数据立即生效
   - 提供数据重置功能
   - 支持多用户场景模拟
   - 详细的请求日志输出
   - 错误率可配置

请提供完整的Mock API实现代码和配置，确保前端可以完全基于Mock数据进行开发。
```

#### ✅ 验收标准
- [ ] Mock API服务可以正常启动
- [ ] 所有API接口都有Mock实现
- [ ] Mock数据生成真实可用
- [ ] 业务逻辑模拟正确
- [ ] 错误场景覆盖完整
- [ ] 前端可以完全基于Mock API开发

#### 🔍 质量检查清单
- [ ] Mock API覆盖所有前端需求
- [ ] 数据结构与真实API保持一致
- [ ] 业务逻辑模拟准确
- [ ] 错误处理场景完整
- [ ] 数据关联关系正确
- [ ] 性能模拟合理
- [ ] 配置灵活可调
- [ ] 易于维护和扩展

---

### 1.3 前端开发环境配置

#### 📋 任务目标
配置前端项目的开发环境，确保前端可以完全独立开发，Mock API无缝切换到真实API。

#### 💬 Augment Code对话模板
```
请帮我配置前端开发环境，支持前后端分离开发工作流程：

前端配置需求（遵循Vue3最佳实践）：
1. vite.config.ts配置 (代理、构建优化)
   - 配置开发服务器，支持Mock API
   - 设置代码分割策略
   - 配置构建优化选项
   - 配置API代理，支持Mock/真实API切换

2. TypeScript配置 (严格模式)
   - 启用strict模式
   - 配置路径别名
   - 设置类型检查选项
   - 配置完整的API类型定义

3. 环境变量配置 (.env文件)
   - 配置不同环境的API基础URL
   - 设置Mock API开关 (VITE_USE_MOCK_API)
   - 配置构建标识和版本信息
   - 支持开发/测试/生产环境切换

4. 路由和状态管理配置
   - 配置Vue Router懒加载
   - 设置Pinia store结构
   - 配置状态持久化
   - 设置开发工具

5. API调用封装配置
   - 创建axios实例，支持Mock/真实API切换
   - 配置请求/响应拦截器
   - 统一错误处理机制
   - 支持请求取消和重试

6. 开发工具配置
   - 配置Vue DevTools
   - 设置热重载
   - 配置代码格式化和检查
   - 配置测试环境

请提供完整的配置文件，确保前端可以完全独立开发，并支持无缝切换到真实API。
```

#### ✅ 验收标准
- [ ] 前端项目可以完全独立运行
- [ ] Mock API正常工作，覆盖所有接口
- [ ] 环境变量配置正确，支持多环境切换
- [ ] TypeScript严格模式启用，类型检查正常
- [ ] API调用封装支持Mock/真实API无缝切换
- [ ] 代码格式化规则生效，符合规范
- [ ] 开发工具配置正确，调试便利
- [ ] 前端功能可以完全基于Mock数据验证

#### 🔍 质量检查清单
- [ ] 前端完全独立开发，不依赖后端
- [ ] Mock API数据结构完整，覆盖所有业务场景
- [ ] API调用封装合理，易于维护
- [ ] 环境变量配置安全，敏感信息保护
- [ ] 代码质量检查工具配置正确
- [ ] 开发服务器热重载正常工作
- [ ] 项目文档完整，便于团队协作
- [ ] Mock/真实API切换机制完善

---

## 🎨 阶段2: 前端完整开发

### 2.1 基础组件开发

#### 📋 任务目标
开发通用的基础组件，建立前端组件库，使用Mock数据验证组件功能，为完整的前端应用提供支撑。

#### 💬 Augment Code对话模板
```
请帮我开发AI智能审校系统的基础Vue3组件，采用前后端分离开发策略：

基础组件需求（遵循Vue3 Composition API最佳实践）：
1. Layout组件系列
   - AppLayout (主布局组件)
   - AppHeader (顶部导航栏，集成用户信息和菜单)
   - AppSidebar (侧边栏导航，支持权限控制)
   - AppMain (主内容区域，支持路由切换)
   - AppFooter (页脚组件)

2. 通用UI组件
   - LoadingSpinner (加载状态组件，支持全局和局部加载)
   - EmptyState (空状态组件，支持自定义图标和文案)
   - ErrorBoundary (错误边界组件，优雅处理组件错误)
   - ConfirmDialog (确认对话框，支持自定义按钮和回调)
   - Toast (消息提示组件，支持多种类型和位置)

3. 表单组件
   - FormWrapper (表单容器，集成验证和提交逻辑)
   - FormField (表单字段，支持多种输入类型)
   - FileUpload (文件上传组件，支持拖拽和进度显示)
   - SearchInput (搜索输入框，支持防抖和历史记录)

4. 数据展示组件
   - DataTable (数据表格，支持排序、筛选、分页)
   - Pagination (分页组件，支持多种分页模式)
   - StatusBadge (状态徽章，支持自定义颜色和图标)
   - ProgressBar (进度条，支持动画和百分比显示)

技术要求：
- 使用Vue3 Composition API和<script setup>语法
- TypeScript严格模式，完整的类型定义
- Element Plus UI库作为基础
- 响应式设计，支持移动端
- 详细的中文注释和文档
- 支持主题切换
- 可访问性支持 (ARIA属性)
- 集成Mock数据进行功能验证

请为每个组件提供：
1. 组件定义文件 (.vue)
2. TypeScript类型文件 (.ts)
3. 样式文件 (.scss)
4. 使用示例和Mock数据验证
```

#### ✅ 验收标准
- [ ] 所有基础组件功能正常
- [ ] TypeScript类型定义完整
- [ ] 响应式设计适配良好
- [ ] 组件文档完整清晰
- [ ] 样式统一美观
- [ ] 可访问性支持到位
- [ ] 组件复用性良好
- [ ] 性能表现优秀
- [ ] Mock数据集成验证通过

#### 🔍 质量检查清单
- [ ] 组件使用Composition API，代码结构清晰
- [ ] Props和Emits定义完整，类型安全
- [ ] 组件支持插槽和自定义配置
- [ ] 样式使用CSS变量，支持主题切换
- [ ] 组件包含详细的中文注释
- [ ] 错误处理完整，用户体验友好
- [ ] 组件测试覆盖率高
- [ ] 组件文档包含使用示例
- [ ] Mock数据验证组件功能完整

---

### 2.2 编辑器组件开发

#### 📋 任务目标
开发核心的文本编辑器组件，实现双栏编辑和对比功能，使用Mock数据验证编辑器的所有功能，这是系统的核心用户界面。

#### 💬 Augment Code对话模板
```
请帮我开发文本编辑器组件，这是前后端分离开发的核心组件：

编辑器组件需求（遵循Vue3 Composition API最佳实践）：
1. WangEditorWrapper组件
   - 封装WangEditor 5，使用Composition API
   - 支持富文本编辑，TypeScript类型完整
   - 自定义工具栏配置
   - 内容同步功能，使用v-model双向绑定
   - 实现组件的props验证和emits定义
   - 支持插件扩展和自定义功能
   - 集成Mock数据进行功能验证

2. DualEditor组件 (核心组件)
   - 上下双栏布局 (原文框 + 修改稿框)
   - 使用CSS Grid或Flexbox响应式布局
   - 同步滚动功能，使用IntersectionObserver API
   - 实时差异高亮显示
   - 支持拖拽调整布局比例
   - 全屏编辑模式
   - 使用Mock数据模拟文档加载和保存

3. EditorToolbar组件
   - 自定义工具栏，支持文档操作
   - 保存、撤销、重做功能 (使用Mock API)
   - 格式化工具
   - 导入导出功能 (模拟文件操作)
   - 对比模式切换

4. EditorStatusBar组件
   - 显示文档状态信息
   - 字数统计
   - 保存状态 (基于Mock API响应)
   - 协作状态模拟

技术要求（遵循Vue3最佳实践）：
- 使用Vue3 Composition API和<script setup>语法
- TypeScript严格模式，完整的类型定义
- 响应式布局，支持移动端适配
- 性能优化 (虚拟滚动、懒加载、防抖)
- 详细中文注释，符合JSDoc规范
- 使用Teleport处理弹窗和提示
- 实现组件的可访问性 (ARIA属性)
- 支持键盘快捷键操作
- 完整的Mock数据集成和功能验证

在开发过程中，请定义以下API需求并用Mock实现：
1. 文档保存API (POST /api/documents/{id}/save)
2. 文档加载API (GET /api/documents/{id})
3. 文档版本API (GET /api/documents/{id}/versions)
4. 实时协作API (WebSocket /ws/documents/{id})

请提供完整的组件实现代码、Mock API实现和API需求文档。
```

#### ✅ 验收标准
- [ ] WangEditor集成成功，TypeScript类型完整
- [ ] 双栏布局正确显示，响应式适配良好
- [ ] 同步滚动功能正常，性能流畅
- [ ] 实时差异高亮显示正确
- [ ] 工具栏功能完整，用户体验良好
- [ ] 组件使用Composition API，代码结构清晰
- [ ] Props和Emits定义完整，类型安全
- [ ] 可访问性支持，键盘操作友好
- [ ] Mock API集成完整，功能验证通过
- [ ] API需求文档完整清晰

#### 🔍 质量检查清单
- [ ] 组件性能优化，大文档处理流畅
- [ ] 内存管理良好，无内存泄漏
- [ ] 错误处理完整，异常情况友好提示
- [ ] 组件文档完整，使用示例清晰
- [ ] 代码注释详细，便于维护
- [ ] 支持主题切换和自定义样式
- [ ] 移动端适配良好
- [ ] Mock数据验证功能完整
- [ ] API需求定义明确，便于后端开发

---

### 2.3 对比显示组件开发

#### 📋 任务目标
开发文档对比结果的可视化显示组件，实现差异高亮和导航功能，使用Mock数据验证所有对比功能。

#### 💬 Augment Code对话模板
```
请帮我开发文档对比显示组件，这是前端用户体验的关键组件：

对比组件需求（遵循Vue3最佳实践）：
1. ComparisonView组件 (主对比视图)
   - 支持多种对比模式 (并排/行内/统一视图)
   - 差异统计显示 (新增、删除、修改数量)
   - 导航跳转功能 (上一个/下一个差异)
   - 筛选和搜索功能
   - 全屏对比模式

2. DiffHighlight组件 (差异高亮)
   - 文本差异高亮显示
   - 颜色编码 (新增绿色/删除红色/修改蓝色)
   - 悬浮提示显示详细信息
   - 点击交互和选择功能
   - 支持段落级和词级高亮

3. ComparisonSidebar组件 (对比侧边栏)
   - 差异导航列表
   - 统计信息面板
   - 筛选控制面板
   - 导出功能按钮
   - 设置面板

4. DiffNavigator组件 (差异导航器)
   - 差异位置指示器
   - 快速跳转功能
   - 进度显示
   - 键盘快捷键支持

5. ComparisonSettings组件 (对比设置)
   - 对比模式设置
   - 高亮颜色配置
   - 忽略选项设置
   - 导出格式选择

技术要求：
- 高性能渲染 (支持大文档对比)
- 虚拟滚动优化
- 可访问性支持
- 键盘快捷键
- 自定义主题支持
- 详细中文注释

在开发过程中，请定义以下API需求：
1. 文档对比API
2. 差异数据获取API
3. 对比结果保存API
4. 导出功能API

请提供完整的组件实现代码和API需求文档。
```

#### ✅ 验收标准
- [ ] 对比模式切换正常，用户体验流畅
- [ ] 差异高亮显示正确，颜色编码清晰
- [ ] 导航功能完整，跳转准确
- [ ] 性能满足要求，大文档处理流畅
- [ ] 统计信息准确，数据展示清晰
- [ ] 筛选和搜索功能正常
- [ ] 导出功能完整
- [ ] API需求文档完整

#### 🔍 质量检查清单
- [ ] 组件性能优化，虚拟滚动有效
- [ ] 内存使用合理，无性能瓶颈
- [ ] 用户交互友好，操作直观
- [ ] 可访问性支持完整
- [ ] 键盘操作支持全面
- [ ] 响应式设计适配良好
- [ ] 错误处理完整
- [ ] API需求定义明确

---

### 2.4 页面组件集成

#### 📋 任务目标
将基础组件、编辑器组件和对比组件集成为完整的页面，形成完整的用户界面，使用Mock数据验证所有页面功能。

#### 💬 Augment Code对话模板
```
请帮我集成页面组件，构建完整的用户界面，使用Mock数据验证所有功能：

页面组件需求（遵循Vue3最佳实践）：
1. DocumentListPage (文档列表页面)
   - 集成DataTable、Pagination、SearchInput组件
   - 文档创建、编辑、删除功能 (使用Mock API)
   - 文档筛选和排序功能
   - 批量操作功能
   - 使用Mock数据展示文档列表

2. DocumentEditorPage (文档编辑页面)
   - 集成DualEditor组件
   - 集成EditorToolbar和EditorStatusBar
   - 自动保存功能 (Mock API模拟)
   - 版本历史功能 (Mock数据)
   - 实时协作状态模拟

3. DocumentComparePage (文档对比页面)
   - 集成ComparisonView组件
   - 集成ComparisonSidebar和DiffNavigator
   - 对比结果展示 (Mock对比数据)
   - 导出和分享功能 (模拟操作)

4. DashboardPage (仪表板页面)
   - 统计信息展示 (Mock统计数据)
   - 最近文档列表 (Mock数据)
   - 快速操作入口
   - 系统状态显示

5. SettingsPage (设置页面)
   - 用户设置 (Mock用户数据)
   - 系统配置
   - 主题设置
   - 导入导出设置

技术要求：
- 使用Vue3 Composition API
- 路由懒加载
- 状态管理 (Pinia)
- 错误边界处理
- 加载状态管理
- 响应式设计
- SEO优化
- 完整的Mock数据集成

在页面集成过程中，请完善以下API需求并用Mock实现：
1. 用户认证API (登录、注册、权限验证)
2. 文档管理API (CRUD、搜索、分页)
3. 文件上传API (上传、下载、删除)
4. 系统配置API (设置、主题、偏好)
5. 统计数据API (仪表板数据、报表)

请提供完整的页面组件代码、Mock API实现和完整的API需求文档。
```

#### ✅ 验收标准
- [ ] 所有页面组件正常工作
- [ ] 页面间导航流畅
- [ ] 状态管理正确
- [ ] 错误处理完整
- [ ] 加载状态友好
- [ ] 响应式设计良好
- [ ] Mock数据集成完整
- [ ] API需求文档完整
- [ ] 用户体验优秀

#### 🔍 质量检查清单
- [ ] 页面性能优化，加载速度快
- [ ] 组件复用性好，代码结构清晰
- [ ] 状态管理合理，数据流清晰
- [ ] 错误边界处理完整
- [ ] 用户交互友好，操作直观
- [ ] 可访问性支持完整
- [ ] SEO优化到位
- [ ] Mock数据验证功能完整
- [ ] API需求定义完整明确

---

### 2.5 前端功能完整性验证

#### 📋 任务目标
验证前端所有功能的完整性，确保基于Mock数据的前端应用可以完整运行，为后端开发提供明确的需求规范。

#### 💬 Augment Code对话模板
```
请帮我验证前端功能的完整性，确保所有功能都能基于Mock数据正常运行：

功能验证需求：
1. 用户流程验证
   - 用户注册登录流程 (Mock认证)
   - 文档创建编辑流程 (Mock CRUD)
   - 文档对比查看流程 (Mock对比数据)
   - 文件上传下载流程 (Mock文件操作)
   - 设置配置流程 (Mock配置数据)

2. 界面交互验证
   - 所有页面正常渲染
   - 组件间交互正常
   - 路由跳转正确
   - 状态管理有效
   - 错误处理友好

3. 数据流验证
   - API调用正常 (Mock响应)
   - 数据展示正确
   - 状态同步有效
   - 缓存机制正常
   - 错误恢复机制

4. 性能验证
   - 页面加载速度
   - 组件渲染性能
   - 内存使用情况
   - 网络请求优化

5. 兼容性验证
   - 浏览器兼容性
   - 移动端适配
   - 响应式设计
   - 可访问性支持

请提供完整的验证报告和发现的问题清单，确保前端功能完整可用。
```

#### ✅ 验收标准
- [ ] 所有用户流程可以完整走通
- [ ] 界面交互流畅无错误
- [ ] 数据流转正确
- [ ] 性能满足要求
- [ ] 兼容性良好
- [ ] Mock数据覆盖所有场景
- [ ] API需求文档完整
- [ ] 前端功能验证报告完整

---

## 📋 阶段3: API需求整理

### 5.1 文本差异算法

#### 📋 任务目标
实现高精度的文本差异识别算法，支持段落级和词级对比。

#### 💬 Augment Code对话模板
```
请帮我实现AI智能审校系统的核心文本差异算法：

算法需求：
1. 段落级差异检测 (准确率>95%)
2. 词级差异标记 (支持中英文)
3. 语义相似度计算
4. 差异高亮显示
5. 性能优化 (10万字符<2秒)

具体实现：
1. AdvancedTextDiffEngine类
   - compare_documents方法
   - _preprocess_text方法
   - _compare_paragraphs方法
   - _compare_words方法
   - _calculate_similarity方法

2. DiffHighlighter类
   - highlight_differences方法
   - generate_html_diff方法
   - export_diff_report方法

3. 性能优化策略
   - 分块处理大文档
   - 缓存计算结果
   - 异步处理

请提供完整的算法实现代码，包含详细注释和使用示例。
```

#### ✅ 验收标准
- [ ] 算法准确率达到95%以上
- [ ] 性能满足要求 (10万字符<2秒)
- [ ] 支持中英文文本
- [ ] 差异高亮显示正确
- [ ] 代码注释详细

---

## 📊 前后端分离开发进度跟踪

### 🗓️ 前后端分离开发时间表

| 阶段 | 预计时间 | 主要交付物 | 开发重点 | 状态 |
|------|----------|------------|----------|------|
| 阶段1: 前端项目初始化 | 1天 | 前端环境、Mock API服务 | 前端独立开发环境 | ⏳ 待开始 |
| 阶段2: 前端完整开发 | 5天 | 完整前端应用、用户体验 | 前端功能完整性 | ⏳ 待开始 |
| 阶段3: API需求整理 | 1天 | API契约、数据模型 | 前端驱动API设计 | ⏳ 待开始 |
| 阶段4: 后端项目初始化 | 1天 | 后端环境、数据模型 | 支持前端API需求 | ⏳ 待开始 |
| 阶段5: 后端API开发 | 3天 | REST API、认证系统 | 严格按API契约实现 | ⏳ 待开始 |
| 阶段6: 核心算法实现 | 2天 | 差异算法、AI集成 | 业务逻辑实现 | ⏳ 待开始 |
| 阶段7: 前后端对接集成 | 2天 | API联调、切换验证 | 无缝对接验证 | ⏳ 待开始 |
| 阶段8: 部署优化 | 1天 | 生产部署、性能优化 | 上线准备 | ⏳ 待开始 |

### 📈 前后端分离开发质量指标

| 指标类别 | 指标 | 目标值 | 当前值 | 状态 |
|----------|------|--------|--------|------|
| 前端性能 | 首屏加载时间 | <2s | - | ⏳ |
| 前端性能 | 组件渲染时间 | <100ms | - | ⏳ |
| 前端质量 | TypeScript覆盖率 | >95% | - | ⏳ |
| 前端质量 | 组件测试覆盖率 | >90% | - | ⏳ |
| API质量 | API响应时间 | <500ms | - | ⏳ |
| API质量 | API测试覆盖率 | >90% | - | ⏳ |
| 业务指标 | 文本对比准确率 | >95% | - | ⏳ |
| 业务指标 | AI校对准确率 | >90% | - | ⏳ |
| 集成质量 | Mock/真实API一致性 | 100% | - | ⏳ |

### 🎯 前后端分离开发里程碑

| 里程碑 | 完成标准 | 验收要点 |
|--------|----------|----------|
| 前端功能完成 | 基于Mock数据的完整应用 | 用户可以完整体验产品流程 |
| API契约确定 | API文档与Mock数据一致 | 后端开发有明确规范 |
| Mock API验证 | 前端功能完全可用 | 前端可以独立测试和演示 |
| 后端API完成 | 真实API按契约实现 | API格式与Mock完全一致 |
| 无缝对接完成 | 前端切换真实API成功 | 用户体验无差异 |
| 集成测试完成 | 端到端功能正常 | 产品可以发布上线 |

### 2.3 数据迁移

#### 📋 任务目标
创建和执行数据库迁移，确保数据库结构正确建立。

#### 💬 Augment Code对话模板
```
请帮我处理AI智能审校系统的数据库迁移，遵循Django最佳实践：

迁移任务（遵循Django迁移最佳实践）：
1. 生成所有模型的初始迁移文件
   - 使用python manage.py makemigrations
   - 为每个应用单独生成迁移
   - 确保迁移文件命名清晰
2. 创建数据库索引迁移
   - 为频繁查询字段添加索引
   - 使用RunSQL进行复杂索引创建
   - 考虑索引对性能的影响
3. 添加初始数据迁移 (超级用户、默认角色等)
   - 使用数据迁移而非fixtures
   - 创建管理员用户和基础权限
   - 设置默认配置数据
4. 创建数据库视图 (如果需要)
   - 使用原生SQL创建视图
   - 为复杂查询优化性能
5. 处理数据分离配置 (本地SQLite + 远程MySQL)
   - 配置数据库路由
   - 处理跨数据库关系
   - 确保数据一致性

具体要求：
- 迁移文件包含详细中文注释
- 支持回滚操作 (reverse_sql)
- 包含数据验证和完整性检查
- 考虑生产环境迁移安全性
- 遵循Django迁移最佳实践

请提供：
1. 迁移文件生成命令和最佳实践
2. 自定义迁移文件示例 (包含数据迁移)
3. 数据初始化脚本 (management commands)
4. 迁移执行和回滚指南
5. 生产环境迁移检查清单
```

#### ✅ 验收标准
- [ ] 所有迁移文件生成成功，命名规范
- [ ] 数据库表结构正确，符合设计要求
- [ ] 索引创建成功，查询性能优化
- [ ] 初始数据插入正确，包含必要的基础数据
- [ ] 支持迁移回滚，回滚操作安全
- [ ] 迁移文件包含详细中文注释
- [ ] 数据完整性约束正确设置
- [ ] 跨数据库迁移配置正确

---

## 🔌 阶段3: 后端API开发

### 3.1 基础API框架

#### 📋 任务目标
搭建REST API的基础框架，包括认证、权限、异常处理等。

#### 💬 Augment Code对话模板
```
请帮我搭建AI智能审校系统的REST API基础框架：

API框架要求：
1. 统一的响应格式 (成功/失败/错误码)
2. JWT认证系统
3. 基于角色的权限控制
4. 全局异常处理
5. API版本控制
6. 请求日志记录
7. 分页和过滤支持

具体实现：
1. 创建自定义Response类
2. 实现JWT认证中间件
3. 创建权限装饰器
4. 实现全局异常处理器
5. 配置API路由结构
6. 创建基础ViewSet类

请提供完整的框架代码和使用示例。
```

#### ✅ 验收标准
- [ ] API响应格式统一
- [ ] JWT认证正常工作
- [ ] 权限控制生效
- [ ] 异常处理完整
- [ ] 日志记录正常

### 3.2 文档管理API

#### 📋 任务目标
实现文档管理的完整API接口，支持CRUD操作和高级功能。

#### 💬 Augment Code对话模板
```
请帮我实现文档管理API：

API接口需求：
1. 文档CRUD操作
   - POST /api/v1/documents/ (创建文档)
   - GET /api/v1/documents/ (文档列表，支持分页、搜索、筛选)
   - GET /api/v1/documents/{id}/ (文档详情)
   - PUT /api/v1/documents/{id}/ (更新文档)
   - DELETE /api/v1/documents/{id}/ (删除文档)

2. 文档版本管理
   - GET /api/v1/documents/{id}/versions/ (版本列表)
   - POST /api/v1/documents/{id}/versions/ (创建新版本)
   - GET /api/v1/documents/{id}/versions/{version_id}/ (版本详情)

3. 文档对比
   - POST /api/v1/documents/compare/ (文档对比)
   - GET /api/v1/documents/{id}/diff/{version1}/{version2}/ (版本对比)

4. 文件上传
   - POST /api/v1/documents/upload/ (上传文档文件)
   - POST /api/v1/documents/{id}/upload-pdf/ (上传PDF文件)

技术要求：
- 使用Django REST Framework ViewSet
- 支持权限验证
- 包含输入验证
- 错误处理完整
- 详细的API文档注释

请提供完整的views.py、serializers.py和urls.py代码。
```

#### ✅ 验收标准
- [ ] 所有API接口正常工作
- [ ] 权限验证正确
- [ ] 输入验证完整
- [ ] 错误处理合理
- [ ] API文档清晰

### 3.3 用户认证API

#### 📋 任务目标
实现用户认证和权限管理的API接口。

#### 💬 Augment Code对话模板
```
请帮我实现用户认证和权限管理API：

认证API需求：
1. 用户注册登录
   - POST /api/v1/auth/register/ (用户注册)
   - POST /api/v1/auth/login/ (用户登录)
   - POST /api/v1/auth/logout/ (用户登出)
   - POST /api/v1/auth/refresh/ (刷新Token)

2. 用户管理
   - GET /api/v1/users/profile/ (获取用户信息)
   - PUT /api/v1/users/profile/ (更新用户信息)
   - POST /api/v1/users/change-password/ (修改密码)

3. 权限管理
   - GET /api/v1/users/permissions/ (获取用户权限)
   - GET /api/v1/roles/ (角色列表)
   - POST /api/v1/users/{id}/assign-role/ (分配角色)

技术要求：
- JWT Token认证
- 密码加密存储
- 权限验证中间件
- 登录日志记录
- 安全性考虑 (防暴力破解等)

请提供完整的认证系统实现代码。
```

#### ✅ 验收标准
- [ ] JWT认证正常工作
- [ ] 用户注册登录功能正常
- [ ] 权限验证正确
- [ ] 密码安全处理
- [ ] 登录日志记录

---

## 🎨 阶段4: 前端组件开发

### 4.1 基础组件

#### 📋 任务目标
开发通用的基础组件，为后续功能开发提供支撑。

#### 💬 Augment Code对话模板
```
请帮我开发AI智能审校系统的基础Vue3组件：

组件需求：
1. Layout组件 (主布局、侧边栏、顶部导航)
2. Loading组件 (加载状态显示)
3. Dialog组件 (模态对话框)
4. Table组件 (数据表格，支持分页、排序、筛选)
5. Form组件 (表单组件，支持验证)
6. Upload组件 (文件上传)
7. Breadcrumb组件 (面包屑导航)

技术要求：
- 使用Vue3 Composition API
- TypeScript类型定义
- Element Plus UI库
- 响应式设计
- 详细的中文注释
- 支持主题切换

请为每个组件提供：
1. 组件定义文件
2. TypeScript类型文件
3. 样式文件
4. 使用示例
```

#### ✅ 验收标准
- [ ] 所有组件功能正常
- [ ] TypeScript类型定义完整
- [ ] 响应式设计适配
- [ ] 组件文档完整
- [ ] 样式统一美观

## 📋 阶段3: API需求定义

### 3.1 API契约文档编写

#### 📋 任务目标
基于前端完整开发和Mock数据验证，整理和编写详细的API契约文档，为后端开发提供精确的规范。

#### 💬 Augment Code对话模板
```
基于前端完整开发和Mock数据验证，现在需要整理详细的API契约文档：

API契约文档要求（基于前端实际需求，遵循OpenAPI 3.0规范）：
1. 用户认证API (基于前端登录流程需求)
   - POST /api/v1/auth/login (用户登录)
   - POST /api/v1/auth/logout (用户登出)
   - POST /api/v1/auth/refresh (刷新Token)
   - GET /api/v1/auth/profile (获取用户信息)
   - PUT /api/v1/auth/profile (更新用户信息)
   - POST /api/v1/auth/register (用户注册)

2. 文档管理API (基于前端文档管理功能需求)
   - GET /api/v1/documents/ (文档列表，支持分页、搜索、筛选)
   - POST /api/v1/documents/ (创建文档)
   - GET /api/v1/documents/{id}/ (文档详情)
   - PUT /api/v1/documents/{id}/ (更新文档)
   - DELETE /api/v1/documents/{id}/ (删除文档)
   - GET /api/v1/documents/{id}/versions/ (文档版本列表)
   - POST /api/v1/documents/{id}/versions/ (创建文档版本)
   - POST /api/v1/documents/{id}/save/ (保存文档)

3. 文档对比API (基于前端对比功能需求)
   - POST /api/v1/documents/compare/ (文档对比)
   - GET /api/v1/documents/{id}/diff/{version1}/{version2}/ (版本对比)
   - POST /api/v1/documents/{id}/diff/save/ (保存对比结果)
   - GET /api/v1/documents/{id}/diff/history/ (对比历史)

4. AI校对API (基于前端AI功能需求)
   - POST /api/v1/ai/proofread/ (AI校对)
   - GET /api/v1/ai/proofread/{id}/ (获取校对结果)
   - POST /api/v1/ai/proofread/{id}/apply/ (应用校对建议)
   - GET /api/v1/ai/proofread/history/ (校对历史)

5. 文件管理API (基于前端文件操作需求)
   - POST /api/v1/files/upload/ (文件上传)
   - GET /api/v1/files/{id}/ (文件下载)
   - DELETE /api/v1/files/{id}/ (删除文件)
   - GET /api/v1/files/ (文件列表)

6. 系统配置API (基于前端设置页面需求)
   - GET /api/v1/settings/ (获取系统设置)
   - PUT /api/v1/settings/ (更新系统设置)
   - GET /api/v1/dashboard/stats/ (仪表板统计数据)

API规范要求（基于前端实际使用）：
- 统一的响应格式 (success, data, message, code)
- 完整的错误码定义
- 详细的请求/响应数据结构 (与Mock数据一致)
- 认证和权限要求说明
- 分页和筛选参数规范
- 文件上传格式和大小限制
- 性能要求 (响应时间<500ms)

请提供完整的OpenAPI 3.0规范文档，确保与前端Mock数据结构完全一致。
```

#### ✅ 验收标准
- [ ] API文档符合OpenAPI 3.0规范
- [ ] 所有接口定义完整清晰
- [ ] 请求/响应数据结构与Mock数据一致
- [ ] 错误码和错误处理完整
- [ ] 认证和权限要求明确
- [ ] 性能要求和限制说明清楚
- [ ] 文档可以生成交互式API文档
- [ ] 前端开发需求完全覆盖
- [ ] 与前端Mock API完全对应

#### 🔍 质量检查清单
- [ ] API设计遵循RESTful原则
- [ ] 数据结构与前端Mock数据完全一致
- [ ] 错误处理机制完善
- [ ] 安全性考虑充分
- [ ] 性能要求明确可测量
- [ ] 文档描述详细准确
- [ ] 示例数据与Mock数据一致
- [ ] 版本控制策略清晰

### 3.2 数据模型定义

#### 📋 任务目标
基于API契约文档和前端Mock数据，定义详细的数据模型结构，确保前后端数据结构完全一致。

#### 💬 Augment Code对话模板
```
基于API契约文档，请帮我定义详细的数据模型结构：

数据模型定义要求（遵循数据建模最佳实践）：
1. 用户相关模型
   - User (用户基础信息)
   - UserProfile (用户详细信息)
   - Role (角色定义)
   - Permission (权限定义)

2. 文档相关模型
   - Document (文档基础信息)
   - DocumentVersion (文档版本)
   - DocumentContent (文档内容)
   - DocumentMetadata (文档元数据)

3. 对比相关模型
   - ComparisonResult (对比结果)
   - DiffDetail (差异详情)
   - DiffStatistics (差异统计)

4. AI校对相关模型
   - ProofreadingResult (校对结果)
   - ProofreadingSuggestion (校对建议)
   - ProofreadingStatistics (校对统计)

5. 文件相关模型
   - FileUpload (文件上传)
   - FileMetadata (文件元数据)

6. 系统相关模型
   - ApiResponse (统一响应格式)
   - PaginationInfo (分页信息)
   - ErrorInfo (错误信息)

模型设计要求：
- 字段类型明确 (string, number, boolean, date等)
- 必填字段和可选字段标识
- 字段长度和格式限制
- 关联关系定义
- 索引设计建议
- 数据验证规则
- 默认值设置
- 软删除支持

请提供完整的数据模型定义文档，包含TypeScript接口定义和数据库表结构设计。
```

#### ✅ 验收标准
- [ ] 数据模型覆盖所有业务需求
- [ ] 字段定义完整准确
- [ ] 关联关系设计合理
- [ ] 数据验证规则完整
- [ ] TypeScript接口定义正确
- [ ] 数据库表结构设计合理
- [ ] 索引设计优化查询性能
- [ ] 支持软删除和版本控制

#### 🔍 质量检查清单
- [ ] 数据模型设计遵循范式理论
- [ ] 字段命名规范统一
- [ ] 数据类型选择合理
- [ ] 约束条件设置正确
- [ ] 性能考虑充分
- [ ] 扩展性设计良好
- [ ] 文档描述详细
- [ ] 前后端数据结构一致

---

### 3.3 Mock API完善

#### 📋 任务目标
基于前端开发过程中发现的需求，完善Mock API服务，确保覆盖所有业务场景和边界情况。

#### 💬 Augment Code对话模板
```
基于前端完整开发过程，请帮我完善Mock API服务：

Mock API完善要求（基于前端实际使用反馈）：
1. Mock服务优化
   - 完善所有API接口的Mock实现
   - 优化网络延迟模拟，更接近真实情况
   - 增强错误场景模拟的真实性
   - 支持复杂业务逻辑模拟

2. 数据生成优化
   - 根据前端使用情况优化测试数据
   - 增加更多中文数据场景
   - 完善数据关联关系
   - 支持大数据量测试

3. 业务逻辑完善
   - 完善用户认证流程模拟
   - 优化文档CRUD操作逻辑
   - 增强文档对比算法模拟
   - 完善文件上传下载流程
   - 优化AI校对结果生成

4. 状态管理优化
   - 优化数据持久化机制
   - 支持更复杂的用户场景
   - 增强并发操作支持
   - 完善数据同步机制

5. 错误场景完善
   - 增加更多网络错误类型
   - 完善服务器错误模拟
   - 优化权限错误处理
   - 增强数据验证错误场景

6. 性能模拟优化
   - 模拟真实的响应时间分布
   - 支持负载情况模拟
   - 增加内存使用模拟
   - 支持并发请求测试

Mock API配置优化：
- 更精确的响应时间模拟
- 可配置的错误率和错误类型
- 支持不同数据量级的测试
- 完善的开发/测试环境切换
- 详细的请求响应日志

请提供完善的Mock API实现代码和配置文档。
```

#### ✅ 验收标准
- [ ] Mock API覆盖所有定义的接口
- [ ] 数据生成真实可用
- [ ] 业务逻辑模拟正确
- [ ] 错误场景覆盖完整
- [ ] 前端可以完全基于Mock API开发
- [ ] 性能模拟真实
- [ ] 配置灵活可调
- [ ] 文档完整清晰

#### 🔍 质量检查清单
- [ ] Mock数据结构与API契约一致
- [ ] 业务逻辑模拟准确
- [ ] 错误处理场景完整
- [ ] 数据关联关系正确
- [ ] 性能模拟合理
- [ ] 配置文档详细
- [ ] 易于维护和扩展
- [ ] 支持自动化测试

### 3.4 前端Mock集成验证

#### 📋 任务目标
验证前端与Mock API的完整集成，确保所有功能基于Mock数据正常运行，为后端开发提供准确的验证标准。

#### 💬 Augment Code对话模板
```
请帮我验证前端与Mock API的完整集成：

前端Mock集成验证要求（确保前端功能完整性）：
1. API服务集成验证
   - 验证axios API客户端正常工作
   - 验证请求/响应拦截器功能
   - 验证统一错误处理机制
   - 验证请求取消和重试机制
   - 验证Mock/真实API切换功能

2. 状态管理集成验证
   - 验证Pinia store与Mock API集成
   - 验证数据缓存策略有效性
   - 验证加载状态管理
   - 验证错误状态处理
   - 验证状态持久化

3. 组件API集成验证
   - 验证文档列表组件与Mock API集成
   - 验证编辑器组件与Mock API集成
   - 验证对比组件与Mock API集成
   - 验证文件上传组件与Mock API集成
   - 验证所有组件的数据流转

4. 完整用户流程验证
   - 验证用户登录注册流程
   - 验证文档创建编辑流程
   - 验证文档对比查看流程
   - 验证文件上传下载流程
   - 验证AI校对功能流程
   - 验证设置配置流程

5. 错误场景处理验证
   - 验证网络错误处理
   - 验证认证失败处理
   - 验证权限不足处理
   - 验证数据验证错误处理
   - 验证服务器错误处理

6. 性能和兼容性验证
   - 验证大文档处理性能
   - 验证并发请求处理
   - 验证内存使用情况
   - 验证响应时间表现
   - 验证浏览器兼容性

验证工具和方法：
- 手动功能测试
- 自动化测试用例
- 性能监控工具
- 错误日志分析

请提供完整的验证报告和问题清单，确保前端功能完全可用。
```

#### ✅ 验收标准
- [ ] 前端与Mock API集成完全正常
- [ ] 所有用户流程可以完整验证
- [ ] 错误场景处理完整
- [ ] 性能表现满足要求
- [ ] 兼容性验证通过
- [ ] Mock数据覆盖所有业务场景
- [ ] API切换机制验证通过
- [ ] 验证报告完整详细

#### 🔍 质量检查清单
- [ ] API调用封装合理，易于维护
- [ ] 错误处理机制完善
- [ ] 加载状态管理友好
- [ ] 数据缓存策略合理
- [ ] 组件与API解耦良好
- [ ] Mock数据验证全面
- [ ] 性能表现优秀
- [ ] 代码质量高，注释详细
- [ ] 为后端开发提供明确标准

---

## 🗄️ 阶段4: 后端项目初始化

### 4.1 后端项目搭建

#### 📋 任务目标
基于前端完整开发和API需求文档，搭建Django后端项目，为精确实现API接口做准备。

#### 💬 Augment Code对话模板
```
基于前端完整开发和API需求文档，现在需要搭建Django后端项目：

后端项目搭建要求（严格按照API契约实现）：
1. 创建Django 5后端项目结构
   - 项目名称：backend
   - 应用模块：users, documents, diff_engine, ai_proofreading, files
   - 配置Django REST Framework (JWT认证、权限控制、分页)
   - 配置CORS (django-cors-headers)
   - 配置MySQL和Redis连接 (支持多环境配置)
   - 遵循MVT架构模式，使用自定义Manager和Signal

2. 后端开发环境配置
   - Django: Black格式化 + flake8检查 + pytest测试
   - 数据库配置 (开发环境使用SQLite，生产环境使用MySQL)
   - Redis缓存配置
   - 日志配置
   - 环境变量管理

3. 基础API框架搭建
   - 统一响应格式 (与前端Mock API一致)
   - 全局异常处理
   - API版本控制
   - 请求日志记录
   - 健康检查接口

4. 项目目录结构（遵循Django最佳实践）
   - backend/
     - config/ (项目配置，分环境settings)
     - apps/ (应用模块，每个应用独立)
     - requirements/ (依赖管理 - 分环境requirements)
     - tests/ (测试文件 - pytest配置)
     - static/ (静态文件)
     - media/ (媒体文件)
     - logs/ (日志文件)

5. API接口框架准备
   - 根据API契约文档准备接口结构
   - 配置序列化器基础框架
   - 准备ViewSet基础类
   - 配置权限和认证系统

请提供完整的后端项目初始化脚本和配置文件，确保严格按照前端API需求实现。
```

## 🔗 阶段7: 前后端对接集成

### 7.1 API接口联调

#### 📋 任务目标
完成前后端API接口联调，确保前端可以无缝从Mock API切换到真实API。

#### 💬 Augment Code对话模板
```
请帮我完成前后端API接口联调，实现从Mock API到真实API的无缝切换：

API接口联调任务（确保前后端数据格式完全一致）：
1. API接口逐一验证
   - 验证用户认证API (登录、注册、权限验证)
   - 验证文档管理API (CRUD、搜索、分页)
   - 验证文档对比API (对比、版本管理)
   - 验证AI校对API (校对请求、结果处理)
   - 验证文件管理API (上传、下载、删除)
   - 验证系统配置API (设置、统计数据)

2. 数据格式对比验证
   - 对比Mock API和真实API的响应格式
   - 验证数据结构的完全一致性
   - 检查字段类型和命名规范
   - 验证错误码和错误信息格式
   - 确认分页和筛选参数格式

3. 前端API调用调整
   - 调整axios配置，支持真实API
   - 更新环境变量配置
   - 验证请求/响应拦截器
   - 调整错误处理逻辑
   - 优化加载状态管理

4. 认证和权限集成
   - 集成JWT token认证
   - 验证权限控制机制
   - 实现token自动刷新
   - 处理认证失败场景
   - 验证路由权限守卫

5. 错误处理统一
   - 统一前后端错误码
   - 完善错误信息显示
   - 处理网络异常情况
   - 实现友好的错误提示
   - 添加错误日志记录

请提供完整的联调代码和问题解决方案。
```

#### ✅ 验收标准
- [ ] 所有API接口联调成功
- [ ] 前端成功切换到真实API
- [ ] 数据格式完全一致
- [ ] 认证和权限正常工作
- [ ] 错误处理统一有效
- [ ] 性能满足要求
- [ ] 用户体验无差异
- [ ] 所有功能正常运行

### 7.2 前端切换真实API

#### 📋 任务目标
将前端从Mock API完全切换到真实API，确保功能无缝迁移。

#### 💬 Augment Code对话模板
```
请帮我将前端从Mock API切换到真实API：

API切换任务（确保无缝迁移）：
1. 环境配置切换
   - 更新.env文件，设置VITE_USE_MOCK_API=false
   - 配置真实API的基础URL
   - 更新API端点配置
   - 验证环境变量生效

2. API客户端配置
   - 更新axios配置，指向真实API
   - 验证请求拦截器配置
   - 确认响应拦截器处理
   - 测试错误处理机制

3. 数据格式验证
   - 验证所有API响应格式
   - 检查数据类型转换
   - 确认分页数据处理
   - 验证错误响应格式

4. 功能完整性测试
   - 测试所有用户流程
   - 验证数据CRUD操作
   - 检查文件上传下载
   - 测试实时功能

5. 性能和稳定性验证
   - 测试API响应时间
   - 验证并发请求处理
   - 检查内存使用情况
   - 测试错误恢复机制

请提供完整的切换方案和验证结果。
```

#### ✅ 验收标准
- [ ] 前端成功切换到真实API
- [ ] 所有功能正常运行
- [ ] 性能满足要求
- [ ] 错误处理正常
- [ ] 用户体验良好

### 7.3 集成测试验证

#### 📋 任务目标
执行全面的集成测试，验证前后端系统的完整性和稳定性。

#### 💬 Augment Code对话模板
```
请帮我编写系统功能测试，遵循Django和Vue3测试最佳实践：

测试需求（遵循测试金字塔原则）：
1. 单元测试（遵循Django和Vue3测试最佳实践）
   - Django模型测试
     * 使用Django TestCase和pytest
     * 测试模型验证、关系和自定义方法
     * 使用Factory Boy创建测试数据
     * 测试自定义Manager和QuerySet
   - API接口测试
     * 使用Django REST Framework测试工具
     * 测试认证、权限和数据验证
     * 使用APIClient进行接口测试
     * 测试序列化器和视图集
   - Vue组件测试
     * 使用Vitest和Vue Test Utils
     * 测试组件的props、emits和slots
     * 测试Composition API的逻辑
     * 使用@vue/test-utils进行组件渲染测试
   - 工具函数测试
     * 测试纯函数和工具类
     * 使用参数化测试覆盖边界情况
     * 测试错误处理和异常情况

2. 集成测试（测试组件间交互）
   - 前后端集成测试
     * 测试API调用和数据流转
     * 测试认证和权限集成
     * 使用Mock服务进行接口测试
   - 数据库操作测试
     * 测试复杂查询和事务
     * 测试数据迁移和回滚
     * 使用测试数据库进行隔离测试
   - 第三方服务测试
     * 测试AI API集成
     * 使用Mock和Stub模拟外部服务
     * 测试服务降级和容错机制
   - 文件处理测试
     * 测试文件上传和下载
     * 测试PDF处理和生成
     * 测试文件存储和访问

3. 端到端测试（用户场景测试）
   - 用户流程测试
     * 测试完整的用户操作流程
     * 使用Cypress进行自动化测试
     * 测试关键业务场景
   - 浏览器兼容性测试
     * 测试主流浏览器兼容性
     * 测试响应式设计
     * 测试移动端适配
   - 性能测试
     * 使用Lighthouse进行性能测试
     * 测试API响应时间和吞吐量
     * 测试前端加载性能
   - 安全测试
     * 测试XSS和CSRF防护
     * 测试SQL注入防护
     * 测试认证和授权安全

测试工具和配置：
- 后端：pytest + Django TestCase + Factory Boy + coverage
- 前端：Vitest + Vue Test Utils + @testing-library/vue
- E2E：Cypress + Percy (视觉回归测试)
- 性能：Lighthouse + WebPageTest
- 安全：OWASP ZAP + Bandit

请提供完整的测试代码和测试计划，确保测试覆盖率和质量。
```

#### ✅ 验收标准
- [ ] 测试覆盖率>90%，关键路径100%覆盖
- [ ] 所有测试用例通过，无失败用例
- [ ] 性能测试达标，满足性能要求
- [ ] 安全测试通过，无安全漏洞
- [ ] 兼容性测试通过，支持主流浏览器
- [ ] 测试代码质量高，遵循最佳实践
- [ ] 测试文档完整，便于维护
- [ ] CI/CD集成，自动化测试执行
- [ ] 测试数据管理规范，测试环境隔离

### 6.3 性能优化

#### 📋 任务目标
优化系统性能，确保满足性能要求。

#### 💬 Augment Code对话模板
```
请帮我优化系统性能，遵循Django和Vue3性能优化最佳实践：

优化目标（基于性能指标要求）：
1. 前端性能优化（遵循Vue3性能最佳实践）
   - 首屏加载时间 <2s
     * 使用代码分割和懒加载
     * 实施资源预加载和预连接
     * 优化关键渲染路径
   - 组件渲染优化
     * 使用v-memo缓存渲染结果
     * 实施虚拟滚动处理大列表
     * 使用keep-alive缓存组件状态
   - 代码分割和打包优化
     * 使用Vite的动态导入
     * 配置合理的chunk分割策略
     * 实施Tree Shaking去除无用代码
   - 资源压缩和优化
     * 图片压缩和WebP格式转换
     * CSS和JS文件压缩
     * 使用CDN加速静态资源

2. 后端性能优化（遵循Django性能最佳实践）
   - API响应时间 <500ms
     * 使用select_related和prefetch_related优化查询
     * 实施数据库连接池
     * 使用Django缓存框架
   - 数据库查询优化
     * 添加合适的数据库索引
     * 使用explain分析查询计划
     * 实施查询结果缓存
   - 缓存策略
     * 使用Redis缓存热点数据
     * 实施分层缓存策略
     * 配置缓存过期和更新机制
   - 并发处理
     * 使用Gunicorn多进程部署
     * 配置合理的worker数量
     * 实施异步任务处理 (Celery)

3. 算法性能优化
   - 文本对比 <2s (10万字符)
     * 实施分块处理大文档
     * 使用多线程并行计算
     * 优化差异算法复杂度
   - 内存使用优化
     * 使用生成器处理大数据
     * 实施内存池管理
     * 及时释放不用的对象
   - 并行处理
     * 使用multiprocessing进行CPU密集计算
     * 实施任务队列异步处理
     * 配置合理的并发数量
   - 结果缓存
     * 缓存计算结果避免重复计算
     * 使用LRU缓存策略
     * 实施缓存预热机制

优化策略和监控：
1. 性能监控
   - 使用Django Debug Toolbar监控查询
   - 配置APM工具监控应用性能
   - 实施前端性能监控
2. 瓶颈分析
   - 使用profiling工具分析性能瓶颈
   - 分析数据库慢查询日志
   - 监控系统资源使用情况
3. 优化实施
   - 按优先级实施优化措施
   - 进行A/B测试验证效果
   - 持续监控和调优
4. 效果验证
   - 使用性能测试验证优化效果
   - 监控生产环境性能指标
   - 建立性能基线和告警

请提供具体的优化方案和实现代码，确保符合最佳实践。
```

#### ✅ 验收标准
- [ ] 前端加载时间达标，首屏<2s
- [ ] API响应时间达标，平均<500ms
- [ ] 算法性能达标，10万字符对比<2s
- [ ] 内存使用合理，无内存泄漏
- [ ] 并发处理正常，支持100并发用户
- [ ] 数据库查询优化，慢查询<1%
- [ ] 缓存命中率高，>80%
- [ ] 资源压缩有效，减少传输大小
- [ ] 性能监控完善，实时监控关键指标

---

## 🚀 阶段7: 部署优化

### 7.1 生产环境配置

#### 📋 任务目标
配置生产环境，确保系统稳定运行。

#### 💬 Augment Code对话模板
```
请帮我配置生产环境，遵循Django和Vue3部署最佳实践：

部署需求（遵循容器化和微服务最佳实践）：
1. Docker容器化（遵循Docker最佳实践）
   - Django应用容器
     * 使用多阶段构建优化镜像大小
     * 配置非root用户运行应用
     * 使用Gunicorn作为WSGI服务器
     * 配置健康检查和优雅关闭
   - Vue应用容器
     * 使用Nginx作为静态文件服务器
     * 配置Gzip压缩和缓存策略
     * 实施安全头配置
     * 支持SPA路由配置
   - 数据库容器
     * 使用官方MySQL镜像
     * 配置数据持久化存储
     * 设置备份和恢复策略
     * 配置主从复制 (如需要)
   - Redis容器
     * 配置持久化和集群模式
     * 设置内存限制和淘汰策略
     * 配置安全认证
   - Nginx容器
     * 配置反向代理和负载均衡
     * 实施SSL/TLS终止
     * 配置限流和防护策略

2. 环境配置（遵循12-Factor App原则）
   - 生产环境变量
     * 使用.env文件管理敏感配置
     * 配置数据库连接字符串
     * 设置API密钥和第三方服务配置
   - 数据库配置
     * 配置连接池和超时设置
     * 启用查询日志和慢查询监控
     * 配置备份和恢复策略
   - 缓存配置
     * 配置Redis集群和哨兵模式
     * 设置缓存策略和过期时间
     * 配置缓存监控和告警
   - 日志配置
     * 使用结构化日志格式
     * 配置日志轮转和归档
     * 集成ELK或类似日志系统
   - 安全配置
     * 配置防火墙和网络隔离
     * 启用HTTPS和HSTS
     * 配置安全头和CSP策略

3. 服务编排（使用Docker Compose或Kubernetes）
   - docker-compose配置
     * 定义服务依赖关系
     * 配置网络和存储卷
     * 设置环境变量和秘钥管理
   - 服务依赖管理
     * 配置服务启动顺序
     * 实施依赖健康检查
     * 配置服务发现机制
   - 健康检查
     * 配置应用健康检查端点
     * 设置检查间隔和超时
     * 实施自动故障转移
   - 自动重启
     * 配置重启策略
     * 设置资源限制
     * 实施优雅关闭机制

4. 监控和日志（实施全面监控）
   - 应用监控
     * 使用Prometheus + Grafana
     * 监控应用性能指标
     * 配置告警规则和通知
   - 性能监控
     * 监控响应时间和吞吐量
     * 跟踪数据库性能
     * 监控系统资源使用
   - 错误日志
     * 集成Sentry错误追踪
     * 配置错误分类和告警
     * 实施错误趋势分析
   - 访问日志
     * 配置Nginx访问日志
     * 实施日志分析和统计
     * 监控异常访问模式

请提供完整的部署配置文件，确保符合生产环境最佳实践。
```

#### ✅ 验收标准
- [ ] Docker镜像构建成功，镜像大小优化
- [ ] 容器正常启动，健康检查通过
- [ ] 服务间通信正常，网络配置正确
- [ ] 监控系统工作，指标收集正常
- [ ] 日志收集正常，日志格式规范
- [ ] 安全配置到位，通过安全扫描
- [ ] 性能满足要求，负载测试通过
- [ ] 备份恢复机制有效
- [ ] SSL证书配置正确，HTTPS访问正常

### 7.2 部署脚本

#### 📋 任务目标
编写自动化部署脚本，简化部署流程。

#### 💬 Augment Code对话模板
```
请帮我编写自动化部署脚本，遵循DevOps最佳实践：

部署脚本需求（遵循CI/CD最佳实践）：
1. 构建脚本（实施自动化构建）
   - 代码拉取
     * 从Git仓库拉取指定版本代码
     * 验证代码完整性和签名
     * 处理子模块和依赖仓库
   - 依赖安装
     * Python依赖安装 (pip install -r requirements.txt)
     * Node.js依赖安装 (npm ci)
     * 系统依赖检查和安装
   - 应用构建
     * Django静态文件收集
     * Vue应用构建 (npm run build)
     * 代码质量检查 (lint, test)
   - 镜像打包
     * Docker镜像构建和标签
     * 镜像安全扫描
     * 推送到镜像仓库

2. 部署脚本（实施零停机部署）
   - 环境检查
     * 检查系统资源和依赖
     * 验证配置文件和环境变量
     * 检查数据库连接和权限
   - 服务停止
     * 优雅关闭应用服务
     * 等待请求处理完成
     * 保存应用状态
   - 更新部署
     * 拉取新版本镜像
     * 更新配置文件
     * 执行数据库迁移
   - 服务启动
     * 启动新版本服务
     * 配置负载均衡
     * 预热应用缓存
   - 健康检查
     * 检查服务健康状态
     * 验证关键功能
     * 监控性能指标

3. 回滚脚本（实施快速回滚）
   - 版本回滚
     * 回滚到上一个稳定版本
     * 恢复配置文件
     * 回滚数据库迁移 (如需要)
   - 数据恢复
     * 从备份恢复数据
     * 验证数据完整性
     * 重建索引和缓存
   - 服务重启
     * 重启应用服务
     * 更新负载均衡配置
     * 验证服务可用性

4. 维护脚本（实施自动化运维）
   - 数据备份
     * 定期备份数据库
     * 备份应用配置和日志
     * 验证备份完整性
   - 日志清理
     * 清理过期日志文件
     * 压缩和归档日志
     * 维护日志索引
   - 性能监控
     * 收集性能指标
     * 生成性能报告
     * 发送告警通知
   - 安全检查
     * 扫描安全漏洞
     * 检查权限配置
     * 更新安全补丁

5. CI/CD集成（实施持续集成和部署）
   - GitHub Actions / GitLab CI配置
   - 自动化测试和构建
   - 分环境部署策略
   - 部署审批和通知机制

请提供完整的Shell脚本和使用文档，确保符合DevOps最佳实践。
```

#### ✅ 验收标准
- [ ] 部署脚本正常执行，无错误
- [ ] 自动化流程完整，支持CI/CD
- [ ] 错误处理完善，异常情况可恢复
- [ ] 回滚功能正常，可快速回滚
- [ ] 文档清晰完整，操作步骤明确
- [ ] 零停机部署，用户无感知
- [ ] 健康检查有效，确保服务可用
- [ ] 备份恢复机制可靠
- [ ] 监控告警及时，问题快速发现
- [ ] 安全检查通过，符合安全标准

---

## 🎯 Augment Code协作技巧

### 💡 高效对话策略

#### 1. 明确任务边界（基于Django和Vue3最佳实践）
```
✅ 好的提问（Django后端）：
"请帮我实现Document模型的CRUD API，遵循Django REST Framework最佳实践：
- 使用ViewSet和序列化器
- 支持分页、搜索和过滤
- 实施JWT认证和基于角色的权限验证
- 包含输入验证和错误处理
- 返回标准化的JSON格式数据
- 遵循RESTful API设计原则"

✅ 好的提问（Vue3前端）：
"请帮我实现文档列表组件，遵循Vue3 Composition API最佳实践：
- 使用<script setup>语法和TypeScript
- 实现响应式数据管理和状态同步
- 支持分页、搜索和排序功能
- 使用Element Plus UI组件
- 实施错误处理和加载状态管理
- 遵循可访问性标准"

❌ 不好的提问：
"帮我写个文档管理的API" / "做个文档列表页面"
```

#### 2. 提供充分上下文（包含技术规范要求）
```
✅ 包含完整项目信息：
- 技术栈：Django 5 + Vue3 + TypeScript
- 数据库：MySQL + Redis
- 认证方式：JWT + 基于角色的权限控制
- 相关模型：User, Project, Document, DocumentVersion
- 前端状态管理：Pinia
- 代码规范：PEP 8 + ESLint + Prettier
- 性能要求：API响应<500ms，前端加载<2s
- 安全要求：遵循OWASP安全指南
```

#### 3. 分步骤验证（遵循最佳实践流程）
```
✅ 渐进式开发（Django后端）：
1. 先实现模型和序列化器（遵循DRY原则）
2. 创建基础ViewSet（使用Django REST Framework）
3. 添加认证和权限控制
4. 实施输入验证和错误处理
5. 优化数据库查询性能
6. 编写单元测试和集成测试
7. 完善API文档和注释

✅ 渐进式开发（Vue3前端）：
1. 创建基础组件结构（使用Composition API）
2. 实现数据获取和状态管理（Pinia）
3. 添加用户交互和事件处理
4. 实施错误处理和加载状态
5. 优化性能（懒加载、虚拟滚动）
6. 编写组件测试
7. 完善TypeScript类型定义和文档
```

#### 4. 遵循代码质量标准
```
✅ Django代码质量要求：
- 遵循PEP 8代码风格
- 使用类型提示 (Type Hints)
- 实施DRY原则，避免代码重复
- 使用Django最佳实践模式
- 包含详细的中文注释
- 实施适当的错误处理
- 编写单元测试

✅ Vue3代码质量要求：
- 使用TypeScript严格模式
- 遵循Vue3 Composition API最佳实践
- 实施组件的props和emits验证
- 使用ESLint和Prettier格式化
- 包含详细的中文注释
- 实施错误边界和异常处理
- 编写组件测试
```

### 🔧 代码质量保证

#### 检查清单模板（基于Django和Vue3最佳实践）

##### Django后端代码审查清单：
```
Django代码审查清单：
□ 功能是否符合需求，遵循业务逻辑
□ 代码逻辑是否正确，遵循MVT架构
□ 模型设计是否合理，关系配置正确
□ 序列化器是否完整，验证规则正确
□ ViewSet是否遵循REST原则
□ 错误处理是否完整，异常捕获合理
□ 数据库查询是否优化，避免N+1问题
□ 缓存策略是否合理，提升性能
□ 安全性是否考虑，防止常见漏洞
□ 权限控制是否正确，访问控制到位
□ 注释是否详细，遵循中文注释规范
□ 单元测试是否覆盖，测试用例完整
□ 代码风格是否统一，遵循PEP 8
□ 日志记录是否合理，便于调试
□ 配置管理是否安全，敏感信息保护
```

##### Vue3前端代码审查清单：
```
Vue3代码审查清单：
□ 功能是否符合需求，用户体验良好
□ 组件逻辑是否正确，Composition API使用合理
□ TypeScript类型是否完整，类型安全
□ Props和Emits定义是否正确，接口清晰
□ 状态管理是否合理，Pinia使用正确
□ 错误处理是否完整，用户友好提示
□ 性能是否优化，懒加载和缓存合理
□ 响应式设计是否适配，多设备支持
□ 可访问性是否考虑，ARIA属性完整
□ 安全性是否考虑，XSS防护到位
□ 注释是否详细，遵循中文注释规范
□ 组件测试是否覆盖，测试用例完整
□ 代码风格是否统一，遵循ESLint规范
□ 组件复用性是否良好，设计合理
□ 路由配置是否正确，权限控制有效
```

### 📝 常用对话模板

#### 功能开发模板
```
我需要开发[功能名称]功能：

功能描述：
[详细描述功能需求和业务逻辑]

技术要求：
- 使用技术栈：[具体技术]
- 性能要求：[具体指标]
- 安全要求：[安全考虑]

验收标准：
1. [具体验收条件1]
2. [具体验收条件2]
3. [具体验收条件3]

请提供完整的实现方案和代码。
```

#### 问题排查模板
```
我遇到了以下问题：

问题现象：
[详细描述问题表现]

错误信息：
[完整的错误日志或截图]

环境信息：
- 操作系统：[系统版本]
- 软件版本：[相关软件版本]
- 浏览器：[浏览器版本]

重现步骤：
1. [步骤1]
2. [步骤2]
3. [步骤3]

期望结果：
[描述期望的正确行为]

已尝试方案：
[列出已经尝试过的解决方案]

请帮我分析问题原因并提供解决方案。
```

#### 代码审查模板
```
请审查以下代码：

代码功能：
[说明代码的功能和用途]

代码内容：
[粘贴代码]

审查重点：
1. 代码逻辑是否正确
2. 是否存在性能问题
3. 是否符合最佳实践
4. 是否需要添加注释
5. 是否需要优化
6. 安全性是否考虑

请提供详细的审查意见和改进建议。
```

---

## 📚 Django和Vue3规范应用指南

### 🐍 Django最佳实践应用

#### 模型设计规范
```python
# ✅ 好的模型设计示例
class Document(BaseModel):
    """
    文档模型 - 遵循Django最佳实践

    继承BaseModel提供通用字段：
    - id: 主键
    - created_at: 创建时间
    - updated_at: 更新时间
    - is_deleted: 软删除标记
    """
    title = models.CharField(
        max_length=200,
        verbose_name="文档标题",
        help_text="文档的标题，最大长度200字符"
    )
    content = models.TextField(
        verbose_name="文档内容",
        help_text="文档的主要内容"
    )
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name="作者"
    )

    # 自定义Manager
    objects = DocumentManager()

    class Meta:
        verbose_name = "文档"
        verbose_name_plural = "文档"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['title']),
            models.Index(fields=['author', 'created_at']),
        ]

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('document-detail', kwargs={'pk': self.pk})
```

#### ViewSet设计规范
```python
# ✅ 好的ViewSet设计示例
class DocumentViewSet(viewsets.ModelViewSet):
    """
    文档ViewSet - 遵循DRF最佳实践

    提供文档的CRUD操作，包括：
    - 列表查询（支持分页、搜索、过滤）
    - 详情查询
    - 创建文档
    - 更新文档
    - 删除文档（软删除）
    """
    queryset = Document.objects.select_related('author').prefetch_related('versions')
    serializer_class = DocumentSerializer
    permission_classes = [IsAuthenticated, DocumentPermission]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['author', 'created_at']
    search_fields = ['title', 'content']
    ordering_fields = ['created_at', 'updated_at', 'title']
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        """
        重写queryset，只返回当前用户有权限的文档
        """
        return super().get_queryset().filter(
            author=self.request.user
        ).exclude(is_deleted=True)

    def perform_create(self, serializer):
        """
        创建文档时自动设置作者
        """
        serializer.save(author=self.request.user)

    @action(detail=True, methods=['post'])
    def create_version(self, request, pk=None):
        """
        为文档创建新版本
        """
        document = self.get_object()
        # 实现版本创建逻辑
        return Response({'status': 'version created'})
```

### 🎨 Vue3最佳实践应用

#### 组件设计规范
```vue
<!-- ✅ 好的Vue3组件设计示例 -->
<template>
  <div class="document-list">
    <!-- 搜索和过滤区域 -->
    <div class="search-section">
      <el-input
        v-model="searchQuery"
        placeholder="搜索文档..."
        @input="handleSearch"
        clearable
      />
      <el-select v-model="selectedAuthor" placeholder="选择作者" clearable>
        <el-option
          v-for="author in authors"
          :key="author.id"
          :label="author.name"
          :value="author.id"
        />
      </el-select>
    </div>

    <!-- 文档列表 -->
    <div class="document-grid">
      <DocumentCard
        v-for="document in documents"
        :key="document.id"
        :document="document"
        @edit="handleEdit"
        @delete="handleDelete"
      />
    </div>

    <!-- 分页组件 -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useDocumentStore } from '@/stores/document'
import { useUserStore } from '@/stores/user'
import type { Document, User } from '@/types'

// Props定义
interface Props {
  initialQuery?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialQuery: ''
})

// Emits定义
interface Emits {
  (e: 'document-selected', document: Document): void
  (e: 'search-changed', query: string): void
}

const emit = defineEmits<Emits>()

// Store使用
const documentStore = useDocumentStore()
const userStore = useUserStore()

// 响应式数据
const searchQuery = ref(props.initialQuery)
const selectedAuthor = ref<number | null>(null)
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const documents = computed(() => documentStore.documents)
const total = computed(() => documentStore.total)
const authors = computed(() => userStore.authors)
const loading = computed(() => documentStore.loading)

// 方法定义
const handleSearch = useDebounceFn((query: string) => {
  emit('search-changed', query)
  fetchDocuments()
}, 300)

const handleEdit = (document: Document) => {
  emit('document-selected', document)
}

const handleDelete = async (document: Document) => {
  try {
    await documentStore.deleteDocument(document.id)
    ElMessage.success('文档删除成功')
  } catch (error) {
    ElMessage.error('删除失败，请重试')
  }
}

const fetchDocuments = async () => {
  try {
    await documentStore.fetchDocuments({
      search: searchQuery.value,
      author: selectedAuthor.value,
      page: currentPage.value,
      page_size: pageSize.value
    })
  } catch (error) {
    ElMessage.error('获取文档列表失败')
  }
}

// 生命周期
onMounted(() => {
  fetchDocuments()
  userStore.fetchAuthors()
})

// 监听器
watch([selectedAuthor, currentPage, pageSize], () => {
  fetchDocuments()
})
</script>

<style scoped lang="scss">
.document-list {
  padding: 20px;

  .search-section {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;

    .el-input {
      flex: 1;
      max-width: 300px;
    }
  }

  .document-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
  }
}
</style>
```

#### Pinia Store设计规范
```typescript
// ✅ 好的Pinia Store设计示例
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Document, DocumentQuery, ApiResponse } from '@/types'
import { documentApi } from '@/api/document'

export const useDocumentStore = defineStore('document', () => {
  // State
  const documents = ref<Document[]>([])
  const currentDocument = ref<Document | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const total = ref(0)
  const currentPage = ref(1)

  // Getters
  const hasDocuments = computed(() => documents.value.length > 0)
  const isLoading = computed(() => loading.value)
  const currentError = computed(() => error.value)

  // Actions
  const fetchDocuments = async (query: DocumentQuery = {}) => {
    loading.value = true
    error.value = null

    try {
      const response: ApiResponse<Document[]> = await documentApi.getDocuments(query)

      if (response.success) {
        documents.value = response.data
        total.value = response.total || 0
        currentPage.value = query.page || 1
      } else {
        throw new Error(response.message || '获取文档失败')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '未知错误'
      console.error('获取文档失败:', err)
    } finally {
      loading.value = false
    }
  }

  const createDocument = async (documentData: Partial<Document>) => {
    loading.value = true
    error.value = null

    try {
      const response = await documentApi.createDocument(documentData)

      if (response.success) {
        documents.value.unshift(response.data)
        return response.data
      } else {
        throw new Error(response.message || '创建文档失败')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateDocument = async (id: number, documentData: Partial<Document>) => {
    loading.value = true
    error.value = null

    try {
      const response = await documentApi.updateDocument(id, documentData)

      if (response.success) {
        const index = documents.value.findIndex(doc => doc.id === id)
        if (index !== -1) {
          documents.value[index] = response.data
        }
        return response.data
      } else {
        throw new Error(response.message || '更新文档失败')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteDocument = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await documentApi.deleteDocument(id)

      if (response.success) {
        documents.value = documents.value.filter(doc => doc.id !== id)
      } else {
        throw new Error(response.message || '删除文档失败')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const resetStore = () => {
    documents.value = []
    currentDocument.value = null
    loading.value = false
    error.value = null
    total.value = 0
    currentPage.value = 1
  }

  return {
    // State
    documents,
    currentDocument,
    loading,
    error,
    total,
    currentPage,

    // Getters
    hasDocuments,
    isLoading,
    currentError,

    // Actions
    fetchDocuments,
    createDocument,
    updateDocument,
    deleteDocument,
    clearError,
    resetStore
  }
})
```

---

## 🚀 前后端分离开发工作流程指南

### 📋 前后端分离开发核心原则

#### 1. 前端优先，Mock数据驱动
```
✅ 前后端分离开发流程：
1. 用户需求分析 → 界面设计 → 交互原型
2. 前端完整开发 → Mock数据验证 → 功能完善
3. API需求整理 → 数据结构确定 → 后端开发
4. 前后端对接 → 无缝切换验证 → 产品发布

❌ 传统开发流程：
1. 需求分析 → 数据库设计 → 后端API开发
2. 前端界面开发 → 前后端集成 → 测试修复
```

#### 2. Mock数据完整验证
```
✅ Mock数据驱动开发：
1. 前端开发使用完整的Mock数据
2. Mock数据覆盖所有业务场景和边界情况
3. 前端功能基于Mock数据完全可用
4. API契约基于Mock数据结构定义
5. 后端严格按照Mock数据格式实现

好处：
- 前端可以完全独立开发和测试
- API设计更贴近前端实际需求
- 后端开发有明确的数据格式规范
- 前后端对接问题最小化
```

#### 3. 无缝对接验证
```
✅ 无缝对接流程：
1. 前端基于Mock数据完整开发
2. 后端按照API契约精确实现
3. 前端一键切换到真实API
4. 验证功能无差异运行
5. 确保用户体验完全一致

验证重点：
- 数据格式完全一致
- 功能行为完全一致
- 性能表现满足要求
- 错误处理机制一致
```

### 🔄 前后端分离开发检查点

#### 阶段1检查点：前端独立环境
- [ ] 前端项目可以完全独立运行
- [ ] Mock API服务覆盖所有接口
- [ ] 开发工具配置完整
- [ ] 代码质量检查通过

#### 阶段2检查点：前端功能完整
- [ ] 所有页面组件开发完成
- [ ] 用户交互流程完整
- [ ] Mock数据验证功能完整
- [ ] 用户体验验证通过

#### 阶段3检查点：API需求明确
- [ ] API契约文档与Mock数据一致
- [ ] 数据模型定义清晰
- [ ] Mock API功能完善
- [ ] 前端Mock集成验证通过

#### 阶段4-6检查点：后端开发完成
- [ ] 后端项目按规范搭建
- [ ] 数据库模型实现正确
- [ ] API接口严格按契约实现
- [ ] 后端测试覆盖率达标

#### 阶段7检查点：无缝对接完成
- [ ] 前端成功切换到真实API
- [ ] 所有功能正常运行
- [ ] 性能测试达标
- [ ] 用户体验无差异

### 📚 前后端分离开发最佳实践

#### 1. 前端开发原则
- **完整独立**：前端可以完全独立开发和运行
- **Mock驱动**：所有功能基于Mock数据验证
- **API抽象**：API调用层支持Mock/真实API切换
- **用户体验优先**：以最佳用户体验为开发目标

#### 2. Mock数据设计原则
- **真实性**：Mock数据尽可能接近真实数据
- **完整性**：覆盖所有业务场景和边界情况
- **一致性**：与最终API格式完全一致
- **可维护性**：易于更新和扩展

#### 3. API契约设计原则
- **前端驱动**：API设计以前端实际需求为准
- **格式统一**：与Mock数据格式完全一致
- **文档详细**：包含完整的请求响应示例
- **版本控制**：支持API版本管理

#### 4. 后端开发原则
- **契约优先**：严格按照API契约实现
- **格式一致**：确保与Mock数据格式完全一致
- **性能达标**：满足前端性能要求
- **测试完整**：确保API功能正确性

### 🎯 成功标准

#### 开发效率指标
- 前端开发完全独立，不依赖后端
- 后端开发有明确规范，无歧义
- 前后端对接问题减少90%以上
- 整体开发周期缩短40%以上

#### 质量指标
- 前端功能完整性100%
- Mock/真实API一致性100%
- 用户体验满意度>95%
- 系统稳定性>99.9%

---

**📝 文档维护说明**：
- 本指南遵循前后端分离开发理念，应根据开发进展实时更新
- 每完成一个阶段后更新进度状态和经验总结
- 记录前后端分离开发过程中遇到的问题和解决方案
- 持续优化前后端分离协作流程
- 定期回顾和改进开发效率
- 确保所有代码示例符合最新的Vue3和Django最佳实践
- 重点关注前端用户体验和Mock/真实API的一致性
- 强调前端完整独立开发和无缝API切换的重要性