{"version": 3, "file": "build-list.js", "sources": ["../../../../../../../packages/components/virtual-list/src/builders/build-list.ts"], "sourcesContent": ["import {\n  Fragment,\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  h,\n  nextTick,\n  onActivated,\n  onMounted,\n  onUpdated,\n  ref,\n  resolveDynamicComponent,\n  unref,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { hasOwn, isClient, isNumber, isString } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useCache } from '../hooks/use-cache'\nimport useWheel from '../hooks/use-wheel'\nimport Scrollbar from '../components/scrollbar'\nimport { getRTLOffsetType, getScrollDir, isHorizontal } from '../utils'\nimport { virtualizedListProps } from '../props'\nimport {\n  AUTO_ALIGNMENT,\n  BACKWARD,\n  FORWARD,\n  HORIZONTAL,\n  ITEM_RENDER_EVT,\n  RTL,\n  RTL_OFFSET_NAG,\n  RTL_OFFSET_POS_ASC,\n  RTL_OFFSET_POS_DESC,\n  SCROLL_EVT,\n} from '../defaults'\n\nimport type { CSSProperties, Slot, VNode, VNodeChild } from 'vue'\nimport type { Alignment, ListConstructorProps } from '../types'\nimport type { VirtualizedListProps } from '../props'\n\nconst createList = ({\n  name,\n  getOffset,\n  getItemSize,\n  getItemOffset,\n  getEstimatedTotalSize,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initCache,\n  clearCache,\n  validateProps,\n}: ListConstructorProps<VirtualizedListProps>) => {\n  return defineComponent({\n    name: name ?? 'ElVirtualList',\n    props: virtualizedListProps,\n    emits: [ITEM_RENDER_EVT, SCROLL_EVT],\n    setup(props, { emit, expose }) {\n      validateProps(props)\n      const instance = getCurrentInstance()!\n\n      const ns = useNamespace('vl')\n\n      const dynamicSizeCache = ref(initCache(props, instance))\n\n      const getItemStyleCache = useCache<CSSProperties>()\n      // refs\n      // here windowRef and innerRef can be type of HTMLElement\n      // or user defined component type, depends on the type passed\n      // by user\n      const windowRef = ref<HTMLElement>()\n      const innerRef = ref<HTMLElement>()\n      const scrollbarRef = ref()\n      const states = ref({\n        isScrolling: false,\n        scrollDir: 'forward',\n        scrollOffset: isNumber(props.initScrollOffset)\n          ? props.initScrollOffset\n          : 0,\n        updateRequested: false,\n        isScrollbarDragging: false,\n        scrollbarAlwaysOn: props.scrollbarAlwaysOn,\n      })\n\n      // computed\n      const itemsToRender = computed(() => {\n        const { total, cache } = props\n        const { isScrolling, scrollDir, scrollOffset } = unref(states)\n\n        if (total === 0) {\n          return [0, 0, 0, 0]\n        }\n\n        const startIndex = getStartIndexForOffset(\n          props,\n          scrollOffset,\n          unref(dynamicSizeCache)\n        )\n        const stopIndex = getStopIndexForStartIndex(\n          props,\n          startIndex,\n          scrollOffset,\n          unref(dynamicSizeCache)\n        )\n\n        const cacheBackward =\n          !isScrolling || scrollDir === BACKWARD ? Math.max(1, cache) : 1\n        const cacheForward =\n          !isScrolling || scrollDir === FORWARD ? Math.max(1, cache) : 1\n\n        return [\n          Math.max(0, startIndex - cacheBackward),\n          Math.max(0, Math.min(total! - 1, stopIndex + cacheForward)),\n          startIndex,\n          stopIndex,\n        ]\n      })\n\n      const estimatedTotalSize = computed(() =>\n        getEstimatedTotalSize(props, unref(dynamicSizeCache))\n      )\n\n      const _isHorizontal = computed(() => isHorizontal(props.layout))\n\n      const windowStyle = computed(() => [\n        {\n          position: 'relative',\n          [`overflow-${_isHorizontal.value ? 'x' : 'y'}`]: 'scroll',\n          WebkitOverflowScrolling: 'touch',\n          willChange: 'transform',\n        },\n        {\n          direction: props.direction,\n          height: isNumber(props.height) ? `${props.height}px` : props.height,\n          width: isNumber(props.width) ? `${props.width}px` : props.width,\n        },\n        props.style,\n      ])\n\n      const innerStyle = computed(() => {\n        const size = unref(estimatedTotalSize)\n        const horizontal = unref(_isHorizontal)\n        return {\n          height: horizontal ? '100%' : `${size}px`,\n          pointerEvents: unref(states).isScrolling ? 'none' : undefined,\n          width: horizontal ? `${size}px` : '100%',\n        }\n      })\n\n      const clientSize = computed(() =>\n        _isHorizontal.value ? props.width : props.height\n      )\n\n      // methods\n      const { onWheel } = useWheel(\n        {\n          atStartEdge: computed(() => states.value.scrollOffset <= 0),\n          atEndEdge: computed(\n            () => states.value.scrollOffset >= estimatedTotalSize.value\n          ),\n          layout: computed(() => props.layout),\n        },\n        (offset) => {\n          ;(\n            scrollbarRef.value as {\n              onMouseUp: () => void\n            }\n          ).onMouseUp?.()\n          scrollTo(\n            Math.min(\n              states.value.scrollOffset + offset,\n              estimatedTotalSize.value - (clientSize.value as number)\n            )\n          )\n        }\n      )\n\n      useEventListener(windowRef, 'wheel', onWheel, {\n        passive: false,\n      })\n\n      const emitEvents = () => {\n        const { total } = props\n\n        if (total! > 0) {\n          const [cacheStart, cacheEnd, visibleStart, visibleEnd] =\n            unref(itemsToRender)\n          emit(ITEM_RENDER_EVT, cacheStart, cacheEnd, visibleStart, visibleEnd)\n        }\n\n        const { scrollDir, scrollOffset, updateRequested } = unref(states)\n        emit(SCROLL_EVT, scrollDir, scrollOffset, updateRequested)\n      }\n\n      const scrollVertically = (e: Event) => {\n        const { clientHeight, scrollHeight, scrollTop } =\n          e.currentTarget as HTMLElement\n        const _states = unref(states)\n        if (_states.scrollOffset === scrollTop) {\n          return\n        }\n\n        const scrollOffset = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        )\n\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollDir: getScrollDir(_states.scrollOffset, scrollOffset),\n          scrollOffset,\n          updateRequested: false,\n        }\n\n        nextTick(resetIsScrolling)\n      }\n\n      const scrollHorizontally = (e: Event) => {\n        const { clientWidth, scrollLeft, scrollWidth } =\n          e.currentTarget as HTMLElement\n        const _states = unref(states)\n\n        if (_states.scrollOffset === scrollLeft) {\n          return\n        }\n\n        const { direction } = props\n\n        let scrollOffset = scrollLeft\n\n        if (direction === RTL) {\n          // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n          // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n          // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n          // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n          switch (getRTLOffsetType()) {\n            case RTL_OFFSET_NAG: {\n              scrollOffset = -scrollLeft\n              break\n            }\n            case RTL_OFFSET_POS_DESC: {\n              scrollOffset = scrollWidth - clientWidth - scrollLeft\n              break\n            }\n          }\n        }\n\n        scrollOffset = Math.max(\n          0,\n          Math.min(scrollOffset, scrollWidth - clientWidth)\n        )\n\n        states.value = {\n          ..._states,\n          isScrolling: true,\n          scrollDir: getScrollDir(_states.scrollOffset, scrollOffset),\n          scrollOffset,\n          updateRequested: false,\n        }\n\n        nextTick(resetIsScrolling)\n      }\n\n      const onScroll = (e: Event) => {\n        unref(_isHorizontal) ? scrollHorizontally(e) : scrollVertically(e)\n        emitEvents()\n      }\n\n      const onScrollbarScroll = (distanceToGo: number, totalSteps: number) => {\n        const offset =\n          ((estimatedTotalSize.value - (clientSize.value as number)) /\n            totalSteps) *\n          distanceToGo\n        scrollTo(\n          Math.min(\n            estimatedTotalSize.value - (clientSize.value as number),\n            offset\n          )\n        )\n      }\n\n      const scrollTo = (offset: number) => {\n        offset = Math.max(offset, 0)\n\n        if (offset === unref(states).scrollOffset) {\n          return\n        }\n\n        states.value = {\n          ...unref(states),\n          scrollOffset: offset,\n          scrollDir: getScrollDir(unref(states).scrollOffset, offset),\n          updateRequested: true,\n        }\n\n        nextTick(resetIsScrolling)\n      }\n\n      const scrollToItem = (\n        idx: number,\n        alignment: Alignment = AUTO_ALIGNMENT\n      ) => {\n        const { scrollOffset } = unref(states)\n\n        idx = Math.max(0, Math.min(idx, props.total! - 1))\n        scrollTo(\n          getOffset(\n            props,\n            idx,\n            alignment,\n            scrollOffset,\n            unref(dynamicSizeCache)\n          )\n        )\n      }\n\n      const getItemStyle = (idx: number) => {\n        const { direction, itemSize, layout } = props\n\n        const itemStyleCache = getItemStyleCache.value(\n          clearCache && itemSize,\n          clearCache && layout,\n          clearCache && direction\n        )\n\n        let style: CSSProperties\n        if (hasOwn(itemStyleCache, String(idx))) {\n          style = itemStyleCache[idx]\n        } else {\n          const offset = getItemOffset(props, idx, unref(dynamicSizeCache))\n          const size = getItemSize(props, idx, unref(dynamicSizeCache))\n          const horizontal = unref(_isHorizontal)\n\n          const isRtl = direction === RTL\n          const offsetHorizontal = horizontal ? offset : 0\n          itemStyleCache[idx] = style = {\n            position: 'absolute',\n            left: isRtl ? undefined : `${offsetHorizontal}px`,\n            right: isRtl ? `${offsetHorizontal}px` : undefined,\n            top: !horizontal ? `${offset}px` : 0,\n            height: !horizontal ? `${size}px` : '100%',\n            width: horizontal ? `${size}px` : '100%',\n          }\n        }\n\n        return style\n      }\n\n      // TODO: perf optimization here, reset isScrolling with debounce.\n\n      const resetIsScrolling = () => {\n        states.value.isScrolling = false\n        nextTick(() => {\n          getItemStyleCache.value(-1, null, null)\n        })\n      }\n\n      const resetScrollTop = () => {\n        const window = windowRef.value\n        if (window) {\n          window.scrollTop = 0\n        }\n      }\n\n      // life cycles\n      onMounted(() => {\n        if (!isClient) return\n        const { initScrollOffset } = props\n        const windowElement = unref(windowRef)\n        if (isNumber(initScrollOffset) && windowElement) {\n          if (unref(_isHorizontal)) {\n            windowElement.scrollLeft = initScrollOffset\n          } else {\n            windowElement.scrollTop = initScrollOffset\n          }\n        }\n\n        emitEvents()\n      })\n\n      onUpdated(() => {\n        const { direction, layout } = props\n        const { scrollOffset, updateRequested } = unref(states)\n        const windowElement = unref(windowRef)\n\n        if (updateRequested && windowElement) {\n          if (layout === HORIZONTAL) {\n            if (direction === RTL) {\n              // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n              // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n              // So we need to determine which browser behavior we're dealing with, and mimic it.\n              switch (getRTLOffsetType()) {\n                case RTL_OFFSET_NAG: {\n                  windowElement.scrollLeft = -scrollOffset\n                  break\n                }\n                case RTL_OFFSET_POS_ASC: {\n                  windowElement.scrollLeft = scrollOffset\n                  break\n                }\n                default: {\n                  const { clientWidth, scrollWidth } = windowElement\n                  windowElement.scrollLeft =\n                    scrollWidth - clientWidth - scrollOffset\n                  break\n                }\n              }\n            } else {\n              windowElement.scrollLeft = scrollOffset\n            }\n          } else {\n            windowElement.scrollTop = scrollOffset\n          }\n        }\n      })\n\n      onActivated(() => {\n        unref(windowRef)!.scrollTop = unref(states).scrollOffset\n      })\n\n      const api = {\n        ns,\n        clientSize,\n        estimatedTotalSize,\n        windowStyle,\n        windowRef,\n        innerRef,\n        innerStyle,\n        itemsToRender,\n        scrollbarRef,\n        states,\n        getItemStyle,\n        onScroll,\n        onScrollbarScroll,\n        onWheel,\n        scrollTo,\n        scrollToItem,\n        resetScrollTop,\n      }\n\n      expose({\n        windowRef,\n        innerRef,\n        getItemStyleCache,\n        scrollTo,\n        scrollToItem,\n        resetScrollTop,\n        states,\n      })\n\n      return api\n    },\n\n    render(ctx: any) {\n      const {\n        $slots,\n        className,\n        clientSize,\n        containerElement,\n        data,\n        getItemStyle,\n        innerElement,\n        itemsToRender,\n        innerStyle,\n        layout,\n        total,\n        onScroll,\n        onScrollbarScroll,\n        states,\n        useIsScrolling,\n        windowStyle,\n        ns,\n      } = ctx\n\n      const [start, end] = itemsToRender\n\n      const Container = resolveDynamicComponent(containerElement)\n      const Inner = resolveDynamicComponent(innerElement)\n\n      const children = [] as VNodeChild[]\n\n      if (total > 0) {\n        for (let i = start; i <= end; i++) {\n          children.push(\n            h(\n              Fragment,\n              { key: i },\n              ($slots.default as Slot)?.({\n                data,\n                index: i,\n                isScrolling: useIsScrolling ? states.isScrolling : undefined,\n                style: getItemStyle(i),\n              })\n            )\n          )\n        }\n      }\n\n      const InnerNode = [\n        h(\n          Inner as VNode,\n          {\n            style: innerStyle,\n            ref: 'innerRef',\n          },\n          !isString(Inner)\n            ? {\n                default: () => children,\n              }\n            : children\n        ),\n      ]\n\n      const scrollbar = h(Scrollbar, {\n        ref: 'scrollbarRef',\n        clientSize,\n        layout,\n        onScroll: onScrollbarScroll,\n        ratio: (clientSize * 100) / this.estimatedTotalSize,\n        scrollFrom:\n          states.scrollOffset / (this.estimatedTotalSize - clientSize),\n        total,\n      })\n\n      const listContainer = h(\n        Container as VNode,\n        {\n          class: [ns.e('window'), className],\n          style: windowStyle,\n          onScroll,\n          ref: 'windowRef',\n          key: 0,\n        },\n        !isString(Container) ? { default: () => [InnerNode] } : [InnerNode]\n      )\n\n      return h(\n        'div',\n        {\n          key: 0,\n          class: [ns.e('wrapper'), states.scrollbarAlwaysOn ? 'always-on' : ''],\n        },\n        [listContainer, scrollbar]\n      )\n    },\n  })\n}\n\nexport default createList\n"], "names": ["defineComponent", "virtualizedListProps", "ITEM_RENDER_EVT", "SCROLL_EVT", "getCurrentInstance", "useNamespace", "ref", "useCache", "isNumber", "computed", "unref", "BACKWARD", "FORWARD", "isHorizontal", "useWheel", "useEventListener", "getScrollDir", "nextTick", "RTL", "getRTLOffsetType", "RTL_OFFSET_NAG", "RTL_OFFSET_POS_DESC", "AUTO_ALIGNMENT", "hasOwn", "onMounted", "isClient", "onUpdated", "HORIZONTAL", "RTL_OFFSET_POS_ASC", "onActivated", "resolveDynamicComponent", "h", "Fragment", "isString", "scrollbar", "Sc<PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;AAkCK,MAAC,UAAU,GAAG,CAAC;AACpB,EAAE,IAAI;AACN,EAAE,SAAS;AACX,EAAE,WAAW;AACb,EAAE,aAAa;AACf,EAAE,qBAAqB;AACvB,EAAE,sBAAsB;AACxB,EAAE,yBAAyB;AAC3B,EAAE,SAAS;AACX,EAAE,UAAU;AACZ,EAAE,aAAa;AACf,CAAC,KAAK;AACN,EAAE,OAAOA,mBAAe,CAAC;AACzB,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,eAAe;AAC/C,IAAI,KAAK,EAAEC,0BAAoB;AAC/B,IAAI,KAAK,EAAE,CAACC,wBAAe,EAAEC,mBAAU,CAAC;AACxC,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE;AACnC,MAAM,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3B,MAAM,MAAM,QAAQ,GAAGC,sBAAkB,EAAE,CAAC;AAC5C,MAAM,MAAM,EAAE,GAAGC,kBAAY,CAAC,IAAI,CAAC,CAAC;AACpC,MAAM,MAAM,gBAAgB,GAAGC,OAAG,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC/D,MAAM,MAAM,iBAAiB,GAAGC,iBAAQ,EAAE,CAAC;AAC3C,MAAM,MAAM,SAAS,GAAGD,OAAG,EAAE,CAAC;AAC9B,MAAM,MAAM,QAAQ,GAAGA,OAAG,EAAE,CAAC;AAC7B,MAAM,MAAM,YAAY,GAAGA,OAAG,EAAE,CAAC;AACjC,MAAM,MAAM,MAAM,GAAGA,OAAG,CAAC;AACzB,QAAQ,WAAW,EAAE,KAAK;AAC1B,QAAQ,SAAS,EAAE,SAAS;AAC5B,QAAQ,YAAY,EAAEE,cAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC,gBAAgB,GAAG,CAAC;AACnF,QAAQ,eAAe,EAAE,KAAK;AAC9B,QAAQ,mBAAmB,EAAE,KAAK;AAClC,QAAQ,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;AAClD,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,aAAa,GAAGC,YAAQ,CAAC,MAAM;AAC3C,QAAQ,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;AACvC,QAAQ,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,GAAGC,SAAK,CAAC,MAAM,CAAC,CAAC;AACvE,QAAQ,IAAI,KAAK,KAAK,CAAC,EAAE;AACzB,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B,SAAS;AACT,QAAQ,MAAM,UAAU,GAAG,sBAAsB,CAAC,KAAK,EAAE,YAAY,EAAEA,SAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAChG,QAAQ,MAAM,SAAS,GAAG,yBAAyB,CAAC,KAAK,EAAE,UAAU,EAAE,YAAY,EAAEA,SAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC9G,QAAQ,MAAM,aAAa,GAAG,CAAC,WAAW,IAAI,SAAS,KAAKC,iBAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9F,QAAQ,MAAM,YAAY,GAAG,CAAC,WAAW,IAAI,SAAS,KAAKC,gBAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5F,QAAQ,OAAO;AACf,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,aAAa,CAAC;AACjD,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,CAAC;AACpE,UAAU,UAAU;AACpB,UAAU,SAAS;AACnB,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,kBAAkB,GAAGH,YAAQ,CAAC,MAAM,qBAAqB,CAAC,KAAK,EAAEC,SAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AACvG,MAAM,MAAM,aAAa,GAAGD,YAAQ,CAAC,MAAMI,kBAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AACvE,MAAM,MAAM,WAAW,GAAGJ,YAAQ,CAAC,MAAM;AACzC,QAAQ;AACR,UAAU,QAAQ,EAAE,UAAU;AAC9B,UAAU,CAAC,CAAC,SAAS,EAAE,aAAa,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,QAAQ;AACnE,UAAU,uBAAuB,EAAE,OAAO;AAC1C,UAAU,UAAU,EAAE,WAAW;AACjC,SAAS;AACT,QAAQ;AACR,UAAU,SAAS,EAAE,KAAK,CAAC,SAAS;AACpC,UAAU,MAAM,EAAED,cAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM;AAC7E,UAAU,KAAK,EAAEA,cAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK;AACzE,SAAS;AACT,QAAQ,KAAK,CAAC,KAAK;AACnB,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,UAAU,GAAGC,YAAQ,CAAC,MAAM;AACxC,QAAQ,MAAM,IAAI,GAAGC,SAAK,CAAC,kBAAkB,CAAC,CAAC;AAC/C,QAAQ,MAAM,UAAU,GAAGA,SAAK,CAAC,aAAa,CAAC,CAAC;AAChD,QAAQ,OAAO;AACf,UAAU,MAAM,EAAE,UAAU,GAAG,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;AACnD,UAAU,aAAa,EAAEA,SAAK,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC;AACpE,UAAU,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM;AAClD,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,UAAU,GAAGD,YAAQ,CAAC,MAAM,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAC1F,MAAM,MAAM,EAAE,OAAO,EAAE,GAAGK,mBAAQ,CAAC;AACnC,QAAQ,WAAW,EAAEL,YAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,CAAC;AACnE,QAAQ,SAAS,EAAEA,YAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,YAAY,IAAI,kBAAkB,CAAC,KAAK,CAAC;AACxF,QAAQ,MAAM,EAAEA,YAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC;AAC5C,OAAO,EAAE,CAAC,MAAM,KAAK;AACrB,QAAQ,IAAI,EAAE,EAAE,EAAE,CAAC;AAEnB,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClF,QAAQ,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,EAAE,kBAAkB,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC5G,OAAO,CAAC,CAAC;AACT,MAAMM,qBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE;AACpD,QAAQ,OAAO,EAAE,KAAK;AACtB,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,UAAU,GAAG,MAAM;AAC/B,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;AAChC,QAAQ,IAAI,KAAK,GAAG,CAAC,EAAE;AACvB,UAAU,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,GAAGL,SAAK,CAAC,aAAa,CAAC,CAAC;AACxF,UAAU,IAAI,CAACR,wBAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AAChF,SAAS;AACT,QAAQ,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,EAAE,GAAGQ,SAAK,CAAC,MAAM,CAAC,CAAC;AAC3E,QAAQ,IAAI,CAACP,mBAAU,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;AACnE,OAAO,CAAC;AACR,MAAM,MAAM,gBAAgB,GAAG,CAAC,CAAC,KAAK;AACtC,QAAQ,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,aAAa,CAAC;AAC1E,QAAQ,MAAM,OAAO,GAAGO,SAAK,CAAC,MAAM,CAAC,CAAC;AACtC,QAAQ,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;AAChD,UAAU,OAAO;AACjB,SAAS;AACT,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC;AAC3F,QAAQ,MAAM,CAAC,KAAK,GAAG;AACvB,UAAU,GAAG,OAAO;AACpB,UAAU,WAAW,EAAE,IAAI;AAC3B,UAAU,SAAS,EAAEM,kBAAY,CAAC,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC;AACrE,UAAU,YAAY;AACtB,UAAU,eAAe,EAAE,KAAK;AAChC,SAAS,CAAC;AACV,QAAQC,YAAQ,CAAC,gBAAgB,CAAC,CAAC;AACnC,OAAO,CAAC;AACR,MAAM,MAAM,kBAAkB,GAAG,CAAC,CAAC,KAAK;AACxC,QAAQ,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC,aAAa,CAAC;AACzE,QAAQ,MAAM,OAAO,GAAGP,SAAK,CAAC,MAAM,CAAC,CAAC;AACtC,QAAQ,IAAI,OAAO,CAAC,YAAY,KAAK,UAAU,EAAE;AACjD,UAAU,OAAO;AACjB,SAAS;AACT,QAAQ,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;AACpC,QAAQ,IAAI,YAAY,GAAG,UAAU,CAAC;AACtC,QAAQ,IAAI,SAAS,KAAKQ,YAAG,EAAE;AAC/B,UAAU,QAAQC,sBAAgB,EAAE;AACpC,YAAY,KAAKC,uBAAc,EAAE;AACjC,cAAc,YAAY,GAAG,CAAC,UAAU,CAAC;AACzC,cAAc,MAAM;AACpB,aAAa;AACb,YAAY,KAAKC,4BAAmB,EAAE;AACtC,cAAc,YAAY,GAAG,WAAW,GAAG,WAAW,GAAG,UAAU,CAAC;AACpE,cAAc,MAAM;AACpB,aAAa;AACb,WAAW;AACX,SAAS;AACT,QAAQ,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC;AACtF,QAAQ,MAAM,CAAC,KAAK,GAAG;AACvB,UAAU,GAAG,OAAO;AACpB,UAAU,WAAW,EAAE,IAAI;AAC3B,UAAU,SAAS,EAAEL,kBAAY,CAAC,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC;AACrE,UAAU,YAAY;AACtB,UAAU,eAAe,EAAE,KAAK;AAChC,SAAS,CAAC;AACV,QAAQC,YAAQ,CAAC,gBAAgB,CAAC,CAAC;AACnC,OAAO,CAAC;AACR,MAAM,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK;AAC9B,QAAQP,SAAK,CAAC,aAAa,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC3E,QAAQ,UAAU,EAAE,CAAC;AACrB,OAAO,CAAC;AACR,MAAM,MAAM,iBAAiB,GAAG,CAAC,YAAY,EAAE,UAAU,KAAK;AAC9D,QAAQ,MAAM,MAAM,GAAG,CAAC,kBAAkB,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,GAAG,YAAY,CAAC;AACjG,QAAQ,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAChF,OAAO,CAAC;AACR,MAAM,MAAM,QAAQ,GAAG,CAAC,MAAM,KAAK;AACnC,QAAQ,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACrC,QAAQ,IAAI,MAAM,KAAKA,SAAK,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE;AACnD,UAAU,OAAO;AACjB,SAAS;AACT,QAAQ,MAAM,CAAC,KAAK,GAAG;AACvB,UAAU,GAAGA,SAAK,CAAC,MAAM,CAAC;AAC1B,UAAU,YAAY,EAAE,MAAM;AAC9B,UAAU,SAAS,EAAEM,kBAAY,CAACN,SAAK,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC;AACrE,UAAU,eAAe,EAAE,IAAI;AAC/B,SAAS,CAAC;AACV,QAAQO,YAAQ,CAAC,gBAAgB,CAAC,CAAC;AACnC,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE,SAAS,GAAGK,uBAAc,KAAK;AAChE,QAAQ,MAAM,EAAE,YAAY,EAAE,GAAGZ,SAAK,CAAC,MAAM,CAAC,CAAC;AAC/C,QAAQ,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1D,QAAQ,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,YAAY,EAAEA,SAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC1F,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,CAAC,GAAG,KAAK;AACpC,QAAQ,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AACtD,QAAQ,MAAM,cAAc,GAAG,iBAAiB,CAAC,KAAK,CAAC,UAAU,IAAI,QAAQ,EAAE,UAAU,IAAI,MAAM,EAAE,UAAU,IAAI,SAAS,CAAC,CAAC;AAC9H,QAAQ,IAAI,KAAK,CAAC;AAClB,QAAQ,IAAIa,aAAM,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AACjD,UAAU,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;AACtC,SAAS,MAAM;AACf,UAAU,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,EAAEb,SAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC5E,UAAU,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,EAAE,GAAG,EAAEA,SAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACxE,UAAU,MAAM,UAAU,GAAGA,SAAK,CAAC,aAAa,CAAC,CAAC;AAClD,UAAU,MAAM,KAAK,GAAG,SAAS,KAAKQ,YAAG,CAAC;AAC1C,UAAU,MAAM,gBAAgB,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC;AAC3D,UAAU,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG;AACxC,YAAY,QAAQ,EAAE,UAAU;AAChC,YAAY,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,gBAAgB,CAAC,EAAE,CAAC;AAC1D,YAAY,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,gBAAgB,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;AAC3D,YAAY,GAAG,EAAE,CAAC,UAAU,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC;AAChD,YAAY,MAAM,EAAE,CAAC,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM;AACtD,YAAY,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM;AACpD,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO,CAAC;AACR,MAAM,MAAM,gBAAgB,GAAG,MAAM;AACrC,QAAQ,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;AACzC,QAAQD,YAAQ,CAAC,MAAM;AACvB,UAAU,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAClD,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,MAAM,MAAM,cAAc,GAAG,MAAM;AACnC,QAAQ,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC;AACvC,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;AAC/B,SAAS;AACT,OAAO,CAAC;AACR,MAAMO,aAAS,CAAC,MAAM;AACtB,QAAQ,IAAI,CAACC,aAAQ;AACrB,UAAU,OAAO;AACjB,QAAQ,MAAM,EAAE,gBAAgB,EAAE,GAAG,KAAK,CAAC;AAC3C,QAAQ,MAAM,aAAa,GAAGf,SAAK,CAAC,SAAS,CAAC,CAAC;AAC/C,QAAQ,IAAIF,cAAQ,CAAC,gBAAgB,CAAC,IAAI,aAAa,EAAE;AACzD,UAAU,IAAIE,SAAK,CAAC,aAAa,CAAC,EAAE;AACpC,YAAY,aAAa,CAAC,UAAU,GAAG,gBAAgB,CAAC;AACxD,WAAW,MAAM;AACjB,YAAY,aAAa,CAAC,SAAS,GAAG,gBAAgB,CAAC;AACvD,WAAW;AACX,SAAS;AACT,QAAQ,UAAU,EAAE,CAAC;AACrB,OAAO,CAAC,CAAC;AACT,MAAMgB,aAAS,CAAC,MAAM;AACtB,QAAQ,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AAC5C,QAAQ,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAGhB,SAAK,CAAC,MAAM,CAAC,CAAC;AAChE,QAAQ,MAAM,aAAa,GAAGA,SAAK,CAAC,SAAS,CAAC,CAAC;AAC/C,QAAQ,IAAI,eAAe,IAAI,aAAa,EAAE;AAC9C,UAAU,IAAI,MAAM,KAAKiB,mBAAU,EAAE;AACrC,YAAY,IAAI,SAAS,KAAKT,YAAG,EAAE;AACnC,cAAc,QAAQC,sBAAgB,EAAE;AACxC,gBAAgB,KAAKC,uBAAc,EAAE;AACrC,kBAAkB,aAAa,CAAC,UAAU,GAAG,CAAC,YAAY,CAAC;AAC3D,kBAAkB,MAAM;AACxB,iBAAiB;AACjB,gBAAgB,KAAKQ,2BAAkB,EAAE;AACzC,kBAAkB,aAAa,CAAC,UAAU,GAAG,YAAY,CAAC;AAC1D,kBAAkB,MAAM;AACxB,iBAAiB;AACjB,gBAAgB,SAAS;AACzB,kBAAkB,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,aAAa,CAAC;AACrE,kBAAkB,aAAa,CAAC,UAAU,GAAG,WAAW,GAAG,WAAW,GAAG,YAAY,CAAC;AACtF,kBAAkB,MAAM;AACxB,iBAAiB;AACjB,eAAe;AACf,aAAa,MAAM;AACnB,cAAc,aAAa,CAAC,UAAU,GAAG,YAAY,CAAC;AACtD,aAAa;AACb,WAAW,MAAM;AACjB,YAAY,aAAa,CAAC,SAAS,GAAG,YAAY,CAAC;AACnD,WAAW;AACX,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAMC,eAAW,CAAC,MAAM;AACxB,QAAQnB,SAAK,CAAC,SAAS,CAAC,CAAC,SAAS,GAAGA,SAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC;AAChE,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,GAAG,GAAG;AAClB,QAAQ,EAAE;AACV,QAAQ,UAAU;AAClB,QAAQ,kBAAkB;AAC1B,QAAQ,WAAW;AACnB,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,QAAQ,UAAU;AAClB,QAAQ,aAAa;AACrB,QAAQ,YAAY;AACpB,QAAQ,MAAM;AACd,QAAQ,YAAY;AACpB,QAAQ,QAAQ;AAChB,QAAQ,iBAAiB;AACzB,QAAQ,OAAO;AACf,QAAQ,QAAQ;AAChB,QAAQ,YAAY;AACpB,QAAQ,cAAc;AACtB,OAAO,CAAC;AACR,MAAM,MAAM,CAAC;AACb,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,QAAQ,iBAAiB;AACzB,QAAQ,QAAQ;AAChB,QAAQ,YAAY;AACpB,QAAQ,cAAc;AACtB,QAAQ,MAAM;AACd,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,CAAC,GAAG,EAAE;AAChB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,MAAM;AACZ,QAAQ,MAAM;AACd,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,QAAQ,gBAAgB;AACxB,QAAQ,IAAI;AACZ,QAAQ,YAAY;AACpB,QAAQ,YAAY;AACpB,QAAQ,aAAa;AACrB,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ,iBAAiB;AACzB,QAAQ,MAAM;AACd,QAAQ,cAAc;AACtB,QAAQ,WAAW;AACnB,QAAQ,EAAE;AACV,OAAO,GAAG,GAAG,CAAC;AACd,MAAM,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,aAAa,CAAC;AACzC,MAAM,MAAM,SAAS,GAAGoB,2BAAuB,CAAC,gBAAgB,CAAC,CAAC;AAClE,MAAM,MAAM,KAAK,GAAGA,2BAAuB,CAAC,YAAY,CAAC,CAAC;AAC1D,MAAM,MAAM,QAAQ,GAAG,EAAE,CAAC;AAC1B,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE;AACrB,QAAQ,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE;AAC3C,UAAU,QAAQ,CAAC,IAAI,CAACC,KAAC,CAACC,YAAQ,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE;AACzG,YAAY,IAAI;AAChB,YAAY,KAAK,EAAE,CAAC;AACpB,YAAY,WAAW,EAAE,cAAc,GAAG,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;AACrE,YAAY,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;AAClC,WAAW,CAAC,CAAC,CAAC,CAAC;AACf,SAAS;AACT,OAAO;AACP,MAAM,MAAM,SAAS,GAAG;AACxB,QAAQD,KAAC,CAAC,KAAK,EAAE;AACjB,UAAU,KAAK,EAAE,UAAU;AAC3B,UAAU,GAAG,EAAE,UAAU;AACzB,SAAS,EAAE,CAACE,eAAQ,CAAC,KAAK,CAAC,GAAG;AAC9B,UAAU,OAAO,EAAE,MAAM,QAAQ;AACjC,SAAS,GAAG,QAAQ,CAAC;AACrB,OAAO,CAAC;AACR,MAAM,MAAMC,WAAS,GAAGH,KAAC,CAACI,oBAAS,EAAE;AACrC,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,QAAQ,EAAE,iBAAiB;AACnC,QAAQ,KAAK,EAAE,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,kBAAkB;AACzD,QAAQ,UAAU,EAAE,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;AAChF,QAAQ,KAAK;AACb,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,aAAa,GAAGJ,KAAC,CAAC,SAAS,EAAE;AACzC,QAAQ,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC;AAC1C,QAAQ,KAAK,EAAE,WAAW;AAC1B,QAAQ,QAAQ;AAChB,QAAQ,GAAG,EAAE,WAAW;AACxB,QAAQ,GAAG,EAAE,CAAC;AACd,OAAO,EAAE,CAACE,eAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;AAC9E,MAAM,OAAOF,KAAC,CAAC,KAAK,EAAE;AACtB,QAAQ,GAAG,EAAE,CAAC;AACd,QAAQ,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,iBAAiB,GAAG,WAAW,GAAG,EAAE,CAAC;AAC7E,OAAO,EAAE,CAAC,aAAa,EAAEG,WAAS,CAAC,CAAC,CAAC;AACrC,KAAK;AACL,GAAG,CAAC,CAAC;AACL;;;;"}