import{d as s,c as a,a as e,Q as r,I as l,ag as o,o as t}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as c}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const p={class:"official-reports-query"},n={class:"content"},u=c(s({__name:"OfficialReportsQuery",setup:s=>(s,c)=>{const u=o("el-card");return t(),a("div",p,[c[1]||(c[1]=e("div",{class:"page-header"},[e("h1",null,"官方报道查询"),e("p",{class:"page-description"},"查询官方新闻和报道内容")],-1)),e("div",n,[r(u,null,{default:l(()=>c[0]||(c[0]=[e("p",null,"官方报道查询功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-d7859447"]]);export{u as default};
