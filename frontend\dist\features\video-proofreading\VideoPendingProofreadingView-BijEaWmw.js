import{M as e,x as a,a0 as l,T as t,R as u}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as n,r,b as i,m as o,c as s,a as d,Q as p,I as c,ag as v,o as m,u as g,M as f,O as _,J as h,aq as w,H as b,K as y}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as C}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const z={class:"video-pending-proofreading"},V={class:"content"},k={class:"file-name"},T={class:"pagination-container"},x={key:0,class:"preview-content"},B=["src"],Y={class:"preview-info"},I=C(n({__name:"VideoPendingProofreadingView",setup(n){const C=r(!1),I=r([]),M=r([]),U=r(""),j=r(""),D=r(""),S=r([]),P=r(1),$=r(20),F=r(0),K=r(!1),L=r(null),O=i(()=>{let e=I.value;return U.value&&(e=e.filter(e=>e.name.toLowerCase().includes(U.value.toLowerCase()))),j.value&&(e=e.filter(e=>e.status===j.value)),D.value&&(e=e.filter(e=>{const a=E(e.duration);switch(D.value){case"short":return a<=300;case"medium":return a>300&&a<=1800;case"long":return a>1800;default:return!0}})),S.value&&2===S.value.length&&(e=e.filter(e=>{const a=new Date(e.uploadTime).toISOString().split("T")[0];return a>=S.value[0]&&a<=S.value[1]})),e});o(()=>{q()});const q=async()=>{C.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),I.value=[{id:1,name:"presentation_video_001.mp4",duration:"15:32",size:125829120,uploadTime:"2024-01-15 10:30:00",status:"pending",uploader:"张三",url:"/api/files/preview/video/1"},{id:2,name:"meeting_recording_20240115.avi",duration:"45:18",size:387420160,uploadTime:"2024-01-15 09:15:00",status:"processing",uploader:"李四",url:"/api/files/preview/video/2"},{id:3,name:"training_video_part1.mov",duration:"28:45",size:256901120,uploadTime:"2024-01-14 16:45:00",status:"uploaded",uploader:"王五",url:"/api/files/preview/video/3"},{id:4,name:"interview_session_001.mp4",duration:"12:20",size:98304e3,uploadTime:"2024-01-14 14:20:00",status:"pending",uploader:"赵六",url:"/api/files/preview/video/4"},{id:5,name:"product_demo_v2.wmv",duration:"08:15",size:67108864,uploadTime:"2024-01-13 11:30:00",status:"pending",uploader:"钱七",url:"/api/files/preview/video/5"}],F.value=I.value.length}catch{e.error("加载文件列表失败")}finally{C.value=!1}},E=e=>{const a=e.split(":");return 60*parseInt(a[0])+parseInt(a[1])},G=()=>{P.value=1},H=()=>{P.value=1},J=()=>{q()},Q=e=>{M.value=e},R=async()=>{if(0!==M.value.length)try{await u.confirm(`确定要批量处理选中的 ${M.value.length} 个文件吗？`,"批量处理确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.success("批量处理已开始，请稍后查看处理结果"),M.value.forEach(e=>{e.status="processing"}),M.value=[]}catch{}else e.warning("请选择要处理的文件")},A=e=>{$.value=e,P.value=1},N=e=>{P.value=e},W=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":e<1073741824?(e/1048576).toFixed(1)+" MB":(e/1073741824).toFixed(1)+" GB",X=e=>{switch(e){case"pending":return"warning";case"processing":return"primary";case"uploaded":return"success";default:return"info"}},Z=e=>{switch(e){case"pending":return"待处理";case"processing":return"处理中";case"uploaded":return"已上传";default:return"未知"}};return(n,r)=>{const i=v("el-icon"),o=v("el-input"),q=v("el-col"),E=v("el-option"),ee=v("el-select"),ae=v("el-date-picker"),le=v("el-button"),te=v("el-row"),ue=v("el-card"),ne=v("el-table-column"),re=v("el-tag"),ie=v("el-table"),oe=v("el-pagination"),se=v("el-dialog"),de=w("loading");return m(),s("div",z,[r[16]||(r[16]=d("div",{class:"page-header"},[d("h1",null,"待审查视频"),d("p",{class:"page-description"},"显示等待审校的视频文件列表")],-1)),d("div",V,[p(ue,{class:"filter-card"},{default:c(()=>[p(te,{gutter:20},{default:c(()=>[p(q,{span:6},{default:c(()=>[p(o,{modelValue:U.value,"onUpdate:modelValue":r[0]||(r[0]=e=>U.value=e),placeholder:"搜索文件名...",clearable:"",onInput:G},{prefix:c(()=>[p(i,null,{default:c(()=>[p(g(a))]),_:1})]),_:1},8,["modelValue"])]),_:1}),p(q,{span:4},{default:c(()=>[p(ee,{modelValue:j.value,"onUpdate:modelValue":r[1]||(r[1]=e=>j.value=e),placeholder:"状态筛选",clearable:"",onChange:H},{default:c(()=>[p(E,{label:"全部",value:""}),p(E,{label:"待处理",value:"pending"}),p(E,{label:"处理中",value:"processing"}),p(E,{label:"已上传",value:"uploaded"})]),_:1},8,["modelValue"])]),_:1}),p(q,{span:4},{default:c(()=>[p(ee,{modelValue:D.value,"onUpdate:modelValue":r[2]||(r[2]=e=>D.value=e),placeholder:"时长筛选",clearable:"",onChange:H},{default:c(()=>[p(E,{label:"全部",value:""}),p(E,{label:"5分钟以内",value:"short"}),p(E,{label:"5-30分钟",value:"medium"}),p(E,{label:"30分钟以上",value:"long"})]),_:1},8,["modelValue"])]),_:1}),p(q,{span:4},{default:c(()=>[p(ae,{modelValue:S.value,"onUpdate:modelValue":r[3]||(r[3]=e=>S.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:H},null,8,["modelValue"])]),_:1}),p(q,{span:3},{default:c(()=>[p(le,{type:"primary",onClick:J},{default:c(()=>r[7]||(r[7]=[f("刷新列表")])),_:1,__:[7]})]),_:1}),p(q,{span:3},{default:c(()=>[p(le,{onClick:R,disabled:0===M.value.length},{default:c(()=>[f(" 批量处理 ("+_(M.value.length)+") ",1)]),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1}),p(ue,{title:"待审查视频列表",class:"list-card"},{default:c(()=>[h((m(),b(ie,{data:O.value,style:{width:"100%"},onSelectionChange:Q},{default:c(()=>[p(ne,{type:"selection",width:"55"}),p(ne,{prop:"name",label:"文件名",width:"200"},{default:c(e=>[d("div",k,[p(i,null,{default:c(()=>[p(g(l))]),_:1}),d("span",null,_(e.row.name),1)])]),_:1}),p(ne,{prop:"duration",label:"时长",width:"100"}),p(ne,{prop:"size",label:"文件大小",width:"100"},{default:c(e=>[f(_(W(e.row.size)),1)]),_:1}),p(ne,{prop:"uploadTime",label:"上传时间",width:"160"}),p(ne,{prop:"status",label:"状态",width:"100"},{default:c(e=>[p(re,{type:X(e.row.status)},{default:c(()=>[f(_(Z(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),p(ne,{prop:"uploader",label:"上传者",width:"120"}),p(ne,{label:"预览",width:"80"},{default:c(e=>[p(le,{size:"small",onClick:a=>{return l=e.row,L.value=l,void(K.value=!0);var l}},{default:c(()=>[p(i,null,{default:c(()=>[p(g(t))]),_:1})]),_:2},1032,["onClick"])]),_:1}),p(ne,{label:"操作",width:"200"},{default:c(a=>[p(le,{size:"small",type:"primary",onClick:l=>{return t=a.row,void e.success(`开始审校文件：${t.name}`);var t}},{default:c(()=>r[8]||(r[8]=[f(" 开始审校 ")])),_:2,__:[8]},1032,["onClick"]),p(le,{size:"small",onClick:l=>{return t=a.row,void e.success(`开始下载文件：${t.name}`);var t}},{default:c(()=>r[9]||(r[9]=[f("下载")])),_:2,__:[9]},1032,["onClick"]),p(le,{size:"small",type:"danger",onClick:l=>(async a=>{try{await u.confirm(`确定要删除文件 "${a.name}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=I.value.findIndex(e=>e.id===a.id);l>-1&&(I.value.splice(l,1),F.value=I.value.length,e.success("删除成功"))}catch{}})(a.row)},{default:c(()=>r[10]||(r[10]=[f("删除")])),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[de,C.value]]),d("div",T,[p(oe,{"current-page":P.value,"onUpdate:currentPage":r[4]||(r[4]=e=>P.value=e),"page-size":$.value,"onUpdate:pageSize":r[5]||(r[5]=e=>$.value=e),"page-sizes":[10,20,50,100],total:F.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:A,onCurrentChange:N},null,8,["current-page","page-size","total"])])]),_:1})]),p(se,{modelValue:K.value,"onUpdate:modelValue":r[6]||(r[6]=e=>K.value=e),title:"视频预览",width:"70%"},{default:c(()=>[L.value?(m(),s("div",x,[d("video",{src:L.value.url,controls:"",class:"preview-video"},null,8,B),d("div",Y,[d("p",null,[r[11]||(r[11]=d("strong",null,"文件名：",-1)),f(_(L.value.name),1)]),d("p",null,[r[12]||(r[12]=d("strong",null,"时长：",-1)),f(_(L.value.duration),1)]),d("p",null,[r[13]||(r[13]=d("strong",null,"文件大小：",-1)),f(_(W(L.value.size)),1)]),d("p",null,[r[14]||(r[14]=d("strong",null,"上传时间：",-1)),f(_(L.value.uploadTime),1)]),d("p",null,[r[15]||(r[15]=d("strong",null,"上传者：",-1)),f(_(L.value.uploader),1)])])])):y("",!0)]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-55cd7db5"]]);export{I as default};
