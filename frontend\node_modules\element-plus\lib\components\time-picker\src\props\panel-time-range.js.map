{"version": 3, "file": "panel-time-range.js", "sources": ["../../../../../../../packages/components/time-picker/src/props/panel-time-range.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { timePanelSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelTimeRangeProps = buildProps({\n  ...timePanelSharedProps,\n  parsedValue: {\n    type: definePropType<[Dayjs, Dayjs]>(Array),\n  },\n} as const)\n\nexport type PanelTimeRangeProps = ExtractPropTypes<typeof panelTimeRangeProps>\n"], "names": ["buildProps", "timePanelSharedProps", "definePropType"], "mappings": ";;;;;;;AAEY,MAAC,mBAAmB,GAAGA,kBAAU,CAAC;AAC9C,EAAE,GAAGC,2BAAoB;AACzB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,CAAC;;;;"}