type Format = (string: string) => string;

declare const reset: Format;
declare const bold: Format;
declare const dim: Format;
declare const italic: Format;
declare const underline: Format;
declare const overline: Format;
declare const inverse: Format;
declare const hidden: Format;
declare const strikethrough: Format;

declare const black: Format;
declare const red: Format;
declare const green: Format;
declare const yellow: Format;
declare const blue: Format;
declare const magenta: Format;
declare const cyan: Format;
declare const white: Format;
declare const gray: Format;

declare const bgBlack: Format;
declare const bgRed: Format;
declare const bgGreen: Format;
declare const bgYellow: Format;
declare const bgBlue: Format;
declare const bgMagenta: Format;
declare const bgCyan: Format;
declare const bgWhite: Format;
declare const bgGray: Format;

declare const redBright: Format;
declare const greenBright: Format;
declare const yellowBright: Format;
declare const blueBright: Format;
declare const magentaBright: Format;
declare const cyanBright: Format;
declare const whiteBright: Format;

declare const bgRedBright: Format;
declare const bgGreenBright: Format;
declare const bgYellowBright: Format;
declare const bgBlueBright: Format;
declare const bgMagentaBright: Format;
declare const bgCyanBright: Format;
declare const bgWhiteBright: Format;

declare const formats: {
	reset: Format;
	bold: Format;
	dim: Format;
	italic: Format;
	underline: Format;
	overline: Format;
	inverse: Format;
	hidden: Format;
	strikethrough: Format;
	black: Format;
	red: Format;
	green: Format;
	yellow: Format;
	blue: Format;
	magenta: Format;
	cyan: Format;
	white: Format;
	gray: Format;
	bgBlack: Format;
	bgRed: Format;
	bgGreen: Format;
	bgYellow: Format;
	bgBlue: Format;
	bgMagenta: Format;
	bgCyan: Format;
	bgWhite: Format;
	bgGray: Format;
	redBright: Format;
	greenBright: Format;
	yellowBright: Format;
	blueBright: Format;
	magentaBright: Format;
	cyanBright: Format;
	whiteBright: Format;
	bgRedBright: Format;
	bgGreenBright: Format;
	bgYellowBright: Format;
	bgBlueBright: Format;
	bgMagentaBright: Format;
	bgCyanBright: Format;
	bgWhiteBright: Format;
};

export = formats;

