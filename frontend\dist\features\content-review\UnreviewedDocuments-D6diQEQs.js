import{aC as e,r as a,b as l,d as t,X as o,H as u,I as r,ag as s,o as n,Q as d,a as i,c,a6 as p,u as m,P as g,M as v,aD as h,m as f,Y as y,p as b,a4 as w,J as _,aq as V,O as x,L as k,aB as z,aA as D}from"../../chunks/vue-vendor-BCsylZgc.js";import{M as S,Q as P,x as C,C as U,B as A,R as N}from"../../chunks/ui-vendor-DZ6owSRu.js";import{P as Y}from"../../chunks/preReview-DUK05fM-.js";import{u as I}from"../../chunks/useDocumentTable-DFS8VTHq.js";import{_ as T}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";import"../../chunks/index-DU7Wk3Qr.js";const B=e("preReview",()=>{const e=a([]),t=a(0),o=a({table:!1,search:!1,create:!1,batch:!1}),u=a({currentPage:1,pageSize:20,total:0,pageSizes:[10,20,50,100]}),r=a({page:1,pageSize:20,title:"",authorName:"",status:"pending",type:"",category:"",createdAtStart:"",createdAtEnd:"",keyword:"",sortBy:"createdAt",sortOrder:"desc"}),s=a([]),n=l(()=>s.value.length>0),d=l(()=>s.value.length),i=async(a=0)=>{try{o.value.table=!0;const a={...r.value,page:u.value.currentPage,pageSize:u.value.pageSize},l=await Y.getPendingDocuments(a);if(!l)throw new Error("API响应为空");const s=l.list||l.items;if(!s)throw new Error("API响应格式错误：缺少list或items字段");e.value=s||[];const n=Number(l.total)||0,d=Number(l.page)||1;t.value=n,u.value.total=n,u.value.currentPage=d}catch{if(a<3){const e=1e3*(a+1);return await new Promise(a=>setTimeout(a,e)),i(a+1)}S.error("获取文档列表失败，请刷新页面重试"),e.value=[],t.value=0,u.value.total=0}finally{o.value.table=!1}};return{documents:e,total:t,loading:o,pagination:u,searchParams:r,selectedDocumentIds:s,hasSelectedDocuments:n,selectedDocumentsCount:d,fetchPendingDocuments:i,searchDocuments:async e=>{try{o.value.search=!0,Object.assign(r.value,e),u.value.currentPage=1,r.value.page=1,await i()}catch(a){S.error("搜索文档失败")}finally{o.value.search=!1}},createDocument:async e=>{try{o.value.create=!0;const a=await Y.createDocument(e);return await i(),S.success("文档创建成功"),a}catch(a){throw S.error("创建文档失败"),a}finally{o.value.create=!1}},deleteDocument:async e=>{try{await Y.deleteDocument(e),await i(),S.success("文档删除成功")}catch(a){throw S.error("删除文档失败"),a}},aiProofreading:async e=>{try{o.value.batch=!0;const a=await Y.aiProofreading(e);return S.success("AI校对完成"),a}catch(a){throw S.error("AI校对失败"),a}finally{o.value.batch=!1}},batchApprove:async e=>{try{o.value.batch=!0;const a={documentIds:e},l=await Y.batchApprove(a);return s.value=[],await i(),S.success(`成功通过 ${l.successCount} 个文档的预审`),l}catch(a){throw S.error("批量通过预审失败"),a}finally{o.value.batch=!1}},batchReject:async(e,a)=>{try{o.value.batch=!0;const l={documentIds:e,reason:a},t=await Y.batchReject(l);return s.value=[],await i(),S.success(`成功拒绝 ${t.successCount} 个文档的预审`),t}catch(l){throw S.error("批量拒绝预审失败"),l}finally{o.value.batch=!1}},updatePagination:(e,a)=>{u.value.currentPage=e,a&&(u.value.pageSize=a),r.value.page=e,r.value.pageSize=u.value.pageSize,i()},setSelectedDocumentIds:e=>{s.value=e},resetSearchParams:()=>{r.value={page:1,pageSize:20,title:"",authorName:"",status:"pending",type:"",category:"",createdAtStart:"",createdAtEnd:"",keyword:"",sortBy:"createdAt",sortOrder:"desc"},u.value.currentPage=1}}}),R={class:"form-group"},j={class:"form-group"},M={class:"form-group"},O={class:"dialog-footer"},$=T(t({__name:"CreateDocumentDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","submit"],setup(e,{emit:t}){const h=e,f=t,{typeOptions:y,categoryOptions:b}=I(),w=a(),_=a(!1),V=a([]),x=o({title:"",content:"",type:"text",category:"",tags:[],authorName:"",authorOrganization:"",authorBio:"",fileUrl:"",estimatedTime:""}),k=a(["重要","紧急","草稿","已审核","待修改","已发布","技术文档","产品文档","用户手册","培训材料"]),z={title:[{required:!0,message:"请输入文档标题",trigger:"blur"},{min:2,max:100,message:"标题长度在 2 到 100 个字符",trigger:"blur"}],content:[{required:!0,message:"请输入内容简介",trigger:"blur"},{min:10,max:500,message:"内容简介长度在 10 到 500 个字符",trigger:"blur"}],type:[{required:!0,message:"请选择文档类型",trigger:"change"}],category:[{required:!0,message:"请选择文档分类",trigger:"change"}],authorName:[{required:!0,message:"请输入作者姓名",trigger:"blur"},{min:2,max:50,message:"作者姓名长度在 2 到 50 个字符",trigger:"blur"}],authorOrganization:[{required:!0,message:"请输入作者单位",trigger:"blur"},{min:2,max:100,message:"作者单位长度在 2 到 100 个字符",trigger:"blur"}]},D=l({get:()=>h.modelValue,set:e=>f("update:modelValue",e)}),C=e=>{if(e.size&&e.size>10485760)return S.error("文件大小不能超过 10MB"),!1;const a="."+(e.name.split(".").pop()?.toLowerCase()||"");if(![".txt",".doc",".docx",".pdf",".wps"].includes(a))return S.error("不支持的文件类型"),!1;x.fileUrl=`https://example.com/files/${e.name}`,V.value=[e]},U=async()=>{if(w.value)try{await w.value.validate(),_.value=!0,f("submit",{...x})}catch(e){}finally{_.value=!1}},A=()=>{w.value&&w.value.resetFields(),V.value=[],x.fileUrl="",D.value=!1};return(e,a)=>{const l=s("el-input"),t=s("el-form-item"),o=s("el-option"),h=s("el-select"),f=s("el-col"),S=s("el-row"),N=s("el-icon"),Y=s("el-upload"),I=s("el-date-picker"),T=s("el-form"),B=s("el-button"),$=s("el-dialog");return n(),u($,{modelValue:D.value,"onUpdate:modelValue":a[9]||(a[9]=e=>D.value=e),title:"新建文档",width:"800px","close-on-click-modal":!1,onClose:A},{footer:r(()=>[i("span",O,[d(B,{onClick:A},{default:r(()=>a[15]||(a[15]=[v("取消")])),_:1,__:[15]}),d(B,{type:"primary",onClick:U,loading:_.value},{default:r(()=>a[16]||(a[16]=[v(" 创建文档 ")])),_:1,__:[16]},8,["loading"])])]),default:r(()=>[d(T,{ref_key:"formRef",ref:w,model:x,rules:z,"label-width":"120px",class:"create-document-form"},{default:r(()=>[i("div",R,[a[10]||(a[10]=i("h4",{class:"group-title"},"文档信息",-1)),d(t,{label:"文档标题",prop:"title"},{default:r(()=>[d(l,{modelValue:x.title,"onUpdate:modelValue":a[0]||(a[0]=e=>x.title=e),placeholder:"请输入文档标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),d(t,{label:"内容简介",prop:"content"},{default:r(()=>[d(l,{modelValue:x.content,"onUpdate:modelValue":a[1]||(a[1]=e=>x.content=e),type:"textarea",rows:4,placeholder:"请输入文档内容简介",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),d(S,{gutter:20},{default:r(()=>[d(f,{span:12},{default:r(()=>[d(t,{label:"文档类型",prop:"type"},{default:r(()=>[d(h,{modelValue:x.type,"onUpdate:modelValue":a[2]||(a[2]=e=>x.type=e),placeholder:"请选择文档类型",style:{width:"100%"}},{default:r(()=>[(n(!0),c(g,null,p(m(y),e=>(n(),u(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(f,{span:12},{default:r(()=>[d(t,{label:"文档分类",prop:"category"},{default:r(()=>[d(h,{modelValue:x.category,"onUpdate:modelValue":a[3]||(a[3]=e=>x.category=e),placeholder:"请选择文档分类",style:{width:"100%"}},{default:r(()=>[(n(!0),c(g,null,p(m(b),e=>(n(),u(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(t,{label:"文档标签",prop:"tags"},{default:r(()=>[d(h,{modelValue:x.tags,"onUpdate:modelValue":a[4]||(a[4]=e=>x.tags=e),multiple:"",filterable:"","allow-create":"",placeholder:"请选择或输入文档标签",style:{width:"100%"}},{default:r(()=>[(n(!0),c(g,null,p(k.value,e=>(n(),u(o,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),i("div",j,[a[11]||(a[11]=i("h4",{class:"group-title"},"作者信息",-1)),d(S,{gutter:20},{default:r(()=>[d(f,{span:12},{default:r(()=>[d(t,{label:"作者姓名",prop:"authorName"},{default:r(()=>[d(l,{modelValue:x.authorName,"onUpdate:modelValue":a[5]||(a[5]=e=>x.authorName=e),placeholder:"请输入作者姓名",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1}),d(f,{span:12},{default:r(()=>[d(t,{label:"作者单位",prop:"authorOrganization"},{default:r(()=>[d(l,{modelValue:x.authorOrganization,"onUpdate:modelValue":a[6]||(a[6]=e=>x.authorOrganization=e),placeholder:"请输入作者单位",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(t,{label:"作者简介",prop:"authorBio"},{default:r(()=>[d(l,{modelValue:x.authorBio,"onUpdate:modelValue":a[7]||(a[7]=e=>x.authorBio=e),type:"textarea",rows:3,placeholder:"请输入作者简介（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),i("div",M,[a[14]||(a[14]=i("h4",{class:"group-title"},"其他信息",-1)),d(t,{label:"文件上传",prop:"fileUrl"},{default:r(()=>[d(Y,{class:"upload-demo",drag:"",action:"#","auto-upload":!1,"on-change":C,"file-list":V.value,accept:".txt,.doc,.docx,.pdf,.wps"},{tip:r(()=>a[12]||(a[12]=[i("div",{class:"el-upload__tip"},"支持 txt/doc/docx/pdf/wps 格式文件，且不超过 10MB",-1)])),default:r(()=>[d(N,{class:"el-icon--upload"},{default:r(()=>[d(m(P))]),_:1}),a[13]||(a[13]=i("div",{class:"el-upload__text"},[v("将文件拖到此处，或"),i("em",null,"点击上传")],-1))]),_:1,__:[13]},8,["file-list"])]),_:1}),d(t,{label:"预计完成时间",prop:"estimatedTime"},{default:r(()=>[d(I,{modelValue:x.estimatedTime,"onUpdate:modelValue":a[8]||(a[8]=e=>x.estimatedTime=e),type:"datetime",placeholder:"请选择预计完成时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})])]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-ea4f05c0"]]),q={class:"unreviewed-documents"},E={class:"card-header"},H={class:"header-actions"},F={class:"search-section"},W={class:"table-section"},K={class:"action-buttons"},L={class:"pagination-section"},Q=T(t({__name:"UnreviewedDocuments",setup(e){const l=D(),t=z(),o=B(),{documents:P,loading:Y,pagination:T}=h(o),{fetchPendingDocuments:R,searchDocuments:j,deleteDocument:M,updatePagination:O,setSelectedDocumentIds:Q,resetSearchParams:J}=o,{getStatusTagType:X,getTypeIcon:G,formatFileSize:Z,formatDateTime:ee}=I(),ae=a([{value:"all",label:"全部状态",color:""},{value:"draft",label:"草稿",color:"info"},{value:"pending",label:"待预审",color:"warning"},{value:"reviewing",label:"预审中",color:"primary"},{value:"approved",label:"已通过",color:"success"},{value:"rejected",label:"已拒绝",color:"danger"},{value:"published",label:"已发布",color:"success"}]),le=a([{value:"all",label:"全部类型",icon:""},{value:"text",label:"纯文本",icon:"Document"},{value:"docx",label:"Word文档",icon:"Document"},{value:"pdf",label:"PDF文档",icon:"Document"},{value:"wps",label:"WPS文档",icon:"Document"}]),te=a([{value:"all",label:"全部分类"},{value:"技术文档",label:"技术文档"},{value:"产品说明",label:"产品说明"},{value:"用户手册",label:"用户手册"},{value:"营销文案",label:"营销文案"},{value:"法律文件",label:"法律文件"},{value:"学术论文",label:"学术论文"},{value:"新闻报道",label:"新闻报道"},{value:"教育培训",label:"教育培训"},{value:"政策解读",label:"政策解读"},{value:"行业分析",label:"行业分析"}]),oe=a({title:"",authorName:"",status:"all",type:"all",category:"all",dateRange:null}),ue=a(!1),re=e=>({text:"纯文本",docx:"Word",pdf:"PDF",wps:"WPS"}[e]||e),se=async()=>{const e={title:oe.value.title,authorName:oe.value.authorName,status:"all"===oe.value.status?"pending":oe.value.status||"pending",type:"all"===oe.value.type?"":oe.value.type,category:"all"===oe.value.category?"":oe.value.category};oe.value.dateRange&&2===oe.value.dateRange.length&&(e.createdAtStart=oe.value.dateRange[0],e.createdAtEnd=oe.value.dateRange[1]),await j(e)},ne=async()=>{oe.value.title="",oe.value.authorName="",oe.value.status="all",oe.value.type="all",oe.value.category="all",oe.value.dateRange=null,J(),await R()},de=async()=>{await R()},ie=e=>{const a=e.map(e=>e.id);Q(a)},ce=({prop:e,order:a})=>{if(a){j({sortBy:e,sortOrder:"ascending"===a?"asc":"desc"})}else R()},pe=async e=>{O(1,e)},me=async e=>{O(e)},ge=e=>{S.info(`查看文档详情: ${e.title}`)},ve=async e=>{try{await createDocument(e),ue.value=!1}catch{}},he=async()=>{try{await R()}catch(e){}};return f(async()=>{let e=0;const a=async()=>{try{const e=await fetch("/api/test");if(!e.ok)throw new Error(`Mock服务未就绪 (状态码: ${e.status})`);await he()}catch(l){e++,e<10?setTimeout(a,1e3):await he()}};await a()}),y(async()=>{0===P.value.length&&await he()}),b(()=>l.path,e=>{e.includes("unreviewed-documents")&&0===P.value.length&&he()}),(e,a)=>{const l=s("el-icon"),o=s("el-button"),h=s("el-input"),f=s("el-form-item"),y=s("el-option"),b=s("el-select"),z=s("el-date-picker"),D=s("el-form"),I=s("el-table-column"),B=s("el-link"),R=s("el-tag"),j=s("el-table"),O=s("el-pagination"),Q=s("el-card"),J=V("loading");return n(),c("div",q,[d(Q,null,{header:r(()=>[i("div",E,[a[12]||(a[12]=i("span",null,"未预审文档",-1)),i("div",H,[d(o,{type:"primary",size:"default",onClick:a[0]||(a[0]=e=>ue.value=!0)},{default:r(()=>[d(l,null,{default:r(()=>[d(m(A))]),_:1}),a[10]||(a[10]=v(" 新建文档 "))]),_:1,__:[10]}),d(o,{type:"info",size:"default",onClick:de},{default:r(()=>[d(l,null,{default:r(()=>[d(m(U))]),_:1}),a[11]||(a[11]=v(" 刷新 "))]),_:1,__:[11]})])])]),default:r(()=>[i("div",F,[d(D,{model:oe.value,inline:"",class:"search-form"},{default:r(()=>[d(f,{label:"文档标题"},{default:r(()=>[d(h,{modelValue:oe.value.title,"onUpdate:modelValue":a[1]||(a[1]=e=>oe.value.title=e),placeholder:"请输入文档标题",clearable:"",style:{width:"200px"},onKeyup:w(se,["enter"])},null,8,["modelValue"])]),_:1}),d(f,{label:"作者姓名"},{default:r(()=>[d(h,{modelValue:oe.value.authorName,"onUpdate:modelValue":a[2]||(a[2]=e=>oe.value.authorName=e),placeholder:"请输入作者姓名",clearable:"",style:{width:"150px"},onKeyup:w(se,["enter"])},null,8,["modelValue"])]),_:1}),d(f,{label:"文档状态"},{default:r(()=>[d(b,{modelValue:oe.value.status,"onUpdate:modelValue":a[3]||(a[3]=e=>oe.value.status=e),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:r(()=>[(n(!0),c(g,null,p(ae.value,e=>(n(),u(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(f,{label:"文档类型"},{default:r(()=>[d(b,{modelValue:oe.value.type,"onUpdate:modelValue":a[4]||(a[4]=e=>oe.value.type=e),placeholder:"请选择类型",clearable:"",style:{width:"120px"}},{default:r(()=>[(n(!0),c(g,null,p(le.value,e=>(n(),u(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(f,{label:"文档分类"},{default:r(()=>[d(b,{modelValue:oe.value.category,"onUpdate:modelValue":a[5]||(a[5]=e=>oe.value.category=e),placeholder:"请选择分类",clearable:"",style:{width:"120px"}},{default:r(()=>[(n(!0),c(g,null,p(te.value,e=>(n(),u(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(f,{label:"时间范围"},{default:r(()=>[d(z,{modelValue:oe.value.dateRange,"onUpdate:modelValue":a[6]||(a[6]=e=>oe.value.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),d(f,null,{default:r(()=>[d(o,{type:"primary",loading:m(Y).search,onClick:se},{default:r(()=>[d(l,null,{default:r(()=>[d(m(C))]),_:1}),a[13]||(a[13]=v(" 搜索 "))]),_:1,__:[13]},8,["loading"]),d(o,{onClick:ne},{default:r(()=>[d(l,null,{default:r(()=>[d(m(U))]),_:1}),a[14]||(a[14]=v(" 重置 "))]),_:1,__:[14]})]),_:1})]),_:1},8,["model"])]),i("div",W,[_((n(),u(j,{data:m(P),stripe:"",border:"",style:{width:"100%"},onSelectionChange:ie,onSortChange:ce},{default:r(()=>[d(I,{type:"selection",width:"55"}),d(I,{label:"序号",width:"60",align:"center"},{default:r(({$index:e})=>[v(x((m(T).currentPage-1)*m(T).pageSize+e+1),1)]),_:1}),d(I,{prop:"title",label:"标题",width:"200",sortable:"custom","show-overflow-tooltip":""},{default:r(({row:e})=>[d(B,{type:"primary",onClick:a=>ge(e)},{default:r(()=>[v(x(e.title),1)]),_:2},1032,["onClick"])]),_:1}),d(I,{prop:"type",label:"类型",width:"100",sortable:"custom"},{default:r(({row:e})=>{return[d(R,{type:(a=e.type,{text:"info",docx:"primary",pdf:"danger",wps:"success"}[a]||"info")},{default:r(()=>[d(l,null,{default:r(()=>[(n(),u(k(m(G)(e.type))))]),_:2},1024),v(" "+x(re(e.type)),1)]),_:2},1032,["type"])];var a}),_:1}),d(I,{prop:"status",label:"状态",width:"100",sortable:"custom"},{default:r(({row:e})=>[d(R,{type:m(X)(e.status)},{default:r(()=>{return[v(x((a=e.status,{draft:"草稿",pending:"待预审",reviewing:"预审中",approved:"已通过",rejected:"已拒绝",published:"已发布"}[a]||a)),1)];var a}),_:2},1032,["type"])]),_:1}),d(I,{prop:"category",label:"分类",width:"120",sortable:"custom","show-overflow-tooltip":""}),d(I,{prop:"authorName",label:"作者姓名",width:"120",sortable:"custom","show-overflow-tooltip":""}),d(I,{prop:"authorOrganization",label:"作者单位",width:"180",sortable:"custom","show-overflow-tooltip":""}),d(I,{prop:"creatorName",label:"创建者",width:"120",sortable:"custom","show-overflow-tooltip":""}),d(I,{prop:"fileSize",label:"大小",width:"100",sortable:"custom"},{default:r(({row:e})=>[v(x(m(Z)(e.fileSize)),1)]),_:1}),d(I,{prop:"createdAt",label:"创建时间",width:"160",sortable:"custom"},{default:r(({row:e})=>[v(x(m(ee)(e.createdAt)),1)]),_:1}),d(I,{prop:"updatedAt",label:"更新时间",width:"160",sortable:"custom"},{default:r(({row:e})=>[v(x(m(ee)(e.updatedAt)),1)]),_:1}),d(I,{prop:"estimatedTime",label:"预计完成时间",width:"160"},{default:r(({row:e})=>[v(x(e.estimatedTime?m(ee)(e.estimatedTime):"-"),1)]),_:1}),d(I,{label:"操作",width:"240",fixed:"right"},{default:r(({row:e})=>[i("div",K,[d(o,{type:"primary",size:"small",onClick:a=>ge(e)},{default:r(()=>a[15]||(a[15]=[v(" 详情 ")])),_:2,__:[15]},1032,["onClick"]),d(o,{type:"warning",size:"small",onClick:a=>(async e=>{try{t.push({name:"AIProofreadingPage",params:{documentId:e.id}})}catch(a){S.error("跳转失败，请重试")}})(e)},{default:r(()=>a[16]||(a[16]=[v(" AI校对 ")])),_:2,__:[16]},1032,["onClick"]),d(o,{type:"info",size:"small",onClick:a=>(e=>{S.info(`编辑文档: ${e.title}`)})(e)},{default:r(()=>a[17]||(a[17]=[v(" 编辑 ")])),_:2,__:[17]},1032,["onClick"]),d(o,{type:"danger",size:"small",plain:"",onClick:a=>(async e=>{try{await N.confirm(`确定要删除文档"${e.title}"吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await M(e.id)}catch{}})(e)},{default:r(()=>a[18]||(a[18]=[v(" 删除 ")])),_:2,__:[18]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[J,m(Y).table]]),i("div",L,[d(O,{"current-page":m(T).currentPage,"onUpdate:currentPage":a[7]||(a[7]=e=>m(T).currentPage=e),"page-size":m(T).pageSize,"onUpdate:pageSize":a[8]||(a[8]=e=>m(T).pageSize=e),"page-sizes":m(T).pageSizes,total:m(T).total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:pe,onCurrentChange:me},null,8,["current-page","page-size","page-sizes","total"])])])]),_:1}),d($,{modelValue:ue.value,"onUpdate:modelValue":a[9]||(a[9]=e=>ue.value=e),onSubmit:ve},null,8,["modelValue"])])}}}),[["__scopeId","data-v-c374c726"]]);export{Q as default};
