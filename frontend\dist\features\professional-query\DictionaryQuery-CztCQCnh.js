import{d as a,c as s,a as e,Q as r,I as n,ag as t,o as c}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as l}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const o={class:"dictionary-query"},d={class:"content"},u=l(a({__name:"DictionaryQuery",setup:a=>(a,l)=>{const u=t("el-card");return c(),s("div",o,[l[1]||(l[1]=e("div",{class:"page-header"},[e("h1",null,"词典查询"),e("p",{class:"page-description"},"查询多语言词典和专业术语")],-1)),e("div",d,[r(u,null,{default:n(()=>l[0]||(l[0]=[e("p",null,"词典查询功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-2be4eed3"]]);export{u as default};
