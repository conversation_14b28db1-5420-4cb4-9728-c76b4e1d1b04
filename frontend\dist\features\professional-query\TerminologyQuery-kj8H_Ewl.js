import{x as e}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as a,r as l,c as s,a as u,K as n,Q as t,I as r,ag as d,o as i,M as o,u as c,P as p,a6 as m,O as v,H as _}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as h}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const f={class:"terminology-query"},g={class:"search-section"},y={key:0,class:"results-section"},k=h(a({__name:"TerminologyQuery",setup(a){const h=l(""),k=l([]),j=()=>{k.value=[{id:1,term:h.value,definition:"这是一个示例术语定义...",tags:["专业术语","技术"]}]};return(a,l)=>{const x=d("el-icon"),V=d("el-button"),b=d("el-input"),z=d("el-card"),I=d("el-tag");return i(),s("div",f,[l[3]||(l[3]=u("div",{class:"page-header"},[u("h1",null,"术语查询"),u("p",{class:"page-description"},"查询专业术语和词汇释义")],-1)),u("div",g,[t(z,null,{default:r(()=>[t(b,{modelValue:h.value,"onUpdate:modelValue":l[0]||(l[0]=e=>h.value=e),placeholder:"请输入要查询的术语",size:"large",clearable:""},{append:r(()=>[t(V,{type:"primary",onClick:j},{default:r(()=>[t(x,null,{default:r(()=>[t(c(e))]),_:1}),l[1]||(l[1]=o(" 查询 "))]),_:1,__:[1]})]),_:1},8,["modelValue"])]),_:1})]),k.value.length>0?(i(),s("div",y,[t(z,null,{header:r(()=>l[2]||(l[2]=[u("div",{class:"card-header"},[u("span",null,"查询结果")],-1)])),default:r(()=>[(i(!0),s(p,null,m(k.value,e=>(i(),s("div",{key:e.id,class:"result-item"},[u("h3",null,v(e.term),1),u("p",null,v(e.definition),1),(i(!0),s(p,null,m(e.tags,e=>(i(),_(I,{key:e,size:"small"},{default:r(()=>[o(v(e),1)]),_:2},1024))),128))]))),128))]),_:1})])):n("",!0)])}}}),[["__scopeId","data-v-5017eda4"]]);export{k as default};
