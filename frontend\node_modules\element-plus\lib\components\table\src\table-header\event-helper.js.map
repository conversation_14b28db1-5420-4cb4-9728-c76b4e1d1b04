{"version": 3, "file": "event-helper.js", "sources": ["../../../../../../../packages/components/table/src/table-header/event-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, inject, ref } from 'vue'\nimport { isNull } from 'lodash-unified'\nimport {\n  addClass,\n  hasClass,\n  isClient,\n  isElement,\n  removeClass,\n} from '@element-plus/utils'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableHeaderProps } from '.'\nimport type { TableColumnCtx } from '../table-column/defaults'\n\nfunction useEvent<T>(props: TableHeaderProps<T>, emit) {\n  const instance = getCurrentInstance()\n  const parent = inject(TABLE_INJECTION_KEY)\n  const handleFilterClick = (event: Event) => {\n    event.stopPropagation()\n    return\n  }\n\n  const handleHeaderClick = (event: Event, column: TableColumnCtx<T>) => {\n    if (!column.filters && column.sortable) {\n      handleSortClick(event, column, false)\n    } else if (column.filterable && !column.sortable) {\n      handleFilterClick(event)\n    }\n    parent?.emit('header-click', column, event)\n  }\n\n  const handleHeaderContextMenu = (event: Event, column: TableColumnCtx<T>) => {\n    parent?.emit('header-contextmenu', column, event)\n  }\n  const draggingColumn = ref(null)\n  const dragging = ref(false)\n  const dragState = ref({})\n  const handleMouseDown = (event: MouseEvent, column: TableColumnCtx<T>) => {\n    if (!isClient) return\n    if (column.children && column.children.length > 0) return\n    /* istanbul ignore if */\n    if (draggingColumn.value && props.border) {\n      dragging.value = true\n\n      const table = parent\n      emit('set-drag-visible', true)\n      const tableEl = table?.vnode.el\n      const tableLeft = tableEl.getBoundingClientRect().left\n      const columnEl = instance.vnode.el.querySelector(`th.${column.id}`)\n      const columnRect = columnEl.getBoundingClientRect()\n      const minLeft = columnRect.left - tableLeft + 30\n\n      addClass(columnEl, 'noclick')\n\n      dragState.value = {\n        startMouseLeft: event.clientX,\n        startLeft: columnRect.right - tableLeft,\n        startColumnLeft: columnRect.left - tableLeft,\n        tableLeft,\n      }\n      const resizeProxy = table?.refs.resizeProxy as HTMLElement\n      resizeProxy.style.left = `${(dragState.value as any).startLeft}px`\n\n      document.onselectstart = function () {\n        return false\n      }\n      document.ondragstart = function () {\n        return false\n      }\n\n      const handleMouseMove = (event: MouseEvent) => {\n        const deltaLeft =\n          event.clientX - (dragState.value as any).startMouseLeft\n        const proxyLeft = (dragState.value as any).startLeft + deltaLeft\n\n        resizeProxy.style.left = `${Math.max(minLeft, proxyLeft)}px`\n      }\n\n      const handleMouseUp = () => {\n        if (dragging.value) {\n          const { startColumnLeft, startLeft } = dragState.value as any\n          const finalLeft = Number.parseInt(resizeProxy.style.left, 10)\n          const columnWidth = finalLeft - startColumnLeft\n          column.width = column.realWidth = columnWidth\n          table?.emit(\n            'header-dragend',\n            column.width,\n            startLeft - startColumnLeft,\n            column,\n            event\n          )\n          requestAnimationFrame(() => {\n            props.store.scheduleLayout(false, true)\n          })\n          document.body.style.cursor = ''\n          dragging.value = false\n          draggingColumn.value = null\n          dragState.value = {}\n          emit('set-drag-visible', false)\n        }\n\n        document.removeEventListener('mousemove', handleMouseMove)\n        document.removeEventListener('mouseup', handleMouseUp)\n        document.onselectstart = null\n        document.ondragstart = null\n\n        setTimeout(() => {\n          removeClass(columnEl, 'noclick')\n        }, 0)\n      }\n\n      document.addEventListener('mousemove', handleMouseMove)\n      document.addEventListener('mouseup', handleMouseUp)\n    }\n  }\n\n  const handleMouseMove = (event: MouseEvent, column: TableColumnCtx<T>) => {\n    if (column.children && column.children.length > 0) return\n    const el = event.target as HTMLElement\n    if (!isElement(el)) {\n      return\n    }\n    const target = el?.closest('th')\n\n    if (!column || !column.resizable || !target) return\n\n    if (!dragging.value && props.border) {\n      const rect = target.getBoundingClientRect()\n\n      const bodyStyle = document.body.style\n      const isLastTh = target.parentNode?.lastElementChild === target\n      const allowDarg = props.allowDragLastColumn || !isLastTh\n      if (rect.width > 12 && rect.right - event.clientX < 8 && allowDarg) {\n        bodyStyle.cursor = 'col-resize'\n        if (hasClass(target, 'is-sortable')) {\n          target.style.cursor = 'col-resize'\n        }\n        draggingColumn.value = column\n      } else if (!dragging.value) {\n        bodyStyle.cursor = ''\n        if (hasClass(target, 'is-sortable')) {\n          target.style.cursor = 'pointer'\n        }\n        draggingColumn.value = null\n      }\n    }\n  }\n\n  const handleMouseOut = () => {\n    if (!isClient) return\n    document.body.style.cursor = ''\n  }\n  const toggleOrder = ({ order, sortOrders }) => {\n    if (order === '') return sortOrders[0]\n    const index = sortOrders.indexOf(order || null)\n    return sortOrders[index > sortOrders.length - 2 ? 0 : index + 1]\n  }\n  const handleSortClick = (\n    event: Event,\n    column: TableColumnCtx<T>,\n    givenOrder: string | boolean\n  ) => {\n    event.stopPropagation()\n    const order =\n      column.order === givenOrder ? null : givenOrder || toggleOrder(column)\n    const target = (event.target as HTMLElement)?.closest('th')\n\n    if (target) {\n      if (hasClass(target, 'noclick')) {\n        removeClass(target, 'noclick')\n        return\n      }\n    }\n\n    if (!column.sortable) return\n\n    const clickTarget = event.currentTarget\n\n    if (\n      ['ascending', 'descending'].some(\n        (str) => hasClass(clickTarget, str) && !column.sortOrders.includes(str)\n      )\n    ) {\n      return\n    }\n\n    const states = props.store.states\n    let sortProp = states.sortProp.value\n    let sortOrder\n    const sortingColumn = states.sortingColumn.value\n\n    if (\n      sortingColumn !== column ||\n      (sortingColumn === column && isNull(sortingColumn.order))\n    ) {\n      if (sortingColumn) {\n        sortingColumn.order = null\n      }\n      states.sortingColumn.value = column\n      sortProp = column.property\n    }\n    if (!order) {\n      sortOrder = column.order = null\n    } else {\n      sortOrder = column.order = order\n    }\n\n    states.sortProp.value = sortProp\n    states.sortOrder.value = sortOrder\n\n    parent?.store.commit('changeSortCondition')\n  }\n\n  return {\n    handleHeaderClick,\n    handleHeaderContextMenu,\n    handleMouseDown,\n    handleMouseMove,\n    handleMouseOut,\n    handleSortClick,\n    handleFilterClick,\n  }\n}\n\nexport default useEvent\n"], "names": ["getCurrentInstance", "inject", "TABLE_INJECTION_KEY", "ref", "isClient", "addClass", "removeClass", "isElement", "hasClass", "isNull"], "mappings": ";;;;;;;;;;;AAUA,SAAS,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE;AAC/B,EAAE,MAAM,QAAQ,GAAGA,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,MAAM,GAAGC,UAAM,CAACC,0BAAmB,CAAC,CAAC;AAC7C,EAAE,MAAM,iBAAiB,GAAG,CAAC,KAAK,KAAK;AACvC,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;AAC5B,IAAI,OAAO;AACX,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK;AAC/C,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE;AAC5C,MAAM,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5C,KAAK,MAAM,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACtD,MAAM,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACzE,GAAG,CAAC;AACJ,EAAE,MAAM,uBAAuB,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK;AACrD,IAAI,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC/E,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAGC,OAAG,CAAC,IAAI,CAAC,CAAC;AACnC,EAAE,MAAM,QAAQ,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,EAAE,MAAM,SAAS,GAAGA,OAAG,CAAC,EAAE,CAAC,CAAC;AAC5B,EAAE,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK;AAC7C,IAAI,IAAI,CAACC,aAAQ;AACjB,MAAM,OAAO;AACb,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;AACrD,MAAM,OAAO;AACb,IAAI,IAAI,cAAc,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AAC9C,MAAM,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5B,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC;AAC3B,MAAM,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;AACrC,MAAM,MAAM,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;AAC9D,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;AAC7D,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1E,MAAM,MAAM,UAAU,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAC;AAC1D,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE,CAAC;AACvD,MAAMC,cAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AACpC,MAAM,SAAS,CAAC,KAAK,GAAG;AACxB,QAAQ,cAAc,EAAE,KAAK,CAAC,OAAO;AACrC,QAAQ,SAAS,EAAE,UAAU,CAAC,KAAK,GAAG,SAAS;AAC/C,QAAQ,eAAe,EAAE,UAAU,CAAC,IAAI,GAAG,SAAS;AACpD,QAAQ,SAAS;AACjB,OAAO,CAAC;AACR,MAAM,MAAM,WAAW,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1E,MAAM,WAAW,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AAChE,MAAM,QAAQ,CAAC,aAAa,GAAG,WAAW;AAC1C,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO,CAAC;AACR,MAAM,QAAQ,CAAC,WAAW,GAAG,WAAW;AACxC,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO,CAAC;AACR,MAAM,MAAM,gBAAgB,GAAG,CAAC,MAAM,KAAK;AAC3C,QAAQ,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC;AAC1E,QAAQ,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AAChE,QAAQ,WAAW,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AACrE,OAAO,CAAC;AACR,MAAM,MAAM,aAAa,GAAG,MAAM;AAClC,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE;AAC5B,UAAU,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC;AACjE,UAAU,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACxE,UAAU,MAAM,WAAW,GAAG,SAAS,GAAG,eAAe,CAAC;AAC1D,UAAU,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC;AACxD,UAAU,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,KAAK,EAAE,SAAS,GAAG,eAAe,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC1H,UAAU,qBAAqB,CAAC,MAAM;AACtC,YAAY,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACpD,WAAW,CAAC,CAAC;AACb,UAAU,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;AAC1C,UAAU,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AACjC,UAAU,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC;AACtC,UAAU,SAAS,CAAC,KAAK,GAAG,EAAE,CAAC;AAC/B,UAAU,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AAC1C,SAAS;AACT,QAAQ,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;AACpE,QAAQ,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAC/D,QAAQ,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;AACtC,QAAQ,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACpC,QAAQ,UAAU,CAAC,MAAM;AACzB,UAAUC,iBAAW,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AAC3C,SAAS,EAAE,CAAC,CAAC,CAAC;AACd,OAAO,CAAC;AACR,MAAM,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;AAC/D,MAAM,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAC1D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK;AAC7C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;AACrD,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;AAC5B,IAAI,IAAI,CAACC,eAAS,CAAC,EAAE,CAAC,EAAE;AACxB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM;AAC/C,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AACzC,MAAM,MAAM,IAAI,GAAG,MAAM,CAAC,qBAAqB,EAAE,CAAC;AAClD,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;AAC5C,MAAM,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,MAAM,MAAM,CAAC;AACpG,MAAM,MAAM,SAAS,GAAG,KAAK,CAAC,mBAAmB,IAAI,CAAC,QAAQ,CAAC;AAC/D,MAAM,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,IAAI,SAAS,EAAE;AAC1E,QAAQ,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC;AACxC,QAAQ,IAAIC,cAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE;AAC7C,UAAU,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC;AAC7C,SAAS;AACT,QAAQ,cAAc,CAAC,KAAK,GAAG,MAAM,CAAC;AACtC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AAClC,QAAQ,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;AAC9B,QAAQ,IAAIA,cAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE;AAC7C,UAAU,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;AAC1C,SAAS;AACT,QAAQ,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC;AACpC,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,IAAI,CAACJ,aAAQ;AACjB,MAAM,OAAO;AACb,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;AACpC,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK;AACjD,IAAI,IAAI,KAAK,KAAK,EAAE;AACpB,MAAM,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC;AACpD,IAAI,OAAO,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;AACrE,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,KAAK;AACzD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;AAC5B,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,KAAK,UAAU,GAAG,IAAI,GAAG,UAAU,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;AACzF,IAAI,MAAM,MAAM,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC3E,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,IAAII,cAAQ,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;AACvC,QAAQF,iBAAW,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACvC,QAAQ,OAAO;AACf,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ;AACxB,MAAM,OAAO;AACb,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,aAAa,CAAC;AAC5C,IAAI,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAKE,cAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;AACnH,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AACtC,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;AACzC,IAAI,IAAI,SAAS,CAAC;AAClB,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC;AACrD,IAAI,IAAI,aAAa,KAAK,MAAM,IAAI,aAAa,KAAK,MAAM,IAAIC,oBAAM,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;AAC7F,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC;AACnC,OAAO;AACP,MAAM,MAAM,CAAC,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AACtC,KAAK,MAAM;AACX,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AACvC,KAAK;AACL,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC;AACrC,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC;AACvC,IAAI,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;AACzE,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,iBAAiB;AACrB,IAAI,uBAAuB;AAC3B,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,GAAG,CAAC;AACJ;;;;"}