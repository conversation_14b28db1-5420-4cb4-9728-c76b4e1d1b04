<template>
  <div class="test-proofread-editor">
    <div class="test-header">
      <h2>ProofreadResultEditor 下拉菜单收起功能测试</h2>
      <p>测试以下功能：</p>
      <ul>
        <li>点击工具栏下拉菜单（字体、颜色、字号等）</li>
        <li>选择菜单项后自动收起</li>
        <li>点击外部区域自动收起</li>
        <li>按 ESC 键自动收起</li>
      </ul>
    </div>

    <div class="editor-container">
      <ProofreadResultEditor
        :content="testContent"
        :changes="testChanges"
        :editable="true"
        edit-mode="edit"
        :show-toolbar="true"
        height="400px"
        @content-change="handleContentChange"
        @change-accept="handleChangeAccept"
        @change-reject="handleChangeReject"
        @save="handleSave"
      />
    </div>

    <div class="test-instructions">
      <h3>测试步骤：</h3>
      <ol>
        <li>点击工具栏中的字体选择按钮，观察下拉菜单是否出现</li>
        <li>选择一个字体，观察下拉菜单是否自动收起</li>
        <li>点击颜色选择按钮，然后点击编辑器外部区域，观察菜单是否收起</li>
        <li>点击字号选择按钮，然后按 ESC 键，观察菜单是否收起</li>
        <li>测试其他下拉菜单按钮的收起功能</li>
      </ol>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ProofreadResultEditor from '@/features/online-proofreading/components/ProofreadResultEditor.vue'
import type { ProofreadingChange } from '@/features/online-proofreading/types'

// 测试内容
const testContent = ref(`
<h1>测试标题</h1>
<p>这是一段测试文本，用于测试 <strong>ProofreadResultEditor</strong> 组件的下拉菜单自动收起功能。</p>
<p>请尝试使用工具栏中的各种下拉菜单，包括：</p>
<ul>
  <li>字体选择</li>
  <li>字号选择</li>
  <li>文字颜色</li>
  <li>背景颜色</li>
  <li>标题选择</li>
</ul>
<p>测试完成后，确保所有下拉菜单都能正确自动收起。</p>
`)

// 测试修改数据
const testChanges = ref<ProofreadingChange[]>([
  {
    id: 'test-change-1',
    type: 'replace',
    position: { start: 50, end: 60 },
    original: '测试文本',
    modified: '示例文本',
    reason: '用词更准确',
    timestamp: new Date(),
    confidence: 0.9,
  },
  {
    id: 'test-change-2',
    type: 'insert',
    position: { start: 100, end: 100 },
    original: '',
    modified: '新增内容',
    reason: '补充说明',
    timestamp: new Date(),
    confidence: 0.8,
  },
])

// 事件处理函数
const handleContentChange = (content: string) => {
  console.log('内容变化:', content)
}

const handleChangeAccept = (change: ProofreadingChange) => {
  console.log('接受修改:', change)
}

const handleChangeReject = (change: ProofreadingChange) => {
  console.log('拒绝修改:', change)
}

const handleSave = (content: string) => {
  console.log('保存内容:', content)
}
</script>

<style scoped>
.test-proofread-editor {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.test-header h2 {
  color: #409eff;
  margin-bottom: 10px;
}

.test-header ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-header li {
  margin: 5px 0;
}

.editor-container {
  margin: 20px 0;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.test-instructions {
  margin-top: 20px;
  padding: 20px;
  background: #fff7e6;
  border-radius: 8px;
  border-left: 4px solid #faad14;
}

.test-instructions h3 {
  color: #fa8c16;
  margin-bottom: 10px;
}

.test-instructions ol {
  margin: 10px 0;
  padding-left: 20px;
}

.test-instructions li {
  margin: 8px 0;
  line-height: 1.5;
}
</style>
