{"root": ["../../src/components/__tests__/helloworld.spec.ts", "../../src/features/ai-batch-proofreading/__tests__/unproofreaddocuments.test.ts", "../../src/features/ai-batch-proofreading/__tests__/batchproofreadingstore.test.ts", "../../src/features/ai-batch-proofreading/__tests__/e2e.test.ts", "../../src/features/ai-batch-proofreading/__tests__/integration.test.ts", "../../src/features/ai-batch-proofreading/__tests__/performance.test.ts", "../../src/features/ai-batch-proofreading/__tests__/persistenceplugin.test.ts", "../../src/features/ai-batch-proofreading/__tests__/statemanagement.integration.test.ts", "../../src/features/ai-batch-proofreading/__tests__/syncplugin.test.ts", "../../src/features/online-proofreading/__tests__/aiproofreadingsystem.test.ts", "../../src/features/online-proofreading/__tests__/basic.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/chunkmanager.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/configmanager.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/deepseekprovider.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/documentprocessor.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/exportmanager.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/mergealgorithm.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/moduleapi.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/reportgenerator.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/resultprocessor.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/sessionmanager.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/statisticsanalyzer.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/textprocessor.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/acceptance-validation.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/ai-service-acceptance.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/basic-acceptance.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/basic-performance.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/code-quality-validation.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/coverage-validation.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/document-processing-integration.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/export-statistics-integration.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/export-system-acceptance.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/export-system-simple-acceptance.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/final-comprehensive-test.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/integration-validation.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/integration.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/performance-optimization.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/performance-validation.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/performance.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/result-processing-integration.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/run-tests.ts", "../../src/modules/ai-proofreading-engine/__tests__/setup.ts", "../../src/modules/ai-proofreading-engine/__tests__/simple-test.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/template-edge-cases.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/template-management.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/template-performance.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/test-setup.ts", "../../src/modules/ai-proofreading-engine/__tests__/validation.test.ts", "../../src/modules/ai-proofreading-engine/__tests__/vitest.config.ts", "../../src/modules/ai-proofreading-engine/storage/__tests__/acceptancetest.test.ts", "../../src/modules/ai-proofreading-engine/storage/__tests__/batchapi.test.ts", "../../src/modules/ai-proofreading-engine/storage/__tests__/batchprocessingintegration.test.ts", "../../src/modules/ai-proofreading-engine/storage/__tests__/cachemanager.test.ts", "../../src/modules/ai-proofreading-engine/storage/__tests__/concurrencycontrol.test.ts", "../../src/modules/ai-proofreading-engine/storage/__tests__/filemanagementintegration.test.ts", "../../src/modules/ai-proofreading-engine/storage/__tests__/performancebenchmark.test.ts", "../../src/modules/ai-proofreading-engine/storage/__tests__/simpleacceptancetest.test.ts", "../../src/modules/ai-proofreading-engine/storage/__tests__/workspacemanager.test.ts", "../../src/utils/__tests__/advancedstorageintegration.test.ts", "../../src/utils/__tests__/performancebenchmark.test.ts", "../../env.d.ts"], "errors": true, "version": "5.8.3"}