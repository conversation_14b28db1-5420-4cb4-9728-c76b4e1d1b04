{
  // ========== 编辑器基础设置 ==========
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.trimAutoWhitespace": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  "editor.defaultFormatter": "esbenp.prettier-vscode",

  // ========== 文件和搜索设置 ==========
  "files.autoSave": "onFocusChange",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.eol": "\n",
  "files.associations": {
    "*.vue": "vue"
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/.vscode": false
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true,
    "**/coverage": true,
    "**/*.log": true
  },

  // ========== Vue 和 TypeScript 设置 ==========
  "vue.codeActions.enabled": true,
  "vue.complete.casing.tags": "pascal",
  "vue.complete.casing.props": "camel",
  "vue.updateImportsOnFileMove.enabled": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.suggest.autoImports": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.inlayHints.parameterNames.enabled": "all",
  "typescript.inlayHints.variableTypes.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,

  // ========== ESLint 设置 ==========
  "eslint.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  "eslint.workingDirectories": ["frontend"],
  "eslint.codeActionsOnSave.mode": "all",

  // ========== Prettier 设置 ==========
  "prettier.enable": true,
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,

  // ========== 特定文件类型设置 ==========
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
      "source.organizeImports": "explicit"
    }
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.wordWrap": "on",
    "editor.quickSuggestions": {
      "comments": "off",
      "strings": "off",
      "other": "off"
    }
  },

  // ========== 终端设置 ==========
  "terminal.integrated.defaultProfile.windows": "Git Bash",
  "terminal.integrated.cwd": "${workspaceFolder}/frontend",

  // ========== 调试设置 ==========
  "debug.inlineValues": "on",
  "debug.toolBarLocation": "floating",

  // ========== Git 设置 ==========
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,

  // ========== 扩展特定设置 ==========
  "emmet.includeLanguages": {
    "vue-html": "html",
    "vue": "html"
  },
  "emmet.triggerExpansionOnTab": true,

  // Path Intellisense 设置
  "path-intellisense.mappings": {
    "@": "${workspaceRoot}/frontend/src",
    "@components": "${workspaceRoot}/frontend/src/components",
    "@views": "${workspaceRoot}/frontend/src/views",
    "@stores": "${workspaceRoot}/frontend/src/stores",
    "@api": "${workspaceRoot}/frontend/src/api",
    "@utils": "${workspaceRoot}/frontend/src/utils",
    "@types": "${workspaceRoot}/frontend/src/types",
    "@assets": "${workspaceRoot}/frontend/src/assets",
    "@composables": "${workspaceRoot}/frontend/src/composables"
  },

  // ========== 性能优化设置 ==========
  "extensions.experimental.affinity": {
    "Vue.volar": 1,
    "Vue.vscode-typescript-vue-plugin": 1
  },

  // ========== 工作区特定设置 ==========
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.js",
    "*.vue": "${capture}.vue.d.ts",
    "package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml",
    "vite.config.ts": "vitest.config.ts",
    "tsconfig.json": "tsconfig.*.json",
    ".eslintrc.js": ".eslintignore,.prettierrc,.prettierignore"
  },

  // ========== 其他实用设置 ==========
  "breadcrumbs.enabled": true,
  "editor.minimap.enabled": true,
  "editor.minimap.maxColumn": 120,
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "editor.suggest.snippetsPreventQuickSuggestions": false,
  "editor.acceptSuggestionOnCommitCharacter": false
}
