import{M as e,x as a,p as l,T as t,R as n}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as u,r as s,b as i,m as r,c as o,a as d,Q as p,I as c,ag as v,o as g,u as m,M as f,J as _,aq as h,H as w,O as y,K as b}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as z}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const C={class:"image-pending-proofreading"},k={class:"content"},T={class:"card-header"},x={class:"header-actions"},V={class:"file-name"},B={class:"pagination-container"},j={key:0,class:"preview-content"},Y=["src"],M={class:"preview-info"},I=z(u({__name:"ImagePendingProofreadingView",setup(u){const z=s(!1),I=s([]),U=s([]),D=s(""),S=s(""),$=s([]),P=s(1),F=s(20),K=s(0),L=s(!1),O=s(null),q=i(()=>{let e=I.value;return D.value&&(e=e.filter(e=>e.name.toLowerCase().includes(D.value.toLowerCase()))),S.value&&(e=e.filter(e=>e.status===S.value)),$.value&&2===$.value.length&&(e=e.filter(e=>{const a=new Date(e.uploadTime).toISOString().split("T")[0];return a>=$.value[0]&&a<=$.value[1]})),e});r(()=>{E()});const E=async()=>{z.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),I.value=[{id:1,name:"document_scan_001.jpg",size:2048576,uploadTime:"2024-01-15 10:30:00",status:"pending",uploader:"张三",url:"/api/files/preview/1"},{id:2,name:"contract_page_01.png",size:1536e3,uploadTime:"2024-01-15 09:15:00",status:"processing",uploader:"李四",url:"/api/files/preview/2"},{id:3,name:"report_image_003.jpg",size:3072e3,uploadTime:"2024-01-14 16:45:00",status:"uploaded",uploader:"王五",url:"/api/files/preview/3"},{id:4,name:"meeting_notes_scan.png",size:256e4,uploadTime:"2024-01-14 14:20:00",status:"pending",uploader:"赵六",url:"/api/files/preview/4"},{id:5,name:"invoice_202401_001.jpg",size:1792e3,uploadTime:"2024-01-13 11:30:00",status:"pending",uploader:"钱七",url:"/api/files/preview/5"}],K.value=I.value.length}catch{e.error("加载文件列表失败")}finally{z.value=!1}},H=()=>{P.value=1},J=()=>{P.value=1},Q=()=>{E()},R=e=>{U.value=e},A=async()=>{if(0!==U.value.length)try{await n.confirm(`确定要批量处理选中的 ${U.value.length} 个文件吗？`,"批量处理确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.success("批量处理已开始，请稍后查看处理结果"),U.value.forEach(e=>{e.status="processing"}),U.value=[]}catch{}else e.warning("请选择要处理的文件")},G=async()=>{if(0!==U.value.length)try{await n.confirm(`确定要删除选中的 ${U.value.length} 个文件吗？此操作不可恢复。`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=U.value.map(e=>e.id);I.value=I.value.filter(e=>!a.includes(e.id)),K.value=I.value.length,e.success("删除成功"),U.value=[]}catch{}else e.warning("请选择要删除的文件")},N=e=>{F.value=e,P.value=1},W=e=>{P.value=e},X=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":(e/1048576).toFixed(1)+" MB",Z=e=>{switch(e){case"pending":return"warning";case"processing":return"primary";case"uploaded":return"success";default:return"info"}},ee=e=>{switch(e){case"pending":return"待处理";case"processing":return"处理中";case"uploaded":return"已上传";default:return"未知"}};return(u,s)=>{const i=v("el-icon"),r=v("el-input"),E=v("el-col"),ae=v("el-option"),le=v("el-select"),te=v("el-date-picker"),ne=v("el-button"),ue=v("el-row"),se=v("el-card"),ie=v("el-table-column"),re=v("el-tag"),oe=v("el-table"),de=v("el-pagination"),pe=v("el-dialog"),ce=h("loading");return g(),o("div",C,[s[16]||(s[16]=d("div",{class:"page-header"},[d("h1",null,"待审查图片"),d("p",{class:"page-description"},"显示等待审校的图片文件列表")],-1)),d("div",k,[p(se,{class:"filter-card"},{default:c(()=>[p(ue,{gutter:20},{default:c(()=>[p(E,{span:6},{default:c(()=>[p(r,{modelValue:D.value,"onUpdate:modelValue":s[0]||(s[0]=e=>D.value=e),placeholder:"搜索文件名...",clearable:"",onInput:H},{prefix:c(()=>[p(i,null,{default:c(()=>[p(m(a))]),_:1})]),_:1},8,["modelValue"])]),_:1}),p(E,{span:4},{default:c(()=>[p(le,{modelValue:S.value,"onUpdate:modelValue":s[1]||(s[1]=e=>S.value=e),placeholder:"状态筛选",clearable:"",onChange:J},{default:c(()=>[p(ae,{label:"全部",value:""}),p(ae,{label:"待处理",value:"pending"}),p(ae,{label:"处理中",value:"processing"}),p(ae,{label:"已上传",value:"uploaded"})]),_:1},8,["modelValue"])]),_:1}),p(E,{span:4},{default:c(()=>[p(te,{modelValue:$.value,"onUpdate:modelValue":s[2]||(s[2]=e=>$.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:J},null,8,["modelValue"])]),_:1}),p(E,{span:4},{default:c(()=>[p(ne,{type:"primary",onClick:Q},{default:c(()=>s[6]||(s[6]=[f("刷新列表")])),_:1,__:[6]})]),_:1})]),_:1})]),_:1}),p(se,{title:"待审查图片列表",class:"list-card"},{header:c(()=>[d("div",T,[s[8]||(s[8]=d("span",null,"待审查图片列表",-1)),d("div",x,[p(ne,{size:"small",onClick:A,disabled:0===U.value.length},{default:c(()=>[f(" 批量处理 ("+y(U.value.length)+") ",1)]),_:1},8,["disabled"]),p(ne,{size:"small",onClick:G,disabled:0===U.value.length},{default:c(()=>s[7]||(s[7]=[f(" 批量删除 ")])),_:1,__:[7]},8,["disabled"])])])]),default:c(()=>[_((g(),w(oe,{data:q.value,style:{width:"100%"},onSelectionChange:R},{default:c(()=>[p(ie,{type:"selection",width:"55"}),p(ie,{prop:"name",label:"文件名",width:"200"},{default:c(e=>[d("div",V,[p(i,null,{default:c(()=>[p(m(l))]),_:1}),d("span",null,y(e.row.name),1)])]),_:1}),p(ie,{prop:"size",label:"文件大小",width:"100"},{default:c(e=>[f(y(X(e.row.size)),1)]),_:1}),p(ie,{prop:"uploadTime",label:"上传时间",width:"160"}),p(ie,{prop:"status",label:"状态",width:"100"},{default:c(e=>[p(re,{type:Z(e.row.status)},{default:c(()=>[f(y(ee(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),p(ie,{prop:"uploader",label:"上传者",width:"120"}),p(ie,{label:"预览",width:"80"},{default:c(e=>[p(ne,{size:"small",onClick:a=>{return l=e.row,O.value=l,void(L.value=!0);var l}},{default:c(()=>[p(i,null,{default:c(()=>[p(m(t))]),_:1})]),_:2},1032,["onClick"])]),_:1}),p(ie,{label:"操作",width:"200"},{default:c(a=>[p(ne,{size:"small",type:"primary",onClick:l=>{return t=a.row,void e.success(`开始审校文件：${t.name}`);var t}},{default:c(()=>s[9]||(s[9]=[f(" 开始审校 ")])),_:2,__:[9]},1032,["onClick"]),p(ne,{size:"small",onClick:l=>{return t=a.row,void e.success(`开始下载文件：${t.name}`);var t}},{default:c(()=>s[10]||(s[10]=[f("下载")])),_:2,__:[10]},1032,["onClick"]),p(ne,{size:"small",type:"danger",onClick:l=>(async a=>{try{await n.confirm(`确定要删除文件 "${a.name}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=I.value.findIndex(e=>e.id===a.id);l>-1&&(I.value.splice(l,1),K.value=I.value.length,e.success("删除成功"))}catch{}})(a.row)},{default:c(()=>s[11]||(s[11]=[f("删除")])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ce,z.value]]),d("div",B,[p(de,{"current-page":P.value,"onUpdate:currentPage":s[3]||(s[3]=e=>P.value=e),"page-size":F.value,"onUpdate:pageSize":s[4]||(s[4]=e=>F.value=e),"page-sizes":[10,20,50,100],total:K.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:N,onCurrentChange:W},null,8,["current-page","page-size","total"])])]),_:1})]),p(pe,{modelValue:L.value,"onUpdate:modelValue":s[5]||(s[5]=e=>L.value=e),title:"图片预览",width:"60%"},{default:c(()=>[O.value?(g(),o("div",j,[d("img",{src:O.value.url,alt:"预览图片",class:"preview-image"},null,8,Y),d("div",M,[d("p",null,[s[12]||(s[12]=d("strong",null,"文件名：",-1)),f(y(O.value.name),1)]),d("p",null,[s[13]||(s[13]=d("strong",null,"文件大小：",-1)),f(y(X(O.value.size)),1)]),d("p",null,[s[14]||(s[14]=d("strong",null,"上传时间：",-1)),f(y(O.value.uploadTime),1)]),d("p",null,[s[15]||(s[15]=d("strong",null,"上传者：",-1)),f(y(O.value.uploader),1)])])])):b("",!0)]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-b458404e"]]);export{I as default};
