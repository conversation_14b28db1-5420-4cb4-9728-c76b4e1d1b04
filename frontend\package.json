{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "lint:check": "eslint . --max-warnings 0", "format:check": "prettier --check src/", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "clean": "rimraf dist node_modules/.vite", "verify-toolchain": "npm run lint:check && npm run format:check && npm run type-check && npm run test", "dev:mock": "cross-env VITE_ENABLE_MOCK=true vite", "build:dev": "cross-env NODE_ENV=development vite build", "build:prod": "cross-env NODE_ENV=production vite build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.11.1", "@wangeditor/core": "^1.1.19", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.10.0", "element-plus": "^2.10.2", "fabric": "^5.5.2", "iconv-lite": "^0.6.3", "mammoth": "^1.9.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.3.31", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.3", "pinyin-pro": "^3.26.0", "socket.io-client": "^4.8.1", "video.js": "^8.23.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "wavesurfer.js": "^7.9.8"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@playwright/test": "^1.53.1", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-vue": "^5.1.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "eslint": "^9.29.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "~10.2.0", "fake-indexeddb": "^6.0.1", "husky": "^8.0.3", "jiti": "^2.4.2", "jsdom": "^26.1.0", "lint-staged": "^15.5.2", "msw": "^2.10.2", "npm-run-all2": "^6.2.0", "prettier": "3.5.3", "terser": "^5.43.1", "typescript": "~5.8.0", "vite": "^5.4.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}, "msw": {"workerDirectory": "public"}}