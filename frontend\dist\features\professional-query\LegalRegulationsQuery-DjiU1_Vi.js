import{d as a,c as s,a as e,Q as l,I as n,ag as r,o as t}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as u}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const o={class:"legal-regulations-query"},c={class:"content"},p=u(a({__name:"LegalRegulationsQuery",setup:a=>(a,u)=>{const p=r("el-card");return t(),s("div",o,[u[1]||(u[1]=e("div",{class:"page-header"},[e("h1",null,"法律法规查询"),e("p",{class:"page-description"},"查询法律条文和法规文件")],-1)),e("div",c,[l(p,null,{default:n(()=>u[0]||(u[0]=[e("p",null,"法律法规查询功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-f546f6e0"]]);export{p as default};
