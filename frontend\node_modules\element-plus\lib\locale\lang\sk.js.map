{"version": 3, "file": "sk.js", "sources": ["../../../../../packages/locale/lang/sk.ts"], "sourcesContent": ["export default {\n  name: 'sk',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON><PERSON>ť dátum',\n      selectTime: 'V<PERSON>brať čas',\n      startDate: '<PERSON><PERSON><PERSON> začiatku',\n      startTime: '<PERSON>as začiatku',\n      endDate: 'D<PERSON>tum konca',\n      endTime: 'Čas konca',\n      prevYear: 'Predošlý rok',\n      nextYear: 'Ďalší rok',\n      prevMonth: 'Predošlý mesiac',\n      nextMonth: 'Ďalší mesiac',\n      day: 'Deň',\n      week: 'Týždeň',\n      month: 'Mesiac',\n      year: 'Rok',\n      month1: 'Január',\n      month2: 'Február',\n      month3: '<PERSON><PERSON>',\n      month4: 'Apríl',\n      month5: '<PERSON>á<PERSON>',\n      month6: 'J<PERSON>',\n      month7: 'J<PERSON>',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Október',\n      month11: 'November',\n      month12: 'December',\n      weeks: {\n        sun: 'Ne',\n        mon: 'Po',\n        tue: 'Ut',\n        wed: 'St',\n        thu: 'Št',\n        fri: 'Pi',\n        sat: 'So',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Máj',\n        jun: 'Jún',\n        jul: 'Júl',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Načítavanie',\n      noMatch: 'Žiadna zhoda',\n      noData: 'Žiadne dáta',\n      placeholder: 'Vybrať',\n    },\n    mention: {\n      loading: 'Načítavanie',\n    },\n    cascader: {\n      noMatch: 'Žiadna zhoda',\n      loading: 'Načítavanie',\n      placeholder: 'Vybrať',\n      noData: 'Žiadne dáta',\n    },\n    pagination: {\n      goto: 'Choď na',\n      pagesize: 'na stranu',\n      total: 'Všetko {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Správa',\n      confirm: 'OK',\n      cancel: 'Zrušiť',\n      error: 'Neplatný vstup',\n    },\n    upload: {\n      deleteTip: 'pre odstránenie stisni klávesu Delete',\n      delete: 'Vymazať',\n      preview: 'Prehliadať',\n      continue: 'Pokračovať',\n    },\n    table: {\n      emptyText: 'Žiadne dáta',\n      confirmFilter: 'Potvrdiť',\n      resetFilter: 'Zresetovať',\n      clearFilter: 'Všetko',\n      sumText: 'Spolu',\n    },\n    tree: {\n      emptyText: 'Žiadne dáta',\n    },\n    transfer: {\n      noMatch: 'Žiadna zhoda',\n      noData: 'Žiadne dáta',\n      titles: ['Zoznam 1', 'Zoznam 2'],\n      filterPlaceholder: 'Filtrovať podľa',\n      noCheckedFormat: '{total} položiek',\n      hasCheckedFormat: '{checked}/{total} označených',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,aAAa;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,MAAM,EAAE,kBAAkB;AAChC,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,QAAQ,EAAE,sBAAsB;AACtC,MAAM,QAAQ,EAAE,wBAAwB;AACxC,MAAM,SAAS,EAAE,yBAAyB;AAC1C,MAAM,SAAS,EAAE,2BAA2B;AAC5C,MAAM,GAAG,EAAE,UAAU;AACrB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,MAAM,EAAE,qBAAqB;AACnC,MAAM,WAAW,EAAE,aAAa;AAChC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,qBAAqB;AACpC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,MAAM,EAAE,qBAAqB;AACnC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,KAAK,EAAE,qBAAqB;AAClC,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,kBAAkB;AAChC,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,6CAA6C;AAC9D,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,QAAQ,EAAE,sBAAsB;AACtC,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,aAAa,EAAE,eAAe;AACpC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,qBAAqB;AACtC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,MAAM,EAAE,qBAAqB;AACnC,MAAM,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;AACtC,MAAM,iBAAiB,EAAE,2BAA2B;AACpD,MAAM,eAAe,EAAE,uBAAuB;AAC9C,MAAM,gBAAgB,EAAE,sCAAsC;AAC9D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}