{"version": 3, "sources": ["../../pinia-plugin-persistedstate/dist/index.js"], "sourcesContent": ["// src/normalize.ts\r\nfunction isObject(v) {\r\n  return typeof v === \"object\" && v !== null;\r\n}\r\nfunction normalizeOptions(options, factoryOptions) {\r\n  options = isObject(options) ? options : /* @__PURE__ */ Object.create(null);\r\n  return new Proxy(options, {\r\n    get(target, key, receiver) {\r\n      if (key === \"key\")\r\n        return Reflect.get(target, key, receiver);\r\n      return Reflect.get(target, key, receiver) || Reflect.get(factoryOptions, key, receiver);\r\n    }\r\n  });\r\n}\r\n\r\n// src/pick.ts\r\nfunction get(state, path) {\r\n  return path.reduce((obj, p) => {\r\n    return obj == null ? void 0 : obj[p];\r\n  }, state);\r\n}\r\nfunction set(state, path, val) {\r\n  return path.slice(0, -1).reduce((obj, p) => {\r\n    if (/^(__proto__)$/.test(p))\r\n      return {};\r\n    else return obj[p] = obj[p] || {};\r\n  }, state)[path[path.length - 1]] = val, state;\r\n}\r\nfunction pick(baseState, paths) {\r\n  return paths.reduce((substate, path) => {\r\n    const pathArray = path.split(\".\");\r\n    return set(substate, pathArray, get(baseState, pathArray));\r\n  }, {});\r\n}\r\n\r\n// src/plugin.ts\r\nfunction parsePersistence(factoryOptions, store) {\r\n  return (o) => {\r\n    var _a;\r\n    try {\r\n      const {\r\n        storage = localStorage,\r\n        beforeRestore = void 0,\r\n        afterRestore = void 0,\r\n        serializer = {\r\n          serialize: JSON.stringify,\r\n          deserialize: JSON.parse\r\n        },\r\n        key = store.$id,\r\n        paths = null,\r\n        debug = false\r\n      } = o;\r\n      return {\r\n        storage,\r\n        beforeRestore,\r\n        afterRestore,\r\n        serializer,\r\n        key: ((_a = factoryOptions.key) != null ? _a : (k) => k)(typeof key == \"string\" ? key : key(store.$id)),\r\n        paths,\r\n        debug\r\n      };\r\n    } catch (e) {\r\n      if (o.debug)\r\n        console.error(\"[pinia-plugin-persistedstate]\", e);\r\n      return null;\r\n    }\r\n  };\r\n}\r\nfunction hydrateStore(store, { storage, serializer, key, debug }) {\r\n  try {\r\n    const fromStorage = storage == null ? void 0 : storage.getItem(key);\r\n    if (fromStorage)\r\n      store.$patch(serializer == null ? void 0 : serializer.deserialize(fromStorage));\r\n  } catch (e) {\r\n    if (debug)\r\n      console.error(\"[pinia-plugin-persistedstate]\", e);\r\n  }\r\n}\r\nfunction persistState(state, { storage, serializer, key, paths, debug }) {\r\n  try {\r\n    const toStore = Array.isArray(paths) ? pick(state, paths) : state;\r\n    storage.setItem(key, serializer.serialize(toStore));\r\n  } catch (e) {\r\n    if (debug)\r\n      console.error(\"[pinia-plugin-persistedstate]\", e);\r\n  }\r\n}\r\nfunction createPersistedState(factoryOptions = {}) {\r\n  return (context) => {\r\n    const { auto = false } = factoryOptions;\r\n    const {\r\n      options: { persist = auto },\r\n      store,\r\n      pinia\r\n    } = context;\r\n    if (!persist)\r\n      return;\r\n    if (!(store.$id in pinia.state.value)) {\r\n      const original_store = pinia._s.get(store.$id.replace(\"__hot:\", \"\"));\r\n      if (original_store)\r\n        Promise.resolve().then(() => original_store.$persist());\r\n      return;\r\n    }\r\n    const persistences = (Array.isArray(persist) ? persist.map((p) => normalizeOptions(p, factoryOptions)) : [normalizeOptions(persist, factoryOptions)]).map(parsePersistence(factoryOptions, store)).filter(Boolean);\r\n    store.$persist = () => {\r\n      persistences.forEach((persistence) => {\r\n        persistState(store.$state, persistence);\r\n      });\r\n    };\r\n    store.$hydrate = ({ runHooks = true } = {}) => {\r\n      persistences.forEach((persistence) => {\r\n        const { beforeRestore, afterRestore } = persistence;\r\n        if (runHooks)\r\n          beforeRestore == null ? void 0 : beforeRestore(context);\r\n        hydrateStore(store, persistence);\r\n        if (runHooks)\r\n          afterRestore == null ? void 0 : afterRestore(context);\r\n      });\r\n    };\r\n    persistences.forEach((persistence) => {\r\n      const { beforeRestore, afterRestore } = persistence;\r\n      beforeRestore == null ? void 0 : beforeRestore(context);\r\n      hydrateStore(store, persistence);\r\n      afterRestore == null ? void 0 : afterRestore(context);\r\n      store.$subscribe(\r\n        (_mutation, state) => {\r\n          persistState(state, persistence);\r\n        },\r\n        {\r\n          detached: true\r\n        }\r\n      );\r\n    });\r\n  };\r\n}\r\n\r\n// src/index.ts\r\nvar src_default = createPersistedState();\r\nexport {\r\n  createPersistedState,\r\n  src_default as default\r\n};\r\n"], "mappings": ";;;AACA,SAAS,SAAS,GAAG;AACnB,SAAO,OAAO,MAAM,YAAY,MAAM;AACxC;AACA,SAAS,iBAAiB,SAAS,gBAAgB;AACjD,YAAU,SAAS,OAAO,IAAI,UAA0B,uBAAO,OAAO,IAAI;AAC1E,SAAO,IAAI,MAAM,SAAS;AAAA,IACxB,IAAI,QAAQ,KAAK,UAAU;AACzB,UAAI,QAAQ;AACV,eAAO,QAAQ,IAAI,QAAQ,KAAK,QAAQ;AAC1C,aAAO,QAAQ,IAAI,QAAQ,KAAK,QAAQ,KAAK,QAAQ,IAAI,gBAAgB,KAAK,QAAQ;AAAA,IACxF;AAAA,EACF,CAAC;AACH;AAGA,SAAS,IAAI,OAAO,MAAM;AACxB,SAAO,KAAK,OAAO,CAAC,KAAK,MAAM;AAC7B,WAAO,OAAO,OAAO,SAAS,IAAI,CAAC;AAAA,EACrC,GAAG,KAAK;AACV;AACA,SAAS,IAAI,OAAO,MAAM,KAAK;AAC7B,SAAO,KAAK,MAAM,GAAG,EAAE,EAAE,OAAO,CAAC,KAAK,MAAM;AAC1C,QAAI,gBAAgB,KAAK,CAAC;AACxB,aAAO,CAAC;AAAA,QACL,QAAO,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC;AAAA,EAClC,GAAG,KAAK,EAAE,KAAK,KAAK,SAAS,CAAC,CAAC,IAAI,KAAK;AAC1C;AACA,SAAS,KAAK,WAAW,OAAO;AAC9B,SAAO,MAAM,OAAO,CAAC,UAAU,SAAS;AACtC,UAAM,YAAY,KAAK,MAAM,GAAG;AAChC,WAAO,IAAI,UAAU,WAAW,IAAI,WAAW,SAAS,CAAC;AAAA,EAC3D,GAAG,CAAC,CAAC;AACP;AAGA,SAAS,iBAAiB,gBAAgB,OAAO;AAC/C,SAAO,CAAC,MAAM;AACZ,QAAI;AACJ,QAAI;AACF,YAAM;AAAA,QACJ,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,aAAa;AAAA,UACX,WAAW,KAAK;AAAA,UAChB,aAAa,KAAK;AAAA,QACpB;AAAA,QACA,MAAM,MAAM;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,IAAI;AACJ,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO,KAAK,eAAe,QAAQ,OAAO,KAAK,CAAC,MAAM,GAAG,OAAO,OAAO,WAAW,MAAM,IAAI,MAAM,GAAG,CAAC;AAAA,QACtG;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACV,UAAI,EAAE;AACJ,gBAAQ,MAAM,iCAAiC,CAAC;AAClD,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,aAAa,OAAO,EAAE,SAAS,YAAY,KAAK,MAAM,GAAG;AAChE,MAAI;AACF,UAAM,cAAc,WAAW,OAAO,SAAS,QAAQ,QAAQ,GAAG;AAClE,QAAI;AACF,YAAM,OAAO,cAAc,OAAO,SAAS,WAAW,YAAY,WAAW,CAAC;AAAA,EAClF,SAAS,GAAG;AACV,QAAI;AACF,cAAQ,MAAM,iCAAiC,CAAC;AAAA,EACpD;AACF;AACA,SAAS,aAAa,OAAO,EAAE,SAAS,YAAY,KAAK,OAAO,MAAM,GAAG;AACvE,MAAI;AACF,UAAM,UAAU,MAAM,QAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAC5D,YAAQ,QAAQ,KAAK,WAAW,UAAU,OAAO,CAAC;AAAA,EACpD,SAAS,GAAG;AACV,QAAI;AACF,cAAQ,MAAM,iCAAiC,CAAC;AAAA,EACpD;AACF;AACA,SAAS,qBAAqB,iBAAiB,CAAC,GAAG;AACjD,SAAO,CAAC,YAAY;AAClB,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,UAAM;AAAA,MACJ,SAAS,EAAE,UAAU,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC;AACH;AACF,QAAI,EAAE,MAAM,OAAO,MAAM,MAAM,QAAQ;AACrC,YAAM,iBAAiB,MAAM,GAAG,IAAI,MAAM,IAAI,QAAQ,UAAU,EAAE,CAAC;AACnE,UAAI;AACF,gBAAQ,QAAQ,EAAE,KAAK,MAAM,eAAe,SAAS,CAAC;AACxD;AAAA,IACF;AACA,UAAM,gBAAgB,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAI,CAAC,MAAM,iBAAiB,GAAG,cAAc,CAAC,IAAI,CAAC,iBAAiB,SAAS,cAAc,CAAC,GAAG,IAAI,iBAAiB,gBAAgB,KAAK,CAAC,EAAE,OAAO,OAAO;AACjN,UAAM,WAAW,MAAM;AACrB,mBAAa,QAAQ,CAAC,gBAAgB;AACpC,qBAAa,MAAM,QAAQ,WAAW;AAAA,MACxC,CAAC;AAAA,IACH;AACA,UAAM,WAAW,CAAC,EAAE,WAAW,KAAK,IAAI,CAAC,MAAM;AAC7C,mBAAa,QAAQ,CAAC,gBAAgB;AACpC,cAAM,EAAE,eAAe,aAAa,IAAI;AACxC,YAAI;AACF,2BAAiB,OAAO,SAAS,cAAc,OAAO;AACxD,qBAAa,OAAO,WAAW;AAC/B,YAAI;AACF,0BAAgB,OAAO,SAAS,aAAa,OAAO;AAAA,MACxD,CAAC;AAAA,IACH;AACA,iBAAa,QAAQ,CAAC,gBAAgB;AACpC,YAAM,EAAE,eAAe,aAAa,IAAI;AACxC,uBAAiB,OAAO,SAAS,cAAc,OAAO;AACtD,mBAAa,OAAO,WAAW;AAC/B,sBAAgB,OAAO,SAAS,aAAa,OAAO;AACpD,YAAM;AAAA,QACJ,CAAC,WAAW,UAAU;AACpB,uBAAa,OAAO,WAAW;AAAA,QACjC;AAAA,QACA;AAAA,UACE,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAGA,IAAI,cAAc,qBAAqB;", "names": []}