import{d as s,c as a,a as e,Q as r,I as d,ag as n,o as t}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as l}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const u={class:"standards-query"},c={class:"content"},o=l(s({__name:"StandardsQuery",setup:s=>(s,l)=>{const o=n("el-card");return t(),a("div",u,[l[1]||(l[1]=e("div",{class:"page-header"},[e("h1",null,"标准查询"),e("p",{class:"page-description"},"查询行业标准和规范文档")],-1)),e("div",c,[r(o,null,{default:d(()=>l[0]||(l[0]=[e("p",null,"标准查询功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-215be417"]]);export{o as default};
