import{a as e}from"./index-DU7Wk3Qr.js";class t{static async getPendingDocuments(t){return await e.get("/api/pre-review/pending",{params:t})}static async createDocument(t){return await e.post("/api/pre-review/documents",t)}static async getDocumentDetail(t){return await e.get(`/api/pre-review/documents/${t}`)}static async updateDocument(t,a){return await e.put(`/api/pre-review/documents/${t}`,a)}static async deleteDocument(t){await e.delete(`/api/pre-review/documents/${t}`)}static async batchApprove(t){return await e.post("/api/pre-review/batch-approve",t)}static async batchReject(t){return await e.post("/api/pre-review/batch-reject",t)}static async aiProofreading(t){return await e.post("/api/pre-review/ai-proofreading",{documentId:t})}static async getReviewedDocuments(t){return await e.get("/api/pre-review/reviewed",{params:t})}static async batchReReview(t){return await e.post("/api/pre-review/batch-re-review",t)}static async exportReport(t,a="markdown"){return await e.post("/api/pre-review/export-report",{documentId:t,format:a})}static async batchExportReports(t,a="markdown"){return await e.post("/api/pre-review/batch-export-reports",{documentIds:t,format:a})}static async getStatistics(){return await e.get("/api/pre-review/statistics")}}export{t as P};
