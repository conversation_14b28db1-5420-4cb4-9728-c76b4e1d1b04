import{d as s,c as a,a as e,Q as r,I as t,ag as l,o as n}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as o}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const u={class:"other-query"},c={class:"content"},p=o(s({__name:"OtherQuery",setup:s=>(s,o)=>{const p=l("el-card");return n(),a("div",u,[o[1]||(o[1]=e("div",{class:"page-header"},[e("h1",null,"其他查询"),e("p",{class:"page-description"},"自定义查询和扩展功能")],-1)),e("div",c,[r(p,null,{default:t(()=>o[0]||(o[0]=[e("p",null,"其他查询功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-40897986"]]);export{p as default};
