import{d as s,c as e,a,Q as t,I as p,ag as r,o as n}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as c}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const l={class:"important-speeches-query"},o={class:"content"},u=c(s({__name:"ImportantSpeechesQuery",setup:s=>(s,c)=>{const u=r("el-card");return n(),e("div",l,[c[1]||(c[1]=a("div",{class:"page-header"},[a("h1",null,"重要讲话查询"),a("p",{class:"page-description"},"查询重要讲话和政策解读")],-1)),a("div",o,[t(u,null,{default:p(()=>c[0]||(c[0]=[a("p",null,"重要讲话查询功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-8a73d733"]]);export{u as default};
