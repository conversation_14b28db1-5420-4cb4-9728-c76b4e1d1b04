import{x as e,a7 as a,d as l,M as t,R as s}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as o,r as n,b as u,m as i,c as d,a as r,Q as p,I as c,ag as m,o as f,u as _,M as v,O as g}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as h}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const y={class:"unformatted-documents"},w={class:"toolbar"},C={class:"document-list"},b={class:"document-name"},z={class:"pagination"},k=h(o({__name:"UnformattedDocuments",setup(o){const h=n(""),k=n([]),x=n(1),j=n(20),L=n(0),T=n([{id:1,name:"政府工作报告2024.docx",type:"Word文档",size:"2.5MB",uploadTime:"2024-01-15 10:30:00",uploader:"张三"},{id:2,name:"企业年度总结.pdf",type:"PDF文档",size:"1.8MB",uploadTime:"2024-01-14 16:45:00",uploader:"李四"},{id:3,name:"技术规范文档.txt",type:"文本文档",size:"0.5MB",uploadTime:"2024-01-13 09:20:00",uploader:"王五"}]),B=u(()=>h.value?T.value.filter(e=>e.name.toLowerCase().includes(h.value.toLowerCase())||e.type.toLowerCase().includes(h.value.toLowerCase())||e.uploader.toLowerCase().includes(h.value.toLowerCase())):T.value),M=e=>{k.value=e},U=()=>{t.info("上传文档功能开发中...")},$=()=>{t.info(`批量排版 ${k.value.length} 个文档...`)},D=e=>{j.value=e},P=e=>{x.value=e};return i(()=>{L.value=T.value.length}),(o,n)=>{const u=m("el-icon"),i=m("el-input"),T=m("el-col"),S=m("el-button"),V=m("el-row"),F=m("el-table-column"),I=m("el-tag"),W=m("el-table"),O=m("el-pagination");return f(),d("div",y,[n[8]||(n[8]=r("div",{class:"page-header"},[r("h1",null,"未排版文档"),r("p",{class:"page-description"},"管理和处理需要进行专业排版的文档")],-1)),r("div",w,[p(V,{gutter:16,justify:"space-between"},{default:c(()=>[p(T,{span:12},{default:c(()=>[p(i,{modelValue:h.value,"onUpdate:modelValue":n[0]||(n[0]=e=>h.value=e),placeholder:"搜索文档名称、类型或上传者",clearable:"",class:"search-input"},{prefix:c(()=>[p(u,null,{default:c(()=>[p(_(e))]),_:1})]),_:1},8,["modelValue"])]),_:1}),p(T,{span:12,class:"text-right"},{default:c(()=>[p(S,{type:"primary",onClick:U},{default:c(()=>[p(u,null,{default:c(()=>[p(_(a))]),_:1}),n[3]||(n[3]=v(" 上传文档 "))]),_:1,__:[3]}),p(S,{onClick:$,disabled:0===k.value.length},{default:c(()=>[p(u,null,{default:c(()=>[p(_(l))]),_:1}),n[4]||(n[4]=v(" 批量排版 "))]),_:1,__:[4]},8,["disabled"])]),_:1})]),_:1})]),r("div",C,[p(W,{data:B.value,onSelectionChange:M,stripe:"",style:{width:"100%"}},{default:c(()=>[p(F,{type:"selection",width:"55"}),p(F,{prop:"name",label:"文档名称","min-width":"200"},{default:c(({row:e})=>[r("div",b,[p(u,{class:"file-icon"},{default:c(()=>[p(_(l))]),_:1}),r("span",null,g(e.name),1)])]),_:1}),p(F,{prop:"type",label:"文档类型",width:"120"},{default:c(({row:e})=>{return[p(I,{type:(a=e.type,{"Word文档":"primary","PDF文档":"success","文本文档":"info"}[a]||"default")},{default:c(()=>[v(g(e.type),1)]),_:2},1032,["type"])];var a}),_:1}),p(F,{prop:"size",label:"文件大小",width:"100"}),p(F,{prop:"uploadTime",label:"上传时间",width:"180"}),p(F,{prop:"uploader",label:"上传者",width:"120"}),p(F,{label:"操作",width:"200",fixed:"right"},{default:c(({row:e})=>[p(S,{size:"small",onClick:a=>(e=>{t.info(`预览文档: ${e.name}`)})(e)},{default:c(()=>n[5]||(n[5]=[v("预览")])),_:2,__:[5]},1032,["onClick"]),p(S,{size:"small",type:"primary",onClick:a=>(e=>{t.info(`开始排版文档: ${e.name}`)})(e)},{default:c(()=>n[6]||(n[6]=[v("排版")])),_:2,__:[6]},1032,["onClick"]),p(S,{size:"small",type:"danger",onClick:a=>(async e=>{try{await s.confirm(`确定要删除文档 "${e.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),t.success("删除成功")}catch{t.info("已取消删除")}})(e)},{default:c(()=>n[7]||(n[7]=[v("删除")])),_:2,__:[7]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),r("div",z,[p(O,{"current-page":x.value,"onUpdate:currentPage":n[1]||(n[1]=e=>x.value=e),"page-size":j.value,"onUpdate:pageSize":n[2]||(n[2]=e=>j.value=e),"page-sizes":[10,20,50,100],total:L.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:D,onCurrentChange:P},null,8,["current-page","page-size","total"])])])}}}),[["__scopeId","data-v-5578fb33"]]);export{k as default};
