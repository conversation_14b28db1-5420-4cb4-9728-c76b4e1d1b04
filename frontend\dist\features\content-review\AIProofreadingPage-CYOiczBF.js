import{m as e,S as a,d as t,r as l,X as s,b as n,H as o,ag as i,I as r,o as u,a as c,Q as d,c as p,K as v,O as m,M as g,P as h,a6 as f,p as y,u as _,aC as b,aA as w,aB as k}from"../../chunks/vue-vendor-BCsylZgc.js";import{M as C,W as x,R as T,X as V,x as P}from"../../chunks/ui-vendor-DZ6owSRu.js";import{_ as B}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import{P as D}from"../../chunks/preReview-DUK05fM-.js";import{M as E}from"../../chunks/ModuleAPI-p-N7PV56.js";import{M as R}from"../../chunks/ai-engine-storage-DTARYHrG.js";import"../../chunks/utils-vendor-DYQz1-BF.js";import"../../chunks/index-DU7Wk3Qr.js";import"../../chunks/ai-engine-core-wyUSRaHZ.js";const A={showDetails:!1,autoRetry:!1,maxRetries:3,logErrors:!0};const $=new class{config;errorLog=[];constructor(e={}){this.config={...A,...e}}handleError(e,a){const t=this.normalizeError(e,a);return this.config.logErrors&&this.logError(t),this.showErrorMessage(t),t}normalizeError(e,a){return"string"==typeof e?{type:"system",message:e,timestamp:Date.now(),context:a}:e instanceof Error?{type:this.detectErrorType(e),message:e.message,details:e.stack,timestamp:Date.now(),context:a}:{...e,timestamp:e.timestamp||Date.now(),context:{...e.context,...a}}}detectErrorType(e){const a=e.message.toLowerCase();return a.includes("network")||a.includes("fetch")?"network":a.includes("unauthorized")||a.includes("auth")?"auth":a.includes("validation")||a.includes("invalid")?"validation":a.includes("ai")||a.includes("model")||a.includes("proofreading")?"ai_service":a.includes("file")||a.includes("document")||a.includes("convert")?"file_processing":"system"}showErrorMessage(e){const a=this.getUserFriendlyMessage(e);switch(e.type){case"network":case"file_processing":C.error({message:a,duration:5e3,showClose:!0});break;case"auth":T.alert(a,"认证失败",{confirmButtonText:"重新登录",type:"error"}).then(()=>{window.location.href="/login"});break;case"validation":C.warning({message:a,duration:3e3});break;case"ai_service":x.error({title:"AI服务异常",message:a,duration:8e3,position:"top-right"});break;default:C.error({message:a,duration:4e3})}}getUserFriendlyMessage(e){const a={network:"网络连接异常，请检查网络设置后重试",auth:"登录状态已过期，请重新登录",business:"操作失败，请稍后重试",system:"系统异常，请联系管理员",validation:"输入信息有误，请检查后重新提交",ai_service:"AI服务暂时不可用，请稍后重试",file_processing:"文件处理失败，请检查文件格式是否正确"}[e.type]||"操作失败";return this.config.showDetails&&e.message?`${a}：${e.message}`:a}logError(e){this.errorLog.push(e),this.errorLog.length>100&&(this.errorLog=this.errorLog.slice(-50))}async showConfirmDialog(e,a="确认操作",t={}){try{return await T.confirm(e,a,{confirmButtonText:t.confirmButtonText||"确定",cancelButtonText:t.cancelButtonText||"取消",type:t.type||"info"}),!0}catch{return!1}}showSuccess(e,a=3e3){C.success({message:e,duration:a})}showWarning(e,a=4e3){C.warning({message:e,duration:a})}showInfo(e,a=3e3){C.info({message:e,duration:a})}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}updateConfig(e){this.config={...this.config,...e}}};class S{shortcuts=new Map;isEnabled=!0;register(e){const a=this.generateKey(e);this.shortcuts.set(a,e)}unregister(e){const a=this.generateKey(e);this.shortcuts.delete(a)}handleKeyDown=e=>{if(!this.isEnabled)return;const a=this.generateKeyFromEvent(e),t=this.shortcuts.get(a);t&&(t.preventDefault&&e.preventDefault(),t.stopPropagation&&e.stopPropagation(),t.handler())};enable(){this.isEnabled=!0}disable(){this.isEnabled=!1}getShortcuts(){return Array.from(this.shortcuts.values())}generateKey(e){const a=[];return e.ctrl&&a.push("ctrl"),e.alt&&a.push("alt"),e.shift&&a.push("shift"),e.meta&&a.push("meta"),a.push(e.key.toLowerCase()),a.join("+")}generateKeyFromEvent(e){const a=[];return e.ctrlKey&&a.push("ctrl"),e.altKey&&a.push("alt"),e.shiftKey&&a.push("shift"),e.metaKey&&a.push("meta"),a.push(e.key.toLowerCase()),a.join("+")}}const U={class:"panel-header"},I={class:"document-info"},z={class:"info-section"},M={class:"info-grid"},L={class:"info-item"},O={class:"info-value"},q={class:"info-item"},F={class:"info-item"},N={class:"info-value"},W={class:"info-item"},j={class:"info-item"},K={class:"info-value"},G={class:"info-item"},Y={class:"info-value"},H={key:0,class:"info-section"},J={class:"info-grid"},X={class:"info-item"},Q={class:"info-value"},Z={class:"info-item"},ee={class:"info-value"},ae={key:0,class:"info-item full-width"},te={class:"info-value bio-text"},le={key:1,class:"info-section"},se={class:"tags-container"},ne={key:2,class:"info-section"},oe={class:"content-preview"},ie={class:"preview-content"},re={key:3,class:"action-section"},ue={class:"full-content"},ce=B(t({__name:"DocumentInfoPanel",props:{document:{}},emits:["update"],setup(e,{emit:a}){const t=e,y=a,_=l(!1),b=l(!1),w=l(!1),k=l(),x=s({title:"",category:"",tags:[],content:""}),T={title:[{required:!0,message:"请输入文档标题",trigger:"blur"},{min:1,max:100,message:"标题长度应在1-100个字符之间",trigger:"blur"}],category:[{required:!0,message:"请选择文档分类",trigger:"change"}],content:[{max:500,message:"内容简介不能超过500个字符",trigger:"blur"}]},V=[{label:"学术论文",value:"学术论文"},{label:"技术文档",value:"技术文档"},{label:"商业报告",value:"商业报告"},{label:"新闻稿件",value:"新闻稿件"},{label:"法律文件",value:"法律文件"},{label:"其他",value:"其他"}],P=["重要","紧急","草稿","待审核","已完成","技术","商业","学术","法律","新闻"],B=n(()=>{const e=t.document.content;return e.length>200?e.substring(0,200)+"...":e}),D=()=>{_.value=!_.value},E=e=>({text:"文本文档",docx:"Word文档",pdf:"PDF文档",wps:"WPS文档"}[e]||e.toUpperCase()),R=e=>({draft:"草稿",pending:"待审核",reviewing:"审核中",approved:"已通过",rejected:"已拒绝",published:"已发布"}[e]||e),A=()=>{w.value=!0},$=()=>{x.title=t.document.title,x.category=t.document.category,x.tags=[...t.document.tags],x.content=t.document.content,b.value=!0},S=()=>{if(t.document.fileUrl){const e=document.createElement("a");e.href=t.document.fileUrl,e.download=t.document.title,document.body.appendChild(e),e.click(),document.body.removeChild(e),C.success("文档下载已开始")}else C.warning("文档文件不存在")},ce=()=>{b.value=!1,k.value?.resetFields()},de=async()=>{try{await(k.value?.validate());const e={...t.document,title:x.title,category:x.category,tags:x.tags,content:x.content,updatedAt:(new Date).toISOString()};y("update",e),C.success("文档信息更新成功"),ce()}catch(e){C.error("保存失败，请检查输入信息")}};return(e,a)=>{const t=i("el-button"),l=i("el-tag"),s=i("el-scrollbar"),n=i("el-input"),y=i("el-form-item"),C=i("el-option"),pe=i("el-select"),ve=i("el-form"),me=i("el-dialog"),ge=i("el-card");return u(),o(ge,{class:"document-info-panel"},{header:r(()=>[c("div",U,[a[6]||(a[6]=c("span",{class:"panel-title"},"文档信息",-1)),d(t,{type:"text",size:"small",onClick:D},{default:r(()=>[g(m(_.value?"收起":"展开"),1)]),_:1})])]),default:r(()=>{return[c("div",I,[c("div",z,[a[13]||(a[13]=c("h4",{class:"section-title"},"基本信息",-1)),c("div",M,[c("div",L,[a[7]||(a[7]=c("label",null,"文档标题：",-1)),c("span",O,m(e.document.title),1)]),c("div",q,[a[8]||(a[8]=c("label",null,"文件类型：",-1)),d(l,{type:(ge=e.document.type,{text:"success",docx:"primary",pdf:"danger",wps:"warning"}[ge]||"info"),size:"small"},{default:r(()=>[g(m(E(e.document.type)),1)]),_:1},8,["type"])]),c("div",F,[a[9]||(a[9]=c("label",null,"文件大小：",-1)),c("span",N,m((U=e.document.fileSize,U<1024?`${U} B`:U<1048576?`${(U/1024).toFixed(1)} KB`:U<1073741824?`${(U/1048576).toFixed(1)} MB`:`${(U/1073741824).toFixed(1)} GB`)),1)]),c("div",W,[a[10]||(a[10]=c("label",null,"文档状态：",-1)),d(l,{type:(D=e.document.status,{draft:"info",pending:"warning",reviewing:"primary",approved:"success",rejected:"danger",published:"success"}[D]||"info"),size:"small"},{default:r(()=>[g(m(R(e.document.status)),1)]),_:1},8,["type"])]),c("div",j,[a[11]||(a[11]=c("label",null,"文档分类：",-1)),c("span",K,m(e.document.category),1)]),c("div",G,[a[12]||(a[12]=c("label",null,"创建时间：",-1)),c("span",Y,m((i=e.document.createdAt,new Date(i).toLocaleString("zh-CN"))),1)])])]),_.value?(u(),p("div",H,[a[17]||(a[17]=c("h4",{class:"section-title"},"作者信息",-1)),c("div",J,[c("div",X,[a[14]||(a[14]=c("label",null,"作者姓名：",-1)),c("span",Q,m(e.document.authorName),1)]),c("div",Z,[a[15]||(a[15]=c("label",null,"作者单位：",-1)),c("span",ee,m(e.document.authorOrganization),1)]),e.document.authorBio?(u(),p("div",ae,[a[16]||(a[16]=c("label",null,"作者简介：",-1)),c("p",te,m(e.document.authorBio),1)])):v("",!0)])])):v("",!0),_.value&&e.document.tags.length>0?(u(),p("div",le,[a[18]||(a[18]=c("h4",{class:"section-title"},"文档标签",-1)),c("div",se,[(u(!0),p(h,null,f(e.document.tags,e=>(u(),o(l,{key:e,size:"small",class:"tag-item"},{default:r(()=>[g(m(e),1)]),_:2},1024))),128))])])):v("",!0),_.value?(u(),p("div",ne,[a[19]||(a[19]=c("h4",{class:"section-title"},"内容预览",-1)),c("div",oe,[d(s,{height:"200px"},{default:r(()=>[c("div",ie,m(B.value),1)]),_:1})])])):v("",!0),_.value?(u(),p("div",re,[d(t,{type:"primary",size:"small",onClick:A},{default:r(()=>a[20]||(a[20]=[g(" 查看完整内容 ")])),_:1,__:[20]}),d(t,{type:"info",size:"small",onClick:$},{default:r(()=>a[21]||(a[21]=[g(" 编辑文档信息 ")])),_:1,__:[21]}),d(t,{type:"success",size:"small",onClick:S},{default:r(()=>a[22]||(a[22]=[g(" 下载文档 ")])),_:1,__:[22]})])):v("",!0)]),d(me,{modelValue:b.value,"onUpdate:modelValue":a[4]||(a[4]=e=>b.value=e),title:"编辑文档信息",width:"600px","before-close":ce},{footer:r(()=>[d(t,{onClick:ce},{default:r(()=>a[23]||(a[23]=[g("取消")])),_:1,__:[23]}),d(t,{type:"primary",onClick:de},{default:r(()=>a[24]||(a[24]=[g("保存")])),_:1,__:[24]})]),default:r(()=>[d(ve,{ref_key:"editFormRef",ref:k,model:x,rules:T,"label-width":"100px"},{default:r(()=>[d(y,{label:"文档标题",prop:"title"},{default:r(()=>[d(n,{modelValue:x.title,"onUpdate:modelValue":a[0]||(a[0]=e=>x.title=e),placeholder:"请输入文档标题"},null,8,["modelValue"])]),_:1}),d(y,{label:"文档分类",prop:"category"},{default:r(()=>[d(pe,{modelValue:x.category,"onUpdate:modelValue":a[1]||(a[1]=e=>x.category=e),placeholder:"请选择文档分类"},{default:r(()=>[(u(),p(h,null,f(V,e=>d(C,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),d(y,{label:"文档标签",prop:"tags"},{default:r(()=>[d(pe,{modelValue:x.tags,"onUpdate:modelValue":a[2]||(a[2]=e=>x.tags=e),multiple:"",filterable:"","allow-create":"",placeholder:"请选择或输入标签"},{default:r(()=>[(u(),p(h,null,f(P,e=>d(C,{key:e,label:e,value:e},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),d(y,{label:"内容简介",prop:"content"},{default:r(()=>[d(n,{modelValue:x.content,"onUpdate:modelValue":a[3]||(a[3]=e=>x.content=e),type:"textarea",rows:4,placeholder:"请输入内容简介"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),d(me,{modelValue:w.value,"onUpdate:modelValue":a[5]||(a[5]=e=>w.value=e),title:"文档完整内容",width:"80%",top:"5vh"},{default:r(()=>[d(s,{height:"60vh"},{default:r(()=>[c("div",ue,[c("pre",null,m(e.document.content),1)])]),_:1})]),_:1},8,["modelValue"])];var i,D,U,ge}),_:1})}}}),[["__scopeId","data-v-a074d902"]]),de={class:"panel-header"},pe={class:"config-form"},ve={class:"config-section"},me={class:"model-option"},ge={class:"model-name"},he={class:"model-cost"},fe={key:0,class:"model-details"},ye={class:"detail-item"},_e={class:"detail-item"},be={class:"detail-item"},we={class:"detail-item"},ke={class:"capabilities"},Ce={class:"config-section"},xe={class:"template-option"},Te={class:"template-name"},Ve={class:"template-version"},Pe={key:0,class:"template-details"},Be={class:"detail-item"},De={class:"detail-item"},Ee={class:"scene-tags"},Re={class:"detail-item"},Ae={class:"custom-prompt-section"},$e={class:"config-section"},Se={class:"config-item"},Ue={class:"budget-config"},Ie={class:"config-item"},ze={class:"config-item"},Me={key:0,class:"config-section"},Le={class:"estimation-grid"},Oe={class:"estimation-item"},qe={class:"estimation-value"},Fe={class:"estimation-item"},Ne={class:"estimation-value"},We={class:"estimation-item"},je={class:"estimation-value"},Ke={class:"estimation-item"},Ge={class:"config-validation"},Ye=B(t({__name:"AIConfigPanel",props:{aiModel:{default:null},promptTemplate:{default:null},costBudget:{default:100},qualityRequirement:{default:"medium"}},emits:["update:aiModel","update:promptTemplate","update:costBudget","update:qualityRequirement","config-change"],setup(a,{emit:t}){const s=a,_=t,b=l(""),w=l(""),k=l(!1),x=l(""),T=l(s.costBudget),V=l(s.qualityRequirement),P=l("normal"),B=l([{name:"deepseek",label:"DeepSeek",models:[{id:"deepseek-chat",name:"DeepSeek Chat",provider:"DeepSeek",maxTokens:32768,costPerToken:.001,capabilities:["文本校对","语法检查","风格优化"],supportedLanguages:["中文","英文"]},{id:"deepseek-coder",name:"DeepSeek Coder",provider:"DeepSeek",maxTokens:16384,costPerToken:.001,capabilities:["代码校对","技术文档"],supportedLanguages:["中文","英文"]}]},{name:"wenxin",label:"文心一言",models:[{id:"ernie-bot-4",name:"ERNIE Bot 4.0",provider:"百度",maxTokens:8192,costPerToken:.002,capabilities:["文本校对","创意写作","逻辑分析"],supportedLanguages:["中文"]},{id:"ernie-bot-turbo",name:"ERNIE Bot Turbo",provider:"百度",maxTokens:8192,costPerToken:.001,capabilities:["快速校对","基础语法"],supportedLanguages:["中文"]}]},{name:"doubao",label:"豆包",models:[{id:"doubao-pro",name:"豆包 Pro",provider:"字节跳动",maxTokens:32768,costPerToken:.0015,capabilities:["文本校对","内容优化","风格调整"],supportedLanguages:["中文","英文"]}]},{name:"qwen",label:"通义千问",models:[{id:"qwen-max",name:"通义千问 Max",provider:"阿里云",maxTokens:8192,costPerToken:.002,capabilities:["文本校对","专业写作","学术润色"],supportedLanguages:["中文","英文"]},{id:"qwen-plus",name:"通义千问 Plus",provider:"阿里云",maxTokens:32768,costPerToken:.001,capabilities:["文本校对","内容生成"],supportedLanguages:["中文","英文"]}]}]),D=l([{name:"academic",label:"学术论文",templates:[{id:"academic-paper",name:"学术论文校对",description:"专门用于学术论文的语法、逻辑和表达校对",version:"1.2",sceneTags:["学术","论文","严谨"],usageStats:{totalUsage:1250,successRate:.95}},{id:"research-report",name:"研究报告校对",description:"适用于科研报告的专业术语和结构校对",version:"1.1",sceneTags:["科研","报告","专业"],usageStats:{totalUsage:890,successRate:.92}}]},{name:"business",label:"商业文档",templates:[{id:"business-proposal",name:"商业提案校对",description:"商业提案和计划书的专业校对模板",version:"1.0",sceneTags:["商业","提案","专业"],usageStats:{totalUsage:650,successRate:.88}},{id:"marketing-content",name:"营销内容校对",description:"营销文案和宣传材料的校对优化",version:"1.3",sceneTags:["营销","文案","创意"],usageStats:{totalUsage:1100,successRate:.9}}]},{name:"general",label:"通用文档",templates:[{id:"general-text",name:"通用文本校对",description:"适用于各类文本的基础语法和表达校对",version:"2.0",sceneTags:["通用","基础","全面"],usageStats:{totalUsage:2300,successRate:.93}},{id:"news-article",name:"新闻稿件校对",description:"新闻稿件的事实性和表达准确性校对",version:"1.4",sceneTags:["新闻","时效","准确"],usageStats:{totalUsage:780,successRate:.91}}]}]),E=n(()=>{for(const e of B.value){const a=e.models.find(e=>e.id===b.value);if(a)return a}return null}),R=n(()=>{for(const e of D.value){const a=e.templates.find(e=>e.id===w.value);if(a)return a}return null}),A=n(()=>{if(!E.value)return null;const e="high"===V.value?1.5:"medium"===V.value?1.2:1,a=Math.round(2e3*e),t=a/1e3*E.value.costPerToken,l=Math.round(a/100),s="high"===V.value?5:"medium"===V.value?4:3;return{tokens:a,cost:t.toFixed(2),time:l,qualityScore:s}}),$=n(()=>E.value?R.value||k.value?k.value&&!x.value.trim()?"请输入自定义提示词内容":T.value<10?"成本预算不能少于10元":"":"请选择提示词模板或启用自定义提示词":"请选择AI模型"),S=n(()=>$.value?"warning":"success");y([b,w,T,V],()=>{F()}),e(()=>{s.aiModel&&(b.value=s.aiModel.id),s.promptTemplate&&(w.value=s.promptTemplate.id)});const U=e=>{const a=E.value;_("update:aiModel",a),F()},I=e=>{const a=R.value;_("update:promptTemplate",a),F()},z=e=>{e&&(w.value="",_("update:promptTemplate",null)),F()},M=e=>{_("update:costBudget",e),F()},L=e=>{_("update:qualityRequirement",e),F()},O=()=>{F()},q=()=>{b.value="",w.value="",k.value=!1,x.value="",T.value=100,V.value="medium",P.value="normal",_("update:aiModel",null),_("update:promptTemplate",null),_("update:costBudget",100),_("update:qualityRequirement","medium"),C.success("配置已重置")},F=()=>{const e={model:E.value,promptTemplate:k.value?{id:"custom",name:"自定义提示词",description:"用户自定义的提示词",content:x.value,variables:[],category:"custom",language:"zh-CN",version:"1.0",createdAt:Date.now(),updatedAt:Date.now()}:R.value,costBudget:T.value,qualityRequirement:V.value,priority:P.value};_("config-change",e)};return(e,a)=>{const t=i("el-button"),l=i("el-option"),s=i("el-option-group"),n=i("el-select"),y=i("el-tag"),_=i("el-checkbox"),C=i("el-input"),F=i("el-input-number"),N=i("el-radio"),W=i("el-radio-group"),j=i("el-rate"),K=i("el-alert"),G=i("el-card");return u(),o(G,{class:"ai-config-panel"},{header:r(()=>[c("div",de,[a[9]||(a[9]=c("span",{class:"panel-title"},"AI配置选择",-1)),d(t,{type:"text",size:"small",onClick:q},{default:r(()=>a[8]||(a[8]=[g(" 重置配置 ")])),_:1,__:[8]})])]),default:r(()=>[c("div",pe,[c("div",ve,[a[14]||(a[14]=c("h4",{class:"section-title"},"AI模型选择",-1)),d(n,{modelValue:b.value,"onUpdate:modelValue":a[0]||(a[0]=e=>b.value=e),placeholder:"请选择AI模型",class:"full-width",onChange:U},{default:r(()=>[(u(!0),p(h,null,f(B.value,e=>(u(),o(s,{key:e.name,label:e.label},{default:r(()=>[(u(!0),p(h,null,f(e.models,e=>(u(),o(l,{key:e.id,label:e.name,value:e.id},{default:r(()=>[c("div",me,[c("span",ge,m(e.name),1),c("span",he,m(e.costPerToken)+"元/千token",1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),E.value?(u(),p("div",fe,[c("div",ye,[a[10]||(a[10]=c("label",null,"提供商：",-1)),c("span",null,m(E.value.provider),1)]),c("div",_e,[a[11]||(a[11]=c("label",null,"最大Token：",-1)),c("span",null,m(E.value.maxTokens.toLocaleString()),1)]),c("div",be,[a[12]||(a[12]=c("label",null,"支持语言：",-1)),c("span",null,m(E.value.supportedLanguages.join(", ")),1)]),c("div",we,[a[13]||(a[13]=c("label",null,"模型能力：",-1)),c("div",ke,[(u(!0),p(h,null,f(E.value.capabilities,e=>(u(),o(y,{key:e,size:"small",class:"capability-tag"},{default:r(()=>[g(m(e),1)]),_:2},1024))),128))])])])):v("",!0)]),c("div",Ce,[a[19]||(a[19]=c("h4",{class:"section-title"},"提示词模板",-1)),d(n,{modelValue:w.value,"onUpdate:modelValue":a[1]||(a[1]=e=>w.value=e),placeholder:"请选择提示词模板",class:"full-width",onChange:I},{default:r(()=>[(u(!0),p(h,null,f(D.value,e=>(u(),o(s,{key:e.name,label:e.label},{default:r(()=>[(u(!0),p(h,null,f(e.templates,e=>(u(),o(l,{key:e.id,label:e.name,value:e.id},{default:r(()=>[c("div",xe,[c("span",Te,m(e.name),1),c("span",Ve,"v"+m(e.version),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),R.value?(u(),p("div",Pe,[c("div",Be,[a[15]||(a[15]=c("label",null,"描述：",-1)),c("span",null,m(R.value.description),1)]),c("div",De,[a[16]||(a[16]=c("label",null,"适用场景：",-1)),c("div",Ee,[(u(!0),p(h,null,f(R.value.sceneTags,e=>(u(),o(y,{key:e,size:"small",type:"info",class:"scene-tag"},{default:r(()=>[g(m(e),1)]),_:2},1024))),128))])]),c("div",Re,[a[17]||(a[17]=c("label",null,"使用统计：",-1)),c("span",null,"使用"+m(R.value.usageStats?.totalUsage||0)+"次，成功率"+m(100*(R.value.usageStats?.successRate||0))+"%",1)])])):v("",!0),c("div",Ae,[d(_,{modelValue:k.value,"onUpdate:modelValue":a[2]||(a[2]=e=>k.value=e),onChange:z},{default:r(()=>a[18]||(a[18]=[g(" 使用自定义提示词 ")])),_:1,__:[18]},8,["modelValue"]),k.value?(u(),o(C,{key:0,modelValue:x.value,"onUpdate:modelValue":a[3]||(a[3]=e=>x.value=e),type:"textarea",rows:4,placeholder:"请输入自定义提示词内容...",class:"custom-prompt-input"},null,8,["modelValue"])):v("",!0)])]),c("div",$e,[a[27]||(a[27]=c("h4",{class:"section-title"},"成本与质量配置",-1)),c("div",Se,[a[21]||(a[21]=c("label",{class:"config-label"},"成本预算：",-1)),c("div",Ue,[d(F,{modelValue:T.value,"onUpdate:modelValue":a[4]||(a[4]=e=>T.value=e),min:10,max:1e3,step:10,size:"small",onChange:M},null,8,["modelValue"]),a[20]||(a[20]=c("span",{class:"budget-unit"},"元",-1))])]),c("div",Ie,[a[25]||(a[25]=c("label",{class:"config-label"},"质量要求：",-1)),d(W,{modelValue:V.value,"onUpdate:modelValue":a[5]||(a[5]=e=>V.value=e),onChange:L},{default:r(()=>[d(N,{value:"low"},{default:r(()=>a[22]||(a[22]=[g("快速模式")])),_:1,__:[22]}),d(N,{value:"medium"},{default:r(()=>a[23]||(a[23]=[g("标准模式")])),_:1,__:[23]}),d(N,{value:"high"},{default:r(()=>a[24]||(a[24]=[g("精确模式")])),_:1,__:[24]})]),_:1},8,["modelValue"])]),c("div",ze,[a[26]||(a[26]=c("label",{class:"config-label"},"处理优先级：",-1)),d(n,{modelValue:P.value,"onUpdate:modelValue":a[6]||(a[6]=e=>P.value=e),size:"small",onChange:O},{default:r(()=>[d(l,{label:"低优先级",value:"low"}),d(l,{label:"普通优先级",value:"normal"}),d(l,{label:"高优先级",value:"high"})]),_:1},8,["modelValue"])])]),A.value?(u(),p("div",Me,[a[32]||(a[32]=c("h4",{class:"section-title"},"预估信息",-1)),c("div",Le,[c("div",Oe,[a[28]||(a[28]=c("label",null,"预估成本：",-1)),c("span",qe,m(A.value.cost)+"元",1)]),c("div",Fe,[a[29]||(a[29]=c("label",null,"预估时间：",-1)),c("span",Ne,m(A.value.time)+"分钟",1)]),c("div",We,[a[30]||(a[30]=c("label",null,"Token消耗：",-1)),c("span",je,m(A.value.tokens.toLocaleString()),1)]),c("div",Ke,[a[31]||(a[31]=c("label",null,"质量评分：",-1)),d(j,{modelValue:A.value.qualityScore,"onUpdate:modelValue":a[7]||(a[7]=e=>A.value.qualityScore=e),disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}分"},null,8,["modelValue"])])])])):v("",!0),c("div",Ge,[$.value?(u(),o(K,{key:0,title:$.value,type:S.value,closable:!1,"show-icon":""},null,8,["title","type"])):v("",!0)])])]),_:1})}}}),[["__scopeId","data-v-d5d8bba4"]]),He={class:"panel-header"},Je={class:"converter-content"},Xe={key:0,class:"conversion-config"},Qe={class:"config-item"},Ze={class:"config-item"},ea={class:"config-item"},aa={key:1,class:"conversion-options"},ta={class:"options-grid"},la={class:"encoding-option"},sa={key:2,class:"conversion-progress"},na={class:"progress-info"},oa={class:"progress-text"},ia={class:"progress-details"},ra={class:"detail-item"},ua={class:"detail-item"},ca={class:"detail-item"},da={key:3,class:"conversion-result"},pa={class:"result-header"},va={class:"result-stats"},ma={class:"stat-item"},ga={class:"stat-item"},ha={class:"stat-item"},fa={class:"stat-item"},ya={class:"content-preview"},_a={class:"preview-text"},ba={key:4,class:"conversion-error"},wa={class:"error-actions"},ka={class:"action-buttons"},Ca={class:"error-details"},xa={class:"error-item"},Ta={class:"error-item"},Va={class:"error-item"},Pa={class:"error-item full-width"},Ba={class:"error-message"},Da=B(t({__name:"DocumentConverter",props:{document:{},loading:{type:Boolean}},emits:["conversion-complete","conversion-error"],setup(e,{emit:a}){const t=e,h=a,f=l("idle"),b=l(0),w=l(""),k=l(""),x=l(0),P=l(0),B=l(0),D=l(""),E=l(!1),R=s({sourceFormat:t.document.type,targetFormat:"markdown",options:{preserveFormatting:!0,extractImages:!1,preserveTables:!0,preserveLinks:!0,encoding:"utf-8"}}),A=s({type:"",code:"",timestamp:0,details:""}),$=l("0 KB"),S=l("0 KB"),U=l("--"),I=l("--"),z=n(()=>"converting"===f.value),M=n(()=>"completed"===f.value),L=n(()=>"failed"===f.value?"exception":"completed"===f.value?"success":void 0);y(()=>t.loading,e=>{e&&"idle"===f.value&&(f.value="converting")});const O=e=>({idle:"待转换",converting:"转换中",completed:"已完成",failed:"转换失败",cancelled:"已取消"}[e]),q=e=>({text:"文本文档",docx:"Word文档",pdf:"PDF文档",wps:"WPS文档"}[e]||e.toUpperCase()),F=e=>e<1024?`${e} B`:e<1048576?`${(e/1024).toFixed(1)} KB`:e<1073741824?`${(e/1048576).toFixed(1)} MB`:`${(e/1073741824).toFixed(1)} GB`,N=e=>new Date(e).toLocaleString("zh-CN"),W=async()=>{try{f.value="converting",b.value=0,w.value="正在初始化转换...";const e=Date.now();await j(),B.value=Math.round((Date.now()-e)/1e3),f.value="completed",G(),C.success("文档转换完成"),h("conversion-complete",k.value)}catch(e){f.value="failed",D.value=e instanceof Error?e.message:"转换失败",A.type="转换错误",A.code="CONVERSION_FAILED",A.timestamp=Date.now(),A.details=e instanceof Error?e.stack||e.message:"未知错误",C.error("文档转换失败"),h("conversion-error",D.value)}},j=async()=>{const e=[{text:"正在解析文档结构...",progress:20},{text:"正在提取文本内容...",progress:40},{text:"正在处理格式信息...",progress:60},{text:"正在生成Markdown...",progress:80},{text:"正在优化输出格式...",progress:100}];for(const a of e)if(w.value=a.text,b.value=a.progress,K(a.progress),await new Promise(e=>setTimeout(e,800+400*Math.random())),"cancelled"===f.value)throw new Error("转换已取消")},K=e=>{const a=t.document.fileSize,l=Math.round(a*e/100);if($.value=F(l),S.value=F(a),e>0){const a=Math.round(.1*(100-e));U.value=a>0?`${a}秒`:"即将完成";const t=Math.round(l/(.1*e)/1024);I.value=`${t} KB/s`}},G=()=>{const e=`# ${t.document.title}\n\n## 文档信息\n- **作者**: ${t.document.authorName}\n- **单位**: ${t.document.authorOrganization}\n- **创建时间**: ${N(new Date(t.document.createdAt).getTime())}\n\n## 内容摘要\n${t.document.content}\n\n## 正文内容\n\n这是从${q(t.document.type)}转换而来的Markdown文档。\n\n### 第一章 引言\n本文档经过智能转换处理，保留了原始文档的主要结构和内容。转换过程中${R.options.preserveFormatting?"保留了":"简化了"}原始格式。\n\n### 第二章 主要内容\n${R.options.preserveTables?"表格结构已保留":"表格已转换为文本格式"}，${R.options.preserveLinks?"链接信息已保留":"链接已转换为纯文本"}。\n\n### 第三章 结论\n文档转换完成，可以进行后续的AI校对处理。\n\n---\n*本文档由AI智能审校系统自动转换生成*`;k.value=e,x.value=new Blob([e]).size,P.value=e.length},Y=async()=>{try{await T.confirm("确定要取消转换吗？","确认取消",{confirmButtonText:"确定",cancelButtonText:"继续转换",type:"warning"}),f.value="cancelled",C.info("转换已取消")}catch{}},H=()=>{f.value="idle",b.value=0,D.value="",W()},J=()=>{h("conversion-complete",k.value),C.success("转换结果已确认")},X=()=>{const e=new Blob([k.value],{type:"text/markdown"}),a=URL.createObjectURL(e),l=document.createElement("a");l.href=a,l.download=`${t.document.title}.md`,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(a),C.success("下载已开始")},Q=()=>{E.value=!0},Z=async()=>{const e=`错误类型: ${A.type}\n错误代码: ${A.code}\n错误时间: ${N(A.timestamp)}\n详细信息: ${A.details}`;try{await navigator.clipboard.writeText(e),C.success("错误信息已复制到剪贴板")}catch{C.error("复制失败")}};return(e,a)=>{const t=i("el-tag"),l=i("el-checkbox"),s=i("el-option"),n=i("el-select"),h=i("el-progress"),y=i("el-icon"),C=i("el-scrollbar"),T=i("el-alert"),j=i("el-button"),K=i("el-dialog"),G=i("el-card");return u(),o(G,{class:"document-converter"},{header:r(()=>{return[c("div",He,[a[7]||(a[7]=c("span",{class:"panel-title"},"文档格式转换",-1)),d(t,{type:(e=f.value,{idle:"info",converting:"warning",completed:"success",failed:"danger",cancelled:"info"}[e]),size:"small"},{default:r(()=>[g(m(O(f.value)),1)]),_:1},8,["type"])])];var e}),default:r(()=>[c("div",Je,[z.value||M.value?v("",!0):(u(),p("div",Xe,[c("div",Qe,[a[8]||(a[8]=c("label",null,"源格式：",-1)),d(t,{type:"info"},{default:r(()=>[g(m(q(e.document.type)),1)]),_:1})]),c("div",Ze,[a[10]||(a[10]=c("label",null,"目标格式：",-1)),d(t,{type:"success"},{default:r(()=>a[9]||(a[9]=[g("Markdown")])),_:1,__:[9]})]),c("div",ea,[a[11]||(a[11]=c("label",null,"文件大小：",-1)),c("span",null,m(F(e.document.fileSize)),1)])])),z.value||M.value?v("",!0):(u(),p("div",aa,[a[17]||(a[17]=c("h4",{class:"options-title"},"转换选项",-1)),c("div",ta,[d(l,{modelValue:R.preserveFormatting,"onUpdate:modelValue":a[0]||(a[0]=e=>R.preserveFormatting=e)},{default:r(()=>a[12]||(a[12]=[g(" 保留格式 ")])),_:1,__:[12]},8,["modelValue"]),d(l,{modelValue:R.extractImages,"onUpdate:modelValue":a[1]||(a[1]=e=>R.extractImages=e)},{default:r(()=>a[13]||(a[13]=[g(" 提取图片 ")])),_:1,__:[13]},8,["modelValue"]),d(l,{modelValue:R.preserveTables,"onUpdate:modelValue":a[2]||(a[2]=e=>R.preserveTables=e)},{default:r(()=>a[14]||(a[14]=[g(" 保留表格 ")])),_:1,__:[14]},8,["modelValue"]),d(l,{modelValue:R.preserveLinks,"onUpdate:modelValue":a[3]||(a[3]=e=>R.preserveLinks=e)},{default:r(()=>a[15]||(a[15]=[g(" 保留链接 ")])),_:1,__:[15]},8,["modelValue"])]),c("div",la,[a[16]||(a[16]=c("label",null,"编码格式：",-1)),d(n,{modelValue:R.encoding,"onUpdate:modelValue":a[4]||(a[4]=e=>R.encoding=e),size:"small"},{default:r(()=>[d(s,{label:"UTF-8",value:"utf-8"}),d(s,{label:"GBK",value:"gbk"}),d(s,{label:"GB2312",value:"gb2312"})]),_:1},8,["modelValue"])])])),z.value?(u(),p("div",sa,[c("div",na,[a[18]||(a[18]=c("h4",null,"正在转换文档...",-1)),c("p",oa,m(w.value),1)]),d(h,{percentage:b.value,status:L.value,"stroke-width":8,"text-inside":""},null,8,["percentage","status"]),c("div",ia,[c("div",ra,[a[19]||(a[19]=c("span",null,"已处理：",-1)),c("span",null,m($.value)+" / "+m(S.value),1)]),c("div",ua,[a[20]||(a[20]=c("span",null,"剩余时间：",-1)),c("span",null,m(U.value),1)]),c("div",ca,[a[21]||(a[21]=c("span",null,"转换速度：",-1)),c("span",null,m(I.value),1)])])])):v("",!0),M.value?(u(),p("div",da,[c("div",pa,[d(y,{class:"success-icon",color:"#67c23a"},{default:r(()=>[d(_(V))]),_:1}),a[22]||(a[22]=c("h4",null,"转换完成",-1))]),c("div",va,[c("div",ma,[a[23]||(a[23]=c("label",null,"转换时间：",-1)),c("span",null,m(B.value)+"秒",1)]),c("div",ga,[a[24]||(a[24]=c("label",null,"原始大小：",-1)),c("span",null,m(F(e.document.fileSize)),1)]),c("div",ha,[a[25]||(a[25]=c("label",null,"转换后大小：",-1)),c("span",null,m(F(x.value)),1)]),c("div",fa,[a[26]||(a[26]=c("label",null,"字符数：",-1)),c("span",null,m(P.value.toLocaleString()),1)])]),c("div",ya,[a[27]||(a[27]=c("h5",null,"内容预览",-1)),d(C,{height:"200px"},{default:r(()=>[c("div",_a,m(k.value.length>500?k.value.substring(0,500)+"...":k.value),1)]),_:1})])])):v("",!0),"failed"===f.value?(u(),p("div",ba,[d(T,{title:"转换失败",description:D.value,type:"error","show-icon":"",closable:!1},null,8,["description"]),c("div",wa,[d(j,{type:"primary",size:"small",onClick:H},{default:r(()=>a[28]||(a[28]=[g(" 重试转换 ")])),_:1,__:[28]}),d(j,{type:"info",size:"small",onClick:Q},{default:r(()=>a[29]||(a[29]=[g(" 查看详情 ")])),_:1,__:[29]})])])):v("",!0),c("div",ka,[z.value||M.value?v("",!0):(u(),o(j,{key:0,type:"primary",loading:z.value,onClick:W},{default:r(()=>a[30]||(a[30]=[g(" 开始转换 ")])),_:1,__:[30]},8,["loading"])),z.value?(u(),o(j,{key:1,type:"warning",onClick:Y},{default:r(()=>a[31]||(a[31]=[g(" 取消转换 ")])),_:1,__:[31]})):v("",!0),M.value?(u(),o(j,{key:2,type:"success",onClick:J},{default:r(()=>a[32]||(a[32]=[g(" 确认使用 ")])),_:1,__:[32]})):v("",!0),M.value?(u(),o(j,{key:3,type:"info",onClick:X},{default:r(()=>a[33]||(a[33]=[g(" 下载转换结果 ")])),_:1,__:[33]})):v("",!0),"failed"===f.value?(u(),o(j,{key:4,type:"primary",onClick:H},{default:r(()=>a[34]||(a[34]=[g(" 重新转换 ")])),_:1,__:[34]})):v("",!0)])]),d(K,{modelValue:E.value,"onUpdate:modelValue":a[6]||(a[6]=e=>E.value=e),title:"转换错误详情",width:"600px"},{footer:r(()=>[d(j,{onClick:a[5]||(a[5]=e=>E.value=!1)},{default:r(()=>a[39]||(a[39]=[g("关闭")])),_:1,__:[39]}),d(j,{type:"primary",onClick:Z},{default:r(()=>a[40]||(a[40]=[g("复制错误信息")])),_:1,__:[40]})]),default:r(()=>[c("div",Ca,[c("div",xa,[a[35]||(a[35]=c("label",null,"错误类型：",-1)),c("span",null,m(A.type),1)]),c("div",Ta,[a[36]||(a[36]=c("label",null,"错误代码：",-1)),c("span",null,m(A.code),1)]),c("div",Va,[a[37]||(a[37]=c("label",null,"错误时间：",-1)),c("span",null,m(N(A.timestamp)),1)]),c("div",Pa,[a[38]||(a[38]=c("label",null,"详细信息：",-1)),c("pre",Ba,m(A.details),1)])])]),_:1},8,["modelValue"])]),_:1})}}}),[["__scopeId","data-v-2be4e663"]]);var Ea=(e=>(e.PENDING="pending",e.PROCESSING="processing",e.COMPLETED="completed",e.FAILED="failed",e.APPROVED="approved",e.REJECTED="rejected",e.PAUSED="paused",e))(Ea||{}),Ra=(e=>(e.BY_CHAPTER="by_chapter",e.BY_PARAGRAPH="by_paragraph",e.BY_WORD_COUNT="by_word_count",e.SEMANTIC="semantic",e.HYBRID="hybrid",e))(Ra||{});const Aa={class:"panel-header"},$a={class:"splitter-content"},Sa={key:0,class:"splitting-config"},Ua={class:"config-section"},Ia={class:"config-section"},za={class:"config-grid"},Ma={class:"config-item"},La={class:"config-item"},Oa={class:"config-item full-width"},qa={key:0,class:"config-section"},Fa={class:"preview-info"},Na={class:"info-item"},Wa={class:"info-item"},ja={class:"info-item"},Ka={key:1,class:"splitting-progress"},Ga={class:"progress-info"},Ya={class:"progress-text"},Ha={class:"progress-details"},Ja={class:"detail-item"},Xa={class:"detail-item"},Qa={class:"detail-item"},Za={key:2,class:"splitting-result"},et={class:"result-header"},at={class:"result-stats"},tt={class:"stat-item"},lt={class:"stat-item"},st={class:"stat-item"},nt={class:"stat-item"},ot={class:"chunks-preview"},it={class:"content-preview"},rt={key:0,class:"more-chunks"},ut={key:3,class:"splitting-error"},ct={class:"error-actions"},dt={class:"action-buttons"},pt={class:"chunk-content"},vt=B(t({__name:"DocumentSplitter",props:{content:{},loading:{type:Boolean}},emits:["splitting-complete","splitting-error"],setup(e,{emit:a}){const t=e,h=a,f=l("idle"),b=l(0),w=l(""),k=l([]),x=l(0),P=l(""),B=l(!1),D=l(""),E=s({strategy:Ra.BY_WORD_COUNT,maxWords:2e3,overlapWords:300,preserveStructure:!0}),R=l(0),A=l(0),$=l("--"),S=n(()=>"splitting"===f.value),U=n(()=>"completed"===f.value),I=n(()=>"failed"===f.value?"exception":"completed"===f.value?"success":void 0),z=n(()=>t.content.length),M=n(()=>t.content?Math.ceil(z.value/E.maxWords):0),L=n(()=>{if(0===M.value)return"0字";return`${Math.round(z.value/M.value)}字`}),O=n(()=>{if(0===k.value.length)return"0字";return`${Math.max(...k.value.map(e=>e.wordCount))}字`}),q=n(()=>{if(0===k.value.length)return"0字";return`${Math.min(...k.value.map(e=>e.wordCount))}字`});y(()=>t.loading,e=>{e&&"idle"===f.value&&(f.value="splitting")});const F=e=>({idle:"待拆分",splitting:"拆分中",completed:"已完成",failed:"拆分失败",cancelled:"已取消"}[e]),N=e=>{switch(e){case Ra.BY_CHAPTER:E.maxWords=3e3,E.overlapWords=200;break;case Ra.BY_PARAGRAPH:E.maxWords=1500,E.overlapWords=100;break;case Ra.BY_WORD_COUNT:E.maxWords=2e3,E.overlapWords=300;break;case Ra.SEMANTIC:E.maxWords=2500,E.overlapWords=400;break;case Ra.HYBRID:E.maxWords=2e3,E.overlapWords=300}},W=e=>{E.customSeparators=e.split(",").map(e=>e.trim()).filter(e=>e)},j=async()=>{try{f.value="splitting",b.value=0,w.value="正在初始化拆分...";const e=Date.now();await K(),x.value=Math.round((Date.now()-e)/1e3),f.value="completed",C.success("文档拆分完成"),h("splitting-complete",k.value)}catch(e){f.value="failed",P.value=e instanceof Error?e.message:"拆分失败",C.error("文档拆分失败"),h("splitting-error",P.value)}},K=async()=>{const e=[{text:"正在分析文档结构...",progress:20},{text:"正在应用拆分策略...",progress:40},{text:"正在生成分块...",progress:60},{text:"正在优化分块边界...",progress:80},{text:"正在完成拆分...",progress:100}];for(const a of e)if(w.value=a.text,b.value=a.progress,G(a.progress),await new Promise(e=>setTimeout(e,600+400*Math.random())),"cancelled"===f.value)throw new Error("拆分已取消");Y()},G=e=>{const a=M.value,t=Math.round(a*e/100);if(R.value=t,A.value=a,e>0){const a=Math.round(t/(.01*e));$.value=`${a} 分块/秒`}},Y=()=>{const e=t.content,a=E.maxWords,l=E.overlapWords,s=[];let n=0,o=1;for(;n<e.length;){const t=Math.min(n+a,e.length),i=e.substring(n,t),r={id:`chunk_${o}`,title:H(i,o,E.strategy),content:i,index:o,wordCount:i.length,status:Ea.PENDING,progress:0,createdAt:Date.now(),updatedAt:Date.now()};if(s.push(r),n=t-l,o++,n>=e.length-l)break}k.value=s},H=(e,a,t)=>{const l=e.split("\n").filter(e=>e.trim());if(l.length>0){const e=l[0].trim();if(e.match(/^#{1,6}\s+/)||e.match(/^第[一二三四五六七八九十\d]+[章节部分]/))return e.replace(/^#{1,6}\s+/,"").substring(0,30);if(e.length>0&&e.length<=50)return e}switch(t){case Ra.BY_CHAPTER:return`第${a}章`;case Ra.BY_PARAGRAPH:return`段落${a}`;case Ra.SEMANTIC:return`语义块${a}`;default:return`分块${a}`}},J=async()=>{try{await T.confirm("确定要取消拆分吗？","确认取消",{confirmButtonText:"确定",cancelButtonText:"继续拆分",type:"warning"}),f.value="cancelled",C.info("拆分已取消")}catch{}},X=()=>{f.value="idle",b.value=0,P.value="",k.value=[],j()},Q=()=>{f.value="idle",P.value="",C.info("请调整拆分配置后重试")},Z=()=>{h("splitting-complete",k.value),C.success("拆分结果已确认")},ee=()=>{B.value=!0};return(e,a)=>{const t=i("el-tag"),l=i("el-radio"),s=i("el-radio-group"),n=i("el-input-number"),h=i("el-checkbox"),y=i("el-input"),T=i("el-progress"),K=i("el-icon"),G=i("el-table-column"),Y=i("el-table"),H=i("el-alert"),ae=i("el-button"),te=i("el-scrollbar"),le=i("el-dialog"),se=i("el-card");return u(),o(se,{class:"document-splitter"},{header:r(()=>{return[c("div",Aa,[a[6]||(a[6]=c("span",{class:"panel-title"},"文档智能拆分",-1)),d(t,{type:(e=f.value,{idle:"info",splitting:"warning",completed:"success",failed:"danger",cancelled:"info"}[e]),size:"small"},{default:r(()=>[g(m(F(f.value)),1)]),_:1},8,["type"])])];var e}),default:r(()=>{return[c("div",$a,[S.value||U.value?v("",!0):(u(),p("div",Sa,[c("div",Ua,[a[12]||(a[12]=c("h4",{class:"section-title"},"拆分策略",-1)),d(s,{modelValue:E.strategy,"onUpdate:modelValue":a[0]||(a[0]=e=>E.strategy=e),onChange:N},{default:r(()=>[d(l,{value:"by_chapter"},{default:r(()=>a[7]||(a[7]=[g("按章节拆分")])),_:1,__:[7]}),d(l,{value:"by_paragraph"},{default:r(()=>a[8]||(a[8]=[g("按段落拆分")])),_:1,__:[8]}),d(l,{value:"by_word_count"},{default:r(()=>a[9]||(a[9]=[g("按字数拆分")])),_:1,__:[9]}),d(l,{value:"semantic"},{default:r(()=>a[10]||(a[10]=[g("语义拆分")])),_:1,__:[10]}),d(l,{value:"hybrid"},{default:r(()=>a[11]||(a[11]=[g("混合策略")])),_:1,__:[11]})]),_:1},8,["modelValue"])]),c("div",Ia,[a[16]||(a[16]=c("h4",{class:"section-title"},"拆分参数",-1)),c("div",za,[c("div",Ma,[a[13]||(a[13]=c("label",null,"最大字数：",-1)),d(n,{modelValue:E.maxWords,"onUpdate:modelValue":a[1]||(a[1]=e=>E.maxWords=e),min:500,max:5e3,step:100,size:"small"},null,8,["modelValue"])]),c("div",La,[a[14]||(a[14]=c("label",null,"重叠字数：",-1)),d(n,{modelValue:E.overlapWords,"onUpdate:modelValue":a[2]||(a[2]=e=>E.overlapWords=e),min:0,max:500,step:50,size:"small"},null,8,["modelValue"])]),c("div",Oa,[d(h,{modelValue:E.preserveStructure,"onUpdate:modelValue":a[3]||(a[3]=e=>E.preserveStructure=e)},{default:r(()=>a[15]||(a[15]=[g(" 保留章节结构 ")])),_:1,__:[15]},8,["modelValue"])])])]),"hybrid"===E.strategy?(u(),p("div",qa,[a[17]||(a[17]=c("h4",{class:"section-title"},"自定义分隔符",-1)),d(y,{modelValue:D.value,"onUpdate:modelValue":a[4]||(a[4]=e=>D.value=e),placeholder:"请输入分隔符，用逗号分隔，如：第一章,第二章,##,###",onChange:W},null,8,["modelValue"])])):v("",!0),c("div",Fa,[c("div",Na,[a[18]||(a[18]=c("label",null,"文档总字数：",-1)),c("span",null,m(z.value.toLocaleString()),1)]),c("div",Wa,[a[19]||(a[19]=c("label",null,"预估分块数：",-1)),c("span",null,m(M.value),1)]),c("div",ja,[a[20]||(a[20]=c("label",null,"平均分块大小：",-1)),c("span",null,m(L.value),1)])])])),S.value?(u(),p("div",Ka,[c("div",Ga,[a[21]||(a[21]=c("h4",null,"正在拆分文档...",-1)),c("p",Ya,m(w.value),1)]),d(T,{percentage:b.value,status:I.value,"stroke-width":8,"text-inside":""},null,8,["percentage","status"]),c("div",Ha,[c("div",Ja,[a[22]||(a[22]=c("span",null,"已处理：",-1)),c("span",null,m(R.value)+" / "+m(A.value),1)]),c("div",Xa,[a[23]||(a[23]=c("span",null,"当前策略：",-1)),c("span",null,m((e=E.strategy,{[Ra.BY_CHAPTER]:"按章节拆分",[Ra.BY_PARAGRAPH]:"按段落拆分",[Ra.BY_WORD_COUNT]:"按字数拆分",[Ra.SEMANTIC]:"语义拆分",[Ra.HYBRID]:"混合策略"}[e])),1)]),c("div",Qa,[a[24]||(a[24]=c("span",null,"处理速度：",-1)),c("span",null,m($.value),1)])])])):v("",!0),U.value?(u(),p("div",Za,[c("div",et,[d(K,{class:"success-icon",color:"#67c23a"},{default:r(()=>[d(_(V))]),_:1}),a[25]||(a[25]=c("h4",null,"拆分完成",-1))]),c("div",at,[c("div",tt,[a[26]||(a[26]=c("label",null,"拆分时间：",-1)),c("span",null,m(x.value)+"秒",1)]),c("div",lt,[a[27]||(a[27]=c("label",null,"生成分块：",-1)),c("span",null,m(k.value.length)+"个",1)]),c("div",st,[a[28]||(a[28]=c("label",null,"最大分块：",-1)),c("span",null,m(O.value),1)]),c("div",nt,[a[29]||(a[29]=c("label",null,"最小分块：",-1)),c("span",null,m(q.value),1)])]),c("div",ot,[a[30]||(a[30]=c("h5",null,"分块预览",-1)),d(Y,{data:k.value.slice(0,5),size:"small",style:{width:"100%"}},{default:r(()=>[d(G,{prop:"index",label:"序号",width:"60"}),d(G,{prop:"title",label:"标题","min-width":"120"}),d(G,{prop:"wordCount",label:"字数",width:"80"}),d(G,{label:"内容预览","min-width":"200"},{default:r(({row:e})=>{return[c("span",it,m((a=e.content,a.length>100?a.substring(0,100)+"...":a)),1)];var a}),_:1})]),_:1},8,["data"]),k.value.length>5?(u(),p("div",rt," 还有 "+m(k.value.length-5)+" 个分块... ",1)):v("",!0)])])):v("",!0),"failed"===f.value?(u(),p("div",ut,[d(H,{title:"拆分失败",description:P.value,type:"error","show-icon":"",closable:!1},null,8,["description"]),c("div",ct,[d(ae,{type:"primary",size:"small",onClick:X},{default:r(()=>a[31]||(a[31]=[g(" 重试拆分 ")])),_:1,__:[31]}),d(ae,{type:"info",size:"small",onClick:Q},{default:r(()=>a[32]||(a[32]=[g(" 调整配置 ")])),_:1,__:[32]})])])):v("",!0),c("div",dt,[S.value||U.value?v("",!0):(u(),o(ae,{key:0,type:"primary",loading:S.value,onClick:j},{default:r(()=>a[33]||(a[33]=[g(" 开始拆分 ")])),_:1,__:[33]},8,["loading"])),S.value?(u(),o(ae,{key:1,type:"warning",onClick:J},{default:r(()=>a[34]||(a[34]=[g(" 取消拆分 ")])),_:1,__:[34]})):v("",!0),U.value?(u(),o(ae,{key:2,type:"success",onClick:Z},{default:r(()=>a[35]||(a[35]=[g(" 确认使用 ")])),_:1,__:[35]})):v("",!0),U.value?(u(),o(ae,{key:3,type:"info",onClick:ee},{default:r(()=>a[36]||(a[36]=[g(" 预览所有分块 ")])),_:1,__:[36]})):v("",!0),"failed"===f.value?(u(),o(ae,{key:4,type:"primary",onClick:X},{default:r(()=>a[37]||(a[37]=[g(" 重新拆分 ")])),_:1,__:[37]})):v("",!0)])]),d(le,{modelValue:B.value,"onUpdate:modelValue":a[5]||(a[5]=e=>B.value=e),title:"所有分块预览",width:"80%",top:"5vh"},{default:r(()=>[d(Y,{data:k.value,size:"small",height:"60vh",style:{width:"100%"}},{default:r(()=>[d(G,{prop:"index",label:"序号",width:"60"}),d(G,{prop:"title",label:"标题",width:"150"}),d(G,{prop:"wordCount",label:"字数",width:"80"}),d(G,{label:"内容预览","min-width":"300"},{default:r(({row:e})=>[d(te,{height:"100px"},{default:r(()=>[c("div",pt,m(e.content),1)]),_:2},1024)]),_:1}),d(G,{label:"操作",width:"100"},{default:r(({row:e})=>[d(ae,{type:"text",size:"small",onClick:a=>{return t=e,void C.info(`编辑分块: ${t.title}`);var t}},{default:r(()=>a[38]||(a[38]=[g(" 编辑 ")])),_:2,__:[38]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])];var e}),_:1})}}}),[["__scopeId","data-v-35eda3a5"]]),mt={class:"panel-header"},gt={class:"header-actions"},ht={class:"task-list-content"},ft={class:"filter-section"},yt={class:"filter-row"},_t={class:"stats-section"},bt={class:"stat-item"},wt={class:"stat-value"},kt={class:"stat-item"},Ct={class:"stat-value pending"},xt={class:"stat-item"},Tt={class:"stat-value processing"},Vt={class:"stat-item"},Pt={class:"stat-value completed"},Bt={class:"stat-item"},Dt={class:"stat-value failed"},Et={class:"table-section"},Rt={class:"title-cell"},At={class:"title-text"},$t={class:"cost-text"},St={class:"content-preview"},Ut={class:"action-buttons"},It={class:"pagination-section"},zt={key:0,class:"result-content"},Mt={class:"result-header"},Lt={class:"result-meta"},Ot={class:"content-display"},qt={class:"content-display"},Ft={class:"changes-display"},Nt={class:"change-header"},Wt={class:"change-confidence"},jt={class:"change-content"},Kt={class:"original-text"},Gt={class:"corrected-text"},Yt={class:"change-reason"},Ht=B(t({__name:"TaskListPanel",props:{chunks:{},aiConfig:{},loading:{type:Boolean}},emits:["start-proofreading","stop-proofreading","approve-chunk","reject-chunk","batch-operation"],setup(e,{emit:a}){const t=e,s=a,b=l(""),w=l(""),k=l([]),x=l(1),V=l(20),B=l(!1),D=l(null),E=l("original"),R=l(),A=n(()=>{let e=t.chunks;if(b.value){const a=b.value.toLowerCase();e=e.filter(e=>e.title.toLowerCase().includes(a)||e.content.toLowerCase().includes(a))}return w.value&&(e=e.filter(e=>e.status===w.value)),e}),$=n(()=>t.chunks.some(e=>"processing"===e.status));y(()=>t.chunks,()=>{k.value=k.value.filter(e=>t.chunks.some(a=>a.id===e.id))});const S=e=>A.value.filter(a=>a.status===e).length,U=e=>({pending:"待处理",processing:"处理中",completed:"已完成",failed:"失败",approved:"已通过",rejected:"已拒绝",paused:"已暂停"}[e]||e),I=e=>{if(!t.aiConfig.model)return"0.00";return(Math.ceil(e.wordCount/2)/1e3*t.aiConfig.model.costPerToken).toFixed(2)},z=e=>{k.value=e},M=async e=>{try{await T.confirm(`确定要通过分块"${e.title}"的校对结果吗？`,"确认通过",{confirmButtonText:"确定",cancelButtonText:"取消",type:"success"}),s("approve-chunk",e.id)}catch{}},L=async e=>{try{await T.confirm(`确定要拒绝分块"${e.title}"的校对结果吗？`,"确认拒绝",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),s("reject-chunk",e.id)}catch{}},O=async()=>{if(0!==k.value.length)try{await T.confirm(`确定要开始校对选中的 ${k.value.length} 个分块吗？`,"确认批量开始",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const e=k.value.map(e=>e.id);s("batch-operation",BatchOperationType.START_PROOFREADING,e)}catch{}else C.warning("请先选择要处理的分块")},q=async()=>{try{await T.confirm("确定要暂停所有正在处理的分块吗？","确认批量暂停",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=t.chunks.filter(e=>"processing"===e.status).map(e=>e.id);s("batch-operation",BatchOperationType.STOP_PROOFREADING,e)}catch{}},F=()=>{D.value&&(M(D.value),B.value=!1)},N=()=>{D.value&&(L(D.value),B.value=!1)},W=()=>{C.success("列表已刷新")},j=e=>{V.value=e,x.value=1},K=e=>{x.value=e};return(e,a)=>{const t=i("el-button"),l=i("el-icon"),n=i("el-input"),y=i("el-option"),T=i("el-select"),G=i("el-table-column"),Y=i("el-tag"),H=i("el-progress"),J=i("el-table"),X=i("el-pagination"),Q=i("el-scrollbar"),Z=i("el-tab-pane"),ee=i("el-tabs"),ae=i("el-dialog"),te=i("el-card");return u(),o(te,{class:"task-list-panel"},{header:r(()=>[c("div",mt,[a[8]||(a[8]=c("span",{class:"panel-title"},"校对任务列表",-1)),c("div",gt,[d(t,{type:"primary",size:"small",disabled:0===k.value.length,onClick:O},{default:r(()=>[g(" 批量开始 ("+m(k.value.length)+") ",1)]),_:1},8,["disabled"]),d(t,{type:"warning",size:"small",disabled:!$.value,onClick:q},{default:r(()=>a[7]||(a[7]=[g(" 批量暂停 ")])),_:1,__:[7]},8,["disabled"])])])]),default:r(()=>[c("div",ht,[c("div",ft,[c("div",yt,[d(n,{modelValue:b.value,"onUpdate:modelValue":a[0]||(a[0]=e=>b.value=e),placeholder:"搜索分块标题或内容...",size:"small",clearable:"",class:"search-input"},{prefix:r(()=>[d(l,null,{default:r(()=>[d(_(P))]),_:1})]),_:1},8,["modelValue"]),d(T,{modelValue:w.value,"onUpdate:modelValue":a[1]||(a[1]=e=>w.value=e),placeholder:"筛选状态",size:"small",clearable:"",class:"status-filter"},{default:r(()=>[d(y,{label:"全部状态",value:""}),d(y,{label:"待处理",value:"pending"}),d(y,{label:"处理中",value:"processing"}),d(y,{label:"已完成",value:"completed"}),d(y,{label:"失败",value:"failed"}),d(y,{label:"已通过",value:"approved"}),d(y,{label:"已拒绝",value:"rejected"})]),_:1},8,["modelValue"]),d(t,{type:"info",size:"small",onClick:W},{default:r(()=>a[9]||(a[9]=[g(" 刷新 ")])),_:1,__:[9]})])]),c("div",_t,[c("div",bt,[a[10]||(a[10]=c("span",{class:"stat-label"},"总数：",-1)),c("span",wt,m(A.value.length),1)]),c("div",kt,[a[11]||(a[11]=c("span",{class:"stat-label"},"待处理：",-1)),c("span",Ct,m(S("pending")),1)]),c("div",xt,[a[12]||(a[12]=c("span",{class:"stat-label"},"处理中：",-1)),c("span",Tt,m(S("processing")),1)]),c("div",Vt,[a[13]||(a[13]=c("span",{class:"stat-label"},"已完成：",-1)),c("span",Pt,m(S("completed")),1)]),c("div",Bt,[a[14]||(a[14]=c("span",{class:"stat-label"},"失败：",-1)),c("span",Dt,m(S("failed")),1)])]),c("div",Et,[d(J,{ref_key:"tableRef",ref:R,data:A.value,size:"small",stripe:"",border:"",height:"400px",onSelectionChange:z},{default:r(()=>[d(G,{type:"selection",width:"50"}),d(G,{prop:"index",label:"序号",width:"60"}),d(G,{prop:"title",label:"分块标题","min-width":"150"},{default:r(({row:e})=>{return[c("div",Rt,[c("span",At,m(e.title),1),d(Y,{type:(a=e.status,{pending:"info",processing:"warning",completed:"success",failed:"danger",approved:"success",rejected:"danger",paused:"warning"}[a]||"info"),size:"small",class:"status-tag"},{default:r(()=>[g(m(U(e.status)),1)]),_:2},1032,["type"])])];var a}),_:1}),d(G,{prop:"wordCount",label:"字数",width:"80"},{default:r(({row:e})=>[g(m(e.wordCount.toLocaleString()),1)]),_:1}),d(G,{label:"进度",width:"120"},{default:r(({row:e})=>{return[d(H,{percentage:e.progress,status:(a=e.status,"failed"===a?"exception":"completed"===a||"approved"===a?"success":void 0),"stroke-width":6,"text-inside":"",format:()=>`${e.progress}%`},null,8,["percentage","status","format"])];var a}),_:1}),d(G,{label:"预估成本",width:"80"},{default:r(({row:e})=>[c("span",$t,m(I(e))+"元",1)]),_:1}),d(G,{label:"内容预览","min-width":"200"},{default:r(({row:e})=>{return[c("div",St,m((a=e.content,a.length>100?a.substring(0,100)+"...":a)),1)];var a}),_:1}),d(G,{label:"操作",width:"280",fixed:"right"},{default:r(({row:e})=>[c("div",Ut,["pending"===e.status?(u(),o(t,{key:0,type:"primary",size:"small",onClick:a=>{s("start-proofreading",e.id)}},{default:r(()=>a[15]||(a[15]=[g(" 开始校对 ")])),_:2,__:[15]},1032,["onClick"])):v("",!0),"processing"===e.status?(u(),o(t,{key:1,type:"warning",size:"small",onClick:a=>{s("stop-proofreading",e.id)}},{default:r(()=>a[16]||(a[16]=[g(" 停止校对 ")])),_:2,__:[16]},1032,["onClick"])):v("",!0),"failed"===e.status?(u(),o(t,{key:2,type:"info",size:"small",onClick:a=>{s("start-proofreading",e.id)}},{default:r(()=>a[17]||(a[17]=[g(" 重试 ")])),_:2,__:[17]},1032,["onClick"])):v("",!0),"completed"===e.status?(u(),o(t,{key:3,type:"success",size:"small",onClick:a=>{return t=e,D.value=t,E.value="original",void(B.value=!0);var t}},{default:r(()=>a[18]||(a[18]=[g(" 查看结果 ")])),_:2,__:[18]},1032,["onClick"])):v("",!0),["completed"].includes(e.status)?(u(),o(t,{key:4,type:"success",size:"small",plain:"",onClick:a=>M(e)},{default:r(()=>a[19]||(a[19]=[g(" 通过 ")])),_:2,__:[19]},1032,["onClick"])):v("",!0),["completed"].includes(e.status)?(u(),o(t,{key:5,type:"danger",size:"small",plain:"",onClick:a=>L(e)},{default:r(()=>a[20]||(a[20]=[g(" 拒绝 ")])),_:2,__:[20]},1032,["onClick"])):v("",!0),d(t,{type:"info",size:"small",plain:"",onClick:a=>{return t=e,void C.info(`编辑分块: ${t.title}`);var t}},{default:r(()=>a[21]||(a[21]=[g(" 编辑 ")])),_:2,__:[21]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])]),c("div",It,[d(X,{"current-page":x.value,"onUpdate:currentPage":a[2]||(a[2]=e=>x.value=e),"page-size":V.value,"onUpdate:pageSize":a[3]||(a[3]=e=>V.value=e),"page-sizes":[10,20,50,100],total:A.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:j,onCurrentChange:K},null,8,["current-page","page-size","total"])])]),d(ae,{modelValue:B.value,"onUpdate:modelValue":a[6]||(a[6]=e=>B.value=e),title:"校对结果",width:"80%",top:"5vh"},{footer:r(()=>[d(t,{onClick:a[5]||(a[5]=e=>B.value=!1)},{default:r(()=>a[25]||(a[25]=[g("关闭")])),_:1,__:[25]}),D.value&&"completed"===D.value.status?(u(),o(t,{key:0,type:"success",onClick:F},{default:r(()=>a[26]||(a[26]=[g(" 通过 ")])),_:1,__:[26]})):v("",!0),D.value&&"completed"===D.value.status?(u(),o(t,{key:1,type:"danger",onClick:N},{default:r(()=>a[27]||(a[27]=[g(" 拒绝 ")])),_:1,__:[27]})):v("",!0)]),default:r(()=>[D.value?(u(),p("div",zt,[c("div",Mt,[c("h4",null,m(D.value.title),1),c("div",Lt,[c("span",null,"字数: "+m(D.value.wordCount),1),c("span",null,"进度: "+m(D.value.progress)+"%",1),c("span",null,"状态: "+m(U(D.value.status)),1)])]),d(ee,{modelValue:E.value,"onUpdate:modelValue":a[4]||(a[4]=e=>E.value=e),type:"border-card"},{default:r(()=>[d(Z,{label:"原始内容",name:"original"},{default:r(()=>[d(Q,{height:"400px"},{default:r(()=>[c("div",Ot,[c("pre",null,m(D.value.content),1)])]),_:1})]),_:1}),D.value.result?(u(),o(Z,{key:0,label:"校对结果",name:"result"},{default:r(()=>[d(Q,{height:"400px"},{default:r(()=>[c("div",qt,[c("pre",null,m(D.value.result.correctedContent),1)])]),_:1})]),_:1})):v("",!0),D.value.result?(u(),o(Z,{key:1,label:"修改对比",name:"changes"},{default:r(()=>[d(Q,{height:"400px"},{default:r(()=>[c("div",Ft,[(u(!0),p(h,null,f(D.value.result.changes,(e,t)=>{return u(),p("div",{key:t,class:"change-item"},[c("div",Nt,[d(Y,{type:(l=e.type,{"语法错误":"danger","拼写错误":"warning","标点符号":"info","风格优化":"success","逻辑调整":"primary"}[l]||"info"),size:"small"},{default:r(()=>[g(m(e.type),1)]),_:2},1032,["type"]),c("span",Wt,"置信度: "+m((100*e.confidence).toFixed(1))+"%",1)]),c("div",jt,[c("div",Kt,[a[22]||(a[22]=c("label",null,"原文:",-1)),c("span",null,m(e.original),1)]),c("div",Gt,[a[23]||(a[23]=c("label",null,"修改:",-1)),c("span",null,m(e.corrected),1)]),c("div",Yt,[a[24]||(a[24]=c("label",null,"原因:",-1)),c("span",null,m(e.reason),1)])])]);var l}),128))])]),_:1})]),_:1})):v("",!0)]),_:1},8,["modelValue"])])):v("",!0)]),_:1},8,["modelValue"])]),_:1})}}}),[["__scopeId","data-v-4032df4c"]]),Jt=b("aiProofreading",()=>{const e=new E,a=l(null),t=l(null),s=l(""),o=l([]),i=l({model:null,promptTemplate:null,costBudget:100,qualityRequirement:"medium"}),r=l({conversion:!1,splitting:!1,proofreading:!1,batch:!1}),u=l({totalChunks:0,completedChunks:0,processingChunks:0,failedChunks:0,overallProgress:0,estimatedTimeRemaining:0,lastUpdated:Date.now()}),c=l(null),d=l([]),p=n(()=>o.value.some(e=>"processing"===e.status)),v=n(()=>o.value.length>0&&i.value.model&&i.value.promptTemplate&&!r.value.batch),m=n(()=>o.value.filter(e=>["completed","approved"].includes(e.status)).length),g=n(()=>o.value.filter(e=>"failed"===e.status).length),h=n(()=>0===o.value.length?0:Math.round(m.value/o.value.length*100)),f=async(e,t)=>{try{const l=o.value.find(a=>a.id===e);if(!l)throw new Error("分块不存在");l.status="processing",l.progress=0,l.updatedAt=Date.now(),a.value||(a.value=await k(t)),await x(l,t),B(),C.success(`分块 "${l.title}" 校对完成`)}catch(l){const a=o.value.find(a=>a.id===e);throw a&&(a.status="failed",a.error=l instanceof Error?l.message:"校对失败",a.updatedAt=Date.now()),B(),l}},y=async e=>{const a=o.value.find(a=>a.id===e);if(!a)throw new Error("分块不存在");a.status="paused",a.updatedAt=Date.now(),B(),C.info(`分块 "${a.title}" 已暂停校对`)},_=async e=>{const a=o.value.find(a=>a.id===e);if(!a)throw new Error("分块不存在");a.status="approved",a.updatedAt=Date.now(),B(),await $()},b=async e=>{const a=o.value.find(a=>a.id===e);if(!a)throw new Error("分块不存在");a.status="rejected",a.updatedAt=Date.now(),B()},w=async(e,a)=>{try{r.value.batch=!0;for(const t of a){const a=o.value.find(e=>e.id===t);if(a)switch(e){case BatchOperationType.START_PROOFREADING:"pending"===a.status&&await f(t,i.value);break;case BatchOperationType.STOP_PROOFREADING:"processing"===a.status&&await y(t);break;case BatchOperationType.APPROVE:"completed"===a.status&&await _(t);break;case BatchOperationType.REJECT:"completed"===a.status&&await b(t)}}B()}catch(t){throw t}finally{r.value.batch=!1}},k=async a=>{try{if(!a.model||!a.promptTemplate)throw new Error("AI模型或提示词模板未配置");const t={aiModel:{id:a.model.id,name:a.model.name,provider:a.model.provider,maxTokens:a.model.maxTokens,costPerToken:a.model.costPerToken},promptTemplate:{id:a.promptTemplate.id,name:a.promptTemplate.name,content:a.promptTemplate.content,variables:a.promptTemplate.variables||[],category:a.promptTemplate.category,language:a.promptTemplate.language||"zh-CN",version:a.promptTemplate.version,createdAt:a.promptTemplate.createdAt,updatedAt:a.promptTemplate.updatedAt},costBudget:a.costBudget,qualityRequirement:a.qualityRequirement};return await e.initialize(R.CONTENT_REVIEW,t)}catch(t){throw new Error(`初始化AI会话失败: ${t instanceof Error?t.message:"未知错误"}`)}},x=async(t,l)=>{try{if(!a.value)throw new Error("AI会话未初始化");const s=e=>{t.progress=Math.round(e.overallProgress||0),t.updatedAt=Date.now()},n=await e.startProofreading(a.value,t.content,{enableProgress:!0,autoRetry:!0,maxRetries:3,timeout:6e4},s);t.result={chunkId:t.id,originalContent:t.content,correctedContent:n.correctedContent,changes:n.changes||[],suggestions:n.suggestions||[],confidence:n.confidence||.85,processingTime:n.processingTime||0,cost:n.cost||0,modelUsed:l.model?.name||"Unknown",timestamp:Date.now()},t.status="completed",t.progress=100,t.updatedAt=Date.now()}catch(s){await T(t,l)}},T=async(e,a)=>{for(let t=0;t<=100;t+=20)e.progress=t,await new Promise(e=>setTimeout(e,300));e.result={chunkId:e.id,originalContent:e.content,correctedContent:V(e.content),changes:P(e.content),suggestions:["建议1","建议2"],confidence:.85+.1*Math.random(),processingTime:1e3+2e3*Math.random(),cost:parseFloat((e.wordCount/1e3*(a.model?.costPerToken||.001)).toFixed(3)),modelUsed:a.model?.name||"Unknown",timestamp:Date.now()},e.status="completed",e.progress=100,e.updatedAt=Date.now()},V=e=>e.replace(/，/g,"，").replace(/。/g,"。").replace(/\s+/g," ").trim(),P=e=>{const a=[],t=["语法错误","拼写错误","标点符号","风格优化"],l=Math.floor(3*Math.random())+1;for(let s=0;s<l;s++){const l=t[Math.floor(Math.random()*t.length)],s=Math.floor(Math.random()*Math.max(1,e.length-10)),n=Math.min(s+5,e.length);a.push({original:e.substring(s,n),corrected:e.substring(s,n)+"(已修正)",type:l,reason:`${l}修正`,position:{start:s,end:n},confidence:.8+.2*Math.random(),severity:"medium"})}return a},B=()=>{u.value={totalChunks:o.value.length,completedChunks:m.value,processingChunks:o.value.filter(e=>"processing"===e.status).length,failedChunks:g.value,overallProgress:h.value,estimatedTimeRemaining:A(),lastUpdated:Date.now()}},A=()=>30*o.value.filter(e=>"pending"===e.status).length,$=async()=>{if(o.value.every(e=>["approved","rejected"].includes(e.status))&&t.value)try{await D.batchApprove({documentIds:[t.value.id],reason:"AI校对完成"}),C.success("所有分块已完成，文档已转入已预审状态")}catch(e){}},S=()=>o.value.sort((e,a)=>e.index-a.index).map(e=>e.result?.correctedContent||e.content).join("\n\n"),U=()=>{const e=o.value.reduce((e,a)=>e+(a.result?.processingTime||0),0),a=o.value.reduce((e,a)=>e+(a.result?.cost||0),0),t=o.value.reduce((e,a)=>e+(a.result?.changes.length||0),0);return{totalProcessingTime:e,averageProcessingTime:e/o.value.length,totalCost:a,averageCost:a/o.value.length,successRate:m.value/o.value.length,totalChanges:t,averageChanges:t/o.value.length,qualityScore:.85+.1*Math.random()}},I=()=>{if(!t.value)return"";const e=U();return`# ${t.value.title} - AI校对报告\n\n## 基本信息\n- 文档标题：${t.value.title}\n- 作者：${t.value.authorName}\n- 校对时间：${(new Date).toLocaleString("zh-CN")}\n\n## 校对统计\n- 总分块数：${o.value.length}\n- 成功处理：${m.value}\n- 失败处理：${g.value}\n- 成功率：${(100*e.successRate).toFixed(1)}%\n\n## 修改统计\n- 总修改数：${e.totalChanges}\n- 平均每块修改：${e.averageChanges.toFixed(1)}个\n\n## 成本统计\n- 总成本：${e.totalCost.toFixed(2)}元\n- 平均成本：${e.averageCost.toFixed(2)}元/块\n\n## 质量评分\n- 整体质量：${(100*e.qualityScore).toFixed(1)}分\n\n---\n*本报告由AI智能审校系统自动生成*`};return{currentDocument:t,convertedContent:s,documentChunks:o,aiConfig:i,loading:r,progress:u,resultSummary:c,errors:d,hasProcessingChunks:p,canStartBatchProofreading:v,completedChunksCount:m,failedChunksCount:g,overallProgress:h,setCurrentDocument:e=>{t.value=e,"text"!==e.type&&"md"!==e.type||(s.value=e.content)},setConvertedContent:e=>{s.value=e},setDocumentChunks:e=>{o.value=e,B()},updateAIConfig:e=>{i.value={...i.value,...e}},startChunkProofreading:f,stopChunkProofreading:y,approveChunk:_,rejectChunk:b,batchOperation:w,startBatchProofreading:async(e,a)=>{try{r.value.batch=!0;const t=3,l=e.filter(e=>"pending"===e.status);for(let e=0;e<l.length;e+=t){const s=l.slice(e,e+t).map(e=>f(e.id,a));await Promise.allSettled(s),await new Promise(e=>setTimeout(e,500))}B(),C.success("批量校对完成")}catch(t){throw t}finally{r.value.batch=!1}},pauseBatchProofreading:async()=>{const e=o.value.filter(e=>"processing"===e.status).map(e=>e.id);await w(BatchOperationType.STOP_PROOFREADING,e),C.info("批量校对已暂停")},exportResults:async()=>{try{if(!t.value)throw new Error("没有当前文档");const e={documentId:t.value.id,documentTitle:t.value.title,chunkResults:o.value,mergedContent:S(),statistics:U(),report:I(),completedAt:Date.now()};c.value=e,await new Promise(e=>setTimeout(e,1e3)),C.success("结果导出完成")}catch(e){throw e}},resetState:()=>{t.value=null,s.value="",o.value=[],i.value={model:null,promptTemplate:null,costBudget:100,qualityRequirement:"medium"},r.value={conversion:!1,splitting:!1,proofreading:!1,batch:!1},u.value={totalChunks:0,completedChunks:0,processingChunks:0,failedChunks:0,overallProgress:0,estimatedTimeRemaining:0,lastUpdated:Date.now()},c.value=null,d.value=[]}}}),Xt={class:"ai-proofreading-page"},Qt={class:"page-header"},Zt={class:"page-content"},el={class:"left-panel"},al={class:"right-panel"},tl={key:0,class:"page-footer"},ll={class:"footer-stats"},sl={class:"footer-actions"},nl=B(t({__name:"AIProofreadingPage",setup(t){const s=w(),m=k(),h=Jt(),f=l(null),_=l(""),b=l([]),x=l(!1),T=l(!1),V=l(!1),P=l({model:null,promptTemplate:null,costBudget:100,qualityRequirement:"medium"}),B=n(()=>"text"!==f.value?.type&&"md"!==f.value?.type),E=n(()=>b.value.filter(e=>"completed"===e.status).length),R=n(()=>b.value.filter(e=>"processing"===e.status).length),A=n(()=>b.value.filter(e=>"failed"===e.status).length),U=n(()=>b.value.length>0&&P.value?.model&&P.value?.promptTemplate&&!V.value),I=n(()=>R.value>0),z=()=>{m.push("/content-review/unreviewed")},M=async()=>{if(await $.showConfirmDialog(`确定要开始批量校对 ${b.value.length} 个文档吗？`,"确认批量校对",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"})){V.value=!0;try{await h.startBatchProofreading(b.value,P.value),C.success("批量校对已开始")}catch(e){C.error("开始批量校对失败")}finally{V.value=!1}}},L=async()=>{try{await h.pauseBatchProofreading(),C.success("批量校对已暂停")}catch(e){C.error("暂停批量校对失败")}},O=async()=>{try{await h.exportResults(),C.success("结果导出完成")}catch(e){C.error("导出结果失败")}};!function(t){const l=new S,s=[{key:"s",ctrl:!0,description:"开始批量校对",handler:t.startBatchProofreading,preventDefault:!0},{key:"p",ctrl:!0,description:"暂停批量校对",handler:t.pauseBatchProofreading,preventDefault:!0},{key:"e",ctrl:!0,description:"导出结果",handler:t.exportResults,preventDefault:!0},{key:"Escape",description:"返回上一页",handler:t.goBack},{key:"F5",description:"刷新页面",handler:t.refresh,preventDefault:!0},{key:"F1",description:"显示帮助",handler:t.showHelp,preventDefault:!0},{key:"a",ctrl:!0,description:"全选分块",handler:t.selectAll,preventDefault:!0},{key:"d",ctrl:!0,description:"取消选择",handler:t.deselectAll,preventDefault:!0}];s.forEach(e=>l.register(e)),e(()=>{document.addEventListener("keydown",l.handleKeyDown)}),a(()=>{document.removeEventListener("keydown",l.handleKeyDown)})}({startBatchProofreading:M,pauseBatchProofreading:L,exportResults:O,goBack:z,refresh:()=>window.location.reload(),showHelp:()=>(()=>{let e="键盘快捷键：\n\n";[{category:"校对操作",shortcuts:[{keys:"Ctrl + S",description:"开始批量校对"},{keys:"Ctrl + P",description:"暂停批量校对"},{keys:"Ctrl + E",description:"导出结果"}]},{category:"选择操作",shortcuts:[{keys:"Ctrl + A",description:"全选分块"},{keys:"Ctrl + D",description:"取消选择"}]},{category:"导航操作",shortcuts:[{keys:"Esc",description:"返回上一页"},{keys:"F5",description:"刷新页面"},{keys:"F1",description:"显示帮助"}]}].forEach(a=>{e+=`${a.category}：\n`,a.shortcuts.forEach(a=>{e+=`  ${a.keys} - ${a.description}\n`}),e+="\n"}),$.showInfo(e)})(),selectAll:()=>{$.showInfo("全选功能")},deselectAll:()=>{$.showInfo("取消选择功能")}});const q=async e=>{try{const a=await D.getDocumentDetail(e);f.value=a,"text"!==a.type&&"md"!==a.type||(_.value=a.content)}catch(a){C.error("加载文档失败")}},F=async()=>{await async function(e,a,t){try{return await e()}catch(l){return $.handleError(l,t),a}}(async()=>{const e=s.params.documentId;e?await q(e):($.showWarning("缺少文档ID参数"),m.push("/content-review/unreviewed"))},void 0,{action:"initializePage"})};e(async()=>{await F()}),y(()=>s.params.documentId,async e=>{e&&await q(e)},{immediate:!0});const N=e=>{f.value=e},W=e=>{P.value={...e}},j=e=>{_.value=e,C.success("文档转换完成")},K=e=>{C.error(`文档转换失败: ${e}`)},G=e=>{b.value=e,C.success(`文档拆分完成，共生成 ${e.length} 个子文档`)},Y=e=>{C.error(`文档拆分失败: ${e}`)},H=async e=>{try{await h.startChunkProofreading(e,P.value),C.success("开始校对")}catch(a){C.error("开始校对失败")}},J=async e=>{try{await h.stopChunkProofreading(e),C.success("已停止校对")}catch(a){C.error("停止校对失败")}},X=async e=>{try{await h.approveChunk(e),C.success("校对通过")}catch(a){C.error("校对通过失败")}},Q=async e=>{try{await h.rejectChunk(e),C.success("校对拒绝")}catch(a){C.error("校对拒绝失败")}},Z=async(e,a)=>{try{await h.batchOperation(e,a),C.success(`批量${e}完成`)}catch(t){C.error(`批量${e}失败`)}};return(e,a)=>{const t=i("el-breadcrumb-item"),l=i("el-breadcrumb"),s=i("el-statistic"),n=i("el-button");return u(),p("div",Xt,[c("div",Qt,[d(l,{separator:"/"},{default:r(()=>[d(t,{to:{path:"/content-review/unreviewed"}},{default:r(()=>a[4]||(a[4]=[g(" 未预审文档 ")])),_:1,__:[4]}),d(t,null,{default:r(()=>a[5]||(a[5]=[g("AI校对")])),_:1,__:[5]})]),_:1}),a[6]||(a[6]=c("h2",{class:"page-title"},"AI智能校对",-1))]),c("div",Zt,[c("div",el,[f.value?(u(),o(ce,{key:0,document:f.value,onUpdate:N},null,8,["document"])):v("",!0),P.value?(u(),o(Ye,{key:1,"ai-model":P.value.model,"onUpdate:aiModel":a[0]||(a[0]=e=>P.value.model=e),"prompt-template":P.value.promptTemplate,"onUpdate:promptTemplate":a[1]||(a[1]=e=>P.value.promptTemplate=e),"cost-budget":P.value.costBudget,"onUpdate:costBudget":a[2]||(a[2]=e=>P.value.costBudget=e),"quality-requirement":P.value.qualityRequirement,"onUpdate:qualityRequirement":a[3]||(a[3]=e=>P.value.qualityRequirement=e),onConfigChange:W},null,8,["ai-model","prompt-template","cost-budget","quality-requirement"])):v("",!0)]),c("div",al,[f.value&&B.value?(u(),o(Da,{key:0,document:f.value,loading:x.value,onConversionComplete:j,onConversionError:K},null,8,["document","loading"])):v("",!0),_.value?(u(),o(vt,{key:1,content:_.value,loading:T.value,onSplittingComplete:G,onSplittingError:Y},null,8,["content","loading"])):v("",!0),b.value.length>0?(u(),o(Ht,{key:2,chunks:b.value,"ai-config":P.value,loading:V.value,onStartProofreading:H,onStopProofreading:J,onApproveChunk:X,onRejectChunk:Q,onBatchOperation:Z},null,8,["chunks","ai-config","loading"])):v("",!0)])]),b.value.length>0?(u(),p("div",tl,[c("div",ll,[d(s,{title:"总文档数",value:b.value.length},null,8,["value"]),d(s,{title:"已完成",value:E.value},null,8,["value"]),d(s,{title:"进行中",value:R.value},null,8,["value"]),d(s,{title:"失败",value:A.value},null,8,["value"])]),c("div",sl,[d(n,{type:"primary",loading:V.value,disabled:!U.value,onClick:M},{default:r(()=>a[7]||(a[7]=[g(" 开始批量校对 ")])),_:1,__:[7]},8,["loading","disabled"]),d(n,{type:"warning",disabled:!I.value,onClick:L},{default:r(()=>a[8]||(a[8]=[g(" 暂停校对 ")])),_:1,__:[8]},8,["disabled"]),d(n,{type:"success",disabled:0===E.value,onClick:O},{default:r(()=>a[9]||(a[9]=[g(" 导出结果 ")])),_:1,__:[9]},8,["disabled"]),d(n,{onClick:z},{default:r(()=>a[10]||(a[10]=[g("返回")])),_:1,__:[10]})])])):v("",!0)])}}}),[["__scopeId","data-v-72bba91c"]]);export{nl as default};
