import{d as s,c as e,a,Q as n,I as t,ag as l,o}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as c}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const i={class:"existing-review-comments"},r={class:"content"},p=c(s({__name:"ExistingReviewComments",setup:s=>(s,c)=>{const p=l("el-card");return o(),e("div",i,[c[1]||(c[1]=a("div",{class:"page-header"},[a("h1",null,"已审查意见"),a("p",{class:"page-description"},"查看和管理已有的审查意见")],-1)),a("div",r,[n(p,null,{default:t(()=>c[0]||(c[0]=[a("p",null,"已审查意见管理功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-eb6e8280"]]);export{p as default};
