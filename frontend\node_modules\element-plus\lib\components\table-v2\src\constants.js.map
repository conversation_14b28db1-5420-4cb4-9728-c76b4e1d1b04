{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/table-v2/src/constants.ts"], "sourcesContent": ["export enum SortOrder {\n  ASC = 'asc',\n  DESC = 'desc',\n}\n\nexport enum Alignment {\n  CENTER = 'center',\n  RIGHT = 'right',\n}\n\nexport enum FixedDir {\n  LEFT = 'left',\n  RIGHT = 'right',\n}\n\nexport const oppositeOrderMap = {\n  [SortOrder.ASC]: SortOrder.DESC,\n  [SortOrder.DESC]: SortOrder.ASC,\n}\n\nexport const sortOrders = [SortOrder.ASC, SortOrder.DESC] as const\n"], "names": [], "mappings": ";;;;AAAU,IAAC,SAAS,mBAAmB,CAAC,CAAC,UAAU,KAAK;AACxD,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC5B,EAAE,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AAC9B,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,EAAE,SAAS,IAAI,EAAE,EAAE;AACV,IAAC,SAAS,mBAAmB,CAAC,CAAC,UAAU,KAAK;AACxD,EAAE,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AAClC,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AAChC,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,EAAE,SAAS,IAAI,EAAE,EAAE;AACV,IAAC,QAAQ,mBAAmB,CAAC,CAAC,SAAS,KAAK;AACtD,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AAC7B,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AAC/B,EAAE,OAAO,SAAS,CAAC;AACnB,CAAC,EAAE,QAAQ,IAAI,EAAE,EAAE;AACP,MAAC,gBAAgB,GAAG;AAChC,EAAE,CAAC,KAAK,aAAa,MAAM;AAC3B,EAAE,CAAC,MAAM,cAAc,KAAK;AAC5B,EAAE;AACU,MAAC,UAAU,GAAG,CAAC,KAAK,YAAY,MAAM;;;;;;;;"}