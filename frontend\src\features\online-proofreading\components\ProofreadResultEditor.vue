<template>
  <div class="proofread-result-editor">
    <!-- 编辑器工具栏 -->

    <!-- 编辑器容器 -->
    <div
      ref="editorContainer"
      class="editor-container"
      :class="{
        editable: editable,
        'track-changes': trackChanges,
        'has-changes': changes.length > 0,
      }"
    >
      <!-- WangEditor Vue3 组件 -->
      <div style="border: 1px solid #ccc">
        <Toolbar
          style="border-bottom: 1px solid #ccc"
          :editor="editorRef"
          :defaultConfig="toolbarConfig"
          mode="default"
        />
        <Editor
          :style="{ height: height, overflowY: 'hidden' }"
          v-model="valueHtml"
          :defaultConfig="editorConfig"
          mode="default"
          @onCreated="handleCreated"
          @onChange="handleChange"
          @onFocus="handleFocus"
          @onBlur="handleBlur"
        />
      </div>
      <div v-if="showToolbar" class="editor-toolbar">
        <div class="toolbar-left">
          <span class="editor-title">校对结果</span>

          <!-- 模式指示器 -->
          <el-tag
            :type="isReadOnly ? 'info' : isReviewMode ? 'warning' : 'success'"
            size="small"
            class="mode-indicator"
          >
            {{ isReadOnly ? '只读模式' : isReviewMode ? '审查模式' : '编辑模式' }}
          </el-tag>

          <el-tag v-if="changes.length > 0" type="success" size="small">
            {{ changes.length }} 处修改
          </el-tag>
          <el-tag v-if="hasUnsavedChanges" type="warning" size="small"> 未保存 </el-tag>
        </div>

        <div class="toolbar-right">
          <!-- 只读模式：只显示查看修改按钮 -->
          <el-button-group size="small" v-if="isReadOnly">
            <el-button @click="showChangesPanel" :disabled="changes.length === 0">
              <el-icon><List /></el-icon>
              查看修改 ({{ changes.length }})
            </el-button>
          </el-button-group>

          <!-- 审查模式：显示修改管理按钮 -->
          <el-button-group size="small" v-else-if="isReviewMode">
            <el-button @click="showChangesPanel" :disabled="changes.length === 0">
              <el-icon><List /></el-icon>
              查看修改
            </el-button>
            <el-button @click="acceptAllChanges" :disabled="changes.length === 0" type="success">
              <el-icon><Check /></el-icon>
              接受全部
            </el-button>
            <el-button @click="rejectAllChanges" :disabled="changes.length === 0" type="danger">
              <el-icon><Close /></el-icon>
              拒绝全部
            </el-button>
          </el-button-group>

          <!-- 编辑模式：显示所有按钮 -->
          <el-button-group size="small" v-else>
            <el-button @click="toggleTrackChanges" :type="trackChanges ? 'primary' : 'default'">
              <el-icon><Edit /></el-icon>
              {{ trackChanges ? '停止跟踪' : '跟踪修改' }}
            </el-button>
            <el-button @click="showChangesPanel" :disabled="changes.length === 0">
              <el-icon><List /></el-icon>
              查看修改
            </el-button>
            <el-button @click="acceptAllChanges" :disabled="changes.length === 0">
              <el-icon><Check /></el-icon>
              接受全部
            </el-button>
            <el-button @click="rejectAllChanges" :disabled="changes.length === 0">
              <el-icon><Close /></el-icon>
              拒绝全部
            </el-button>
          </el-button-group>

          <el-divider direction="vertical" />

          <el-button-group size="small">
            <el-button @click="undoChange" :disabled="!canUndo">
              <el-icon><RefreshLeft /></el-icon>
              撤销
            </el-button>
            <el-button @click="redoChange" :disabled="!canRedo">
              <el-icon><RefreshRight /></el-icon>
              重做
            </el-button>
          </el-button-group>

          <el-divider direction="vertical" />

          <el-button size="small" @click="saveContent" :loading="saving">
            <el-icon><Document /></el-icon>
            保存
          </el-button>
        </div>
      </div>
      <!-- 修改悬浮提示 -->
      <div
        v-if="hoveredChange"
        ref="changeTooltipRef"
        class="change-tooltip"
        :style="changeTooltipStyle"
      >
        <div class="tooltip-header">
          <span class="change-type">{{ getChangeTypeLabel(hoveredChange.type) }}</span>
          <span class="change-time">{{ formatTime(hoveredChange.timestamp) }}</span>
        </div>
        <div class="tooltip-content">
          <div v-if="hoveredChange.type === 'replace'" class="text-comparison">
            <div class="original-text"><strong>原文:</strong> {{ hoveredChange.original }}</div>
            <div class="modified-text"><strong>修改:</strong> {{ hoveredChange.modified }}</div>
          </div>
          <div v-else-if="hoveredChange.type === 'insert'" class="inserted-text">
            <strong>插入:</strong> {{ hoveredChange.modified }}
          </div>
          <div v-else-if="hoveredChange.type === 'delete'" class="deleted-text">
            <strong>删除:</strong> {{ hoveredChange.original }}
          </div>
          <div class="change-reason"><strong>原因:</strong> {{ hoveredChange.reason }}</div>
        </div>
        <div class="tooltip-actions">
          <el-button size="small" type="primary" @click="acceptChange(hoveredChange)">
            接受
          </el-button>
          <el-button size="small" @click="rejectChange(hoveredChange)"> 拒绝 </el-button>
        </div>
      </div>
    </div>

    <!-- 修改历史面板 -->
    <div v-if="showChangesHistory" class="changes-panel">
      <div class="panel-header">
        <span>修改历史 ({{ changes.length }})</span>
        <div class="panel-actions">
          <el-button size="small" @click="filterChanges">
            <el-icon><Filter /></el-icon>
            筛选
          </el-button>
          <el-button size="small" text @click="toggleChangesPanel">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>

      <div class="changes-list">
        <div
          v-for="change in filteredChanges"
          :key="change.id"
          class="change-item"
          :class="{
            active: hoveredChange?.id === change.id,
            [change.type]: true,
          }"
          @mouseenter="highlightChange(change)"
          @mouseleave="clearChangeHighlight"
          @click="scrollToChange(change)"
        >
          <div class="change-header">
            <el-tag :type="getChangeTagType(change.type)" size="small">
              {{ getChangeTypeLabel(change.type) }}
            </el-tag>
            <span class="change-time">{{ formatTime(change.timestamp) }}</span>
          </div>

          <div class="change-content">
            <div v-if="change.type === 'replace'" class="text-change">
              <div class="original">{{ change.original }}</div>
              <el-icon class="arrow"><ArrowRight /></el-icon>
              <div class="modified">{{ change.modified }}</div>
            </div>
            <div v-else-if="change.type === 'insert'" class="inserted">+ {{ change.modified }}</div>
            <div v-else-if="change.type === 'delete'" class="deleted">- {{ change.original }}</div>
            <div class="reason">{{ change.reason }}</div>
          </div>

          <div class="change-actions">
            <el-button-group size="small">
              <el-button type="primary" @click.stop="acceptChange(change)"> 接受 </el-button>
              <el-button @click.stop="rejectChange(change)"> 拒绝 </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改统计 -->
    <div v-if="showStatistics" class="changes-statistics">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="总修改数" :value="changes.length" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="替换" :value="replaceCount" />
          <!-- 调试信息 -->
          <div style="font-size: 10px; color: #999; margin-top: 4px">Debug: 替换类型统计</div>
        </el-col>
        <el-col :span="6">
          <el-statistic title="插入" :value="insertCount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="删除" :value="deleteCount" />
        </el-col>
      </el-row>
    </div>

    <!-- 筛选对话框 -->
    <el-dialog v-model="filterDialogVisible" title="筛选修改" width="400px">
      <el-form :model="filterForm" label-width="80px">
        <el-form-item label="修改类型">
          <el-checkbox-group v-model="filterForm.types">
            <el-checkbox label="replace">替换</el-checkbox>
            <el-checkbox label="insert">插入</el-checkbox>
            <el-checkbox label="delete">删除</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="resetFilter">重置</el-button>
        <el-button type="primary" @click="applyFilter">应用</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 校对结果编辑器组件
 *
 * 功能特性：
 * - 基于WangEditor的可编辑文本编辑器
 * - 修改跟踪和变更历史记录
 * - 修改的可视化标记和悬浮提示
 * - 修改接受/拒绝操作
 * - 撤销/重做功能
 * - 修改筛选和统计
 */

import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Edit,
  List,
  Check,
  Close,
  RefreshLeft,
  RefreshRight,
  Document,
  ArrowRight,
  Filter,
} from '@element-plus/icons-vue'

// 导入WangEditor Vue3组件
import '@wangeditor/editor/dist/css/style.css'
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'

// 导入类型定义
import type { ProofreadingChange, ChangeType } from '../types'

// 组件属性
interface Props {
  /** 文本内容 */
  content: string
  /** 修改列表 */
  changes: ProofreadingChange[]
  /** 是否可编辑 */
  editable?: boolean
  /** 编辑模式：'readonly' | 'review' | 'edit' */
  editMode?: 'readonly' | 'review' | 'edit'
  /** 是否显示工具栏 */
  showToolbar?: boolean
  /** 是否显示修改历史 */
  showChangesHistory?: boolean
  /** 是否显示统计信息 */
  showStatistics?: boolean
  /** 编辑器高度 */
  height?: string
  /** 是否自动保存 */
  autoSave?: boolean
  /** 自动保存延迟（毫秒） */
  autoSaveDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  editable: true,
  editMode: 'edit',
  showToolbar: true,
  showChangesHistory: false,
  showStatistics: false,
  height: '500px',
  autoSave: true,
  autoSaveDelay: 2000,
})

// 组件事件
interface Emits {
  (e: 'update:content', content: string): void
  (e: 'content-change', content: string): void
  (e: 'change-accept', change: ProofreadingChange): void
  (e: 'change-reject', change: ProofreadingChange): void
  (e: 'save', content: string): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const editorRef = ref<IDomEditor | null>(null)
const editorContainer = ref<HTMLElement>()
const changeTooltipRef = ref<HTMLElement>()
const valueHtml = ref('')
const trackChanges = ref(true)
const hoveredChange = ref<ProofreadingChange | null>(null)
const saving = ref(false)
const hasUnsavedChanges = ref(false)
const filterDialogVisible = ref(false)

// 修改历史和撤销重做
const changeHistory = ref<string[]>([])
const historyIndex = ref(-1)
const maxHistorySize = 50

// 悬浮提示样式
const changeTooltipStyle = reactive({
  left: '0px',
  top: '0px',
  display: 'none',
})

// 筛选表单
const filterForm = reactive({
  types: ['replace', 'insert', 'delete'],
  timeRange: null as [string, string] | null,
})

// 计算属性
const replaceCount = computed(() => {
  return props.changes.filter((c) => c.type === 'replace').length
})

const insertCount = computed(() => {
  return props.changes.filter((c) => c.type === 'insert').length
})

const deleteCount = computed(() => {
  return props.changes.filter((c) => c.type === 'delete').length
})

// 编辑器行为控制
const isReadOnly = computed(() => {
  return props.editMode === 'readonly' || !props.editable
})

const isReviewMode = computed(() => {
  return props.editMode === 'review'
})

const isEditMode = computed(() => {
  return props.editMode === 'edit' && props.editable
})

// 工具栏配置 - 简化为与 OriginalTextEditor 相同的配置
const toolbarConfig = computed(() => {
  const toolbarKeys = (() => {
    if (isReadOnly.value) {
      return ['fullScreen'] // 只读模式只保留全屏功能
    } else if (isReviewMode.value) {
      return ['undo', 'redo', '|', 'fullScreen'] // 审查模式只保留撤销重做和全屏
    } else {
      // 编辑模式 - 使用与 OriginalTextEditor 相同的简洁工具栏配置
      return [
        'headerSelect',
        'bold',
        'italic',
        'underline',
        'through',
        '|',
        'fontSize',
        'fontFamily',
        'lineHeight',
        'color',
        'bgColor',
        '|',
        'bulletedList',
        'numberedList',
        '|',
        'justifyLeft',
        'justifyCenter',
        'justifyRight',
        '|',
        'insertLink',
        'insertTable',
        '|',
        'undo',
        'redo',
        '|',
        'fullScreen',
      ]
    }
  })()

  return {
    toolbarKeys,
  }
})

// 编辑器配置
const editorConfig = computed(() => {
  return {
    placeholder: isReadOnly.value
      ? '校对结果（只读模式）'
      : isReviewMode.value
        ? '校对结果（审查模式 - 点击修改处接受/拒绝）'
        : '校对结果（编辑模式）',
    readOnly: isReadOnly.value,
    scroll: true,
    maxLength: 100000,
    MENU_CONF: {
      // 字体配置
      fontFamily: {
        fontFamilyList: [
          'PingFang SC',
          'Microsoft YaHei',
          'SimSun',
          'SimHei',
          'Arial',
          'Tahoma',
          'Verdana',
        ],
      },
      // 字号配置
      fontSize: {
        fontSizeList: ['12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px'],
      },
      // 颜色配置
      color: {
        colors: ['#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff'],
      },
    },
  }
})

const filteredChanges = computed(() => {
  let filtered = props.changes.filter((change) => filterForm.types.includes(change.type))

  if (filterForm.timeRange) {
    const [start, end] = filterForm.timeRange
    filtered = filtered.filter((change) => {
      const changeTime = change.timestamp.getTime()
      return changeTime >= new Date(start).getTime() && changeTime <= new Date(end).getTime()
    })
  }

  return filtered
})

const canUndo = computed(() => {
  return historyIndex.value > 0
})

const canRedo = computed(() => {
  return historyIndex.value < changeHistory.value.length - 1
})

// 自动保存定时器
let autoSaveTimer: NodeJS.Timeout | null = null

// 监听修改变化
watch(
  () => props.changes,
  () => {
    nextTick(() => {
      applyChangeMarkup()
    })
  },
  { deep: true },
)

// 监听内容变化
watch(
  () => props.content,
  (newContent) => {
    if (valueHtml.value !== newContent) {
      valueHtml.value = newContent
      nextTick(() => {
        applyChangeMarkup()
      })
    }
  },
)

// 监听 valueHtml 变化，同步到 props
watch(valueHtml, (newValue) => {
  if (newValue !== props.content) {
    emit('content-change', newValue)
  }
})

// Vue 组件事件处理
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor
  // 设置初始内容
  if (props.content) {
    valueHtml.value = props.content
  }
  nextTick(() => {
    applyChangeMarkup()
    // 设置精确的下拉菜单收起机制
    setupPreciseDropdownAutoHide()
  })
}

const handleChange = (editor: IDomEditor) => {
  const html = editor.getHtml()
  valueHtml.value = html

  // 在审查模式下，限制直接编辑
  if (isReviewMode.value) {
    console.log('审查模式下，请通过接受/拒绝修改来调整内容')
    return
  }

  // 自动保存
  if (props.autoSave) {
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer)
    }
    autoSaveTimer = setTimeout(() => {
      saveContent()
    }, props.autoSaveDelay)
  }

  hasUnsavedChanges.value = true
}

const handleFocus = () => {
  emit('focus')
}

const handleBlur = () => {
  emit('blur')
}

// 生命周期钩子
onMounted(() => {
  // Vue 组件会自动处理编辑器初始化
})

onUnmounted(() => {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }
  // 清理下拉菜单事件监听器
  cleanupDropdownEventListeners()
})

/**
 * 添加到历史记录
 */
const addToHistory = (content: string) => {
  // 移除当前位置之后的历史
  changeHistory.value = changeHistory.value.slice(0, historyIndex.value + 1)

  // 添加新内容
  changeHistory.value.push(content)
  historyIndex.value = changeHistory.value.length - 1

  // 限制历史记录大小
  if (changeHistory.value.length > maxHistorySize) {
    changeHistory.value.shift()
    historyIndex.value--
  }
}

/**
 * 计划自动保存
 */
const scheduleAutoSave = (content: string) => {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }

  autoSaveTimer = setTimeout(() => {
    saveContent(content)
  }, props.autoSaveDelay)
}

/**
 * 应用修改标记
 */
const applyChangeMarkup = () => {
  if (!editorRef.value || !trackChanges.value) return

  try {
    let html = props.content

    // 按位置倒序排序，避免位置偏移
    const sortedChanges = [...props.changes].sort((a, b) => b.position.start - a.position.start)

    // 为每个修改添加标记
    sortedChanges.forEach((change) => {
      const { start, end } = change.position
      const className = getChangeClassName(change)

      if (change.type === 'replace') {
        const originalText = html.substring(start, end)
        const markedText = `<span class="${className}" data-change-id="${change.id}" data-original="${originalText}" data-modified="${change.modified}">${change.modified}</span>`
        html = html.substring(0, start) + markedText + html.substring(end)
      } else if (change.type === 'insert') {
        const markedText = `<span class="${className}" data-change-id="${change.id}" data-modified="${change.modified}">${change.modified}</span>`
        html = html.substring(0, start) + markedText + html.substring(start)
      } else if (change.type === 'delete') {
        const markedText = `<span class="${className}" data-change-id="${change.id}" data-original="${change.original}" style="text-decoration: line-through; opacity: 0.6;">${change.original}</span>`
        html = html.substring(0, start) + markedText + html.substring(end)
      }
    })

    valueHtml.value = html

    // 绑定修改事件
    bindChangeEvents()
  } catch (error) {
    console.error('应用修改标记失败:', error)
  }
}

/**
 * 获取修改样式类名
 */
const getChangeClassName = (change: ProofreadingChange): string => {
  const baseClass = 'change-mark'
  const typeClass = `change-${change.type}`

  return `${baseClass} ${typeClass}`
}

/**
 * 绑定修改事件
 */
const bindChangeEvents = () => {
  if (!editorRef.value) return

  // 获取编辑器的 DOM 容器
  const editorDom = editorRef.value.getEditableContainer()
  if (!editorDom) return

  const changeElements = editorDom.querySelectorAll('.change-mark')

  changeElements.forEach((element) => {
    element.addEventListener('mouseenter', handleChangeHover)
    element.addEventListener('mouseleave', handleChangeLeave)
    element.addEventListener('click', handleChangeClick)
  })
}

/**
 * 处理修改悬停
 */
const handleChangeHover = (event: Event) => {
  const element = event.target as HTMLElement
  const changeId = element.getAttribute('data-change-id')

  if (changeId) {
    const change = props.changes.find((c) => c.id === changeId)
    if (change) {
      hoveredChange.value = change
      showChangeTooltip(event as MouseEvent)
    }
  }
}

/**
 * 处理修改离开
 */
const handleChangeLeave = () => {
  hideChangeTooltip()
}

/**
 * 处理修改点击
 */
const handleChangeClick = (event: Event) => {
  event.preventDefault()
  const element = event.target as HTMLElement
  const changeId = element.getAttribute('data-change-id')

  if (changeId) {
    const change = props.changes.find((c) => c.id === changeId)
    if (change) {
      // 显示修改详情或操作菜单
      showChangeActions(change, event as MouseEvent)
    }
  }
}

/**
 * 显示修改提示框
 */
const showChangeTooltip = (event: MouseEvent) => {
  if (!changeTooltipRef.value) return

  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const containerRect = editorContainer.value?.getBoundingClientRect()

  if (containerRect) {
    changeTooltipStyle.left = `${rect.left - containerRect.left}px`
    changeTooltipStyle.top = `${rect.bottom - containerRect.top + 5}px`
    changeTooltipStyle.display = 'block'
  }
}

/**
 * 隐藏修改提示框
 */
const hideChangeTooltip = () => {
  changeTooltipStyle.display = 'none'
  hoveredChange.value = null
}

/**
 * 显示修改操作
 */
const showChangeActions = (change: ProofreadingChange, event: MouseEvent) => {
  // TODO: 实现修改操作菜单
  console.log('显示修改操作:', change)
}

/**
 * 切换修改跟踪
 */
const toggleTrackChanges = () => {
  trackChanges.value = !trackChanges.value
  if (trackChanges.value) {
    applyChangeMarkup()
  } else {
    valueHtml.value = props.content
  }
  ElMessage.info(trackChanges.value ? '修改跟踪已开启' : '修改跟踪已关闭')
}

/**
 * 显示修改面板
 */
const showChangesPanel = () => {
  // 这里应该通过emit通知父组件
  ElMessage.info('显示修改面板')
}

/**
 * 切换修改面板
 */
const toggleChangesPanel = () => {
  // 这里应该通过emit通知父组件
}

/**
 * 接受所有修改
 */
const acceptAllChanges = async () => {
  try {
    await ElMessageBox.confirm(`确定要接受所有 ${props.changes.length} 处修改吗？`, '确认接受', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    props.changes.forEach((change) => {
      emit('change-accept', change)
    })

    ElMessage.success('已接受所有修改')
  } catch {
    // 用户取消操作
  }
}

/**
 * 拒绝所有修改
 */
const rejectAllChanges = async () => {
  try {
    await ElMessageBox.confirm(`确定要拒绝所有 ${props.changes.length} 处修改吗？`, '确认拒绝', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    props.changes.forEach((change) => {
      emit('change-reject', change)
    })

    ElMessage.success('已拒绝所有修改')
  } catch {
    // 用户取消操作
  }
}

/**
 * 撤销修改
 */
const undoChange = () => {
  if (canUndo.value) {
    historyIndex.value--
    const content = changeHistory.value[historyIndex.value]
    valueHtml.value = content
    emit('update:content', content)
    ElMessage.info('已撤销')
  }
}

/**
 * 重做修改
 */
const redoChange = () => {
  if (canRedo.value) {
    historyIndex.value++
    const content = changeHistory.value[historyIndex.value]
    valueHtml.value = content
    emit('update:content', content)
    ElMessage.info('已重做')
  }
}

/**
 * 保存内容
 */
const saveContent = async (content?: string) => {
  try {
    saving.value = true
    const contentToSave = content || valueHtml.value || ''

    emit('save', contentToSave)
    hasUnsavedChanges.value = false

    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 接受修改
 */
const acceptChange = (change: ProofreadingChange) => {
  emit('change-accept', change)
  hideChangeTooltip()
}

/**
 * 拒绝修改
 */
const rejectChange = (change: ProofreadingChange) => {
  emit('change-reject', change)
  hideChangeTooltip()
}

/**
 * 高亮修改
 */
const highlightChange = (change: ProofreadingChange) => {
  hoveredChange.value = change
  // TODO: 滚动到修改位置并高亮
}

/**
 * 清除修改高亮
 */
const clearChangeHighlight = () => {
  hoveredChange.value = null
}

/**
 * 滚动到修改
 */
const scrollToChange = (change: ProofreadingChange) => {
  // TODO: 实现滚动到修改位置
  console.log('滚动到修改:', change)
}

/**
 * 筛选修改
 */
const filterChanges = () => {
  filterDialogVisible.value = true
}

/**
 * 应用筛选
 */
const applyFilter = () => {
  filterDialogVisible.value = false
  ElMessage.success('筛选已应用')
}

/**
 * 重置筛选
 */
const resetFilter = () => {
  filterForm.types = ['replace', 'insert', 'delete']
  filterForm.timeRange = null
}

/**
 * 获取修改类型标签
 */
const getChangeTypeLabel = (type: ChangeType): string => {
  const labels = {
    replace: '替换',
    insert: '插入',
    delete: '删除',
    move: '移动',
  }
  return labels[type] || type
}

/**
 * 获取修改标签类型
 */
const getChangeTagType = (type: ChangeType): string => {
  const types = {
    replace: 'primary',
    insert: 'success',
    delete: 'danger',
    move: 'warning',
  }
  return types[type] || 'default'
}

/**
 * 格式化时间
 */
const formatTime = (date: Date): string => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

/**
 * 设置精确的下拉菜单自动收起机制
 * 针对 wangEditor v5.1.23 的特定实现
 */
const setupPreciseDropdownAutoHide = () => {
  if (!editorContainer.value) return

  // 延迟执行，确保编辑器完全初始化
  setTimeout(() => {
    const toolbarElement = editorContainer.value?.querySelector('.w-e-toolbar')
    if (!toolbarElement) return

    // 监听工具栏的点击事件
    toolbarElement.addEventListener('click', handleToolbarMenuClick, true)

    // 监听全局点击事件（用于点击外部收起）
    document.addEventListener('click', handleGlobalClickForDropdown, true)

    // 监听 ESC 键事件
    document.addEventListener('keydown', handleEscKeyForDropdown, true)

    // 设置 MutationObserver 监听下拉菜单的出现
    setupDropdownObserver()
  }, 100)
}

/**
 * 处理下拉菜单项点击事件
 */
const handleDropdownItemClick = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target || !editorRef.value) return

  // 检查是否是可选择的菜单项
  const isSelectableItem =
    target.tagName === 'LI' ||
    target.classList.contains('w-e-color') ||
    target.classList.contains('w-e-color-item') ||
    target.hasAttribute('data-value') ||
    target.closest('li[data-value]') ||
    target.closest('.w-e-color')

  if (isSelectableItem) {
    // 立即收起下拉菜单
    setTimeout(() => {
      try {
        // 尝试多种方法收起下拉菜单
        editorRef.value?.hidePanelOrModal()

        // 如果上面的方法不起作用，尝试直接移除下拉菜单元素
        const dropdowns = document.querySelectorAll(
          '.w-e-panel-container, .w-e-color-panel, .w-e-select-list, .w-e-drop-panel',
        )
        dropdowns.forEach((dropdown) => {
          if (dropdown.parentNode) {
            dropdown.parentNode.removeChild(dropdown)
          }
        })
      } catch (error) {
        // 静默处理错误
      }
    }, 50) // 减少延迟时间，更快收起
  }
}

/**
 * 设置下拉菜单观察器
 */
const setupDropdownObserver = () => {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement

          // 检查是否是下拉菜单相关的元素
          if (
            element.classList?.contains('w-e-panel-container') ||
            element.classList?.contains('w-e-color-panel') ||
            element.classList?.contains('w-e-select-list') ||
            element.classList?.contains('w-e-drop-panel')
          ) {
            // 为新出现的下拉菜单添加点击监听器
            element.addEventListener('click', handleDropdownItemClick, true)
          }
        }
      })
    })
  })

  // 观察整个文档的变化
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })

  // 保存观察器引用以便清理
  dropdownObserver.value = observer
}

// 下拉菜单观察器引用
const dropdownObserver = ref<MutationObserver | null>(null)

// 下拉菜单状态跟踪
const dropdownState = ref({
  isOpen: false,
  lastInteraction: 0,
  autoCloseTimer: null as NodeJS.Timeout | null,
})

// 移除重复的函数定义，已在上面定义

/**
 * 处理工具栏菜单点击事件
 */
const handleToolbarMenuClick = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target || !editorRef.value) return

  // 更精确的选择器，针对字号和颜色选择器
  const selectors = [
    '.w-e-select-list li', // 字号、字体等下拉列表项
    '.w-e-color-panel .w-e-color-item', // 颜色选择器中的颜色项
    '.w-e-color-panel .w-e-color', // 颜色面板中的颜色块
    '.w-e-panel-container li', // 面板容器中的列表项
    '.w-e-drop-panel li', // 下拉面板中的列表项
    '[data-value]', // 带有 data-value 属性的选项
  ]

  // 检查是否点击了任何下拉菜单选项
  const isDropdownOption = selectors.some((selector) => target.closest(selector))

  if (isDropdownOption) {
    // 延迟收起，确保选择操作完成
    setTimeout(() => {
      try {
        editorRef.value?.hidePanelOrModal()
      } catch (error) {
        // 静默处理错误
      }
    }, 150) // 增加延迟时间确保操作完成
  }
}

/**
 * 处理全局点击事件 - 点击外部收起下拉菜单
 */
const handleGlobalClickForDropdown = (event: Event) => {
  if (!editorRef.value || !editorContainer.value) return

  const target = event.target as HTMLElement
  if (!target) return

  // 检查点击是否在编辑器容器外部
  if (!editorContainer.value.contains(target)) {
    try {
      editorRef.value.hidePanelOrModal()
    } catch (error) {
      // 静默处理错误
    }
  }
}

/**
 * 处理 ESC 键事件
 */
const handleEscKeyForDropdown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && editorRef.value) {
    try {
      editorRef.value.hidePanelOrModal()
    } catch (error) {
      // 静默处理错误
    }
  }
}

/**
 * 清理下拉菜单事件监听器
 */
const cleanupDropdownEventListeners = () => {
  const toolbarElement = editorContainer.value?.querySelector('.w-e-toolbar')
  if (toolbarElement) {
    toolbarElement.removeEventListener('click', handleToolbarMenuClick, true)
  }
  document.removeEventListener('click', handleGlobalClickForDropdown, true)
  document.removeEventListener('keydown', handleEscKeyForDropdown, true)

  // 清理 MutationObserver
  if (dropdownObserver.value) {
    dropdownObserver.value.disconnect()
    dropdownObserver.value = null
  }
}
</script>

<style scoped>
.proofread-result-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 2px solid #409eff;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border-bottom: 1px solid #1890ff;
  flex-shrink: 0;
  color: white;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-title {
  font-weight: 600;
  color: white;
  font-size: 16px;
}

.editor-title::before {
  content: '📝 ';
  margin-right: 4px;
}

.mode-indicator {
  margin-left: 8px;
  font-weight: 500;
}

.editor-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #fafcff;
}

.wang-editor {
  height: 100%;
  background: #fafcff;
}

/* WangEditor 内容区域字体优化 - 校对结果专用样式 */
.wang-editor :deep(.w-e-text-container) {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #1f2937;
  background-color: #fafcff;
  border: 1px solid #e1f5fe;
  border-radius: 6px;
  margin: 8px;
  padding: 16px;
  box-shadow: inset 0 2px 4px rgba(64, 158, 255, 0.05);
}

.wang-editor :deep(.w-e-text-container p) {
  margin: 8px 0;
  font-size: 16px;
  line-height: 1.6;
  color: #303133;
}

.wang-editor :deep(.w-e-text-container [data-slate-editor]) {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #303133;
}

/* 工具栏字体优化 - 校对结果专用样式 */
.wang-editor :deep(.w-e-toolbar) {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  border-bottom: 1px solid #1890ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  position: relative;
  z-index: 100;
  border-radius: 6px 6px 0 0;
  margin: 8px 8px 0 8px;
}

/* 确保工具栏弹出框在最顶层 - 采用与 OriginalTextEditor 相同的简洁样式 */
.wang-editor :deep(.w-e-toolbar .w-e-menu) {
  position: relative;
  z-index: 9999;
}

/* 工具栏下拉菜单和弹出框 */
.wang-editor :deep(.w-e-toolbar .w-e-menu .w-e-panel-container) {
  z-index: 9999 !important;
  position: absolute !important;
}

/* 颜色选择器弹出框 */
.wang-editor :deep(.w-e-toolbar .w-e-color-panel) {
  z-index: 9999 !important;
  position: absolute !important;
}

/* 字体选择器弹出框 */
.wang-editor :deep(.w-e-toolbar .w-e-select-list) {
  z-index: 9999 !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #e4e7ed !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 所有工具栏弹出元素 */
.wang-editor :deep(.w-e-toolbar [data-w-e-type='panel']) {
  z-index: 9999 !important;
  position: absolute !important;
}

/* 工具栏按钮悬浮状态 */
.wang-editor :deep(.w-e-toolbar .w-e-menu:hover) {
  z-index: 9999;
}

/* 校对结果编辑器特殊状态样式 */
.editor-container.editable .wang-editor {
  background: #fafcff;
  position: relative;
}

.editor-container.editable .wang-editor::before {
  content: 'AI 校对结果';
  position: absolute;
  top: -2px;
  right: 8px;
  background: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 0 0 6px 6px;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
}

.editor-container.track-changes .wang-editor {
  border-left: 4px solid #52c41a;
  box-shadow: inset 4px 0 8px rgba(82, 196, 26, 0.1);
}

.editor-container.has-changes .wang-editor {
  background: #f0f8ff;
  border: 2px dashed #409eff;
}

.change-tooltip {
  position: absolute;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  max-width: 350px;
  z-index: 1000;
  font-size: 14px;
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.change-type {
  font-weight: 500;
  color: #409eff;
}

.change-time {
  font-size: 12px;
  color: #909399;
}

.tooltip-content {
  margin-bottom: 12px;
}

.original-text,
.modified-text,
.inserted-text,
.deleted-text,
.change-reason {
  margin-bottom: 6px;
  line-height: 1.4;
}

.text-comparison .original-text {
  color: #f56c6c;
}

.text-comparison .modified-text {
  color: #67c23a;
}

.tooltip-actions {
  display: flex;
  gap: 6px;
}

.changes-panel {
  border-top: 1px solid #e4e7ed;
  background: #fafafa;
  max-height: 400px;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.changes-list {
  padding: 8px;
}

.change-item {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.change-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.change-item.active {
  border-color: #409eff;
  background: #e6f7ff;
}

.change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.change-content {
  margin-bottom: 12px;
}

.text-change {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.original {
  color: #f56c6c;
  text-decoration: line-through;
}

.modified {
  color: #67c23a;
  font-weight: 500;
}

.inserted {
  color: #67c23a;
  font-weight: 500;
}

.deleted {
  color: #f56c6c;
  text-decoration: line-through;
}

.arrow {
  color: #909399;
}

.reason {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.change-actions {
  display: flex;
  justify-content: flex-end;
}

.changes-statistics {
  padding: 16px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
}
</style>

<!-- 全局样式 - 修改标记 -->
<style>
.change-mark {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 2px;
  padding: 1px 2px;
}

.change-mark:hover {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
}

/* 替换修改 - 蓝色背景 */
.change-replace {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

/* 插入修改 - 绿色背景 */
.change-insert {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

/* 删除修改 - 红色背景 */
.change-delete {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  text-decoration: line-through;
  opacity: 0.7;
}

/* 移动修改 - 黄色背景 */
.change-move {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}
</style>

<script lang="ts">
import { defineComponent } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

export default defineComponent({
  name: 'ProofreadResultEditor',
  components: {
    Editor,
    Toolbar,
  },
})
</script>
