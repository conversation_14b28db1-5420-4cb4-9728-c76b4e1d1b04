<template>
  <div class="test-dropdown-fix">
    <div class="test-header">
      <h2>wangEditor 下拉菜单收起功能修复验证</h2>
      <div class="status-indicator">
        <span class="status-label">修复状态:</span>
        <span :class="['status-badge', testStatus]">{{ testStatusText }}</span>
      </div>
    </div>

    <div class="test-section">
      <h3>修复后的 ProofreadResultEditor</h3>
      <div class="editor-wrapper">
        <ProofreadResultEditor
          :content="testContent"
          :changes="[]"
          :editable="true"
          edit-mode="edit"
          :show-toolbar="true"
          height="300px"
          @content-change="handleContentChange"
        />
      </div>
    </div>

    <div class="test-instructions">
      <h3>测试步骤</h3>
      <div class="test-steps">
        <div class="step">
          <span class="step-number">1</span>
          <div class="step-content">
            <strong>测试字体选择下拉菜单：</strong>
            <p>点击工具栏中的字体选择按钮，选择一个字体，观察菜单是否自动收起</p>
          </div>
        </div>

        <div class="step">
          <span class="step-number">2</span>
          <div class="step-content">
            <strong>测试颜色选择器：</strong>
            <p>点击文字颜色按钮，选择一个颜色，观察菜单是否自动收起</p>
          </div>
        </div>

        <div class="step">
          <span class="step-number">3</span>
          <div class="step-content">
            <strong>测试点击外部收起：</strong>
            <p>打开任意下拉菜单，然后点击编辑器外部区域，观察菜单是否收起</p>
          </div>
        </div>

        <div class="step">
          <span class="step-number">4</span>
          <div class="step-content">
            <strong>测试 ESC 键收起：</strong>
            <p>打开任意下拉菜单，然后按 ESC 键，观察菜单是否收起</p>
          </div>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h3>调试信息</h3>
      <div class="debug-console">
        <div v-if="debugInfo.length === 0" class="debug-empty">暂无调试信息...</div>
        <div v-for="(info, index) in debugInfo" :key="index" class="debug-item">
          {{ info }}
        </div>
      </div>
    </div>

    <div class="fix-summary">
      <h3>修复要点总结</h3>
      <ul>
        <li>
          <strong>精确的事件监听器：</strong>使用 MutationObserver
          监听下拉菜单的出现，并为其添加精确的点击事件处理
        </li>
        <li>
          <strong>多重选择器匹配：</strong
          >针对字号选择器、颜色选择器等不同类型的下拉菜单使用不同的选择器
        </li>
        <li>
          <strong>延迟收起机制：</strong>增加适当的延迟时间（150ms）确保用户的选择操作能够完成
        </li>
        <li>
          <strong>全面的事件覆盖：</strong
          >同时处理工具栏点击、全局点击和键盘事件，确保所有收起场景都被覆盖
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ProofreadResultEditor from '@/features/online-proofreading/components/ProofreadResultEditor.vue'

// 测试内容
const testContent = ref(`
<h2>下拉菜单收起功能测试</h2>
<p>这是一段测试文本，用于验证 <strong>ProofreadResultEditor</strong> 组件的下拉菜单自动收起功能是否正常工作。</p>
<p>请使用工具栏中的各种下拉菜单进行测试：</p>
<ul>
  <li>字体选择下拉菜单</li>
  <li>字号选择下拉菜单</li>
  <li>文字颜色选择器</li>
  <li>背景颜色选择器</li>
  <li>标题选择下拉菜单</li>
</ul>
<p>如果修复成功，所有下拉菜单都应该能够正确自动收起。</p>
`)

// 测试状态
const testStatus = ref('testing')
const testStatusText = computed(() => {
  switch (testStatus.value) {
    case 'testing':
      return '测试中'
    case 'success':
      return '修复成功'
    case 'failed':
      return '修复失败'
    default:
      return '未知状态'
  }
})

// 事件处理
const handleContentChange = (content: string) => {
  console.log('内容变化:', content)
}

// 添加调试信息
const debugInfo = ref<string[]>([])

const addDebugInfo = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  debugInfo.value.unshift(`[${timestamp}] ${message}`)
  if (debugInfo.value.length > 10) {
    debugInfo.value = debugInfo.value.slice(0, 10)
  }
}

// 监听页面点击事件来调试
document.addEventListener('click', (event) => {
  const target = event.target as HTMLElement
  if (
    target.closest('.w-e-select-list li') ||
    target.closest('.w-e-color-panel .w-e-color') ||
    target.closest('.w-e-color-panel .w-e-color-item')
  ) {
    addDebugInfo(`点击了下拉菜单项: ${target.textContent || target.className}`)
  }
})

// 模拟测试状态更新
setTimeout(() => {
  addDebugInfo('页面加载完成，开始测试')
}, 1000)
</script>

<style scoped>
.test-dropdown-fix {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.test-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-label {
  font-size: 14px;
  opacity: 0.9;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.testing {
  background: #fbbf24;
  color: #92400e;
}

.status-badge.success {
  background: #34d399;
  color: #065f46;
}

.status-badge.failed {
  background: #f87171;
  color: #991b1b;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  color: #374151;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.editor-wrapper {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-instructions {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.test-instructions h3 {
  color: #1e293b;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.test-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content strong {
  color: #1e293b;
  display: block;
  margin-bottom: 5px;
}

.step-content p {
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.fix-summary {
  background: #ecfdf5;
  border: 1px solid #a7f3d0;
  border-radius: 8px;
  padding: 20px;
}

.fix-summary h3 {
  color: #065f46;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.fix-summary ul {
  margin: 0;
  padding-left: 20px;
}

.fix-summary li {
  color: #047857;
  margin-bottom: 10px;
  line-height: 1.6;
}

.fix-summary li strong {
  color: #064e3b;
}

.debug-section {
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.debug-section h3 {
  color: #334155;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.debug-console {
  background: #1e293b;
  color: #e2e8f0;
  border-radius: 6px;
  padding: 15px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  max-height: 200px;
  overflow-y: auto;
}

.debug-empty {
  color: #64748b;
  font-style: italic;
}

.debug-item {
  margin-bottom: 5px;
  padding: 2px 0;
  border-bottom: 1px solid #334155;
}

.debug-item:last-child {
  border-bottom: none;
}
</style>
