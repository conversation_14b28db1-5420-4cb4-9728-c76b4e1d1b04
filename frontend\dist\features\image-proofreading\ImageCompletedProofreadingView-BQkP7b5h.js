import{M as e,x as a,p as l,R as t}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as u,r,b as o,m as n,c as i,a as s,Q as d,I as c,ag as p,o as v,u as m,M as f,J as g,aq as _,H as w,O as h,K as y}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as b}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const C={class:"image-completed-proofreading"},x={class:"content"},T={class:"card-header"},V={class:"header-actions"},k={class:"file-name"},U={class:"pagination-container"},z={key:0,class:"details-content"},S={class:"image-preview"},q=["src"],R={class:"image-info"},O={class:"proofreading-result"},j={class:"result-actions",style:{"margin-top":"15px"}},Y={class:"dialog-footer"},M=b(u({__name:"ImageCompletedProofreadingView",setup(u){const b=r(!1),M=r([]),B=r([]),L=r(""),D=r(""),I=r(""),$=r([]),P=r(1),F=r(20),K=r(0),E=r(0),H=r(0),J=r(0),Q=r(!1),A=r(null),G=r(!1),N=r(""),W=o(()=>{let e=M.value;return L.value&&(e=e.filter(e=>e.name.toLowerCase().includes(L.value.toLowerCase()))),D.value&&(e=e.filter(e=>e.quality===D.value)),I.value&&(e=e.filter(e=>e.proofreader===I.value)),$.value&&2===$.value.length&&(e=e.filter(e=>{const a=new Date(e.completedTime).toISOString().split("T")[0];return a>=$.value[0]&&a<=$.value[1]})),e});n(()=>{X(),Z()});const X=async()=>{b.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),M.value=[{id:1,name:"contract_scan_001.jpg",originalSize:2048576,completedTime:"2024-01-15 14:30:00",proofreader:"张三",quality:"excellent",wordCount:1250,accuracy:98.5,processingTime:"5分30秒",dimensions:"1920×1080",imageUrl:"/api/files/preview/1",ocrText:"这是合同扫描件的OCR识别结果..."},{id:2,name:"report_page_02.png",originalSize:1536e3,completedTime:"2024-01-15 11:20:00",proofreader:"李四",quality:"good",wordCount:890,accuracy:95.2,processingTime:"3分45秒",dimensions:"1600×900",imageUrl:"/api/files/preview/2",ocrText:"这是报告页面的OCR识别结果..."},{id:3,name:"invoice_202401_003.jpg",originalSize:3072e3,completedTime:"2024-01-14 16:45:00",proofreader:"王五",quality:"excellent",wordCount:456,accuracy:99.1,processingTime:"2分15秒",dimensions:"2048×1536",imageUrl:"/api/files/preview/3",ocrText:"这是发票的OCR识别结果..."},{id:4,name:"document_scan_004.png",originalSize:256e4,completedTime:"2024-01-14 09:30:00",proofreader:"张三",quality:"average",wordCount:1120,accuracy:92.8,processingTime:"7分20秒",dimensions:"1800×1200",imageUrl:"/api/files/preview/4",ocrText:"这是文档扫描件的OCR识别结果..."},{id:5,name:"certificate_scan.jpg",originalSize:1792e3,completedTime:"2024-01-13 15:10:00",proofreader:"李四",quality:"good",wordCount:320,accuracy:96.7,processingTime:"1分50秒",dimensions:"1500×1000",imageUrl:"/api/files/preview/5",ocrText:"这是证书扫描件的OCR识别结果..."}],K.value=M.value.length}catch(a){e.error("加载文件列表失败")}finally{b.value=!1}},Z=async()=>{try{K.value=156,E.value=42,H.value=4.2,J.value=68.5}catch(a){e.error("加载统计数据失败")}},ee=()=>{P.value=1},ae=()=>{P.value=1},le=()=>{X(),Z()},te=()=>{e.success("正在生成审校报告...")},ue=e=>{B.value=e},re=()=>{0!==B.value.length?e.success(`开始下载 ${B.value.length} 个文件`):e.warning("请选择要下载的文件")},oe=async()=>{if(0!==B.value.length)try{await t.confirm(`确定要归档选中的 ${B.value.length} 个文件吗？`,"批量归档确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.success("归档成功"),B.value=[]}catch{}else e.warning("请选择要归档的文件")},ne=async()=>{if(A.value?.ocrText)try{await navigator.clipboard.writeText(A.value.ocrText),e.success("文字已复制到剪贴板")}catch(a){e.error("复制失败")}else e.warning("没有可复制的文字")},ie=()=>{if(!A.value?.ocrText)return void e.warning("没有可下载的文字");const a=new Blob([A.value.ocrText],{type:"text/plain"}),l=URL.createObjectURL(a),t=document.createElement("a");t.href=l,t.download=`${A.value.name}_ocr_result.txt`,t.click(),URL.revokeObjectURL(l),e.success("下载成功")},se=()=>{N.value=A.value?.ocrText||"",G.value=!0},de=()=>{A.value&&(A.value.ocrText=N.value,e.success("保存成功")),G.value=!1},ce=e=>{F.value=e,P.value=1},pe=e=>{P.value=e},ve=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":(e/1048576).toFixed(1)+" MB",me=e=>{switch(e){case"excellent":return"success";case"good":return"primary";case"average":return"warning";case"poor":return"danger";default:return"info"}},fe=e=>{switch(e){case"excellent":return"优秀";case"good":return"良好";case"average":return"一般";case"poor":return"需改进";default:return"未知"}};return(t,u)=>{const r=p("el-icon"),o=p("el-input"),n=p("el-col"),M=p("el-option"),X=p("el-select"),Z=p("el-date-picker"),ge=p("el-button"),_e=p("el-row"),we=p("el-card"),he=p("el-statistic"),ye=p("el-table-column"),be=p("el-tag"),Ce=p("el-progress"),xe=p("el-table"),Te=p("el-pagination"),Ve=p("el-descriptions-item"),ke=p("el-descriptions"),Ue=p("el-dialog"),ze=_("loading");return v(),i("div",C,[u[29]||(u[29]=s("div",{class:"page-header"},[s("h1",null,"已审校图片"),s("p",{class:"page-description"},"显示已完成审校的图片文件列表和审校结果")],-1)),s("div",x,[d(we,{class:"filter-card"},{default:c(()=>[d(_e,{gutter:20},{default:c(()=>[d(n,{span:5},{default:c(()=>[d(o,{modelValue:L.value,"onUpdate:modelValue":u[0]||(u[0]=e=>L.value=e),placeholder:"搜索文件名...",clearable:"",onInput:ee},{prefix:c(()=>[d(r,null,{default:c(()=>[d(m(a))]),_:1})]),_:1},8,["modelValue"])]),_:1}),d(n,{span:4},{default:c(()=>[d(X,{modelValue:D.value,"onUpdate:modelValue":u[1]||(u[1]=e=>D.value=e),placeholder:"审校质量",clearable:"",onChange:ae},{default:c(()=>[d(M,{label:"全部",value:""}),d(M,{label:"优秀",value:"excellent"}),d(M,{label:"良好",value:"good"}),d(M,{label:"一般",value:"average"}),d(M,{label:"需改进",value:"poor"})]),_:1},8,["modelValue"])]),_:1}),d(n,{span:4},{default:c(()=>[d(X,{modelValue:I.value,"onUpdate:modelValue":u[2]||(u[2]=e=>I.value=e),placeholder:"审校人员",clearable:"",onChange:ae},{default:c(()=>[d(M,{label:"全部",value:""}),d(M,{label:"张三",value:"张三"}),d(M,{label:"李四",value:"李四"}),d(M,{label:"王五",value:"王五"})]),_:1},8,["modelValue"])]),_:1}),d(n,{span:5},{default:c(()=>[d(Z,{modelValue:$.value,"onUpdate:modelValue":u[3]||(u[3]=e=>$.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:ae},null,8,["modelValue"])]),_:1}),d(n,{span:3},{default:c(()=>[d(ge,{type:"primary",onClick:le},{default:c(()=>u[11]||(u[11]=[f("刷新列表")])),_:1,__:[11]})]),_:1}),d(n,{span:3},{default:c(()=>[d(ge,{onClick:te},{default:c(()=>u[12]||(u[12]=[f("导出报告")])),_:1,__:[12]})]),_:1})]),_:1})]),_:1}),d(we,{class:"stats-card"},{default:c(()=>[d(_e,{gutter:20},{default:c(()=>[d(n,{span:6},{default:c(()=>[d(he,{title:"总审校数量",value:K.value},null,8,["value"])]),_:1}),d(n,{span:6},{default:c(()=>[d(he,{title:"本月审校",value:E.value},null,8,["value"])]),_:1}),d(n,{span:6},{default:c(()=>[d(he,{title:"平均质量分",value:H.value,precision:1},null,8,["value"])]),_:1}),d(n,{span:6},{default:c(()=>[d(he,{title:"优秀率",value:J.value,suffix:"%",precision:1},null,8,["value"])]),_:1})]),_:1})]),_:1}),d(we,{title:"已审校图片列表",class:"list-card"},{header:c(()=>[s("div",T,[u[14]||(u[14]=s("span",null,"已审校图片列表",-1)),s("div",V,[d(ge,{size:"small",onClick:re,disabled:0===B.value.length},{default:c(()=>[f(" 批量下载 ("+h(B.value.length)+") ",1)]),_:1},8,["disabled"]),d(ge,{size:"small",onClick:oe,disabled:0===B.value.length},{default:c(()=>u[13]||(u[13]=[f(" 批量归档 ")])),_:1,__:[13]},8,["disabled"])])])]),default:c(()=>[g((v(),w(xe,{data:W.value,style:{width:"100%"},onSelectionChange:ue},{default:c(()=>[d(ye,{type:"selection",width:"55"}),d(ye,{prop:"name",label:"文件名",width:"180"},{default:c(e=>[s("div",k,[d(r,null,{default:c(()=>[d(m(l))]),_:1}),s("span",null,h(e.row.name),1)])]),_:1}),d(ye,{prop:"originalSize",label:"原始大小",width:"100"},{default:c(e=>[f(h(ve(e.row.originalSize)),1)]),_:1}),d(ye,{prop:"completedTime",label:"完成时间",width:"140"}),d(ye,{prop:"proofreader",label:"审校人员",width:"100"}),d(ye,{prop:"quality",label:"审校质量",width:"100"},{default:c(e=>[d(be,{type:me(e.row.quality)},{default:c(()=>[f(h(fe(e.row.quality)),1)]),_:2},1032,["type"])]),_:1}),d(ye,{prop:"wordCount",label:"识别字数",width:"100"}),d(ye,{prop:"accuracy",label:"识别准确率",width:"120"},{default:c(e=>[d(Ce,{percentage:e.row.accuracy,"stroke-width":8,"show-text":!0,format:e=>e+"%"},null,8,["percentage","format"])]),_:1}),d(ye,{label:"操作",width:"220"},{default:c(a=>[d(ge,{size:"small",onClick:e=>{return l=a.row,A.value=l,void(Q.value=!0);var l}},{default:c(()=>u[15]||(u[15]=[f("查看详情")])),_:2,__:[15]},1032,["onClick"]),d(ge,{size:"small",onClick:l=>{return t=a.row,void e.success(`开始下载文件：${t.name}`);var t}},{default:c(()=>u[16]||(u[16]=[f("下载结果")])),_:2,__:[16]},1032,["onClick"]),d(ge,{size:"small",onClick:l=>{return t=a.row,void e.success(`重新审校文件：${t.name}`);var t}},{default:c(()=>u[17]||(u[17]=[f("重新审校")])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ze,b.value]]),s("div",U,[d(Te,{"current-page":P.value,"onUpdate:currentPage":u[4]||(u[4]=e=>P.value=e),"page-size":F.value,"onUpdate:pageSize":u[5]||(u[5]=e=>F.value=e),"page-sizes":[10,20,50,100],total:K.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ce,onCurrentChange:pe},null,8,["current-page","page-size","total"])])]),_:1})]),d(Ue,{modelValue:Q.value,"onUpdate:modelValue":u[7]||(u[7]=e=>Q.value=e),title:"审校详情",width:"70%"},{default:c(()=>[A.value?(v(),i("div",z,[d(_e,{gutter:20},{default:c(()=>[d(n,{span:12},{default:c(()=>[s("div",S,[u[21]||(u[21]=s("h4",null,"原始图片",-1)),s("img",{src:A.value.imageUrl,alt:"原始图片",class:"preview-image"},null,8,q),s("div",R,[s("p",null,[u[18]||(u[18]=s("strong",null,"文件名：",-1)),f(h(A.value.name),1)]),s("p",null,[u[19]||(u[19]=s("strong",null,"文件大小：",-1)),f(h(ve(A.value.originalSize)),1)]),s("p",null,[u[20]||(u[20]=s("strong",null,"图片尺寸：",-1)),f(h(A.value.dimensions),1)])])])]),_:1}),d(n,{span:12},{default:c(()=>[s("div",O,[u[25]||(u[25]=s("h4",null,"审校结果",-1)),d(ke,{column:2,border:""},{default:c(()=>[d(Ve,{label:"审校人员"},{default:c(()=>[f(h(A.value.proofreader),1)]),_:1}),d(Ve,{label:"完成时间"},{default:c(()=>[f(h(A.value.completedTime),1)]),_:1}),d(Ve,{label:"审校质量"},{default:c(()=>[d(be,{type:me(A.value.quality)},{default:c(()=>[f(h(fe(A.value.quality)),1)]),_:1},8,["type"])]),_:1}),d(Ve,{label:"识别准确率"},{default:c(()=>[f(h(A.value.accuracy)+"%",1)]),_:1}),d(Ve,{label:"识别字数"},{default:c(()=>[f(h(A.value.wordCount),1)]),_:1}),d(Ve,{label:"处理时长"},{default:c(()=>[f(h(A.value.processingTime),1)]),_:1})]),_:1}),u[26]||(u[26]=s("h4",{style:{"margin-top":"20px"}},"识别文字内容",-1)),d(o,{modelValue:A.value.ocrText,"onUpdate:modelValue":u[6]||(u[6]=e=>A.value.ocrText=e),type:"textarea",rows:12,readonly:"",class:"ocr-text"},null,8,["modelValue"]),s("div",j,[d(ge,{onClick:ne},{default:c(()=>u[22]||(u[22]=[f("复制文字")])),_:1,__:[22]}),d(ge,{onClick:ie},{default:c(()=>u[23]||(u[23]=[f("下载文字")])),_:1,__:[23]}),d(ge,{type:"primary",onClick:se},{default:c(()=>u[24]||(u[24]=[f("编辑文字")])),_:1,__:[24]})])])]),_:1})]),_:1})])):y("",!0)]),_:1},8,["modelValue"]),d(Ue,{modelValue:G.value,"onUpdate:modelValue":u[10]||(u[10]=e=>G.value=e),title:"编辑OCR文字",width:"60%"},{footer:c(()=>[s("span",Y,[d(ge,{onClick:u[9]||(u[9]=e=>G.value=!1)},{default:c(()=>u[27]||(u[27]=[f("取消")])),_:1,__:[27]}),d(ge,{type:"primary",onClick:de},{default:c(()=>u[28]||(u[28]=[f("保存修改")])),_:1,__:[28]})])]),default:c(()=>[d(o,{modelValue:N.value,"onUpdate:modelValue":u[8]||(u[8]=e=>N.value=e),type:"textarea",rows:15,placeholder:"请编辑OCR识别的文字..."},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-d441f42d"]]);export{M as default};
