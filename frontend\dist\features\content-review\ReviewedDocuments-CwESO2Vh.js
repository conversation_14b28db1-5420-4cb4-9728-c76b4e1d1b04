import{aC as e,r as a,b as t,d as l,p as o,H as r,I as s,ag as d,o as u,c as n,K as c,Q as i,a as m,M as p,O as v,u as f,P as h,a6 as _,X as g,aD as y,m as w,S as b,a4 as k,J as V,aq as x}from"../../chunks/vue-vendor-BCsylZgc.js";import{M as D,S as C,T as R,z as $,d as z,R as P,x as S,U as T,C as A,V as U}from"../../chunks/ui-vendor-DZ6owSRu.js";import{P as N}from"../../chunks/preReview-DUK05fM-.js";import{u as E}from"../../chunks/useDocumentTable-DFS8VTHq.js";import{_ as I}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";import"../../chunks/index-DU7Wk3Qr.js";const O=e("reviewedDocuments",()=>{const e=a([]),l=a(0),o=a({table:!1,search:!1,export:!1,batch:!1}),r=a({currentPage:1,pageSize:20,total:0,pageSizes:[10,20,50,100]}),s=a({page:1,pageSize:20,title:"",authorName:"",status:"",type:"",category:"",createdAtStart:"",createdAtEnd:"",keyword:"",sortBy:"reviewedAt",sortOrder:"desc"}),d=a([]),u=t(()=>d.value.length>0),n=t(()=>d.value.length),c=async(a=0)=>{try{o.value.table=!0;const a={...s.value,page:r.value.currentPage,pageSize:r.value.pageSize},t=await N.getReviewedDocuments(a);if(!t)throw new Error("API响应为空");const u=t.list||t.items;if(!u)throw new Error("API响应格式错误：缺少list或items字段");e.value=u,l.value=t.total||0,r.value.total=t.total||0,d.value=[]}catch(t){if(a<3)return await new Promise(e=>setTimeout(e,1e3*(a+1))),c(a+1);e.value=[],l.value=0,r.value.total=0;const o=t instanceof Error?t.message:"未知错误";D.error(`获取已预审文档列表失败: ${o}`)}finally{o.value.table=!1}};return{documents:e,total:l,loading:o,pagination:r,searchParams:s,selectedDocumentIds:d,hasSelectedDocuments:u,selectedDocumentsCount:n,fetchReviewedDocuments:c,searchDocuments:async()=>{try{o.value.search=!0,r.value.currentPage=1,await c()}catch(e){D.error("搜索失败，请重试")}finally{o.value.search=!1}},batchReReview:async(e,a)=>{try{o.value.batch=!0;const t=await N.batchReReview({documentIds:e,reason:a||"重新提交预审"});return D.success(`成功将 ${t.successCount} 个文档重新提交预审`),await c(),t}catch(t){const e=t instanceof Error?t.message:"未知错误";throw D.error(`批量重审失败: ${e}`),t}finally{o.value.batch=!1}},deleteDocument:async e=>{try{await N.deleteDocument(e),D.success("文档删除成功"),await c()}catch(a){const e=a instanceof Error?a.message:"未知错误";throw D.error(`删除文档失败: ${e}`),a}},exportReport:async(e,a="markdown")=>{try{o.value.export=!0;const t=await N.exportReport(e,a),l=document.createElement("a");return l.href=t.downloadUrl,l.download=t.fileName,document.body.appendChild(l),l.click(),document.body.removeChild(l),D.success("报告导出成功"),t}catch(t){const e=t instanceof Error?t.message:"未知错误";throw D.error(`导出报告失败: ${e}`),t}finally{o.value.export=!1}},batchExportReports:async(e,a="markdown")=>{try{o.value.export=!0;const t=await N.batchExportReports(e,a),l=document.createElement("a");return l.href=t.downloadUrl,l.download=t.fileName,document.body.appendChild(l),l.click(),document.body.removeChild(l),D.success(`成功导出 ${e.length} 个文档的预审报告`),t}catch(t){const e=t instanceof Error?t.message:"未知错误";throw D.error(`批量导出报告失败: ${e}`),t}finally{o.value.export=!1}},updatePagination:e=>{Object.assign(r.value,e),s.value.page=r.value.currentPage,s.value.pageSize=r.value.pageSize},setSelectedDocumentIds:e=>{d.value=e},resetSearchParams:()=>{s.value={page:1,pageSize:20,title:"",authorName:"",status:"",type:"",category:"",createdAtStart:"",createdAtEnd:"",keyword:"",sortBy:"reviewedAt",sortOrder:"desc"},r.value.currentPage=1}}}),j={key:0,class:"document-detail-content"},M={class:"card-header"},B={key:0,class:"tags-section"},K={class:"content-section"},Y={class:"content-text"},F={key:1,class:"author-bio-section"},W={class:"author-bio-text"},H={class:"card-header"},L={class:"report-actions"},q=["innerHTML"],Z={key:1,class:"markdown-source"},J={class:"dialog-footer"},Q=I(l({__name:"DocumentDetailDialog",props:{modelValue:{type:Boolean},document:{}},emits:["update:modelValue"],setup(e,{emit:l}){const g=e,y=l,w=O(),{exportReport:b}=w,{getStatusTagType:k,formatDateTime:V,formatFileSize:x}=E(),$=t({get:()=>g.modelValue,set:e=>y("update:modelValue",e)}),z=a(!1),P=t(()=>g.document?`文档详情 - ${g.document.title}`:"文档详情"),S=t(()=>g.document?.reviewReport?g.document.reviewReport.replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^\*\*(.*)\*\*/gim,"<strong>$1</strong>").replace(/^\*(.*)\*/gim,"<em>$1</em>").replace(/^- (.*$)/gim,"<li>$1</li>").replace(/\n/gim,"<br>"):""),T=()=>{z.value=!z.value},A=async()=>{if(g.document)try{await b(g.document.id,"markdown")}catch(e){}else D.error("文档信息不存在")},U=()=>{$.value=!1};return o($,e=>{e&&(z.value=!1)}),(e,a)=>{const t=d("el-tag"),l=d("el-descriptions-item"),o=d("el-descriptions"),g=d("el-card"),y=d("el-icon"),w=d("el-button"),b=d("el-input"),D=d("el-dialog");return u(),r(D,{modelValue:$.value,"onUpdate:modelValue":a[0]||(a[0]=e=>$.value=e),title:P.value,width:"80%","before-close":U,class:"document-detail-dialog"},{footer:s(()=>[m("div",J,[i(w,{onClick:U},{default:s(()=>a[7]||(a[7]=[p("关闭")])),_:1,__:[7]}),e.document?.reviewReport?(u(),r(w,{key:0,type:"primary",onClick:A},{default:s(()=>a[8]||(a[8]=[p(" 导出预审报告 ")])),_:1,__:[8]})):c("",!0)])]),default:s(()=>[e.document?(u(),n("div",j,[i(g,{class:"info-card",shadow:"never"},{header:s(()=>[m("div",M,[a[1]||(a[1]=m("span",null,"文档基本信息",-1)),i(t,{type:f(k)(e.document.status),size:"small"},{default:s(()=>{return[p(v((a=e.document.status,{approved:"已通过",rejected:"已拒绝",pending:"待预审",reviewing:"预审中"}[a]||a)),1)];var a}),_:1},8,["type"])])]),default:s(()=>[i(o,{column:2,border:""},{default:s(()=>[i(l,{label:"文档标题"},{default:s(()=>[p(v(e.document.title),1)]),_:1}),i(l,{label:"文档类型"},{default:s(()=>[i(t,{size:"small"},{default:s(()=>{return[p(v((a=e.document.type,{text:"纯文本",docx:"Word文档",pdf:"PDF文档",wps:"WPS文档"}[a]||a)),1)];var a}),_:1})]),_:1}),i(l,{label:"文档分类"},{default:s(()=>[p(v(e.document.category),1)]),_:1}),i(l,{label:"文件大小"},{default:s(()=>[p(v(f(x)(e.document.fileSize)),1)]),_:1}),i(l,{label:"作者姓名"},{default:s(()=>[p(v(e.document.authorName),1)]),_:1}),i(l,{label:"作者单位"},{default:s(()=>[p(v(e.document.authorOrganization),1)]),_:1}),i(l,{label:"创建者"},{default:s(()=>[p(v(e.document.creatorName),1)]),_:1}),i(l,{label:"预审人员"},{default:s(()=>[p(v(e.document.reviewerName||"-"),1)]),_:1}),i(l,{label:"创建时间"},{default:s(()=>[p(v(f(V)(e.document.createdAt)),1)]),_:1}),i(l,{label:"更新时间"},{default:s(()=>[p(v(f(V)(e.document.updatedAt)),1)]),_:1}),i(l,{label:"预审时间"},{default:s(()=>[p(v(e.document.reviewedAt?f(V)(e.document.reviewedAt):"-"),1)]),_:1}),i(l,{label:"预计完成时间"},{default:s(()=>[p(v(e.document.estimatedTime?f(V)(e.document.estimatedTime):"-"),1)]),_:1})]),_:1}),e.document.tags&&e.document.tags.length>0?(u(),n("div",B,[a[2]||(a[2]=m("span",{class:"tags-label"},"文档标签：",-1)),(u(!0),n(h,null,_(e.document.tags,e=>(u(),r(t,{key:e,size:"small",class:"tag-item",type:"info"},{default:s(()=>[p(v(e),1)]),_:2},1024))),128))])):c("",!0),m("div",K,[a[3]||(a[3]=m("h4",null,"内容简介",-1)),m("p",Y,v(e.document.content),1)]),e.document.authorBio?(u(),n("div",F,[a[4]||(a[4]=m("h4",null,"作者简介",-1)),m("p",W,v(e.document.authorBio),1)])):c("",!0)]),_:1}),e.document.reviewReport?(u(),r(g,{key:0,class:"report-card",shadow:"never"},{header:s(()=>[m("div",H,[a[6]||(a[6]=m("span",null,"预审报告",-1)),m("div",L,[i(w,{type:"primary",size:"small",onClick:A},{default:s(()=>[i(y,null,{default:s(()=>[i(f(C))]),_:1}),a[5]||(a[5]=p(" 导出报告 "))]),_:1,__:[5]}),i(w,{type:"info",size:"small",onClick:T},{default:s(()=>[i(y,null,{default:s(()=>[i(f(R))]),_:1}),p(" "+v(z.value?"预览模式":"源码模式"),1)]),_:1})])])]),default:s(()=>[z.value?(u(),n("div",Z,[i(b,{"model-value":e.document.reviewReport,type:"textarea",rows:20,readonly:"",class:"markdown-textarea"},null,8,["model-value"])])):(u(),n("div",{key:0,class:"markdown-preview",innerHTML:S.value},null,8,q))]),_:1})):c("",!0)])):c("",!0)]),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-4f22fad9"]]),X={key:0,class:"report-content"},G={class:"report-header"},ee={class:"document-info"},ae={class:"meta-info"},te={class:"meta-item"},le={class:"meta-item"},oe={class:"report-actions"},re={class:"report-body"},se={key:0,class:"markdown-preview"},de=["innerHTML"],ue={key:1,class:"markdown-source"},ne={key:1,class:"empty-content"},ce={class:"dialog-footer"},ie=I(l({__name:"ReviewReportDialog",props:{modelValue:{type:Boolean},document:{}},emits:["update:modelValue"],setup(e,{emit:l}){const h=e,_=l,g=O(),{exportReport:y}=g,{getStatusTagType:w,formatDateTime:b}=E(),k=t({get:()=>h.modelValue,set:e=>_("update:modelValue",e)}),V=a(!1),x=t(()=>h.document?`预审报告 - ${h.document.title}`:"预审报告"),z=t(()=>{if(!h.document?.reviewReport)return"<p>暂无预审报告</p>";let e=h.document.reviewReport.replace(/^# (.*$)/gim,'<h1 class="md-h1">$1</h1>').replace(/^## (.*$)/gim,'<h2 class="md-h2">$1</h2>').replace(/^### (.*$)/gim,'<h3 class="md-h3">$1</h3>').replace(/\*\*(.*?)\*\*/gim,"<strong>$1</strong>").replace(/\*(.*?)\*/gim,"<em>$1</em>").replace(/\|(.+)\|/gim,e=>`<tr>${e.split("|").filter(e=>e.trim()).map(e=>`<td>${e.trim()}</td>`).join("")}</tr>`).replace(/^- (.*$)/gim,"<li>$1</li>").replace(/\n\n/gim,"</p><p>").replace(/\n/gim,"<br>");return e.includes("<tr>")&&(e=e.replace(/(<tr>.*?<\/tr>)/gims,'<table class="md-table">$1</table>')),e.startsWith("<h")||e.startsWith("<table")||(e=`<p>${e}</p>`),e}),P=()=>{V.value=!V.value},S=async()=>{if(h.document)try{await y(h.document.id,"markdown")}catch(e){}else D.error("文档信息不存在")},T=async()=>{if(h.document?.reviewReport)try{await navigator.clipboard.writeText(h.document.reviewReport),D.success("报告内容已复制到剪贴板")}catch(e){D.error("复制失败，请手动复制")}else D.error("暂无报告内容")},A=()=>{k.value=!1};return o(k,e=>{e&&(V.value=!1)}),(e,a)=>{const t=d("el-tag"),l=d("el-icon"),o=d("el-button"),h=d("el-input"),_=d("el-empty"),g=d("el-dialog");return u(),r(g,{modelValue:k.value,"onUpdate:modelValue":a[0]||(a[0]=e=>k.value=e),title:x.value,width:"70%","before-close":A,class:"review-report-dialog"},{footer:s(()=>[m("div",ce,[i(o,{onClick:A},{default:s(()=>a[2]||(a[2]=[p("关闭")])),_:1,__:[2]}),e.document?.reviewReport?(u(),r(o,{key:0,type:"primary",onClick:S},{default:s(()=>a[3]||(a[3]=[p(" 导出报告 ")])),_:1,__:[3]})):c("",!0),e.document?.reviewReport?(u(),r(o,{key:1,type:"success",onClick:T},{default:s(()=>[i(l,null,{default:s(()=>[i(f($))]),_:1}),a[4]||(a[4]=p(" 复制内容 "))]),_:1,__:[4]})):c("",!0)])]),default:s(()=>[e.document?(u(),n("div",X,[m("div",G,[m("div",ee,[m("h3",null,v(e.document.title),1),m("div",ae,[i(t,{type:f(w)(e.document.status),size:"small"},{default:s(()=>{return[p(v((a=e.document.status,{approved:"已通过",rejected:"已拒绝",pending:"待预审",reviewing:"预审中"}[a]||a)),1)];var a}),_:1},8,["type"]),m("span",te,"作者："+v(e.document.authorName),1),m("span",le," 预审时间："+v(e.document.reviewedAt?f(b)(e.document.reviewedAt):"-"),1)])]),m("div",oe,[i(o,{type:"info",size:"small",onClick:P},{default:s(()=>[i(l,null,{default:s(()=>[i(f(R))]),_:1}),p(" "+v(V.value?"预览模式":"源码模式"),1)]),_:1}),i(o,{type:"primary",size:"small",onClick:S},{default:s(()=>[i(l,null,{default:s(()=>[i(f(C))]),_:1}),a[1]||(a[1]=p(" 导出报告 "))]),_:1,__:[1]})])]),m("div",re,[V.value?(u(),n("div",ue,[i(h,{"model-value":e.document.reviewReport,type:"textarea",rows:25,readonly:"",class:"markdown-textarea",placeholder:"暂无预审报告"},null,8,["model-value"])])):(u(),n("div",se,[m("div",{innerHTML:z.value},null,8,de)]))])])):(u(),n("div",ne,[i(_,{description:"暂无预审报告数据"})]))]),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-2fc6b8d3"]]),me={class:"export-content"},pe={class:"export-info"},ve={class:"format-content"},fe={class:"format-content"},he={class:"format-content"},_e={key:0,class:"export-preview"},ge={class:"preview-item"},ye={class:"preview-name"},we={class:"preview-size"},be={key:1,class:"export-preview"},ke={class:"preview-list"},Ve={class:"preview-name"},xe={key:0,class:"preview-more"},De={class:"batch-info"},Ce={class:"dialog-footer"},Re=I(l({__name:"ExportDialog",props:{modelValue:{type:Boolean},exportType:{},selectedDocuments:{}},emits:["update:modelValue","export-complete"],setup(e,{emit:l}){const y=e,w=l,b=O(),{exportReport:k,batchExportReports:V}=b,x=t({get:()=>y.modelValue,set:e=>w("update:modelValue",e)}),R=a(!1),$=g({format:"markdown",fileName:"",includeContent:["basicInfo","reviewReport","statistics"],pdfOptions:["pageNumbers"]}),P=t(()=>"batch"===y.exportType?`批量导出预审报告（${y.selectedDocuments.length} 个文件）`:"导出预审报告"),S=t(()=>"batch"===y.exportType?"批量导出说明":"导出说明"),T=t(()=>"batch"===y.exportType?`将导出 ${y.selectedDocuments.length} 个文档的预审报告，并打包为ZIP文件。`:"将导出选中文档的完整预审报告，包含文档信息和审校结果。"),A=t(()=>{if(0===y.selectedDocuments.length)return"0 KB";const e=y.selectedDocuments[0].reviewReport?.length||0;return`约 ${50+Math.ceil(e/1024)} KB`}),U=t(()=>{const e=50*y.selectedDocuments.length;return e>1024?`约 ${(e/1024).toFixed(1)} MB`:`约 ${e} KB`}),N=e=>({markdown:"md",pdf:"pdf",word:"docx"}[e]||"md"),E=async()=>{if(0!==y.selectedDocuments.length)try{if(R.value=!0,"single"===y.exportType)await k(y.selectedDocuments[0].id,$.format),D.success("报告导出成功");else{const e=y.selectedDocuments.map(e=>e.id);await V(e,$.format),D.success(`成功导出 ${y.selectedDocuments.length} 个文档的预审报告`)}w("export-complete"),I()}catch(e){}finally{R.value=!1}else D.error("请选择要导出的文档")},I=()=>{x.value=!1};return o(x,e=>{if(e&&($.format="markdown",$.fileName="",$.includeContent=["basicInfo","reviewReport","statistics"],$.pdfOptions=["pageNumbers"],"batch"===y.exportType)){const e=(new Date).toISOString().slice(0,10);$.fileName=`预审报告批量导出-${e}`}}),(e,a)=>{const t=d("el-alert"),l=d("el-icon"),o=d("el-radio"),g=d("el-radio-group"),y=d("el-form-item"),w=d("el-input"),b=d("el-checkbox"),k=d("el-checkbox-group"),V=d("el-form"),D=d("el-button"),O=d("el-dialog");return u(),r(O,{modelValue:x.value,"onUpdate:modelValue":a[4]||(a[4]=e=>x.value=e),title:P.value,width:"500px","before-close":I,class:"export-dialog"},{footer:s(()=>[m("div",Ce,[i(D,{onClick:I},{default:s(()=>a[17]||(a[17]=[p("取消")])),_:1,__:[17]}),i(D,{type:"primary",loading:R.value,onClick:E},{default:s(()=>[i(l,null,{default:s(()=>[i(f(C))]),_:1}),p(" "+v("batch"===e.exportType?"批量导出":"导出"),1)]),_:1},8,["loading"])])]),default:s(()=>[m("div",me,[m("div",pe,[i(t,{title:S.value,description:T.value,type:"info","show-icon":"",closable:!1},null,8,["title","description"])]),i(V,{model:$,"label-width":"100px",class:"export-form"},{default:s(()=>[i(y,{label:"导出格式",required:""},{default:s(()=>[i(g,{modelValue:$.format,"onUpdate:modelValue":a[0]||(a[0]=e=>$.format=e),class:"format-group"},{default:s(()=>[i(o,{value:"markdown",class:"format-option"},{default:s(()=>[m("div",ve,[i(l,null,{default:s(()=>[i(f(z))]),_:1}),a[5]||(a[5]=m("div",{class:"format-info"},[m("div",{class:"format-name"},"Markdown"),m("div",{class:"format-desc"},"轻量级标记语言，易于编辑")],-1))])]),_:1}),i(o,{value:"pdf",class:"format-option"},{default:s(()=>[m("div",fe,[i(l,null,{default:s(()=>[i(f(z))]),_:1}),a[6]||(a[6]=m("div",{class:"format-info"},[m("div",{class:"format-name"},"PDF"),m("div",{class:"format-desc"},"便携式文档格式，适合打印")],-1))])]),_:1}),i(o,{value:"word",class:"format-option"},{default:s(()=>[m("div",he,[i(l,null,{default:s(()=>[i(f(z))]),_:1}),a[7]||(a[7]=m("div",{class:"format-info"},[m("div",{class:"format-name"},"Word"),m("div",{class:"format-desc"},"Microsoft Word文档格式")],-1))])]),_:1})]),_:1},8,["modelValue"])]),_:1}),"batch"===e.exportType?(u(),r(y,{key:0,label:"文件命名"},{default:s(()=>[i(w,{modelValue:$.fileName,"onUpdate:modelValue":a[1]||(a[1]=e=>$.fileName=e),placeholder:"请输入文件名（不含扩展名）",clearable:""},null,8,["modelValue"]),a[8]||(a[8]=m("div",{class:"file-name-tip"},"批量导出将生成ZIP压缩包，包含所有选中文档的预审报告",-1))]),_:1,__:[8]})):c("",!0),i(y,{label:"包含内容"},{default:s(()=>[i(k,{modelValue:$.includeContent,"onUpdate:modelValue":a[2]||(a[2]=e=>$.includeContent=e),class:"content-group"},{default:s(()=>[i(b,{value:"basicInfo"},{default:s(()=>a[9]||(a[9]=[p("文档基本信息")])),_:1,__:[9]}),i(b,{value:"reviewReport"},{default:s(()=>a[10]||(a[10]=[p("预审报告内容")])),_:1,__:[10]}),i(b,{value:"statistics"},{default:s(()=>a[11]||(a[11]=[p("统计摘要")])),_:1,__:[11]}),i(b,{value:"metadata"},{default:s(()=>a[12]||(a[12]=[p("元数据信息")])),_:1,__:[12]})]),_:1},8,["modelValue"])]),_:1}),"pdf"===$.format?(u(),r(y,{key:1,label:"PDF选项"},{default:s(()=>[i(k,{modelValue:$.pdfOptions,"onUpdate:modelValue":a[3]||(a[3]=e=>$.pdfOptions=e),class:"pdf-options"},{default:s(()=>[i(b,{value:"watermark"},{default:s(()=>a[13]||(a[13]=[p("添加水印")])),_:1,__:[13]}),i(b,{value:"pageNumbers"},{default:s(()=>a[14]||(a[14]=[p("显示页码")])),_:1,__:[14]}),i(b,{value:"tableOfContents"},{default:s(()=>a[15]||(a[15]=[p("生成目录")])),_:1,__:[15]})]),_:1},8,["modelValue"])]),_:1})):c("",!0)]),_:1},8,["model"]),"single"===e.exportType&&e.selectedDocuments.length>0?(u(),n("div",_e,[a[16]||(a[16]=m("h4",null,"导出预览",-1)),m("div",ge,[i(l,null,{default:s(()=>[i(f(z))]),_:1}),m("span",ye,v(e.selectedDocuments[0].title)+"-AI预审意见表."+v(N($.format)),1),m("span",we,v(A.value),1)])])):"batch"===e.exportType?(u(),n("div",be,[m("h4",null,"导出预览（"+v(e.selectedDocuments.length)+" 个文件）",1),m("div",ke,[(u(!0),n(h,null,_(e.selectedDocuments.slice(0,3),e=>(u(),n("div",{key:e.id,class:"preview-item"},[i(l,null,{default:s(()=>[i(f(z))]),_:1}),m("span",Ve,v(e.title)+"-AI预审意见表."+v(N($.format)),1)]))),128)),e.selectedDocuments.length>3?(u(),n("div",xe," 还有 "+v(e.selectedDocuments.length-3)+" 个文件... ",1)):c("",!0)]),m("div",De,[m("span",null,"压缩包名称："+v($.fileName||"预审报告批量导出")+".zip",1),m("span",null,"预计大小："+v(U.value),1)])])):c("",!0)])]),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-16e0face"]]),$e={class:"reviewed-documents"},ze={class:"card-header"},Pe={class:"header-actions"},Se={class:"search-section"},Te={class:"table-section"},Ae={class:"pagination-section"},Ue=I(l({__name:"ReviewedDocuments",setup(e){const l=O(),{documents:o,loading:c,pagination:C,hasSelectedDocuments:R,selectedDocumentsCount:$}=y(l),{fetchReviewedDocuments:z,searchDocuments:N,batchReReview:I,deleteDocument:j,batchExportReports:M,updatePagination:B,setSelectedDocumentIds:K,resetSearchParams:Y}=l,{typeOptions:F,categoryOptions:W,getStatusTagType:H,formatDateTime:L}=E(),q=g({title:"",authorName:"",status:"",type:"",category:"",dateRange:null}),Z=a(!1),J=a(!1),X=a(!1),G=a(!1),ee=a(!1),ae=a(!1),te=a(null),le=a("single"),oe=a([]),re=t(()=>window.innerHeight-60-120-60-40-40),se=g({reason:""}),de=g({format:"markdown"}),ue=async()=>{l.searchParams.title=q.title,l.searchParams.authorName=q.authorName,l.searchParams.status=q.status,l.searchParams.type=q.type,l.searchParams.category=q.category,q.dateRange?(l.searchParams.createdAtStart=q.dateRange[0],l.searchParams.createdAtEnd=q.dateRange[1]):(l.searchParams.createdAtStart="",l.searchParams.createdAtEnd=""),await N()},ne=async()=>{Object.assign(q,{title:"",authorName:"",status:"",type:"",category:"",dateRange:null}),Y(),await z()},ce=async()=>{await z()},me=e=>{const a=e.map(e=>e.id);K(a)},pe=({prop:e,order:a})=>{if(a){const t="ascending"===a?"asc":"desc";l.searchParams.sortBy=e,l.searchParams.sortOrder=t,z()}else l.searchParams.sortBy="reviewedAt",l.searchParams.sortOrder="desc",z()},ve=async e=>{B({pageSize:e,currentPage:1}),await z()},fe=async e=>{B({currentPage:e}),await z()},he=()=>{Z.value=!0,se.reason=""},_e=async()=>{try{await I(l.selectedDocumentIds,se.reason||void 0),Z.value=!1}catch{}},ge=()=>{const e=o.value.filter(e=>l.selectedDocumentIds.includes(e.id));le.value="batch",oe.value=e,ee.value=!0},ye=async()=>{try{await M(l.selectedDocumentIds,de.format),J.value=!1}catch{}},we=e=>{te.value=e,X.value=!0},be=()=>{},ke=e=>{if((e.ctrlKey||e.metaKey)&&"r"===e.key)return e.preventDefault(),void ce();if((e.ctrlKey||e.metaKey)&&"f"===e.key){e.preventDefault();const a=document.querySelector('.search-form input[placeholder*="文档标题"]');return void(a&&a.focus())}if((e.ctrlKey||e.metaKey)&&"a"===e.key){e.preventDefault();const a=o.value.map(e=>e.id);return void K(a)}if("Escape"!==e.key)return"Delete"===e.key&&R.value?(e.preventDefault(),void Ve()):void 0;K([])},Ve=async()=>{if(R.value)try{await P.confirm(`确定要删除选中的 ${$.value} 个文档吗？`,"批量删除确认",{type:"warning"});for(const e of l.selectedDocumentIds)await j(e);D.success(`成功删除 ${$.value} 个文档`)}catch(e){}};return w(async()=>{document.addEventListener("keydown",ke),await z()}),b(()=>{document.removeEventListener("keydown",ke)}),(e,a)=>{const t=d("el-button"),l=d("el-icon"),g=d("el-input"),y=d("el-form-item"),w=d("el-option"),b=d("el-select"),D=d("el-date-picker"),z=d("el-form"),N=d("el-table-column"),E=d("el-tag"),I=d("el-table"),O=d("el-pagination"),M=d("el-card"),B=d("el-dialog"),K=d("el-radio"),Y=d("el-radio-group"),ke=x("loading");return u(),n("div",$e,[i(M,null,{header:s(()=>[m("div",ze,[a[22]||(a[22]=m("span",null,"已预审文档",-1)),m("div",Pe,[i(t,{type:"primary",size:"default",disabled:!f(R),loading:f(c).batch,onClick:he},{default:s(()=>[p(" 批量重审 ("+v(f($))+") ",1)]),_:1},8,["disabled","loading"]),i(t,{type:"success",size:"default",disabled:!f(R),loading:f(c).export,onClick:ge},{default:s(()=>[p(" 批量导出 ("+v(f($))+") ",1)]),_:1},8,["disabled","loading"]),i(t,{type:"info",size:"default",onClick:ce},{default:s(()=>[i(l,null,{default:s(()=>[i(f(A))]),_:1}),a[20]||(a[20]=p(" 刷新 "))]),_:1,__:[20]}),i(t,{type:"text",size:"default",onClick:a[0]||(a[0]=e=>ae.value=!0)},{default:s(()=>[i(l,null,{default:s(()=>[i(f(U))]),_:1}),a[21]||(a[21]=p(" 快捷键 "))]),_:1,__:[21]})])])]),default:s(()=>[m("div",Se,[i(z,{model:q,inline:"",class:"search-form"},{default:s(()=>[i(y,{label:"文档标题"},{default:s(()=>[i(g,{modelValue:q.title,"onUpdate:modelValue":a[1]||(a[1]=e=>q.title=e),placeholder:"请输入文档标题",clearable:"",style:{width:"200px"},onKeyup:k(ue,["enter"])},null,8,["modelValue"])]),_:1}),i(y,{label:"作者姓名"},{default:s(()=>[i(g,{modelValue:q.authorName,"onUpdate:modelValue":a[2]||(a[2]=e=>q.authorName=e),placeholder:"请输入作者姓名",clearable:"",style:{width:"150px"},onKeyup:k(ue,["enter"])},null,8,["modelValue"])]),_:1}),i(y,{label:"审核状态"},{default:s(()=>[i(b,{modelValue:q.status,"onUpdate:modelValue":a[3]||(a[3]=e=>q.status=e),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:s(()=>[i(w,{label:"全部",value:""}),i(w,{label:"已通过",value:"approved"}),i(w,{label:"已拒绝",value:"rejected"})]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"文档类型"},{default:s(()=>[i(b,{modelValue:q.type,"onUpdate:modelValue":a[4]||(a[4]=e=>q.type=e),placeholder:"请选择类型",clearable:"",style:{width:"120px"}},{default:s(()=>[(u(!0),n(h,null,_(f(F),e=>(u(),r(w,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"文档分类"},{default:s(()=>[i(b,{modelValue:q.category,"onUpdate:modelValue":a[5]||(a[5]=e=>q.category=e),placeholder:"请选择分类",clearable:"",style:{width:"120px"}},{default:s(()=>[(u(!0),n(h,null,_(f(W),e=>(u(),r(w,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"时间范围"},{default:s(()=>[i(D,{modelValue:q.dateRange,"onUpdate:modelValue":a[6]||(a[6]=e=>q.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),i(y,null,{default:s(()=>[i(t,{type:"primary",loading:f(c).search,onClick:ue},{default:s(()=>[i(l,null,{default:s(()=>[i(f(S))]),_:1}),a[23]||(a[23]=p(" 搜索 "))]),_:1,__:[23]},8,["loading"]),i(t,{onClick:ne},{default:s(()=>[i(l,null,{default:s(()=>[i(f(T))]),_:1}),a[24]||(a[24]=p(" 重置 "))]),_:1,__:[24]})]),_:1})]),_:1},8,["model"])]),m("div",Te,[a[29]||(a[29]=m("div",{class:"table-toolbar"},null,-1)),V((u(),r(I,{data:f(o),stripe:"",border:"",style:{width:"100%"},height:re.value,onSelectionChange:me,onSortChange:pe},{default:s(()=>[i(N,{type:"selection",width:"55",align:"center"}),i(N,{label:"序号",width:"60",align:"center"},{default:s(({$index:e})=>[p(v((f(C).currentPage-1)*f(C).pageSize+e+1),1)]),_:1}),i(N,{prop:"title",label:"标题",width:"200",sortable:"custom","show-overflow-tooltip":""},{default:s(({row:e})=>[i(t,{type:"primary",link:"",onClick:a=>we(e)},{default:s(()=>[p(v(e.title),1)]),_:2},1032,["onClick"])]),_:1}),i(N,{prop:"reviewReport",label:"预审报告",width:"150","show-overflow-tooltip":""},{default:s(({row:e})=>[i(t,{type:"info",link:"",onClick:a=>{return t=e,te.value=t,void(G.value=!0);var t}},{default:s(()=>a[25]||(a[25]=[p(" 查看报告 ")])),_:2,__:[25]},1032,["onClick"])]),_:1}),i(N,{prop:"status",label:"状态",width:"100",sortable:"custom",align:"center"},{default:s(({row:e})=>[i(E,{type:f(H)(e.status),size:"small"},{default:s(()=>{return[p(v((a=e.status,{approved:"已通过",rejected:"已拒绝"}[a]||a)),1)];var a}),_:2},1032,["type"])]),_:1}),i(N,{prop:"category",label:"分类",width:"120",sortable:"custom","show-overflow-tooltip":""}),i(N,{prop:"authorName",label:"作者姓名",width:"120",sortable:"custom","show-overflow-tooltip":""}),i(N,{prop:"authorOrganization",label:"作者单位",width:"150",sortable:"custom","show-overflow-tooltip":""}),i(N,{prop:"creatorName",label:"创建者",width:"120",sortable:"custom","show-overflow-tooltip":""}),i(N,{prop:"createdAt",label:"创建时间",width:"150",align:"center"},{default:s(({row:e})=>[p(v(f(L)(e.createdAt)),1)]),_:1}),i(N,{prop:"updatedAt",label:"更新时间",width:"150",align:"center"},{default:s(({row:e})=>[p(v(f(L)(e.updatedAt)),1)]),_:1}),i(N,{prop:"estimatedTime",label:"预计完成时间",width:"150",align:"center"},{default:s(({row:e})=>[p(v(e.estimatedTime?f(L)(e.estimatedTime):"-"),1)]),_:1}),i(N,{label:"操作",width:"200",align:"center",fixed:"right"},{default:s(({row:e})=>[i(t,{type:"success",link:"",size:"small",onClick:a=>(async e=>{le.value="single",oe.value=[e],ee.value=!0})(e)},{default:s(()=>a[26]||(a[26]=[p(" 导出报告 ")])),_:2,__:[26]},1032,["onClick"]),i(t,{type:"primary",link:"",size:"small",onClick:a=>we(e)},{default:s(()=>a[27]||(a[27]=[p(" 详情 ")])),_:2,__:[27]},1032,["onClick"]),i(t,{type:"danger",link:"",size:"small",onClick:a=>(async e=>{try{await P.confirm(`确定要删除文档"${e.title}"吗？`,"确认删除",{type:"warning"}),await j(e.id)}catch(a){}})(e)},{default:s(()=>a[28]||(a[28]=[p(" 删除 ")])),_:2,__:[28]},1032,["onClick"])]),_:1})]),_:1},8,["data","height"])),[[ke,f(c).table]]),m("div",Ae,[i(O,{"current-page":f(C).currentPage,"onUpdate:currentPage":a[7]||(a[7]=e=>f(C).currentPage=e),"page-size":f(C).pageSize,"onUpdate:pageSize":a[8]||(a[8]=e=>f(C).pageSize=e),"page-sizes":f(C).pageSizes,total:f(C).total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ve,onCurrentChange:fe},null,8,["current-page","page-size","page-sizes","total"])])])]),_:1}),i(B,{modelValue:Z.value,"onUpdate:modelValue":a[11]||(a[11]=e=>Z.value=e),title:"批量重审",width:"500px"},{footer:s(()=>[i(t,{onClick:a[10]||(a[10]=e=>Z.value=!1)},{default:s(()=>a[30]||(a[30]=[p("取消")])),_:1,__:[30]}),i(t,{type:"primary",loading:f(c).batch,onClick:_e},{default:s(()=>a[31]||(a[31]=[p(" 确认重审 ")])),_:1,__:[31]},8,["loading"])]),default:s(()=>[i(z,{model:se,"label-width":"80px"},{default:s(()=>[i(y,{label:"重审原因"},{default:s(()=>[i(g,{modelValue:se.reason,"onUpdate:modelValue":a[9]||(a[9]=e=>se.reason=e),type:"textarea",rows:4,placeholder:"请输入重审原因（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),i(B,{modelValue:J.value,"onUpdate:modelValue":a[14]||(a[14]=e=>J.value=e),title:"批量导出",width:"400px"},{footer:s(()=>[i(t,{onClick:a[13]||(a[13]=e=>J.value=!1)},{default:s(()=>a[35]||(a[35]=[p("取消")])),_:1,__:[35]}),i(t,{type:"primary",loading:f(c).export,onClick:ye},{default:s(()=>a[36]||(a[36]=[p(" 确认导出 ")])),_:1,__:[36]},8,["loading"])]),default:s(()=>[i(z,{model:de,"label-width":"80px"},{default:s(()=>[i(y,{label:"导出格式"},{default:s(()=>[i(Y,{modelValue:de.format,"onUpdate:modelValue":a[12]||(a[12]=e=>de.format=e)},{default:s(()=>[i(K,{value:"markdown"},{default:s(()=>a[32]||(a[32]=[p("Markdown")])),_:1,__:[32]}),i(K,{value:"pdf"},{default:s(()=>a[33]||(a[33]=[p("PDF")])),_:1,__:[33]}),i(K,{value:"word"},{default:s(()=>a[34]||(a[34]=[p("Word")])),_:1,__:[34]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),i(Q,{modelValue:X.value,"onUpdate:modelValue":a[15]||(a[15]=e=>X.value=e),document:te.value},null,8,["modelValue","document"]),i(ie,{modelValue:G.value,"onUpdate:modelValue":a[16]||(a[16]=e=>G.value=e),document:te.value},null,8,["modelValue","document"]),i(Re,{modelValue:ee.value,"onUpdate:modelValue":a[17]||(a[17]=e=>ee.value=e),"export-type":le.value,"selected-documents":oe.value,onExportComplete:be},null,8,["modelValue","export-type","selected-documents"]),i(B,{modelValue:ae.value,"onUpdate:modelValue":a[19]||(a[19]=e=>ae.value=e),title:"键盘快捷键",width:"500px"},{footer:s(()=>[i(t,{onClick:a[18]||(a[18]=e=>ae.value=!1)},{default:s(()=>a[37]||(a[37]=[p("关闭")])),_:1,__:[37]})]),default:s(()=>[a[38]||(a[38]=m("div",{class:"shortcuts-content"},[m("div",{class:"shortcut-group"},[m("h4",null,"常用操作"),m("div",{class:"shortcut-item"},[m("kbd",null,"Ctrl"),p(" + "),m("kbd",null,"R"),m("span",null,"刷新页面")]),m("div",{class:"shortcut-item"},[m("kbd",null,"Ctrl"),p(" + "),m("kbd",null,"F"),m("span",null,"聚焦搜索框")]),m("div",{class:"shortcut-item"},[m("kbd",null,"Ctrl"),p(" + "),m("kbd",null,"A"),m("span",null,"全选文档")]),m("div",{class:"shortcut-item"},[m("kbd",null,"Esc"),m("span",null,"清空选择")])]),m("div",{class:"shortcut-group"},[m("h4",null,"文档操作"),m("div",{class:"shortcut-item"},[m("kbd",null,"Delete"),m("span",null,"删除选中文档")]),m("div",{class:"shortcut-item"},[m("kbd",null,"Enter"),m("span",null,"执行搜索")])])],-1))]),_:1,__:[38]},8,["modelValue"])])}}}),[["__scopeId","data-v-e1b5e276"]]);export{Ue as default};
