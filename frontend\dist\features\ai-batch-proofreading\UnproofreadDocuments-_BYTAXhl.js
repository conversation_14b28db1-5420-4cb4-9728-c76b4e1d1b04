import{aC as e,r as a,b as t,d as l,p as o,H as s,I as r,ag as n,o as u,a as i,Q as c,O as d,c as p,a6 as m,P as v,K as g,M as f,D as h,aD as y,m as b,T as w,u as _,a4 as k,J as C,aq as S,L as x,aB as P,aA as D}from"../../chunks/vue-vendor-BCsylZgc.js";import{M as V,R as I,x as T,C as z,a9 as R}from"../../chunks/ui-vendor-DZ6owSRu.js";import{a as A}from"../../chunks/index-DU7Wk3Qr.js";import{a as B}from"../../chunks/ModuleAPI-p-N7PV56.js";import{M as U}from"../../chunks/ai-engine-storage-DTARYHrG.js";import{u as q}from"../../chunks/useDocumentTable-DFS8VTHq.js";import{_ as $}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";import"../../chunks/ai-engine-core-wyUSRaHZ.js";const E={getUnproofreadDocuments:async e=>A.get("/api/batch-proofreading/unproofread-documents",{params:e}),getStatistics:async()=>A.get("/api/batch-proofreading/unproofread-documents/statistics"),submitSingleProofreading:async e=>A.post(`/api/batch-proofreading/unproofread-documents/${e}/single-submit`),submitBatchProofreading:async(e,a)=>A.post("/api/batch-proofreading/unproofread-documents/batch-submit",{documentIds:e,config:a}),deleteDocument:async e=>A.delete(`/api/batch-proofreading/unproofread-documents/${e}`),getDocumentDetail:async e=>A.get(`/api/batch-proofreading/unproofread-documents/${e}`),updateDocument:async(e,a)=>A.put(`/api/batch-proofreading/unproofread-documents/${e}`,a),batchDeleteDocuments:async e=>A.post("/api/batch-proofreading/unproofread-documents/batch-delete",{documentIds:e}),exportDocuments:async e=>A.get("/api/batch-proofreading/unproofread-documents/export",{params:e,responseType:"blob"}),getProofreadingProgress:async e=>A.get(`/api/ai-batch-proofreading/tasks/${e}/progress`),cancelProofreadingTask:async e=>A.post(`/api/ai-batch-proofreading/tasks/${e}/cancel`)},j=e("unproofreadDocuments",()=>{const e=a([]),l=a({table:!1,search:!1,submit:!1}),o=a({currentPage:1,pageSize:20,pageSizes:[10,20,50,100],total:0}),s=a({totalDocuments:0,pendingDocuments:0,batchableDocuments:0,totalEstimatedCost:0,totalEstimatedTime:0,averageComplexity:0}),r=a([]),n=a({title:"",authorName:"",proofreadingStatus:void 0,priority:void 0,type:void 0,category:void 0,batchable:void 0,dateRange:null}),u=t(()=>e.value.filter(e=>r.value.includes(e.id))),i=t(()=>u.value.filter(e=>e.batchable&&"pending"===e.proofreadingStatus)),c=t(()=>i.value.reduce((e,a)=>e+a.estimatedCost,0)),d=t(()=>i.value.reduce((e,a)=>e+a.estimatedTime,0)),p=async a=>{try{l.value.table=!0;const t={...n.value,...a},s=await E.getUnproofreadDocuments({...t,page:o.value.currentPage,pageSize:o.value.pageSize});return e.value=s?.list||s?.documents||[],o.value.total=s?.total||0,s}catch(t){throw Error,e.value=[],o.value.total=0,t}finally{l.value.table=!1}},m=()=>{n.value={title:"",authorName:"",proofreadingStatus:void 0,priority:void 0,type:void 0,category:void 0,batchable:void 0,dateRange:null}};return{documents:e,loading:l,pagination:o,statistics:s,selectedDocumentIds:r,searchParams:n,selectedDocuments:u,batchableSelectedDocuments:i,totalSelectedCost:c,totalSelectedTime:d,fetchUnproofreadDocuments:p,searchDocuments:async e=>(n.value={...e},o.value.currentPage=1,await p(e)),fetchStatistics:async()=>{try{const e=await E.getStatistics();return s.value=e?.overview?{totalDocuments:e.overview.totalDocuments||0,pendingDocuments:e.overview.pendingDocuments||0,batchableDocuments:e.overview.batchableDocuments||0,totalEstimatedCost:e.estimates?.totalEstimatedCost||0,totalEstimatedTime:e.estimates?.totalEstimatedTime||0,averageComplexity:e.content?.averageComplexityScore||0}:e||s.value,e}catch(e){throw Error,s.value={totalDocuments:0,pendingDocuments:0,batchableDocuments:0,totalEstimatedCost:0,totalEstimatedTime:0,averageComplexity:0},e}},submitSingleProofreading:async a=>{try{l.value.submit=!0;const t=await E.submitSingleProofreading(a),o=e.value.findIndex(e=>e.id===a);return-1!==o&&(e.value[o].proofreadingStatus="queued"),t}catch(t){throw t}finally{l.value.submit=!1}},submitBatchProofreading:async(a,t)=>{try{l.value.submit=!0;const o=await E.submitBatchProofreading(a,t);return a.forEach(a=>{const t=e.value.findIndex(e=>e.id===a);-1!==t&&(e.value[t].proofreadingStatus="queued")}),r.value=[],o}catch(o){throw o}finally{l.value.submit=!1}},deleteDocument:async a=>{try{await E.deleteDocument(a);const t=e.value.findIndex(e=>e.id===a);-1!==t&&(e.value.splice(t,1),o.value.total-=1);const l=r.value.indexOf(a);-1!==l&&r.value.splice(l,1)}catch(t){throw t}},updatePagination:e=>{o.value={...o.value,...e}},setSelectedDocumentIds:e=>{r.value=e},resetSearchParams:m,clearAll:()=>{e.value=[],r.value=[],o.value={currentPage:1,pageSize:20,pageSizes:[10,20,50,100],total:0},s.value={totalDocuments:0,pendingDocuments:0,batchableDocuments:0,totalEstimatedCost:0,totalEstimatedTime:0,averageComplexity:0},m()}}}),M=e("aiProofreading",()=>{const e=a(new Map),l=a(new Map),o=a(new Map),s=a({single:!1,batch:!1}),r=t(()=>e.value.size),n=t(()=>{const e=Array.from(l.value.values());if(0===e.length)return 0;const a=e.reduce((e,a)=>e+a.percentage,0);return Math.round(a/e.length)});return{activeSessions:e,sessionProgress:l,sessionResults:o,loading:s,activeSessionCount:r,totalProgress:n,submitSingleProofreading:async(a,t)=>{try{s.value.single=!0;const r=await B.initialize(U.BATCH_PROOFREADING,t);e.value.set(a.id,r);const n={focus:"all",style:"formal",language:"zh-CN",customOptions:{documentId:a.id,documentTitle:a.title,batchMode:!1}};await B.startProofreading(r,a.content||"",n);const u=setInterval(async()=>{try{const t=await B.getProgress(r);if(l.value.set(r,t),"completed"===t.status||"failed"===t.status){if(clearInterval(u),"completed"===t.status){const e=await B.getResult(r);o.value.set(r,e),V.success(`文档 "${a.title}" 校对完成`)}else V.error(`文档 "${a.title}" 校对失败`);e.value.delete(a.id),await B.cleanup(r)}}catch(t){clearInterval(u)}},2e3);return r}catch(r){throw V.error("提交校对失败"),r}finally{s.value.single=!1}},submitBatchProofreading:async(a,t,r)=>{try{s.value.batch=!0;const n=[],u=a.filter(e=>e.batchable);if(0===u.length)throw new Error("没有可批量处理的文档");const i=t.concurrency||3,c=[];for(let e=0;e<u.length;e+=i)c.push(u.slice(e,e+i));for(const a of c){const l=a.map(async a=>{try{const l=await B.initialize(U.BATCH_PROOFREADING,r);n.push(l),e.value.set(a.id,l);const o={focus:"all",style:"formal",language:"zh-CN",customOptions:{documentId:a.id,documentTitle:a.title,batchMode:!0,priority:t.priority,autoRetry:t.autoRetry,retryCount:t.retryCount}};return await B.startProofreading(l,a.content||"",o),l}catch(l){throw l}});await Promise.all(l)}const d=setInterval(async()=>{try{let a=!0;for(const e of n){const t=await B.getProgress(e);if(l.value.set(e,t),"processing"===t.status||"queued"===t.status)a=!1;else if("completed"===t.status){const a=await B.getResult(e);o.value.set(e,a)}}if(a){clearInterval(d),V.success(`批量校对完成，共处理 ${u.length} 个文档`);for(const[a,t]of e.value.entries())n.includes(t)&&(e.value.delete(a),await B.cleanup(t))}}catch(a){clearInterval(d)}},3e3);return n}catch(n){throw V.error("提交批量校对失败"),n}finally{s.value.batch=!1}},getDocumentProgress:a=>{const t=e.value.get(a);return t&&l.value.get(t)||null},getDocumentResult:a=>{const t=e.value.get(a);return t&&o.value.get(t)||null},cancelDocumentProofreading:async a=>{const t=e.value.get(a);if(t)try{await B.cleanup(t),e.value.delete(a),l.value.delete(t),o.value.delete(t),V.info("已取消校对")}catch(s){V.error("取消校对失败")}},clearAllSessions:async()=>{try{for(const a of e.value.values())await B.cleanup(a);e.value.clear(),l.value.clear(),o.value.clear()}catch(a){}}}}),N={class:"batch-config"},F={class:"document-preview"},O={class:"document-list"},W={class:"doc-info"},L={class:"doc-title"},H={class:"doc-meta"},K={class:"mode-description"},G={key:0},J={key:1},Q={key:2},X={class:"cost-estimation"},Y={class:"cost-item"},Z={class:"cost-value"},ee={class:"cost-item"},ae={class:"cost-value"},te={class:"cost-item"},le={class:"cost-value"},oe={class:"dialog-footer"},se=$(l({__name:"BatchProofreadingDialog",props:{modelValue:{type:Boolean,default:!1},documents:{default:()=>[]}},emits:["update:modelValue","submit"],setup(e,{emit:l}){const h=e,y=l,b=t({get:()=>h.modelValue,set:e=>y("update:modelValue",e)}),w=a(),_=a(!1),k=a({mode:"standard",priority:"normal",concurrency:3,autoRetry:!0,retryCount:2,notifications:["system"],notes:""}),C={mode:[{required:!0,message:"请选择校对模式",trigger:"change"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}],concurrency:[{required:!0,message:"请设置并发数量",trigger:"blur"},{type:"number",min:1,max:10,message:"并发数量必须在1-10之间",trigger:"blur"}],retryCount:[{type:"number",min:1,max:5,message:"重试次数必须在1-5之间",trigger:"blur"}]},S=t(()=>h.documents.filter(e=>e.batchable)),x=t(()=>S.value.reduce((e,a)=>e+a.characterCount,0)),P=t(()=>{const e=S.value.reduce((e,a)=>e+a.estimatedTime,0);return Math.ceil(e/k.value.concurrency)}),D=t(()=>S.value.reduce((e,a)=>e+a.estimatedCost,0)*{fast:.8,standard:1,thorough:1.5}[k.value.mode]),I=async()=>{if(w.value)try{if(await w.value.validate(),0===S.value.length)return void V.warning("没有可批量处理的文档");_.value=!0,y("submit",{...k.value})}catch(e){}finally{_.value=!1}},T=()=>{b.value=!1};return o(b,e=>{e&&(k.value={mode:"standard",priority:"normal",concurrency:3,autoRetry:!0,retryCount:2,notifications:["system"],notes:""})}),(e,a)=>{const t=n("el-tag"),l=n("el-radio"),o=n("el-radio-group"),h=n("el-form-item"),y=n("el-option"),S=n("el-select"),V=n("el-input-number"),z=n("el-switch"),R=n("el-checkbox"),A=n("el-checkbox-group"),B=n("el-input"),U=n("el-form"),q=n("el-col"),$=n("el-row"),E=n("el-button"),j=n("el-dialog");return u(),s(j,{modelValue:b.value,"onUpdate:modelValue":a[7]||(a[7]=e=>b.value=e),title:"批量AI校对配置",width:"600px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:r(()=>[i("div",oe,[c(E,{onClick:T},{default:r(()=>a[20]||(a[20]=[f("取消")])),_:1,__:[20]}),c(E,{type:"primary",loading:_.value,onClick:I},{default:r(()=>a[21]||(a[21]=[f(" 确认提交 ")])),_:1,__:[21]},8,["loading"])])]),default:r(()=>[i("div",N,[i("div",F,[i("h4",null,"选中的文档 ("+d(e.documents.length)+"个)",1),i("div",O,[(u(!0),p(v,null,m(e.documents,e=>(u(),p("div",{key:e.id,class:"document-item"},[i("div",W,[i("span",L,d(e.title),1),i("span",H,d(e.characterCount.toLocaleString())+"字符 | "+d(e.estimatedTime)+"分钟 | ¥"+d(e.estimatedCost.toFixed(2)),1)]),c(t,{type:e.batchable?"success":"warning"},{default:r(()=>[f(d(e.batchable?"可批量":"不可批量"),1)]),_:2},1032,["type"])]))),128))])]),c(U,{model:k.value,rules:C,ref_key:"formRef",ref:w,"label-width":"120px"},{default:r(()=>[c(h,{label:"校对模式",prop:"mode"},{default:r(()=>[c(o,{modelValue:k.value.mode,"onUpdate:modelValue":a[0]||(a[0]=e=>k.value.mode=e)},{default:r(()=>[c(l,{value:"standard"},{default:r(()=>a[8]||(a[8]=[f("标准模式")])),_:1,__:[8]}),c(l,{value:"fast"},{default:r(()=>a[9]||(a[9]=[f("快速模式")])),_:1,__:[9]}),c(l,{value:"thorough"},{default:r(()=>a[10]||(a[10]=[f("深度模式")])),_:1,__:[10]})]),_:1},8,["modelValue"]),i("div",K,["standard"===k.value.mode?(u(),p("span",G,"平衡速度和质量，适合大多数文档")):"fast"===k.value.mode?(u(),p("span",J,"快速处理，适合简单文档")):"thorough"===k.value.mode?(u(),p("span",Q,"深度校对，适合重要文档")):g("",!0)])]),_:1}),c(h,{label:"优先级",prop:"priority"},{default:r(()=>[c(S,{modelValue:k.value.priority,"onUpdate:modelValue":a[1]||(a[1]=e=>k.value.priority=e),style:{width:"100%"}},{default:r(()=>[c(y,{label:"低",value:"low"}),c(y,{label:"普通",value:"normal"}),c(y,{label:"高",value:"high"}),c(y,{label:"紧急",value:"urgent"})]),_:1},8,["modelValue"])]),_:1}),c(h,{label:"并发数量",prop:"concurrency"},{default:r(()=>[c(V,{modelValue:k.value.concurrency,"onUpdate:modelValue":a[2]||(a[2]=e=>k.value.concurrency=e),min:1,max:10,style:{width:"100%"}},null,8,["modelValue"]),a[11]||(a[11]=i("div",{class:"field-description"},"同时处理的文档数量，数量越大处理越快但消耗资源越多",-1))]),_:1,__:[11]}),c(h,{label:"自动重试",prop:"autoRetry"},{default:r(()=>[c(z,{modelValue:k.value.autoRetry,"onUpdate:modelValue":a[3]||(a[3]=e=>k.value.autoRetry=e)},null,8,["modelValue"]),a[12]||(a[12]=i("span",{class:"field-description"},"失败时自动重试",-1))]),_:1,__:[12]}),k.value.autoRetry?(u(),s(h,{key:0,label:"重试次数",prop:"retryCount"},{default:r(()=>[c(V,{modelValue:k.value.retryCount,"onUpdate:modelValue":a[4]||(a[4]=e=>k.value.retryCount=e),min:1,max:5,style:{width:"100%"}},null,8,["modelValue"])]),_:1})):g("",!0),c(h,{label:"通知设置",prop:"notifications"},{default:r(()=>[c(A,{modelValue:k.value.notifications,"onUpdate:modelValue":a[5]||(a[5]=e=>k.value.notifications=e)},{default:r(()=>[c(R,{value:"email"},{default:r(()=>a[13]||(a[13]=[f("邮件通知")])),_:1,__:[13]}),c(R,{value:"sms"},{default:r(()=>a[14]||(a[14]=[f("短信通知")])),_:1,__:[14]}),c(R,{value:"system"},{default:r(()=>a[15]||(a[15]=[f("系统通知")])),_:1,__:[15]})]),_:1},8,["modelValue"])]),_:1}),c(h,{label:"备注",prop:"notes"},{default:r(()=>[c(B,{modelValue:k.value.notes,"onUpdate:modelValue":a[6]||(a[6]=e=>k.value.notes=e),type:"textarea",rows:3,placeholder:"可选的备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),i("div",X,[a[19]||(a[19]=i("h4",null,"成本预估",-1)),c($,{gutter:16},{default:r(()=>[c(q,{span:8},{default:r(()=>[i("div",Y,[a[16]||(a[16]=i("div",{class:"cost-label"},"总字符数",-1)),i("div",Z,d(x.value.toLocaleString()),1)])]),_:1}),c(q,{span:8},{default:r(()=>[i("div",ee,[a[17]||(a[17]=i("div",{class:"cost-label"},"预估时间",-1)),i("div",ae,d(P.value)+"分钟",1)])]),_:1}),c(q,{span:8},{default:r(()=>[i("div",te,[a[18]||(a[18]=i("div",{class:"cost-label"},"预估成本",-1)),i("div",le,"¥"+d(D.value.toFixed(2)),1)])]),_:1})]),_:1})])])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-1424393d"]]),re={class:"progress-container"},ne={class:"overall-progress"},ue={class:"progress-stats"},ie={class:"document-progress"},ce={class:"document-list"},de={class:"doc-info"},pe={class:"doc-title"},me={class:"doc-meta"},ve={class:"doc-progress"},ge={class:"doc-actions"},fe={key:0,class:"error-section"},he={class:"error-list"},ye={class:"dialog-footer"},be=$(l({__name:"BatchProgressDialog",props:{modelValue:{type:Boolean,default:!1},documentProgress:{default:()=>[]},overallProgress:{default:0},completedCount:{default:0},totalCount:{default:0},estimatedTimeRemaining:{default:0}},emits:["update:modelValue","cancel-document","retry-document","view-result","cancel-all","export-results"],setup(e,{emit:l}){const y=e,b=l,w=t({get:()=>y.modelValue,set:e=>b("update:modelValue",e)}),_=a([]),k=t(()=>y.documentProgress.some(e=>"processing"===e.status||"queued"===e.status)),C=t(()=>y.documentProgress.some(e=>"completed"===e.status)),S=t(()=>100===y.overallProgress?"success":_.value.length>0?"exception":void 0),x=e=>({queued:"info",processing:"warning",completed:"success",failed:"danger",cancelled:"info"}[e]||"info"),P=e=>({queued:"队列中",processing:"处理中",completed:"已完成",failed:"失败",cancelled:"已取消"}[e]||e),D=async()=>{try{await I.confirm("确定要取消所有正在处理的文档吗？","确认取消",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),b("cancel-all")}catch{}},V=()=>{b("export-results")},T=()=>{w.value=!1};return o(()=>y.documentProgress,e=>{_.value=e.filter(e=>"failed"===e.status&&e.error).map(e=>({title:`文档 "${e.title}" 处理失败`,message:e.error||"未知错误"}))},{deep:!0}),(e,a)=>{const t=n("el-progress"),l=n("el-tag"),o=n("el-button"),y=n("el-alert"),z=n("el-dialog");return u(),s(z,{modelValue:w.value,"onUpdate:modelValue":a[0]||(a[0]=e=>w.value=e),title:"批量校对进度",width:"800px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!k.value},{footer:r(()=>[i("div",ye,[k.value?g("",!0):(u(),s(o,{key:0,onClick:T},{default:r(()=>a[7]||(a[7]=[f(" 关闭 ")])),_:1,__:[7]})),k.value?(u(),s(o,{key:1,type:"danger",onClick:D},{default:r(()=>a[8]||(a[8]=[f(" 取消全部 ")])),_:1,__:[8]})):g("",!0),C.value&&!k.value?(u(),s(o,{key:2,type:"primary",onClick:V},{default:r(()=>a[9]||(a[9]=[f(" 导出结果 ")])),_:1,__:[9]})):g("",!0)])]),default:r(()=>{return[i("div",re,[i("div",ne,[a[1]||(a[1]=i("h4",null,"总体进度",-1)),c(t,{percentage:e.overallProgress,status:S.value,"stroke-width":12,"text-inside":""},null,8,["percentage","status"]),i("div",ue,[i("span",null,"已完成: "+d(e.completedCount)+" / "+d(e.totalCount),1),i("span",null,"预计剩余时间: "+d((n=e.estimatedTimeRemaining,n<60?`${n}秒`:n<3600?`${Math.ceil(n/60)}分钟`:`${Math.ceil(n/3600)}小时`)),1)])]),i("div",ie,[a[5]||(a[5]=i("h4",null,"文档处理状态",-1)),i("div",ce,[(u(!0),p(v,null,m(e.documentProgress,e=>{return u(),p("div",{key:e.documentId,class:h(["document-item",(n=e.status,`status-${n}`)])},[i("div",de,[i("div",pe,d(e.title),1),i("div",me,d(e.characterCount.toLocaleString())+"字符 | 预估"+d(e.estimatedTime)+"分钟 ",1)]),i("div",ve,["processing"===e.status?(u(),s(t,{key:0,percentage:e.progress,"stroke-width":6,"text-inside":""},null,8,["percentage"])):(u(),s(l,{key:1,type:x(e.status),size:"small"},{default:r(()=>[f(d(P(e.status)),1)]),_:2},1032,["type"]))]),i("div",ge,["processing"===e.status||"queued"===e.status?(u(),s(o,{key:0,type:"danger",size:"small",text:"",onClick:a=>(async e=>{try{await I.confirm("确定要取消该文档的校对吗？","确认取消",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),b("cancel-document",e)}catch{}})(e.documentId)},{default:r(()=>a[2]||(a[2]=[f(" 取消 ")])),_:2,__:[2]},1032,["onClick"])):g("",!0),"completed"===e.status?(u(),s(o,{key:1,type:"primary",size:"small",text:"",onClick:a=>{return t=e.documentId,void b("view-result",t);var t}},{default:r(()=>a[3]||(a[3]=[f(" 查看结果 ")])),_:2,__:[3]},1032,["onClick"])):g("",!0),"failed"===e.status?(u(),s(o,{key:2,type:"warning",size:"small",text:"",onClick:a=>{return t=e.documentId,void b("retry-document",t);var t}},{default:r(()=>a[4]||(a[4]=[f(" 重试 ")])),_:2,__:[4]},1032,["onClick"])):g("",!0)])],2);var n}),128))])]),_.value.length>0?(u(),p("div",fe,[a[6]||(a[6]=i("h4",null,"错误信息",-1)),i("div",he,[(u(!0),p(v,null,m(_.value,(e,a)=>(u(),p("div",{key:a,class:"error-item"},[c(y,{title:e.title,description:e.message,type:"error","show-icon":"",closable:!1},null,8,["title","description"])]))),128))])])):g("",!0)])];var n}),_:1},8,["modelValue","show-close"])}}}),[["__scopeId","data-v-949f3d61"]]),we={class:"unproofread-documents"},_e={class:"card-header"},ke={class:"header-actions"},Ce={class:"statistics-section"},Se={class:"stat-content"},xe={class:"stat-number"},Pe={class:"stat-content"},De={class:"stat-number"},Ve={class:"stat-content"},Ie={class:"stat-number"},Te={class:"stat-content"},ze={class:"stat-number"},Re={class:"search-section"},Ae={class:"table-section"},Be={class:"action-buttons"},Ue={class:"pagination-section"},qe={key:0,class:"progress-indicator"},$e={class:"progress-header"},Ee={class:"progress-info"},je=$(l({__name:"UnproofreadDocuments",setup(e){const l=D(),h=P(),A=j(),B=M(),{documents:U,loading:$,pagination:E,statistics:N}=y(A),{fetchUnproofreadDocuments:F,searchDocuments:O,deleteDocument:W,updatePagination:L,setSelectedDocumentIds:H,resetSearchParams:K,submitSingleProofreading:G,submitBatchProofreading:J,fetchStatistics:Q}=A,{submitSingleProofreading:X,submitBatchProofreading:Y,getDocumentProgress:Z,getDocumentResult:ee,cancelDocumentProofreading:ae,clearAllSessions:te}=B,{activeSessions:le,sessionProgress:oe,totalProgress:re,activeSessionCount:ne}=y(B),{getTypeIcon:ue,formatDateTime:ie}=q(),ce=a({aiModel:{provider:"deepseek",model:"deepseek-chat",name:"DeepSeek Chat"},promptTemplate:{id:"default",name:"标准校对",content:"请对以下文本进行AI智能校对，重点关注语法、拼写、标点符号和表达规范性：\n\n{{content}}"},costBudget:100}),de=a({title:"",authorName:"",proofreadingStatus:void 0,priority:void 0,type:void 0,category:void 0,batchable:void 0,dateRange:null}),pe=a([]),me=a(!1),ve=a(!1),ge=t(()=>U.value.filter(e=>le.value.has(e.id)).map(e=>{const a=Z(e.id);return{documentId:e.id,title:e.title,characterCount:e.characterCount||0,estimatedTime:e.estimatedTime||0,status:a?.status||"queued",progress:a?.percentage||0,error:a?.error}})),fe=t(()=>re.value),he=t(()=>ge.value.filter(e=>"completed"===e.status).length),ye=t(()=>ge.value.length),je=t(()=>60*ge.value.filter(e=>"processing"===e.status||"queued"===e.status).reduce((e,a)=>e+a.estimatedTime,0)),Me=[{value:"pending",label:"待校对"},{value:"queued",label:"队列中"},{value:"processing",label:"校对中"},{value:"completed",label:"已完成"},{value:"failed",label:"失败"},{value:"cancelled",label:"已取消"}],Ne=[{value:"low",label:"低"},{value:"normal",label:"普通"},{value:"high",label:"高"},{value:"urgent",label:"紧急"}],Fe=[{value:"text",label:"纯文本"},{value:"docx",label:"Word文档"},{value:"pdf",label:"PDF文档"},{value:"wps",label:"WPS文档"}],Oe=[{value:"技术文档",label:"技术文档"},{value:"产品说明",label:"产品说明"},{value:"用户手册",label:"用户手册"},{value:"营销文案",label:"营销文案"},{value:"法律文件",label:"法律文件"},{value:"学术论文",label:"学术论文"},{value:"新闻报道",label:"新闻报道"},{value:"教育培训",label:"教育培训"},{value:"政策解读",label:"政策解读"},{value:"行业分析",label:"行业分析"}],We=e=>({text:"纯文本",docx:"Word",pdf:"PDF",wps:"WPS"}[e]||e),Le=e=>({pending:"待校对",queued:"队列中",processing:"校对中",completed:"已完成",failed:"失败",cancelled:"已取消"}[e]||e),He=e=>({low:"低",normal:"普通",high:"高",urgent:"紧急"}[e]||e),Ke=e=>"pending"===e.proofreadingStatus&&e.batchable,Ge=async()=>{try{$.value.search=!0,await O(de.value)}catch(e){V.error("搜索失败")}finally{$.value.search=!1}},Je=async()=>{de.value={title:"",authorName:"",proofreadingStatus:void 0,priority:void 0,type:void 0,category:void 0,batchable:void 0,dateRange:null},await Ge()},Qe=async()=>{try{$.value.table=!0,await F(),await Q(),V.success("刷新成功")}catch(e){V.error("刷新失败")}finally{$.value.table=!1}},Xe=e=>{pe.value=e,H(e.map(e=>e.id))},Ye=async e=>{if(e.prop&&e.order)try{$.value.table=!0;const a=e.prop,t="ascending"===e.order?"asc":"desc";await F({sortField:a,sortOrder:t})}catch(a){V.error("排序失败，请重试")}finally{$.value.table=!1}else await F()},Ze=async e=>{L({pageSize:e,currentPage:1}),await Ge()},ea=async e=>{L({currentPage:e}),await Ge()},aa=e=>{h.push({name:"DocumentDetail",params:{id:e.id}})},ta=()=>{if(0===pe.value.length)return void V.warning("请先选择要校对的文档");0!==pe.value.filter(e=>e.batchable).length?me.value=!0:V.warning("所选文档中没有可批量处理的文档")},la=async e=>{try{await Y(pe.value,e,ce.value),V.success("批量AI校对任务已提交，正在处理中..."),me.value=!1,ve.value=!0,pe.value=[],await Qe()}catch(a){V.error("提交批量AI校对任务失败")}},oa=async e=>{try{await ae(e),V.success("已取消文档校对"),await Qe()}catch(a){V.error("取消文档校对失败")}},sa=async e=>{try{const a=U.value.find(a=>a.id===e);a&&(await X(a,ce.value),V.success("已重新提交文档校对"),await Qe())}catch(a){V.error("重试文档校对失败")}},ra=e=>{ee(e)?h.push({name:"ProofreadingResult",params:{id:e},query:{sessionId:le.value.get(e)}}):V.warning("校对结果不存在")},na=async()=>{try{await te(),V.success("已取消所有校对任务"),ve.value=!1,await Qe()}catch(e){V.error("取消所有任务失败")}},ua=()=>{0!==ge.value.filter(e=>"completed"===e.status).length?V.info("导出功能开发中..."):V.warning("没有已完成的校对结果可导出")},ia=e=>{const a=Z(e);return"processing"===a?.status||"queued"===a?.status},ca=({row:e})=>{const a=Z(e.id);if(a)switch(a.status){case"processing":case"queued":return"processing-row";case"completed":return"completed-row";case"failed":return"failed-row";default:return""}return""};o(()=>l.query,e=>{e.title&&(de.value.title=e.title),e.authorName&&(de.value.authorName=e.authorName),e.proofreadingStatus&&(de.value.proofreadingStatus=e.proofreadingStatus),e.priority&&(de.value.priority=e.priority),e.type&&(de.value.type=e.type),e.category&&(de.value.category=e.category),void 0!==e.batchable&&(de.value.batchable="true"===e.batchable)},{immediate:!0});b(async()=>{try{$.value.table=!0;await(async(e=1e4)=>{const a=Date.now();for(;Date.now()-a<e;){if(window.__mockWorker){const e=window.__mockWorker.listHandlers();if(e&&e.length>0)return!0}if("serviceWorker"in navigator){const e=(await navigator.serviceWorker.getRegistrations()).find(e=>e.active?.scriptURL.includes("mockServiceWorker"));if(e&&"activated"===e.active?.state)return await new Promise(e=>setTimeout(e,1e3)),!0}await new Promise(e=>setTimeout(e,500))}return!1})();try{const e=await fetch("/api/test",{headers:{Authorization:`Bearer ${localStorage.getItem("access_token")}`}});await e.json()}catch(e){}const[a,t]=await Promise.all([F(),Q()]);U.value.length,V.success("数据加载成功")}catch(a){V.error("加载数据失败")}finally{$.value.table=!1}});return window.__testBatchProofreadingApi=async()=>{try{const e=await fetch("/api/batch-proofreading/unproofread-documents?page=1&pageSize=10",{headers:{Authorization:"Bearer dev-mock-token-12345","Content-Type":"application/json"}});if(e.ok){await e.json()}const a=await fetch("/api/batch-proofreading/unproofread-documents/statistics",{headers:{Authorization:"Bearer dev-mock-token-12345","Content-Type":"application/json"}});if(a.ok){await a.json()}}catch(e){}},(e,a)=>{const t=n("el-icon"),l=n("el-button"),o=n("el-card"),y=n("el-col"),b=n("el-row"),P=n("el-input"),D=n("el-form-item"),A=n("el-option"),B=n("el-select"),q=n("el-form"),j=n("el-table-column"),M=n("el-link"),F=n("el-tag"),O=n("el-progress"),L=n("ArrowDown"),H=n("el-dropdown-item"),K=n("el-dropdown-menu"),G=n("el-dropdown"),J=n("el-table"),Q=n("el-pagination"),Y=S("loading");return u(),p("div",we,[c(o,null,{header:r(()=>[i("div",_e,[a[13]||(a[13]=i("span",null,"AI批量审校 - 未校对文档",-1)),i("div",ke,[c(l,{type:"primary",size:"default",disabled:0===pe.value.length,onClick:ta},{default:r(()=>[c(t,null,{default:r(()=>[c(_(R))]),_:1}),f(" 批量校对 ("+d(pe.value.length)+") ",1)]),_:1},8,["disabled"]),c(l,{type:"info",size:"default",onClick:Qe},{default:r(()=>[c(t,null,{default:r(()=>[c(_(z))]),_:1}),a[12]||(a[12]=f(" 刷新 "))]),_:1,__:[12]})])])]),default:r(()=>[i("div",Ce,[c(b,{gutter:16},{default:r(()=>[c(y,{span:6},{default:r(()=>[c(o,{class:"stat-card"},{default:r(()=>[i("div",Se,[i("div",xe,d(_(N).totalDocuments),1),a[14]||(a[14]=i("div",{class:"stat-label"},"总文档数",-1))])]),_:1})]),_:1}),c(y,{span:6},{default:r(()=>[c(o,{class:"stat-card"},{default:r(()=>[i("div",Pe,[i("div",De,d(_(N).pendingDocuments),1),a[15]||(a[15]=i("div",{class:"stat-label"},"待校对",-1))])]),_:1})]),_:1}),c(y,{span:6},{default:r(()=>[c(o,{class:"stat-card"},{default:r(()=>[i("div",Ve,[i("div",Ie,d(_(N).batchableDocuments),1),a[16]||(a[16]=i("div",{class:"stat-label"},"可批量处理",-1))])]),_:1})]),_:1}),c(y,{span:6},{default:r(()=>[c(o,{class:"stat-card"},{default:r(()=>[i("div",Te,[i("div",ze,d((_(N).totalEstimatedCost||0).toFixed(2))+"元 ",1),a[17]||(a[17]=i("div",{class:"stat-label"},"预估总成本",-1))])]),_:1})]),_:1})]),_:1})]),i("div",Re,[c(q,{model:de.value,inline:"",class:"search-form"},{default:r(()=>[c(D,{label:"文档标题"},{default:r(()=>[c(P,{modelValue:de.value.title,"onUpdate:modelValue":a[0]||(a[0]=e=>de.value.title=e),placeholder:"请输入文档标题",clearable:"",style:{width:"200px"},onKeyup:k(Ge,["enter"])},null,8,["modelValue"])]),_:1}),c(D,{label:"作者姓名"},{default:r(()=>[c(P,{modelValue:de.value.authorName,"onUpdate:modelValue":a[1]||(a[1]=e=>de.value.authorName=e),placeholder:"请输入作者姓名",clearable:"",style:{width:"150px"},onKeyup:k(Ge,["enter"])},null,8,["modelValue"])]),_:1}),c(D,{label:"校对状态"},{default:r(()=>[c(B,{modelValue:de.value.proofreadingStatus,"onUpdate:modelValue":a[2]||(a[2]=e=>de.value.proofreadingStatus=e),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:r(()=>[(u(),p(v,null,m(Me,e=>c(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),c(D,{label:"优先级"},{default:r(()=>[c(B,{modelValue:de.value.priority,"onUpdate:modelValue":a[3]||(a[3]=e=>de.value.priority=e),placeholder:"请选择优先级",clearable:"",style:{width:"120px"}},{default:r(()=>[(u(),p(v,null,m(Ne,e=>c(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),c(D,{label:"文档类型"},{default:r(()=>[c(B,{modelValue:de.value.type,"onUpdate:modelValue":a[4]||(a[4]=e=>de.value.type=e),placeholder:"请选择类型",clearable:"",style:{width:"120px"}},{default:r(()=>[(u(),p(v,null,m(Fe,e=>c(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),c(D,{label:"文档分类"},{default:r(()=>[c(B,{modelValue:de.value.category,"onUpdate:modelValue":a[5]||(a[5]=e=>de.value.category=e),placeholder:"请选择分类",clearable:"",style:{width:"120px"}},{default:r(()=>[(u(),p(v,null,m(Oe,e=>c(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),c(D,{label:"是否可批量"},{default:r(()=>[c(B,{modelValue:de.value.batchable,"onUpdate:modelValue":a[6]||(a[6]=e=>de.value.batchable=e),placeholder:"请选择",clearable:"",style:{width:"120px"}},{default:r(()=>[c(A,{label:"可批量",value:!0}),c(A,{label:"不可批量",value:!1})]),_:1},8,["modelValue"])]),_:1}),c(D,null,{default:r(()=>[c(l,{type:"primary",loading:_($).search,onClick:Ge},{default:r(()=>[c(t,null,{default:r(()=>[c(_(T))]),_:1}),a[18]||(a[18]=f(" 搜索 "))]),_:1,__:[18]},8,["loading"]),c(l,{onClick:Je},{default:r(()=>[c(t,null,{default:r(()=>[c(_(z))]),_:1}),a[19]||(a[19]=f(" 重置 "))]),_:1,__:[19]})]),_:1})]),_:1},8,["model"])]),i("div",Ae,[C((u(),s(J,{data:_(U),stripe:"",border:"",style:{width:"100%"},"row-class-name":ca,onSelectionChange:Xe,onSortChange:Ye},{default:r(()=>[c(j,{type:"selection",width:"55",selectable:Ke}),c(j,{label:"序号",width:"60",align:"center"},{default:r(({$index:e})=>[f(d((_(E).currentPage-1)*_(E).pageSize+e+1),1)]),_:1}),c(j,{prop:"title",label:"标题",width:"200",sortable:"custom","show-overflow-tooltip":""},{default:r(({row:e})=>[c(M,{type:"primary",onClick:a=>aa(e)},{default:r(()=>[f(d(e.title),1)]),_:2},1032,["onClick"])]),_:1}),c(j,{prop:"type",label:"类型",width:"100",sortable:"custom"},{default:r(({row:e})=>{return[c(F,{type:(a=e.type,{text:"info",docx:"primary",pdf:"danger",wps:"warning"}[a]||"info")},{default:r(()=>[c(t,null,{default:r(()=>[(u(),s(x(_(ue)(e.type))))]),_:2},1024),f(" "+d(We(e.type)),1)]),_:2},1032,["type"])];var a}),_:1}),c(j,{prop:"proofreadingStatus",label:"校对状态",width:"120",sortable:"custom"},{default:r(({row:e})=>{return[c(F,{type:(a=e.proofreadingStatus,{pending:"warning",queued:"info",processing:"primary",completed:"success",failed:"danger",cancelled:"info"}[a]||"info")},{default:r(()=>[f(d(Le(e.proofreadingStatus)),1)]),_:2},1032,["type"])];var a}),_:1}),c(j,{prop:"priority",label:"优先级",width:"100",sortable:"custom"},{default:r(({row:e})=>{return[c(F,{type:(a=e.priority,{low:"info",normal:"primary",high:"warning",urgent:"danger"}[a]||"info")},{default:r(()=>[f(d(He(e.priority)),1)]),_:2},1032,["type"])];var a}),_:1}),c(j,{prop:"category",label:"分类",width:"120",sortable:"custom","show-overflow-tooltip":""}),c(j,{prop:"authorName",label:"作者姓名",width:"120",sortable:"custom","show-overflow-tooltip":""}),c(j,{prop:"characterCount",label:"字符数",width:"100",sortable:"custom"},{default:r(({row:e})=>{return[f(d((a=e.characterCount,a.toLocaleString())),1)];var a}),_:1}),c(j,{prop:"complexityScore",label:"复杂度",width:"100",sortable:"custom"},{default:r(({row:e})=>{return[c(O,{percentage:e.complexityScore,color:(a=e.complexityScore,a<30?"#67c23a":a<60?"#e6a23c":a<80?"#f56c6c":"#909399"),"stroke-width":8,"text-inside":""},null,8,["percentage","color"])];var a}),_:1}),c(j,{prop:"estimatedTime",label:"预估时间",width:"100",sortable:"custom"},{default:r(({row:e})=>[f(d(e.estimatedTime)+"分钟 ",1)]),_:1}),c(j,{prop:"estimatedCost",label:"预估成本",width:"100",sortable:"custom"},{default:r(({row:e})=>[f(" ¥"+d(e.estimatedCost.toFixed(2)),1)]),_:1}),c(j,{prop:"batchable",label:"可批量",width:"80",align:"center"},{default:r(({row:e})=>[c(F,{type:e.batchable?"success":"info"},{default:r(()=>[f(d(e.batchable?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),c(j,{prop:"createdAt",label:"创建时间",width:"160",sortable:"custom"},{default:r(({row:e})=>[f(d(_(ie)(e.createdAt)),1)]),_:1}),c(j,{label:"操作",width:"280",fixed:"right"},{default:r(({row:e})=>[i("div",Be,[c(l,{type:"primary",size:"small",onClick:a=>aa(e)},{default:r(()=>a[20]||(a[20]=[f(" 详情 ")])),_:2,__:[20]},1032,["onClick"]),c(l,{type:"warning",size:"small",disabled:"pending"!==e.proofreadingStatus||ia(e.id),loading:ia(e.id),onClick:a=>(async e=>{try{await I.confirm(`确定要对文档"${e.title}"进行AI校对吗？\n预估时间：${e.estimatedTime}分钟\n预估成本：¥${e.estimatedCost.toFixed(2)}`,"确认AI校对",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await X(e,ce.value),V.success("AI校对任务已提交，正在处理中..."),await Qe()}catch(a){"cancel"!==a&&V.error("提交AI校对任务失败")}})(e)},{default:r(()=>[f(d(ia(e.id)?"校对中":"AI校对"),1)]),_:2},1032,["disabled","loading","onClick"]),c(G,{trigger:"click"},{dropdown:r(()=>[c(K,null,{default:r(()=>{return[c(H,{onClick:a=>(e=>{h.push({name:"DocumentEdit",params:{id:e.id}})})(e)},{default:r(()=>a[22]||(a[22]=[f("编辑")])),_:2,__:[22]},1032,["onClick"]),ia(e.id)?(u(),s(H,{key:0,onClick:a=>oa(e.id)},{default:r(()=>a[23]||(a[23]=[f(" 取消校对 ")])),_:2,__:[23]},1032,["onClick"])):g("",!0),(t=e.id,ee(t)?(u(),s(H,{key:1,onClick:a=>ra(e.id)},{default:r(()=>a[24]||(a[24]=[f(" 查看结果 ")])),_:2,__:[24]},1032,["onClick"])):g("",!0)),c(H,{onClick:a=>(async e=>{try{await I.confirm(`确定要删除文档"${e.title}"吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await W(e.id),V.success("删除成功"),await Qe()}catch(a){"cancel"!==a&&V.error("删除失败")}})(e)},{default:r(()=>a[25]||(a[25]=[f("删除")])),_:2,__:[25]},1032,["onClick"])];var t}),_:2},1024)]),default:r(()=>[c(l,{type:"info",size:"small"},{default:r(()=>[a[21]||(a[21]=f(" 更多 ")),c(t,{class:"el-icon--right"},{default:r(()=>[c(L)]),_:1})]),_:1,__:[21]})]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Y,_($).table]]),i("div",Ue,[c(Q,{"current-page":_(E).currentPage,"onUpdate:currentPage":a[7]||(a[7]=e=>_(E).currentPage=e),"page-size":_(E).pageSize,"onUpdate:pageSize":a[8]||(a[8]=e=>_(E).pageSize=e),"page-sizes":_(E).pageSizes,total:_(E).total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ze,onCurrentChange:ea},null,8,["current-page","page-size","page-sizes","total"])])])]),_:1}),c(se,{modelValue:me.value,"onUpdate:modelValue":a[9]||(a[9]=e=>me.value=e),documents:pe.value,onSubmit:la},null,8,["modelValue","documents"]),c(be,{modelValue:ve.value,"onUpdate:modelValue":a[10]||(a[10]=e=>ve.value=e),"document-progress":ge.value,"overall-progress":fe.value,"completed-count":he.value,"total-count":ye.value,"estimated-time-remaining":je.value,onCancelDocument:oa,onRetryDocument:sa,onViewResult:ra,onCancelAll:na,onExportResults:ua},null,8,["modelValue","document-progress","overall-progress","completed-count","total-count","estimated-time-remaining"]),c(w,{name:"fade"},{default:r(()=>[_(ne)>0?(u(),p("div",qe,[i("div",$e,[a[27]||(a[27]=i("h4",null,"AI校对进度",-1)),c(l,{type:"text",size:"small",onClick:a[11]||(a[11]=e=>ve.value=!0)},{default:r(()=>a[26]||(a[26]=[f(" 查看详情 ")])),_:1,__:[26]})]),c(O,{percentage:fe.value,"stroke-width":8,"text-inside":""},null,8,["percentage"]),i("div",Ee,[i("span",null,d(he.value)+" / "+d(ye.value)+" 已完成",1)])])):g("",!0)]),_:1})])}}}),[["__scopeId","data-v-37161d5f"]]);export{je as default};
