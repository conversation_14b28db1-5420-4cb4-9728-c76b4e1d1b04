{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/api/index.ts", "../../src/api/modules/batchproofreading.ts", "../../src/api/modules/prereview.ts", "../../src/components/helloworld.vue", "../../src/components/thewelcome.vue", "../../src/components/welcomeitem.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/features/ai-batch-proofreading/test-setup.ts", "../../src/features/ai-batch-proofreading/vitest.config.ts", "../../src/features/ai-batch-proofreading/api/unproofreaddocuments.ts", "../../src/features/ai-batch-proofreading/components/batchprogressdialog.vue", "../../src/features/ai-batch-proofreading/components/batchproofreadingdialog.vue", "../../src/features/ai-batch-proofreading/components/configpanel.vue", "../../src/features/ai-batch-proofreading/components/errorhandler.vue", "../../src/features/ai-batch-proofreading/components/exportmanager.vue", "../../src/features/ai-batch-proofreading/components/filedetailsdialog.vue", "../../src/features/ai-batch-proofreading/components/filemanager.vue", "../../src/features/ai-batch-proofreading/components/fileuploadarea.vue", "../../src/features/ai-batch-proofreading/components/loadingstates.vue", "../../src/features/ai-batch-proofreading/components/operationcontroller.vue", "../../src/features/ai-batch-proofreading/components/progressmonitor.vue", "../../src/features/ai-batch-proofreading/components/resourcemonitor.vue", "../../src/features/ai-batch-proofreading/components/resultpreview.vue", "../../src/features/ai-batch-proofreading/plugins/index.ts", "../../src/features/ai-batch-proofreading/plugins/persistenceplugin.ts", "../../src/features/ai-batch-proofreading/plugins/syncplugin.ts", "../../src/features/ai-batch-proofreading/stores/aiproofreadingstore.ts", "../../src/features/ai-batch-proofreading/stores/batchproofreadingstore.ts", "../../src/features/ai-batch-proofreading/stores/unproofreaddocumentsstore.ts", "../../src/features/ai-batch-proofreading/tests/compatibility/browsercompatibility.test.ts", "../../src/features/ai-batch-proofreading/tests/e2e/batchproofreadinge2e.test.ts", "../../src/features/ai-batch-proofreading/tests/performance/stresstest.ts", "../../src/features/ai-batch-proofreading/types/index.ts", "../../src/features/ai-batch-proofreading/types/store-types.ts", "../../src/features/ai-batch-proofreading/types/unproofread-documents.ts", "../../src/features/ai-batch-proofreading/utils/responsetimemonitor.ts", "../../src/features/ai-batch-proofreading/utils/responsivehelper.ts", "../../src/features/ai-batch-proofreading/utils/statesyncmonitor.ts", "../../src/features/ai-batch-proofreading/views/batchproofreadingview.vue", "../../src/features/ai-batch-proofreading/views/completedproofread.vue", "../../src/features/ai-batch-proofreading/views/pendingproofread.vue", "../../src/features/ai-batch-proofreading/views/unproofreaddocuments.vue", "../../src/features/audio-proofreading/views/audiobatchproofreadingview.vue", "../../src/features/audio-proofreading/views/audiocompletedproofreadingview.vue", "../../src/features/audio-proofreading/views/audioonlineproofreadingview.vue", "../../src/features/audio-proofreading/views/audiopendingproofreadingview.vue", "../../src/features/audio-proofreading/views/audioproofreadingview.vue", "../../src/features/content-review/components/createdocumentdialog.vue", "../../src/features/content-review/components/documentdetaildialog.vue", "../../src/features/content-review/components/exportdialog.vue", "../../src/features/content-review/components/reviewreportdialog.vue", "../../src/features/content-review/components/ai-proofreading/aiconfigpanel.vue", "../../src/features/content-review/components/ai-proofreading/documentconverter.vue", "../../src/features/content-review/components/ai-proofreading/documentinfopanel.vue", "../../src/features/content-review/components/ai-proofreading/documentsplitter.vue", "../../src/features/content-review/components/ai-proofreading/tasklistpanel.vue", "../../src/features/content-review/composables/usedocumenttable.ts", "../../src/features/content-review/stores/aiproofreadingstore.ts", "../../src/features/content-review/stores/prereviewstore.ts", "../../src/features/content-review/stores/revieweddocumentsstore.ts", "../../src/features/content-review/tests/ai-proofreading-validation.ts", "../../src/features/content-review/types/ai-proofreading.ts", "../../src/features/content-review/types/index.ts", "../../src/features/content-review/utils/errorhandler.ts", "../../src/features/content-review/utils/keyboardshortcuts.ts", "../../src/features/content-review/views/aiproofreadingpage.vue", "../../src/features/content-review/views/revieweddocuments.vue", "../../src/features/content-review/views/unrevieweddocuments.vue", "../../src/features/dashboard/views/dashboardview.vue", "../../src/features/document-library/views/existingproofreadingcomments.vue", "../../src/features/document-library/views/existingreviewcomments.vue", "../../src/features/document-library/views/newproofreadingcomments.vue", "../../src/features/document-library/views/newreviewcomments.vue", "../../src/features/document-library/views/otherdocuments.vue", "../../src/features/image-proofreading/views/imagebatchproofreadingview.vue", "../../src/features/image-proofreading/views/imagecompletedproofreadingview.vue", "../../src/features/image-proofreading/views/imageonlineproofreadingview.vue", "../../src/features/image-proofreading/views/imagependingproofreadingview.vue", "../../src/features/image-proofreading/views/imageproofreadingview.vue", "../../src/features/modification-accumulation/views/addcaseset.vue", "../../src/features/modification-accumulation/views/queryproofreadingcases.vue", "../../src/features/modification-accumulation/views/queryreviewcases.vue", "../../src/features/modification-accumulation/views/reviewcaseset.vue", "../../src/features/modification-accumulation/views/synccaseset.vue", "../../src/features/online-proofreading/components/documentupload.vue", "../../src/features/online-proofreading/components/originaltexteditor.vue", "../../src/features/online-proofreading/components/pdfpreview.vue", "../../src/features/online-proofreading/components/performancemonitor.vue", "../../src/features/online-proofreading/components/proofreadresulteditor.vue", "../../src/features/online-proofreading/components/proofreadingprogress.vue", "../../src/features/online-proofreading/components/proofreadingtoolbar.vue", "../../src/features/online-proofreading/stores/aiproofreadingstore.ts", "../../src/features/online-proofreading/types/index.ts", "../../src/features/online-proofreading/views/aiproofreadingsystem.vue", "../../src/features/online-proofreading/views/onlineeditor.vue", "../../src/features/online-proofreading/views/proofreadhistory.vue", "../../src/features/professional-query/views/classicalliteraturequery.vue", "../../src/features/professional-query/views/dictionaryquery.vue", "../../src/features/professional-query/views/importantspeechesquery.vue", "../../src/features/professional-query/views/legalregulationsquery.vue", "../../src/features/professional-query/views/officialreportsquery.vue", "../../src/features/professional-query/views/otherquery.vue", "../../src/features/professional-query/views/policyquery.vue", "../../src/features/professional-query/views/standardsquery.vue", "../../src/features/professional-query/views/terminologyquery.vue", "../../src/features/professional-typesetting/views/chineseformatting.vue", "../../src/features/professional-typesetting/views/formatteddocuments.vue", "../../src/features/professional-typesetting/views/unformatteddocuments.vue", "../../src/features/user-center/views/operationlogs.vue", "../../src/features/user-center/views/systempreferences.vue", "../../src/features/user-center/views/userprofile.vue", "../../src/features/user-center/views/usersettings.vue", "../../src/features/video-proofreading/views/videobatchproofreadingview.vue", "../../src/features/video-proofreading/views/videocompletedproofreadingview.vue", "../../src/features/video-proofreading/views/videoonlineproofreadingview.vue", "../../src/features/video-proofreading/views/videopendingproofreadingview.vue", "../../src/features/video-proofreading/views/videoproofreadingview.vue", "../../src/layouts/blanklayout.vue", "../../src/layouts/mainlayout.vue", "../../src/mock/browser.ts", "../../src/mock/index.ts", "../../src/mock/simple.ts", "../../src/mock/types.ts", "../../src/mock/handlers/auth.ts", "../../src/mock/handlers/batchproofreading.ts", "../../src/mock/handlers/dashboard.ts", "../../src/mock/handlers/documentlibrary.ts", "../../src/mock/handlers/index.ts", "../../src/mock/handlers/modificationaccumulation.ts", "../../src/mock/handlers/multimedia.ts", "../../src/mock/handlers/onlineproofreading.ts", "../../src/mock/handlers/prereview.ts", "../../src/mock/handlers/query.ts", "../../src/mock/handlers/typesetting.ts", "../../src/mock/utils/datagenerator.ts", "../../src/mock/utils/mockutils.ts", "../../src/mocks/browser.ts", "../../src/mocks/index.ts", "../../src/mocks/handlers/proofreading.ts", "../../src/modules/ai-proofreading-engine/index.ts", "../../src/modules/ai-proofreading-engine/api/exportmanager.ts", "../../src/modules/ai-proofreading-engine/api/moduleapi.ts", "../../src/modules/ai-proofreading-engine/api/reportgenerator.ts", "../../src/modules/ai-proofreading-engine/api/statisticsanalyzer.ts", "../../src/modules/ai-proofreading-engine/api/types/reporttypes.ts", "../../src/modules/ai-proofreading-engine/config/budgetcontroller.ts", "../../src/modules/ai-proofreading-engine/config/configmanager.ts", "../../src/modules/ai-proofreading-engine/config/prompttemplatemanager.ts", "../../src/modules/ai-proofreading-engine/config/templatevariablesystem.ts", "../../src/modules/ai-proofreading-engine/core/aiservicemanager.ts", "../../src/modules/ai-proofreading-engine/core/chunkmanager.ts", "../../src/modules/ai-proofreading-engine/core/costmanager.ts", "../../src/modules/ai-proofreading-engine/core/documentprocessor.ts", "../../src/modules/ai-proofreading-engine/core/performancemonitor.ts", "../../src/modules/ai-proofreading-engine/core/proofreadingengine.ts", "../../src/modules/ai-proofreading-engine/core/reportgenerator.ts", "../../src/modules/ai-proofreading-engine/core/requestmanager.ts", "../../src/modules/ai-proofreading-engine/core/resultprocessor.ts", "../../src/modules/ai-proofreading-engine/core/types/resulttypes.ts", "../../src/modules/ai-proofreading-engine/examples/templateusageexamples.ts", "../../src/modules/ai-proofreading-engine/examples/export-usage-examples.ts", "../../src/modules/ai-proofreading-engine/examples/usage-examples.ts", "../../src/modules/ai-proofreading-engine/monitoring/enhancedperformancemonitor.ts", "../../src/modules/ai-proofreading-engine/monitoring/performancemonitor.ts", "../../src/modules/ai-proofreading-engine/optimization/aiserviceoptimizer.ts", "../../src/modules/ai-proofreading-engine/optimization/largefileprocessor.ts", "../../src/modules/ai-proofreading-engine/optimization/memorymanager.ts", "../../src/modules/ai-proofreading-engine/optimization/performanceoptimizer.ts", "../../src/modules/ai-proofreading-engine/optimization/webworkermanager.ts", "../../src/modules/ai-proofreading-engine/providers/baseprovider.ts", "../../src/modules/ai-proofreading-engine/providers/deepseekprovider.ts", "../../src/modules/ai-proofreading-engine/providers/doubaoprovider.ts", "../../src/modules/ai-proofreading-engine/providers/professionalprovider.ts", "../../src/modules/ai-proofreading-engine/providers/tongyiprovider.ts", "../../src/modules/ai-proofreading-engine/providers/wenxinprovider.ts", "../../src/modules/ai-proofreading-engine/storage/batchstorage.ts", "../../src/modules/ai-proofreading-engine/storage/cachemanager.ts", "../../src/modules/ai-proofreading-engine/storage/datastoragemanager.ts", "../../src/modules/ai-proofreading-engine/storage/filemanager.ts", "../../src/modules/ai-proofreading-engine/storage/filesystemadapters.ts", "../../src/modules/ai-proofreading-engine/storage/filesystemutils.ts", "../../src/modules/ai-proofreading-engine/storage/sessionmanager.ts", "../../src/modules/ai-proofreading-engine/storage/sessionstorage.ts", "../../src/modules/ai-proofreading-engine/storage/sessionstoragehelpers.ts", "../../src/modules/ai-proofreading-engine/storage/workspacemanager.ts", "../../src/modules/ai-proofreading-engine/storage/api/batchapi.ts", "../../src/modules/ai-proofreading-engine/storage/api/types/batchtypes.ts", "../../src/modules/ai-proofreading-engine/storage/examples/basicusage.ts", "../../src/modules/ai-proofreading-engine/storage/utils/concurrencycontroller.ts", "../../src/modules/ai-proofreading-engine/storage/utils/progresstracker.ts", "../../src/modules/ai-proofreading-engine/storage/utils/taskqueue.ts", "../../src/modules/ai-proofreading-engine/types/chunk.ts", "../../src/modules/ai-proofreading-engine/types/config-types.ts", "../../src/modules/ai-proofreading-engine/types/index.ts", "../../src/modules/ai-proofreading-engine/types/module-types.ts", "../../src/modules/ai-proofreading-engine/types/result-types.ts", "../../src/modules/ai-proofreading-engine/types/service-types.ts", "../../src/modules/ai-proofreading-engine/types/template-types.ts", "../../src/modules/ai-proofreading-engine/utils/costcalculator.ts", "../../src/modules/ai-proofreading-engine/utils/errorhandler.ts", "../../src/modules/ai-proofreading-engine/utils/mergealgorithm.ts", "../../src/modules/ai-proofreading-engine/utils/progresstracker.ts", "../../src/modules/ai-proofreading-engine/utils/resultoptimizer.ts", "../../src/modules/ai-proofreading-engine/utils/resultvalidator.ts", "../../src/modules/ai-proofreading-engine/utils/textprocessor.ts", "../../src/modules/ai-proofreading-engine/ux/enhanceduxoptimizer.ts", "../../src/modules/ai-proofreading-engine/ux/userexperienceoptimizer.ts", "../../src/router/index.ts", "../../src/shared/components/virtualscroller.vue", "../../src/stores/batchproofreadingstore.ts", "../../src/stores/counter.ts", "../../src/stores/user.ts", "../../src/test/setup.ts", "../../src/utils/advancedstorage.ts", "../../src/utils/advancedsync.ts", "../../src/utils/axiosmockadapter.ts", "../../src/utils/enhancedstateui.ts", "../../src/utils/intelligentcache.ts", "../../src/utils/mockinterceptor.ts", "../../src/utils/paginationhelper.ts", "../../src/views/aboutview.vue", "../../src/views/homeview.vue", "../../src/views/auth/loginview.vue", "../../src/views/error/notfoundview.vue"], "errors": true, "version": "5.8.3"}