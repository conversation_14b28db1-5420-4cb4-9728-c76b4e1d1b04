import{d as s,c as e,a,Q as c,I as t,ag as l,o as n}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as r}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const o={class:"review-case-set"},p={class:"content"},d=r(s({__name:"ReviewCaseSet",setup:s=>(s,r)=>{const d=l("el-card");return n(),e("div",o,[r[1]||(r[1]=a("div",{class:"page-header"},[a("h1",null,"审查案例集"),a("p",{class:"page-description"},"审核和管理案例集质量")],-1)),a("div",p,[c(d,null,{default:t(()=>r[0]||(r[0]=[a("p",null,"审查案例集功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-355b12cc"]]);export{d as default};
