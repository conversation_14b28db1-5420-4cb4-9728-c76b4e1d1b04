import{d as s,c as a,a as e,Q as o,I as l,ag as n,o as t}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as p}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const r={class:"operation-logs"},c={class:"content"},d=p(s({__name:"OperationLogs",setup:s=>(s,p)=>{const d=n("el-card");return t(),a("div",r,[p[1]||(p[1]=e("div",{class:"page-header"},[e("h1",null,"操作日志"),e("p",{class:"page-description"},"查看个人操作历史记录")],-1)),e("div",c,[o(d,null,{default:l(()=>p[0]||(p[0]=[e("p",null,"操作日志功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-9f48667e"]]);export{d as default};
