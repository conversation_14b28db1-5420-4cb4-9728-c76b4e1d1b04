{
  "recommendations": [
    // Vue 开发必备
    "Vue.volar",                           // Vue Language Features (Volar)
    "Vue.vscode-typescript-vue-plugin",    // TypeScript Vue Plugin (Volar)
    
    // TypeScript 支持
    "ms-vscode.vscode-typescript-next",    // TypeScript Importer
    
    // 代码质量和格式化
    "esbenp.prettier-vscode",              // Prettier - Code formatter
    "dbaeumer.vscode-eslint",              // ESLint
    
    // 开发工具
    "bradlc.vscode-tailwindcss",           // Tailwind CSS IntelliSense（如果使用）
    "formulahendry.auto-rename-tag",       // Auto Rename Tag
    "formulahendry.auto-close-tag",        // Auto Close Tag
    "christian-kohler.path-intellisense",  // Path Intellisense
    "christian-kohler.npm-intellisense",   // npm Intellisense
    
    // Git 工具
    "eamodio.gitlens",                     // GitLens
    "mhutchie.git-graph",                  // Git Graph
    
    // 测试工具
    "vitest.explorer",                     // Vitest
    
    // 实用工具
    "ms-vscode.vscode-json",               // JSON 支持
    "redhat.vscode-yaml",                  // YAML 支持
    "ms-vscode.vscode-css-peek",           // CSS Peek
    "bradlc.vscode-tailwindcss",           // Tailwind CSS（如果使用）
    
    // 代码片段
    "hollowtree.vue-snippets",             // Vue 3 Snippets
    "sdras.vue-vscode-snippets",           // Vue VSCode Snippets
    
    // 主题和图标（可选）
    "PKief.material-icon-theme",           // Material Icon Theme
    "zhuangtongfa.Material-theme",         // One Dark Pro
    
    // 其他实用扩展
    "usernamehw.errorlens",                // Error Lens
    "streetsidesoftware.code-spell-checker", // Code Spell Checker
    "ms-vscode.vscode-todo-highlight",     // TODO Highlight
    "alefragnani.Bookmarks",               // Bookmarks
    "ms-vscode.vscode-typescript-next"     // TypeScript Importer
  ],
  
  "unwantedRecommendations": [
    // 不推荐的扩展（可能与 Volar 冲突）
    "octref.vetur",                        // Vetur（已被 Volar 替代）
    "ms-vscode.vscode-typescript-vue-plugin-pack" // 旧版 Vue 插件包
  ]
}
