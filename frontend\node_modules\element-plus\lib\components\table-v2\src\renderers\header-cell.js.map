{"version": 3, "file": "header-cell.js", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/header-cell.tsx"], "sourcesContent": ["import { renderSlot } from 'vue'\nimport { HeaderCell, SortIcon } from '../components'\n// import ColumnResizer from '../table-column-resizer'\nimport { Alignment, SortOrder, oppositeOrderMap } from '../constants'\nimport { placeholderSign } from '../private'\nimport { componentToSlot, enforceUnit, tryCall } from '../utils'\n\nimport type { FunctionalComponent, UnwrapNestedRefs } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { TableV2HeaderRowCellRendererParams } from '../components'\nimport type { UseTableReturn } from '../use-table'\nimport type { TableV2Props } from '../table'\n\nexport type HeaderCellRendererProps = TableV2HeaderRowCellRendererParams &\n  UnwrapNestedRefs<Pick<UseTableReturn, 'onColumnSorted'>> &\n  Pick<TableV2Props, 'sortBy' | 'sortState' | 'headerCellProps'> & {\n    ns: UseNamespaceReturn\n  }\n\nconst HeaderCellRenderer: FunctionalComponent<HeaderCellRendererProps> = (\n  props,\n  { slots }\n) => {\n  const { column, ns, style, onColumnSorted } = props\n\n  const cellStyle = enforceUnit(style)\n\n  if (column.placeholderSign === placeholderSign) {\n    return (\n      <div class={ns.em('header-row-cell', 'placeholder')} style={cellStyle} />\n    )\n  }\n\n  const { headerCellRenderer, headerClass, sortable } = column\n\n  /**\n   * render Cell children\n   */\n\n  const cellProps = {\n    ...props,\n    class: ns.e('header-cell-text'),\n  }\n\n  const columnCellRenderer =\n    componentToSlot<typeof cellProps>(headerCellRenderer)\n\n  const Cell = columnCellRenderer\n    ? columnCellRenderer(cellProps)\n    : renderSlot(slots, 'default', cellProps, () => [\n        <HeaderCell {...cellProps} />,\n      ])\n\n  /**\n   * Render cell container and sort indicator\n   */\n  const { sortBy, sortState, headerCellProps } = props\n\n  let sorting: boolean, sortOrder: SortOrder\n  if (sortState) {\n    const order = sortState[column.key!]\n    sorting = Boolean(oppositeOrderMap[order])\n    sortOrder = sorting ? order : SortOrder.ASC\n  } else {\n    sorting = column.key === sortBy.key\n    sortOrder = sorting ? sortBy.order : SortOrder.ASC\n  }\n\n  const cellKls = [\n    ns.e('header-cell'),\n    tryCall(headerClass, props, ''),\n    column.align === Alignment.CENTER && ns.is('align-center'),\n    column.align === Alignment.RIGHT && ns.is('align-right'),\n    sortable && ns.is('sortable'),\n  ]\n\n  const cellWrapperProps = {\n    ...tryCall(headerCellProps, props),\n    onClick: column.sortable ? onColumnSorted : undefined,\n    class: cellKls,\n    style: cellStyle,\n    ['data-key']: column.key,\n  }\n\n  // For now we don't deliver resizable column feature since it has some UX issue.\n  return (\n    <div {...cellWrapperProps} role=\"columnheader\">\n      {Cell}\n\n      {sortable && (\n        <SortIcon\n          class={[ns.e('sort-icon'), sorting && ns.is('sorting')]}\n          sortOrder={sortOrder}\n        />\n      )}\n    </div>\n  )\n}\n\nexport default HeaderCellRenderer\nexport type HeaderCellSlotProps = HeaderCellRendererProps & { class: string }\n"], "names": ["enforceUnit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slots", "column", "ns", "onColumnSorted", "props", "placeholderSign", "_createVNode", "cellStyle", "headerClass", "sortable", "componentToSlot", "renderSlot", "<PERSON><PERSON><PERSON><PERSON>", "class", "Cell", "column<PERSON><PERSON><PERSON><PERSON><PERSON>", "oppositeOrderMap", "SortOrder", "sortBy", "headerCellProps", "sorting", "sortOrder", "sortState", "_mergeProps", "SortIcon", "cellKls", "onClick"], "mappings": ";;;;;;;;;;;AAKA,MAAA,kBAAA,GAA0BA,CAAAA,KAAAA,EAAAA;;AAc1B,CAAA,KAAMC;AAEFC,EAAAA,MAAAA;AAAF,IACG,MAAA;IACG,EAAA;IAAEC,KAAF;IAAUC,cAAV;MAAA,KAAA,CAAA;AAAqBC,EAAAA,MAAAA,SAAAA,GAAAA,iBAAAA,CAAAA,KAAAA,CAAAA,CAAAA;AAArB,EAAA,IAAwCC,MAA9C,CAAA,eAAA,KAAAC,wBAAA,EAAA;AAEA,IAAA,OAAeC,eAAc,CAAA,KAAA,EAAA;;AAE7B,MAAIL,OAAOI,EAAP,SAAA;AACF,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,GAAA;QAC8DE;AAD9D,IAAA,kBAAA;AAGD,IAAA,WAAA;;GAEK,GAAA,MAAA,CAAA;QAAA,SAAA,GAAA;IAAsBC,GAAtB,KAAA;AAAmCC,IAAAA,KAAAA,EAAAA,EAAAA,CAAAA,CAAAA,CAAAA,kBAAAA,CAAAA;AAAnC,GAAA,CAAA;AAEN,EAAA,MAAA,kBAAA,GAAAC,qBAAA,CAAA,kBAAA,CAAA,CAAA;AACF,EAAA,MAAA,IAAA,GAAA,kBAAA,GAAA,kBAAA,CAAA,SAAA,CAAA,GAAAC,cAAA,CAAA,KAAA,EAAA,SAAA,EAAA,SAAA,EAAA,MAAA,CAAAL,eAAA,CAAAM,qBAAA,EAAA,SAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,MAAA;;AAEE,IAAA,SAAe;AAEbC,IAAAA,eAAO;GAFT,GAAA,KAAA,CAAA;AAKA,EAAA,IAAA,OAAwB,EAAA,SAAA,CAAA;EAGxB,IAAMC,SAAOC,EAAkB;AAM/B,IAAA,MAAA,KAAA,GAAA,SAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA;AACF,IAAA,OAAA,GAAA,OAAA,CAAAC,0BAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,IAAA,SAAA,GAAA,OAAA,GAAA,KAAA,GAAAC,mBAAA,CAAA,GAAA,CAAA;;IACQ,OAAA,GAAA,MAAA,CAAA,GAAA,KAAA,MAAA,CAAA,GAAA,CAAA;IAAEC,SAAF,GAAA,OAAA,GAAA,MAAA,CAAA,KAAA,GAAAD,mBAAA,CAAA,GAAA,CAAA;;AAAqBE,EAAAA,MAAAA,OAAAA,GAAAA,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,aAAAA,CAAAA,EAAAA,aAAAA,CAAAA,WAAAA,EAAAA,KAAAA,EAAAA,EAAAA,CAAAA,EAAAA,MAAAA,CAAAA,KAAAA,KAAAA,mBAAAA,CAAAA,MAAAA,IAAAA,EAAAA,CAAAA,EAAAA,CAAAA,cAAAA,CAAAA,EAAAA,MAAAA,CAAAA,KAAAA,KAAAA,mBAAAA,CAAAA,KAAAA,IAAAA,EAAAA,CAAAA,EAAAA,CAAAA,aAAAA,CAAAA,EAAAA,QAAAA,IAAAA,EAAAA,CAAAA,EAAAA,CAAAA,UAAAA,CAAAA,CAAAA,CAAAA;AAArB,EAAA,MAAN,gBAAA,GAAA;IAEIC,GAAAA,cAAkBC,eAAtB,EAAA,KAAA,CAAA;;AACA,IAAA,cAAe;AACb,IAAA,KAAA,WAAcC;AACdF,IAAAA,CAAAA,UAAiB,GAAA;AACjBC,GAAAA,CAAAA;AACD,EAAA,OAAMf,eAAA,CAAA,KAAA,EAAAiB,cAAA,CAAA,gBAAA,EAAA;AACLH,IAAAA,MAAAA,gBAAyBF;IACzBG,EAAS,CAAA,IAAA,EAAA,YAAaH,eAAH,CAAAM,mBAA2B,EAAA;AAC/C,IAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,WAAA,CAAA,EAAA,OAAA,IAAA,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA;;GAEKC,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA;EAQN;AAEEC,iBAAe,kBAAYvB;;;;"}