import{d as s,c as a,a as e,Q as l,I as r,ag as o,o as c}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as n}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const p={class:"user-profile"},t={class:"content"},d=n(s({__name:"UserProfile",setup:s=>(s,n)=>{const d=o("el-card");return c(),a("div",p,[n[1]||(n[1]=e("div",{class:"page-header"},[e("h1",null,"个人资料"),e("p",{class:"page-description"},"查看和编辑个人基本信息")],-1)),e("div",t,[l(d,null,{default:r(()=>n[0]||(n[0]=[e("p",null,"个人资料管理功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-4dcb4a95"]]);export{d as default};
