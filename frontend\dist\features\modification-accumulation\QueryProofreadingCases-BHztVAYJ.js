import{d as s,c as a,a as e,Q as r,I as o,ag as n,o as l}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as c}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const d={class:"query-proofreading-cases"},p={class:"content"},t=c(s({__name:"QueryProofreadingCases",setup:s=>(s,c)=>{const t=n("el-card");return l(),a("div",d,[c[1]||(c[1]=e("div",{class:"page-header"},[e("h1",null,"查询校对案例集"),e("p",{class:"page-description"},"检索和查看校对案例")],-1)),e("div",p,[r(t,null,{default:o(()=>c[0]||(c[0]=[e("p",null,"查询校对案例集功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-e15e103a"]]);export{t as default};
