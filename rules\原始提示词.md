# AI校对
```
在未预审文档模块 表格中的“AI校对”按钮，添加功能：添加一个AI校对的页面，主要是对当前文件进行：文件信息、选择大模型、选择提示词、格式转换（使用Mock数据和接口将docx、PDF转换为MD 格式文件），拆解为多文档、多文档任务列表、列表中每个文档的校对按钮——开始校对、进度条、停止校对，校对通过、校对拒绝
校对通过和校对拒绝后的文件均转入：已预审文档
**开始校对**（调用前端AI校对模块的接口D:\AIpreadfrood\frontend\src\modules进行校对）


```

# 已预审文档页面
```
已预审文档页面，主要是对当前文件的校对情况进行查看，搜索（标题、这种项目、状态、文档分类、时间段）、重置、发回重审；表格中的按钮：导出报告、详情、删除；
标题、预审报告（MD）、状态(通过、拒绝)、分类、作者姓名、作者单位、创建者、创建时间、更新时间、预计完成时间、操作
```
## 优化提示词
```
请为AI智能审校系统实现"已预审文档"模块页面，该页面用于管理和查看已完成预审的文档。具体功能要求如下：

## 页面功能需求

### 1. 文档查看和管理
- 显示当前所有已预审文档的校对情况和状态
- 支持文档的批量管理和状态跟踪
- 页面布局、css、颜色等风格与未预审文档页面保持一致

### 2. 搜索和筛选功能
实现多维度搜索筛选：
- **标题搜索**：支持文档标题关键词模糊搜索
- **项目类型**：按项目类型进行分类筛选
- **审核状态**：按通过/拒绝状态筛选
- **文档分类**：按文档类别进行筛选
- **时间段筛选**：支持创建时间、更新时间、预计完成时间的范围筛选
- **重置功能**：一键清空所有筛选条件

### 3. 文档操作功能
- **批量重审**：将已审核文档批量重新提交审核流程
- **查看详情**：查看文档详细信息和完整预审报告
- **批量导出**：导出该文档的预审报告（支持PDF、Word、Markdown格式）
- **删除文档**：删除文档记录（需确认对话框）

### 4. 数据表格显示
表格需包含以下字段：
- **标题**：文档标题（支持点击查看详情）
- **预审报告**：Markdown格式的预审报告内容
- **状态**：审核状态（通过/拒绝，使用状态标签显示）
- **分类**：文档分类标签
- **作者姓名**：文档作者
- **作者单位**：作者所属机构
- **创建者**：文档创建人
- **创建时间**：文档创建时间
- **更新时间**：最后修改时间
- **预计完成时间**：预估完成时间
- **操作**：操作按钮列

### 5. 表格操作按钮
每行文档提供以下操作：
- **导出报告**：导出该文档的预审报告（支持PDF、Word、Markdown格式）
- **详情**：查看文档详细信息和完整预审报告
- **删除**：删除文档记录（需确认对话框）

## 技术实现要求
- 使用Vue3 + TypeScript + Element Plus实现
- 集成现有的虚拟滚动组件处理大量文档列表
- 支持表格排序、分页和导出功能
- 实现响应式设计，适配不同屏幕尺寸
- 集成现有的性能优化和用户体验提升功能
- 遵循现有的代码规范和架构模式

## 用户体验要求
- 页面加载时间<2秒
- 搜索响应时间<500ms
- 支持键盘快捷键操作
- 提供操作反馈和加载状态提示
- 错误处理和用户友好的提示信息

```

# AI批量审校/未校对文档
```

请为AI智能审校系统实现"AI批量审校/未校对文档"模块页面，该页面用于添加、管理、AI校对、查看待AI校对的文档。具体功能要求如下：
页面布局和CSS参考：D:\AIpreadfrood\frontend\src\features\content-review\views\UnreviewedDocuments.vue

## 页面功能需求

### 1. 文档查看和管理
- 显示当前所有待AI校对文档的列表
- 支持文档的批量管理和状态跟踪
- 页面布局、css、颜色等风格与未预审文档页面保持一致

### 2. 搜索和筛选功能
实现多维度搜索筛选：
- **标题搜索**：支持文档标题关键词模糊搜索
- **项目类型**：按项目类型进行分类筛选
- **审核状态**：按待AI校对/AI校对中状态筛选
- **文档分类**：按文档类别进行筛选
- **时间段筛选**：支持创建时间、更新时间、预计完成时间的范围筛选
- **重置功能**：一键清空所有筛选条件

### 3. 文档操作功能
- **批量AI校对**：将待AI校对文档批量提交AI校对流程
- **查看详情**：查看文档详细信息和AI校对报告
- **批量导出**：导出该文档的AI校对报告（支持PDF、Word、Markdown格式）
- **删除文档**：删除文档记录（需确认对话框）

### 4. 数据表格显示
表格需包含以下字段：
- **标题**：文档标题（支持点击查看详情）
- **AI校对报告**：AI校对生成的报告内容
- **状态**：审校状态（待AI校对/AI校对中，使用状态标签显示）
- **分类**：文档分类标签
- **作者姓名**：文档作者
- **作者单位**：作者所属机构
- **创建者**：文档创建人
- **创建时间**：文档创建时间
- **更新时间**：最后修改时间
- **预计完成时间**：预估完成时间
- **操作**：操作按钮列

### 5. 表格操作按钮
每行文档提供以下操作：
- **AI校对**：将文档提交AI校对流程
- **导出报告**：导出该文档的AI校对报告（支持PDF、Word、Markdown格式）
- **详情**：查看文档详细信息和AI校对报告
- **删除**：删除文档记录（需确认对话框）

## 技术实现要求
- 使用Vue3 + TypeScript + Element Plus实现
- 集成现有的虚拟滚动组件处理大量文档列表
- 支持表格排序、分页和导出功能
- 实现响应式设计，适配不同屏幕尺寸
- 集成现有的性能优化和用户体验提升功能
- 遵循现有的代码规范和架构模式

## 用户体验要求
- 页面加载时间<2秒
- 搜索响应时间<500ms
- 支持键盘快捷键操作
- 提供操作反馈和加载状态提示
- 错误处理和用户友好的提示信息

```




# AI批量审校/待审核文档
```
请为AI智能审校系统实现"AI批量审校/待审核文档"模块页面，该页面用于管理和人工审校对AI批量校对的文档进行审核。具体功能要求如下：

## 页面功能需求

### 1. 文档查看和管理
- 显示当前所有待审核文档的列表
- 支持文档的批量管理和状态跟踪
- 页面布局、css、颜色等风格与未预审文档页面保持一致

### 2. 搜索和筛选功能
实现多维度搜索筛选：
- **标题搜索**：支持文档标题关键词模糊搜索
- **项目类型**：按项目类型进行分类筛选
- **审核状态**：按待审校/审校中状态筛选
- **文档分类**：按文档类别进行筛选
- **时间段筛选**：支持创建时间、更新时间、预计完成时间的范围筛选
- **重置功能**：一键清空所有筛选条件

### 3. 文档操作功能
- **开始审核**：将待审核文档批量提交AI校对流程
- **查看详情**：查看文档详细信息和AI校对报告
- **批量导出**：导出该文档的AI校对报告（支持PDF、Word、Markdown格式）
- **删除文档**：删除文档记录（需确认对话框）

### 4. 数据表格显示
表格需包含以下字段：
- **标题**：文档标题（支持点击查看详情）
- **AI校对报告**：AI校对生成的报告内容
- **状态**：审校状态（待审校/审校中，使用状态标签显示）
- **分类**：文档分类标签
- **作者姓名**：文档作者
- **作者单位**：作者所属机构
- **创建者**：文档创建人
- **创建时间**：文档创建时间
- **更新时间**：最后修改时间
- **预计完成时间**：预估完成时间
- **操作**：操作按钮列

### 5. 表格操作按钮
每行文档提供以下操作：
- **审核**：将文档提交AI校对流程
- **导出报告**：导出该文档的AI校对报告（支持PDF、Word、Markdown格式）
- **详情**：查看文档详细信息和AI校对报告
- **删除**：删除文档记录（需确认对话框）

## 技术实现要求
- 使用Vue3 + TypeScript + Element Plus实现
- 集成现有的虚拟滚动组件处理大量文档列表
- 支持表格排序、分页和导出功能
- 实现响应式设计，适配不同屏幕尺寸
- 集成现有的性能优化和用户体验提升功能
- 遵循现有的代码规范和架构模式

## 用户体验要求
- 页面加载时间<2秒
- 搜索响应时间<500ms
- 支持键盘快捷键操作
- 提供操作反馈和加载状态提示
- 错误处理和用户友好的提示信息

```

# AI批量审校/已完成校对文档
```
请为AI智能审校系统实现"AI批量审校/已完成校对文档"模块页面，该页面用于管理和查看已完成AI校对、人工审核的文档、导出校对报告和审核意见、导出标记的PDF文档或Word文档。具体功能要求如下：

## 页面功能需求

### 1. 文档查看和管理
- 显示当前所有已完成AI校对文档的列表
- 支持文档的批量管理和状态跟踪
- 页面布局、css、颜色等风格与未预审文档页面保持一致

### 2. 搜索和筛选功能
实现多维度搜索筛选：
- **标题搜索**：支持文档标题关键词模糊搜索
- **项目类型**：按项目类型进行分类筛选
- **审核状态**：按通过/拒绝状态筛选
- **文档分类**：按文档类别进行筛选
- **时间段筛选**：支持创建时间、更新时间、预计完成时间的范围筛选
- **重置功能**：一键清空所有筛选条件

### 3. 文档操作功能
- **批量导出**：导出该文档的AI校对报告（支持PDF、Word、Markdown格式）
- **查看详情**：查看文档详细信息和AI校对报告
- **删除文档**：删除文档记录（需确认对话框）

### 4. 数据表格显示
表格需包含以下字段：
- **标题**：文档标题（支持点击查看详情）
- **AI校对报告**：AI校对生成的报告内容
- **状态**：审校状态（通过/拒绝，使用状态标签显示）
- **分类**：文档分类标签
- **作者姓名**：文档作者
- **作者单位**：作者所属机构
- **创建者**：文档创建人
- **创建时间**：文档创建时间
- **更新时间**：最后修改时间
- **预计完成时间**：预估完成时间
- **操作**：操作按钮列

### 5. 表格操作按钮
每行文档提供以下操作：
- **导出报告**：导出该文档的AI校对报告（支持PDF、Word、Markdown格式）
- **详情**：查看文档详细信息和AI校对报告
- **删除**：删除文档记录（需确认对话框）

## 技术实现要求
- 使用Vue3 + TypeScript + Element Plus实现
- 集成现有的虚拟滚动组件处理大量文档列表
- 支持表格排序、分页和导出功能
- 实现响应式设计，适配不同屏幕尺寸
- 集成现有的性能优化和用户体验提升功能
- 遵循现有的代码规范和架构模式

## 用户体验要求
- 页面加载时间<2秒
- 搜索响应时间<500ms
- 支持键盘快捷键操作
- 提供操作反馈和加载状态提示
- 错误处理和用户友好的提示信息

```
