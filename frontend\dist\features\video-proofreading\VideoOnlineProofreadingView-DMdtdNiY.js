import{a7 as e,g as l,Y as a,ab as t,ac as u,a0 as s,B as n,S as d,M as i}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as o,r,X as c,b as v,c as m,a as p,Q as _,H as f,K as h,I as g,ag as b,o as T,M as k,u as y,O as V,P as x,a6 as C,C as w,D as U}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as z}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const $={class:"video-online-proofreading"},j={class:"content"},R={class:"toolbar"},I={class:"toolbar-left"},L={class:"toolbar-right"},S={key:0,class:"video-container"},O=["src"],P={class:"video-info"},M={key:0,class:"subtitle-track"},B={class:"track-header"},E={class:"track-timeline"},A=["onClick"],D={key:1,class:"empty-video"},H={class:"panel-header"},K={class:"header-actions"},Q={key:0,class:"subtitle-list"},W={class:"time-controls"},X={class:"item-actions"},Y={class:"subtitle-text-editor"},q={key:1,class:"subtitle-stats"},F=z(o({__name:"VideoOnlineProofreadingView",setup(o){const z=r(null),F=r(),G=r("00:00"),J=r("00:00"),N=r(-1),Z=r(!1),ee=r(""),le=r([]),ae=r(!0),te=r(!1),ue=c({method:"speech-to-text",language:"zh",accuracy:"standard"}),se=r(!1),ne=v(()=>{if(!ee.value)return{characters:0,words:0};const e=ee.value;return{characters:e.length,words:e.split(/\s+/).filter(e=>e.length>0).length}}),de=e=>e.type.startsWith("video/")?e.size>524288e3?(i.error("文件大小不能超过 500MB"),!1):(z.value={name:e.name,size:e.size,url:URL.createObjectURL(e),file:e},ee.value="",le.value=[],N.value=-1,se.value=!1,i.success("视频上传成功"),!1):(i.error("只能上传视频文件"),!1),ie=()=>{if(F.value){const e=F.value.duration;G.value=me(e)}},oe=()=>{if(F.value){J.value=me(F.value.currentTime);const e=F.value.currentTime,l=le.value.findIndex(l=>{const a=pe(l.startTime),t=pe(l.endTime);return e>=a&&e<=t});N.value=l}},re=()=>{},ce=()=>{},ve=async()=>{if(z.value){Z.value=!0;try{await new Promise(e=>setTimeout(e,3e3)),le.value=[{startTime:"00:00:10",endTime:"00:00:15",text:"欢迎观看本期视频教程"},{startTime:"00:00:15",endTime:"00:00:22",text:"今天我们来学习AI智能审校系统的使用方法"},{startTime:"00:00:22",endTime:"00:00:28",text:"首先我们来看看系统的主要功能模块"},{startTime:"00:00:28",endTime:"00:00:35",text:"包括内容预审、批量审校、在线审校等功能"},{startTime:"00:00:35",endTime:"00:00:42",text:"接下来我们详细介绍每个模块的使用方法"}],ee.value=le.value.map(e=>`[${e.startTime}] ${e.text}`).join("\n"),i.success("字幕提取完成")}catch{i.error("字幕提取失败")}finally{Z.value=!1}}else i.warning("请先上传视频")},me=e=>{const l=Math.floor(e/60),a=Math.floor(e%60);return`${l.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},pe=e=>{const l=e.split(":");return 60*parseInt(l[0])+parseInt(l[1])},_e=e=>{const l=pe(e.startTime),a=pe(e.endTime),t=pe(G.value);return{left:l/t*100+"%",width:(a-l)/t*100+"%"}},fe=()=>{const e=J.value,l={startTime:e,endTime:me(pe(e)+5),text:"新字幕内容"};le.value.push(l),se.value=!0},he=()=>{se.value=!0},ge=()=>{se.value=!0,ee.value=le.value.map(e=>`[${e.startTime}] ${e.text}`).join("\n")},be=()=>{se.value=!0},Te=()=>{ae.value=!ae.value},ke=()=>{if(0===le.value.length)return void i.warning("没有字幕内容可导出");let e="";le.value.forEach((l,a)=>{e+=`${a+1}\n`,e+=`${l.startTime} --\x3e ${l.endTime}\n`,e+=`${l.text}\n\n`});const l=new Blob([e],{type:"text/plain"}),a=URL.createObjectURL(l),t=document.createElement("a");t.href=a,t.download=`${z.value?.name||"subtitle"}.srt`,t.click(),URL.revokeObjectURL(a),i.success("SRT文件导出成功")},ye=()=>{z.value=null,ee.value="",le.value=[],N.value=-1,se.value=!1,G.value="00:00",J.value="00:00"},Ve=()=>{i.success("设置已应用"),te.value=!1},xe=()=>{z.value&&ee.value?(i.success("保存成功"),se.value=!1):i.warning("没有可保存的内容")},Ce=()=>{z.value&&ee.value?i.success("审校结果已提交"):i.warning("请先完成字幕提取")};return(i,o)=>{const r=b("el-icon"),c=b("el-button"),v=b("el-upload"),me=b("el-card"),we=b("el-col"),Ue=b("el-row"),ze=b("el-input"),$e=b("el-statistic"),je=b("el-option"),Re=b("el-select"),Ie=b("el-form-item");return T(),m("div",$,[o[22]||(o[22]=p("div",{class:"page-header"},[p("h1",null,"视频在线审校"),p("p",{class:"page-description"},"在线实时进行视频字幕提取和文字校对")],-1)),p("div",j,[_(me,{class:"toolbar-card"},{default:g(()=>[p("div",R,[p("div",I,[_(v,{action:"#","show-file-list":!1,"before-upload":de,accept:"video/*"},{default:g(()=>[_(c,{type:"primary"},{default:g(()=>[_(r,null,{default:g(()=>[_(y(e))]),_:1}),o[4]||(o[4]=k(" 上传视频 "))]),_:1,__:[4]})]),_:1}),_(c,{onClick:ve,disabled:!z.value,loading:Z.value},{default:g(()=>[_(r,null,{default:g(()=>[_(y(l))]),_:1}),o[5]||(o[5]=k(" 开始提取 "))]),_:1,__:[5]},8,["disabled","loading"]),_(c,{onClick:ye},{default:g(()=>[_(r,null,{default:g(()=>[_(y(a))]),_:1}),o[6]||(o[6]=k(" 清空 "))]),_:1,__:[6]})]),p("div",L,[_(c,{onClick:xe,disabled:!se.value},{default:g(()=>[_(r,null,{default:g(()=>[_(y(t))]),_:1}),o[7]||(o[7]=k(" 保存 "))]),_:1,__:[7]},8,["disabled"]),_(c,{type:"success",onClick:Ce,disabled:!z.value},{default:g(()=>[_(r,null,{default:g(()=>[_(y(u))]),_:1}),o[8]||(o[8]=k(" 提交审校 "))]),_:1,__:[8]},8,["disabled"])])])]),_:1}),_(Ue,{gutter:20,class:"workspace"},{default:g(()=>[_(we,{span:14},{default:g(()=>[_(me,{title:"视频播放器",class:"video-panel"},{default:g(()=>[z.value?(T(),m("div",S,[p("video",{ref_key:"videoPlayer",ref:F,src:z.value.url,controls:"",class:"video-player",onLoadedmetadata:ie,onTimeupdate:oe,onPlay:re,onPause:ce},null,40,O),p("div",P,[_(Ue,{gutter:10},{default:g(()=>[_(we,{span:8},{default:g(()=>[p("p",null,[o[9]||(o[9]=p("strong",null,"文件名：",-1)),k(V(z.value.name),1)])]),_:1}),_(we,{span:8},{default:g(()=>[p("p",null,[o[10]||(o[10]=p("strong",null,"时长：",-1)),k(V(G.value),1)])]),_:1}),_(we,{span:8},{default:g(()=>[p("p",null,[o[11]||(o[11]=p("strong",null,"当前时间：",-1)),k(V(J.value),1)])]),_:1})]),_:1})]),le.value.length>0?(T(),m("div",M,[p("div",B,[o[12]||(o[12]=p("span",null,"字幕轨道",-1)),_(c,{size:"small",onClick:Te},{default:g(()=>[k(V(ae.value?"隐藏字幕":"显示字幕"),1)]),_:1})]),p("div",E,[(T(!0),m(x,null,C(le.value,(e,l)=>(T(),m("div",{key:l,class:U(["subtitle-segment",{active:N.value===l}]),style:w(_e(e)),onClick:l=>(e=>{F.value&&(F.value.currentTime=pe(e.startTime))})(e)},V(e.text),15,A))),128))])])):h("",!0)])):(T(),m("div",D,[_(r,{size:"64"},{default:g(()=>[_(y(s))]),_:1}),o[13]||(o[13]=p("p",null,"请上传视频开始审校",-1))]))]),_:1})]),_:1}),_(we,{span:10},{default:g(()=>[_(me,{title:"字幕编辑",class:"subtitle-panel"},{header:g(()=>[p("div",H,[o[16]||(o[16]=p("span",null,"字幕编辑",-1)),p("div",K,[_(c,{size:"small",onClick:fe},{default:g(()=>[_(r,null,{default:g(()=>[_(y(n))]),_:1}),o[14]||(o[14]=k(" 添加 "))]),_:1,__:[14]}),_(c,{size:"small",onClick:ke,disabled:!ee.value},{default:g(()=>[_(r,null,{default:g(()=>[_(y(d))]),_:1}),o[15]||(o[15]=k(" 导出SRT "))]),_:1,__:[15]},8,["disabled"])])])]),default:g(()=>[le.value.length>0?(T(),m("div",Q,[(T(!0),m(x,null,C(le.value,(e,l)=>(T(),m("div",{key:l,class:U(["subtitle-item",{active:N.value===l}])},[p("div",W,[_(ze,{modelValue:e.startTime,"onUpdate:modelValue":l=>e.startTime=l,size:"small",placeholder:"开始时间",onChange:he},null,8,["modelValue","onUpdate:modelValue"]),o[17]||(o[17]=p("span",null,"-",-1)),_(ze,{modelValue:e.endTime,"onUpdate:modelValue":l=>e.endTime=l,size:"small",placeholder:"结束时间",onChange:he},null,8,["modelValue","onUpdate:modelValue"])]),_(ze,{modelValue:e.text,"onUpdate:modelValue":l=>e.text=l,type:"textarea",rows:2,placeholder:"字幕内容",onInput:ge},null,8,["modelValue","onUpdate:modelValue"]),p("div",X,[_(c,{size:"small",onClick:l=>(e=>{F.value&&(F.value.currentTime=pe(e.startTime),F.value.play())})(e)},{default:g(()=>o[18]||(o[18]=[k("播放")])),_:2,__:[18]},1032,["onClick"]),_(c,{size:"small",type:"danger",onClick:e=>(e=>{le.value.splice(e,1),se.value=!0})(l)},{default:g(()=>o[19]||(o[19]=[k("删除")])),_:2,__:[19]},1032,["onClick"])])],2))),128))])):h("",!0),p("div",Y,[o[20]||(o[20]=p("h4",null,"整体字幕文本",-1)),_(ze,{modelValue:ee.value,"onUpdate:modelValue":o[0]||(o[0]=e=>ee.value=e),type:"textarea",rows:12,placeholder:"字幕提取结果将显示在这里，您可以直接编辑...",onInput:be},null,8,["modelValue"])]),ee.value?(T(),m("div",q,[_(Ue,{gutter:10},{default:g(()=>[_(we,{span:12},{default:g(()=>[_($e,{title:"字符数",value:ne.value.characters},null,8,["value"])]),_:1}),_(we,{span:12},{default:g(()=>[_($e,{title:"字幕段数",value:le.value.length},null,8,["value"])]),_:1})]),_:1})])):h("",!0)]),_:1})]),_:1})]),_:1}),te.value?(T(),f(me,{key:0,title:"提取设置",class:"settings-panel"},{default:g(()=>[_(Ue,{gutter:20},{default:g(()=>[_(we,{span:6},{default:g(()=>[_(Ie,{label:"提取方式"},{default:g(()=>[_(Re,{modelValue:ue.method,"onUpdate:modelValue":o[1]||(o[1]=e=>ue.method=e),placeholder:"选择方式"},{default:g(()=>[_(je,{label:"语音转文字",value:"speech-to-text"}),_(je,{label:"字幕文件提取",value:"subtitle-extract"}),_(je,{label:"OCR识别",value:"ocr"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),_(we,{span:6},{default:g(()=>[_(Ie,{label:"识别语言"},{default:g(()=>[_(Re,{modelValue:ue.language,"onUpdate:modelValue":o[2]||(o[2]=e=>ue.language=e),placeholder:"选择语言"},{default:g(()=>[_(je,{label:"中文",value:"zh"}),_(je,{label:"英文",value:"en"}),_(je,{label:"中英混合",value:"zh-en"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),_(we,{span:6},{default:g(()=>[_(Ie,{label:"识别精度"},{default:g(()=>[_(Re,{modelValue:ue.accuracy,"onUpdate:modelValue":o[3]||(o[3]=e=>ue.accuracy=e),placeholder:"选择精度"},{default:g(()=>[_(je,{label:"快速",value:"fast"}),_(je,{label:"标准",value:"standard"}),_(je,{label:"高精度",value:"high"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),_(we,{span:6},{default:g(()=>[_(Ie,null,{default:g(()=>[_(c,{type:"primary",onClick:Ve},{default:g(()=>o[21]||(o[21]=[k("应用设置")])),_:1,__:[21]})]),_:1})]),_:1})]),_:1})]),_:1})):h("",!0)])])}}}),[["__scopeId","data-v-04a4d97a"]]);export{F as default};
