import{d as e,c as s,a,Q as n,I as o,ag as r,o as t}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as d}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const l={class:"new-proofreading-comments"},c={class:"content"},p=d(e({__name:"NewProofreadingComments",setup:e=>(e,d)=>{const p=r("el-card");return t(),s("div",l,[d[1]||(d[1]=a("div",{class:"page-header"},[a("h1",null,"新建校对意见"),a("p",{class:"page-description"},"创建新的校对意见和建议")],-1)),a("div",c,[n(p,null,{default:o(()=>d[0]||(d[0]=[a("p",null,"新建校对意见功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-13de4eb1"]]);export{p as default};
