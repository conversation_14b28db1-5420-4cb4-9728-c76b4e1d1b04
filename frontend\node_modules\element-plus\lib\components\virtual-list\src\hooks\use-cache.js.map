{"version": 3, "file": "use-cache.js", "sources": ["../../../../../../../packages/components/virtual-list/src/hooks/use-cache.ts"], "sourcesContent": ["import { computed, getCurrentInstance } from 'vue'\nimport { memoize } from 'lodash-unified'\nimport memoOne from 'memoize-one'\n\nimport type { VirtualizedProps } from '../props'\n\nexport const useCache = <T>() => {\n  const vm = getCurrentInstance()!\n\n  const props = vm.proxy!.$props as VirtualizedProps\n\n  return computed(() => {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _getItemStyleCache = (_: any, __: any, ___: any) =>\n      ({} as Record<string, T>)\n    return props.perfMode\n      ? memoize(_getItemStyleCache)\n      : memoOne(_getItemStyleCache)\n  })\n}\n"], "names": ["getCurrentInstance", "computed", "memoize", "memoOne"], "mappings": ";;;;;;;;;;;;AAGY,MAAC,QAAQ,GAAG,MAAM;AAC9B,EAAE,MAAM,EAAE,GAAGA,sBAAkB,EAAE,CAAC;AAClC,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;AAChC,EAAE,OAAOC,YAAQ,CAAC,MAAM;AACxB,IAAI,MAAM,kBAAkB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;AACpD,IAAI,OAAO,KAAK,CAAC,QAAQ,GAAGC,qBAAO,CAAC,kBAAkB,CAAC,GAAGC,2BAAO,CAAC,kBAAkB,CAAC,CAAC;AACtF,GAAG,CAAC,CAAC;AACL;;;;"}