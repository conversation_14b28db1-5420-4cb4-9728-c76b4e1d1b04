# 提示词记录

## 2025-06-26 创建AI智能审校系统协作开发指南v2.0

  **用户需求：**
请基于AI智能审校系统的业务需求，创建一个全新的协作开发指南文档。具体要求如下：

**输入文件分析：**
1. 参考文件：`D:\AIpreadfrood\rules\AI智能审校系统.md` - 作为业务功能需求的基础
2. 模板文件：`D:\AIpreadfrood\rules\Augment Code协作开发指南-new.md` - 作为文档结构和格式的参考模板

**输出要求：**
- 创建新文件：`D:\AIpreadfrood\rules\Augment Code协作开发指南-v2.md`
- 文档版本：标记为 v2.0
- 创建日期：使用当前日期 2025-06-26

**核心任务：**
1. **业务模块映射**：将`AI智能审校系统.md`中的12大核心业务模块转换为前端页面导航结构
   - 校对大屏 → 主菜单项
   - 内容预审管理 → 主菜单项
   - AI批量审校 → 主菜单项
   - 在线AI审校 → 主菜单项
   - 图片审校 → 主菜单项
   - 视频审校 → 主菜单项
   - 音频审校 → 主菜单项
   - 专业排版 → 主菜单项
   - 专业查询 → 主菜单项
   - 编辑文档库 → 主菜单项
   - 我的修改积累 → 主菜单项
   - 个人中心 → 主菜单项

2. **导航层级设计**：
   - 一级标题（#）→ 主菜单项
   - 二级标题（##）→ 子菜单项
   - 三级标题（###）→ 页面功能区块

3. **开发指南内容**：
   - 保持原有的前后端分离开发理念
   - 更新技术栈配置以适应新的业务模块
   - 为每个业务模块提供详细的开发任务分解
   - 包含完整的Mock API设计和前端组件规划
   - 提供针对AI智能审校系统的特定开发流程

4. **文档结构要求**：
   - 保持与`协作开发指南-new.md`相同的章节组织方式
   - 更新项目概述以反映12大业务模块
   - 调整技术栈说明以匹配AI审校系统的特殊需求
   - 提供详细的任务分解和验收标准

**特别注意：**
- 确保所有业务功能都有对应的前端页面规划
- 为多媒体处理（图片、视频、音频）模块提供专门的技术方案
- 考虑AI集成的特殊需求（如实时审校、差异对比等）
- 保持文档的可操作性和实用性

---

## 2025-07-03 在线AI审校系统开发

**用户需求：**
开发在线AI审校系统，基于Vue3 + TypeScript + Element Plus技术栈。系统需要实现多文档段落对比校对功能，支持PDF原稿预览和实时AI校对。

**技术架构要求：**
- 前端框架：Vue3 + TypeScript + Element Plus
- 富文本编辑器：wangEditor v5（需验证与Vue3兼容性）
- AI引擎集成：集成ai-proofreading-engine模块，提供校对API接口
- 开发工具：使用MSW（Mock Service Worker）进行API模拟和测试
- 性能指标：页面初始加载时间<2秒，AI校对响应时间<500毫秒
- 代码规范：遵循Vue3 Composition API + TypeScript严格模式

**核心功能需求：**
1. 三栏布局设计：左侧PDF预览（40%），右侧原文显示区和校对结果区上下布局（60%）
2. PDF原稿预览功能：支持滚动同步、缩放、页面跳转
3. 原文显示区：展示原始文档内容，标记AI建议的修改（只读模式）
4. 校对结果区：显示AI校对后的完整内容，支持用户手动调整
5. AI实时校对引擎：自动触发、手动触发、批量触发校对功能
6. 用户交互功能：校对建议处理、导航功能、进度指示器

**技术实现要求：**
- 组件架构设计：主组件AIProofreadingSystem.vue及相关子组件
- 状态管理：使用Pinia管理校对状态、文档内容、用户操作历史
- 性能优化：虚拟滚动、防抖机制、缓存校对结果
- 错误处理与用户体验：完善的异常处理、加载状态、响应式设计

---

## 2025-06-26 Vue3前端项目初始化

**用户需求：**
请帮我初始化AI智能审校系统的Vue3前端项目，严格按照以下详细规范执行：

请帮我初始化AI智能审校系统的Vue3前端项目，严格按照以下详细规范执行：

**项目基础信息：**
- 项目名称：proofreading-frontend
- 项目路径：D:\AIpreadfrood\frontend
- 技术栈：Vue3 (^3.4.0) + TypeScript (^5.0.0) + Vite (^5.0.0) + Element Plus (^2.4.0)

**1. 项目初始化步骤：**
- 使用 `npm create vue@latest proofreading-frontend` 创建项目
- 选择配置：TypeScript (Yes)、Router (Yes)、Pinia (Yes)、Vitest (Yes)、ESLint (Yes)、Prettier (Yes)
- 配置路径别名：@/ → src/、@components/ → src/components/、@views/ → src/views/、@stores/ → src/stores/、@api/ → src/api/、@utils/ → src/utils/、@types/ → src/types/

**2. 完整目录结构创建：**
```
src/
├── features/                           # 12大业务特性模块
│   ├── dashboard/                      # 校对大屏
│   │   ├── components/                 # 特性专用组件
│   │   ├── composables/               # 特性专用组合函数
│   │   ├── stores/                    # 特性专用状态管理
│   │   ├── types/                     # 特性专用类型定义
│   │   └── views/                     # 特性页面组件
│   ├── content-review/                # 内容预审管理
│   ├── ai-batch-proofreading/         # AI批量审校
│   ├── online-proofreading/           # 在线AI审校
│   ├── image-proofreading/            # 图片审校
│   ├── video-proofreading/            # 视频审校
│   ├── audio-proofreading/            # 音频审校
│   ├── professional-typesetting/      # 专业排版
│   ├── professional-query/            # 专业查询
│   ├── document-library/              # 编辑文档库
│   ├── modification-accumulation/     # 我的修改积累
│   └── user-center/                   # 个人中心
├── shared/                            # 共享资源
│   ├── components/                    # 通用组件库
│   │   ├── base/                     # 基础组件
│   │   ├── business/                 # 业务组件
│   │   └── layout/                   # 布局组件
│   ├── composables/                  # 通用组合函数
│   ├── utils/                        # 工具函数
│   ├── constants/                    # 常量定义
│   └── types/                        # 全局类型定义
├── api/                              # API接口封装
│   └── modules/                      # 按模块分组的API
├── router/                           # 路由配置
├── stores/                           # 全局状态管理
└── assets/                           # 静态资源
```

**3. 核心依赖安装（指定版本）：**
```bash
# UI框架和组件
npm install element-plus@^2.4.0 @element-plus/icons-vue

# 富文本编辑器
npm install @wangeditor/editor@^5.1.0 @wangeditor/editor-for-vue@^5.1.0

# 多媒体处理库
npm install video.js@^8.0.0 @types/video.js
npm install wavesurfer.js@^7.0.0
npm install fabric@^5.3.0 @types/fabric

# 实时通信
npm install socket.io-client@^4.7.0

# 状态管理和工具
npm install pinia@^2.1.0 pinia-plugin-persistedstate@^3.2.0
npm install axios@^1.6.0
npm install @vueuse/core@^10.0.0

# 开发工具
npm install -D @vue/eslint-config-typescript@^12.0.0
npm install -D @typescript-eslint/parser@^6.0.0
npm install -D @typescript-eslint/eslint-plugin@^6.0.0
npm install -D prettier@^3.0.0
npm install -D eslint-plugin-prettier@^5.0.0
npm install -D vitest@^1.0.0
npm install -D @vue/test-utils@^2.4.0
npm install -D husky@^8.0.0
npm install -D lint-staged@^15.0.0
npm install -D cross-env@^7.0.0
```

**4. 关键配置文件内容：**

**vite.config.ts** - 包含路径别名、代理配置、构建优化：
- 配置所有路径别名
- 设置开发服务器代理到后端API
- 配置构建优化选项（代码分割、压缩等）
- 集成ESLint插件

**tsconfig.json** - TypeScript严格模式配置：
- 启用strict模式
- 配置路径映射
- 设置编译选项

**.eslintrc.js** - Vue3 + TypeScript规则：
- 继承Vue3和TypeScript推荐配置
- 配置Prettier集成
- 设置自定义规则

**package.json scripts** - 完整的脚本命令：
- dev、build、preview、test等基础命令
- lint、format、type-check等质量检查命令
- prepare、verify-toolchain等工具命令

**环境变量配置：**
- .env.development（开发环境）
- .env.production（生产环境）
- 包含API基础URL、功能开关、Mock配置等

**5. 验收标准：**
- [ ] 项目能够成功启动（npm run dev）
- [ ] TypeScript严格模式无错误
- [ ] 所有路径别名正确配置
- [ ] Element Plus样式正常加载
- [ ] 12个业务模块目录结构完整
- [ ] ESLint和Prettier配置正确
- [ ] 所有依赖正确安装且版本匹配

请提供：
1. 完整的项目初始化命令序列
2. 所有配置文件的完整内容（包含详细中文注释）
3. package.json的完整scripts配置
4. 基础的main.ts和App.vue文件
5. 路由和状态管理的基础配置
6. 项目启动验证步骤和检查清单


## 请为AI智能审校系统搭建完整的Mock API服务，基于以下12大业务模块构建RESTful API接口：


### 1. 用户认证模块 (auth)
- POST /api/auth/login - 用户登录（支持用户名/邮箱/手机号）
- POST /api/auth/register - 用户注册
- POST /api/auth/logout - 用户登出
- GET /api/auth/profile - 获取用户信息
- PUT /api/auth/profile - 更新用户信息
- POST /api/auth/refresh-token - 刷新JWT令牌
- GET /api/auth/permissions - 获取用户权限列表

### 2. 校对大屏模块 (dashboard)
- GET /api/dashboard/statistics - 实时统计数据（今日/本周/本月校对量）
- GET /api/dashboard/progress - 工作进度数据（各模块处理进度）
- GET /api/dashboard/system-status - 系统状态监控（服务器状态、队列状态）
- GET /api/dashboard/recent-activities - 最近活动记录

### 3. 内容预审管理模块 (pre-review)
- GET /api/pre-review/pending - 获取未预审文档列表（支持分页、筛选）
- POST /api/pre-review/documents - 上传待预审文档
- GET /api/pre-review/documents/:id - 获取文档详情
- PUT /api/pre-review/documents/:id - 更新文档信息
- DELETE /api/pre-review/documents/:id - 删除文档
- POST /api/pre-review/batch-approve - 批量通过预审
- POST /api/pre-review/batch-reject - 批量拒绝预审

### 4. AI批量审校模块 (batch-proofreading)
- GET /api/batch-proofreading/queue - 获取待校对文档队列
- POST /api/batch-proofreading/submit - 提交批量校对任务
- GET /api/batch-proofreading/tasks/:id - 获取校对任务状态
- GET /api/batch-proofreading/results - 获取完成校对文档列表
- GET /api/batch-proofreading/results/:id - 获取校对结果详情

### 5. 在线AI审校模块 (online-proofreading)
- POST /api/online-proofreading/sessions - 创建编辑会话
- GET /api/online-proofreading/sessions/:id - 获取编辑会话
- PUT /api/online-proofreading/sessions/:id/content - 实时更新内容
- GET /api/online-proofreading/sessions/:id/diff - 获取差异对比
- POST /api/online-proofreading/sessions/:id/collaborate - 协作编辑操作

### 6. 多媒体审校模块 (multimedia)
- POST /api/multimedia/upload - 文件上传（图片/视频/音频）
- GET /api/multimedia/files - 获取文件列表
- GET /api/multimedia/files/:id - 获取文件详情
- POST /api/multimedia/ocr - OCR识别请求
- GET /api/multimedia/ocr/:id - 获取OCR结果
- PUT /api/multimedia/files/:id/status - 更新审校状态

### 7. 专业排版模块 (typesetting)
- GET /api/typesetting/tasks - 获取排版任务列表
- POST /api/typesetting/tasks - 创建排版任务
- GET /api/typesetting/templates - 获取排版模板
- POST /api/typesetting/templates - 创建排版模板
- GET /api/typesetting/tasks/:id/result - 获取排版结果

### 8. 专业查询模块 (query)
- POST /api/query/search - 智能搜索接口
- GET /api/query/types - 获取查询类型列表
- GET /api/query/history - 获取查询历史
- GET /api/query/recommendations - 获取推荐结果

### 9. 编辑文档库模块 (document-library)
- GET /api/document-library/documents - 获取文档库列表
- POST /api/document-library/documents - 添加文档到库
- GET /api/document-library/comments - 获取意见列表
- POST /api/document-library/comments - 添加意见
- GET /api/document-library/permissions - 获取权限配置

### 10. 我的修改积累模块 (modification-accumulation)
- GET /api/modification-accumulation/cases - 获取案例集
- POST /api/modification-accumulation/cases - 添加修改案例
- GET /api/modification-accumulation/knowledge - 获取知识库
- GET /api/modification-accumulation/statistics - 获取统计分析

## 技术实现要求

### Mock服务配置
- 使用MSW (Mock Service Worker) v2.x最新版本
- 配置支持开发环境和测试环境
- 实现请求拦截和响应模拟

### 数据生成规范
- 使用faker.js v8.x，配置中文locale ('zh_CN')
- 生成符合中国用户习惯的测试数据（姓名、地址、电话等）
- 文档内容使用中文文本，包含常见的校对场景

### 错误处理模拟
- HTTP状态码模拟：200, 400, 401, 403, 404, 500
- 网络错误模拟：超时、连接失败、服务不可用
- 业务错误模拟：权限不足、数据验证失败、重复操作
- 可配置错误率（默认5%随机错误）

### 性能模拟
- 网络延迟模拟：200-2000ms随机延迟
- 大文件上传进度模拟
- 分页加载性能模拟

### 数据结构要求
- 统一的API响应格式：{ code, message, data, timestamp }
- 完整的TypeScript类型定义
- 支持分页：{ page, pageSize, total, list }
- 支持搜索和筛选参数

### 开发支持功能
- 热重载支持
- 详细的中文注释和文档
- 调试日志输出
- Mock数据持久化（localStorage）

请提供：
1. 完整的MSW配置文件
2. 所有API接口的handlers实现
3. TypeScript类型定义文件
4. faker.js数据生成工具函数
5. 错误处理和延迟模拟配置
6. 使用说明和测试验证方案

确保Mock API服务能够完全支持前端开发和测试需求，数据结构与真实后端API保持一致。

## 请为AI智能审校系统的多媒体审校模块添加三级菜单结构

为AI智能审校系统的左侧导航菜单中的"多媒体审校"模块添加三级菜单结构。具体要求：

**目标模块**：多媒体审校（二级菜单）
- 图片审校
- 视频审校
- 音频审校

**需要添加的三级菜单结构**：
每个二级菜单项（图片审校、视频审校、音频审校）下都需要添加以下4个三级子菜单：

1. **批量审校** - 支持批量上传和处理多个文件
2. **待审查文件** - 显示等待审校的文件列表（根据文件类型命名：待审查图片/待审查视频/待审查音频）
3. **在线审校** - 提供在线实时审校功能
4. **已审校文件** - 显示已完成审校的文件列表（根据文件类型命名：已审校图片/已审校视频/已审校音频）

**实现要求**：
- 修改前端导航菜单组件，确保三级菜单的展开/收起交互正常
- 保持与现有菜单样式的一致性
- 确保路由配置正确，每个三级菜单项都有对应的页面路由
- 遵循项目既有的菜单层级结构和命名规范


## 未预审文档-未预审文档模块数据结构（修改版）

1. 数据结构字段
根据需求修改后的未预审文档模块数据结构如下：

frontend/src/mock
/** 文档信息 */
export interface Document {
  /** 文档ID */
  id: string
  /** 文档标题 */
  title: string
  /** 内容简介 */
  content: string
  /** 文档类型 */
  type: 'text' | 'docx' | 'pdf' | 'wps'
  /** 文档状态 */
  status: 'draft' | 'pending' | 'reviewing' | 'rejected'
  /** 文档分类 */
  category: string
  /** 文档标签 */
  tags: string[]
  /** 创建者ID */
  creatorId: string
  /** 创建者姓名 */
  creatorName: string
  /** 作者姓名 */
  authorName: string
  /** 作者单位 */
  authorOrganization: string
  /** 作者简介 */
  authorBio: string
  /** 文件大小（字节） */
  fileSize: number
  /** 文件URL */
  fileUrl?: string
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 预计完成时间 */
  estimatedTime?: string
}

2. 字段用途说明
字段名	数据类型	用途说明
 id	string	文档的唯一标识符，用于检索和操作特定文档
 title	string	文档的标题，显示在列表和详情页面
 content	string	文档的内容简介，简要描述文档主要内容
type	enum	文档的类型，支持纯文本(text)、Word文档(docx)、PDF文档(pdf)和WPS文档(wps)
status	enum	文档的当前状态，未预审文档主要为'pending'状态
category	string	文档的分类，用于组织和筛选文档
tags	string[]	文档的标签列表，用于更细粒度的分类和筛选
creatorId	string	创建文档的用户ID，关联用户表
creatorName	string	创建文档的用户姓名，便于直接显示
authorName	string	文档作者姓名，可能与创建者不同
authorOrganization	string	文档作者所属单位或机构
authorBio	string	文档作者的简介信息
fileSize	number	文档文件的大小，以字节为单位
fileUrl	string?	文档文件的访问URL，可选字段
 createdAt	string	文档创建的时间戳
 updatedAt	string	文档最后更新的时间戳
 estimatedTime	string?	预计完成预审的时间，可选字段
3. 表格显示列
根据修改后的数据结构，未预审文档表格应显示以下列：

文档标题 ( title)
文档类型 ( type)
文档状态 ( status)
文档分类 ( category)
作者姓名 (authorName)
作者单位 (authorOrganization)
创建者 ( creatorName)
文件大小 ( fileSize，格式化显示)
创建时间 ( createdAt，格式化显示)
更新时间 ( updatedAt，格式化显示)
预计完成时间 ( estimatedTime，格式化显示)
操作列（包含操作按钮）
4. 操作按钮及功能
根据 Mock API 接口和功能描述，未预审文档模块应包含以下操作按钮：

查看详情：打开文档详情页面，显示完整内容和元数据
编辑文档：修改文档信息，对应 PUT /api/pre-review/documents/:id 接口
删除文档：从系统中删除文档，对应 DELETE /api/pre-review/documents/:id 接口
通过预审：将文档状态从 'pending' 更改为 'approved'，可能对应 POST /api/pre-review/batch-approve 接口 转到已预审文档模块、未预审文档模块不再显示
拒绝预审：将文档状态从 'pending' 更改为 'rejected'，可能对应 POST /api/pre-review/batch-reject 接口
批量操作：支持选择多个文档进行批量通过或拒绝操作
5. 状态值说明
文档状态字段  status 的可能值及其含义：

状态值	说明
draft	草稿状态，文档已创建但尚未提交预审
 pending	待预审状态，文档已提交等待预审，未预审文档模块主要显示此状态的文档
reviewing	预审中状态，文档正在被审核人员审核
rejected	预审拒绝状态，文档预审未通过，需要修改后重新提交
未预审文档模块主要关注  status 为  pending 的文档，通过预审管理接口可以将其状态更改为 rejected。



## 未预审文档模块功能增强需求

### 1. 顶部操作区域增强
为未预审文档模块添加以下功能按钮和搜索筛选组件：

#### 1.1 新增按钮
- **新建文档按钮**：位于表格顶部左侧，点击后弹出新建文档表单对话框
- **搜索按钮**：位于筛选区域右侧，触发搜索操作

#### 1.2 搜索筛选区域
在表格顶部添加以下搜索和筛选组件：
- **文档标题搜索框**：用于按`title`字段搜索文档
- **作者姓名搜索框**：用于按`authorName`字段搜索文档
- **状态筛选下拉框**：用于按`status`字段筛选文档
- **文件类型筛选下拉框**：用于按`type`字段筛选文档
- **时间区间选择器**：用于按`createdAt`字段筛选特定时间范围内创建的文档

### 2. 新建文档功能
点击"新建文档"按钮后，弹出模态对话框，包含以下内容：

#### 2.1 对话框标题
- "新建文档"

#### 2.2 表单内容分组
- **文档信息组**：包含文档基本信息字段
- **作者信息组**：包含作者相关字段

#### 2.3 表单字段（至少包含）
- 文档标题（必填）
- 内容简介（必填）
- 文档类型（必选，下拉框）
- 文档分类（必填）
- 文档标签（可多选）
- 作者姓名（必填）
- 作者单位（必填）
- 作者简介（选填）
- 文件上传（必选）

#### 2.4 底部按钮
- **取消按钮**：关闭对话框，不保存数据
- **保存按钮**：验证表单，提交数据，成功后关闭对话框并刷新文档列表

### 3. 技术实现要求
- 使用Element Plus组件库实现界面元素
- 表单验证遵循项目既有规范
- 响应式设计，适配不同屏幕尺寸
- 搜索和筛选功能支持实时或延迟过滤

---

## 2025-06-28 未预审文档模块数据结构（修改版）

**用户需求：**
未预审文档-未预审文档模块数据结构（修改版）

### 1. 数据结构字段
根据需求修改后的未预审文档模块数据结构如下：

frontend/src/mock
```typescript
/** 文档信息 */
export interface Document {
  /** 文档ID */
  id: string
  /** 文档标题 */
  title: string
  /** 内容简介 */
  content: string
  /** 文档类型 */
  type: 'text' | 'docx' | 'pdf' | 'wps'
  /** 文档状态 */
  status: 'draft' | 'pending' | 'reviewing' | 'approved' | 'rejected' | 'published'
  /** 文档分类 */
  category: string
  /** 文档标签 */
  tags: string[]
  /** 创建者ID */
  creatorId: string
  /** 创建者姓名 */
  creatorName: string
  /** 作者姓名 */
  authorName: string
  /** 作者单位 */
  authorOrganization: string
  /** 作者简介 */
  authorBio: string
  /** 文件大小（字节） */
  fileSize: number
  /** 文件URL */
  fileUrl?: string
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 预计完成时间 */
  estimatedTime?: string
}
```

### 2. 字段用途说明
| 字段名 | 数据类型 | 用途说明 |
|--------|----------|----------|
| id | string | 文档的唯一标识符，用于检索和操作特定文档 |
| title | string | 文档的标题，显示在列表和详情页面 |
| content | string | 文档的内容简介，简要描述文档主要内容 |
| type | enum | 文档的类型，支持纯文本(text)、Word文档(docx)、PDF文档(pdf)和WPS文档(wps) |
| status | enum | 文档的当前状态，未预审文档主要为'pending'状态 |
| category | string | 文档的分类，用于组织和筛选文档 |
| tags | string[] | 文档的标签列表，用于更细粒度的分类和筛选 |
| creatorId | string | 创建文档的用户ID，关联用户表 |
| creatorName | string | 创建文档的用户姓名，便于直接显示 |
| authorName | string | 文档作者姓名，可能与创建者不同 |
| authorOrganization | string | 文档作者所属单位或机构 |
| authorBio | string | 文档作者的简介信息 |
| fileSize | number | 文档文件的大小，以字节为单位 |
| fileUrl | string? | 文档文件的访问URL，可选字段 |
| createdAt | string | 文档创建的时间戳 |
| updatedAt | string | 文档最后更新的时间戳 |
| estimatedTime | string? | 预计完成预审的时间，可选字段 |

### 3. 表格显示列
根据修改后的数据结构，未预审文档表格应显示以下列：

- 文档标题 (title)
- 文档类型 (type)
- 文档状态 (status)
- 文档分类 (category)
- 作者姓名 (authorName)
- 作者单位 (authorOrganization)
- 创建者 (creatorName)
- 文件大小 (fileSize，格式化显示)
- 创建时间 (createdAt，格式化显示)
- 更新时间 (updatedAt，格式化显示)
- 预计完成时间 (estimatedTime，格式化显示)
- 操作列（包含操作按钮）

### 4. 操作按钮及功能
根据 Mock API 接口和功能描述，未预审文档模块应包含以下操作按钮：

- **查看详情**：打开文档详情页面，显示完整内容和元数据
- **编辑文档**：修改文档信息，对应 PUT /api/pre-review/documents/:id 接口
- **删除文档**：从系统中删除文档，对应 DELETE /api/pre-review/documents/:id 接口
- **AI校对**：将打开AI预审队列页面，对文档进行预审，对应 POST /api/pre-review/ai-proofreading 接口 ✅ **已实现**
- **通过预审**：将文档状态从 'pending' 更改为 'approved'，可能对应 POST /api/pre-review/batch-approve 接口，转到已预审文档模块、未预审文档模块不再显示
- **拒绝预审**：将文档状态从 'pending' 更改为 'rejected'，可能对应 POST /api/pre-review/batch-reject 接口
- **批量操作**：支持选择多个文档进行批量通过或拒绝操作

## AI校对功能实现详情

### 2024-06-28 AI校对按钮功能实现

### 2025-07-01 AI校对功能页面架构设计

#### 组件架构设计

基于现有的AI校对引擎模块和未预审文档模块，设计完整的AI校对功能页面架构：

**1. 页面主组件**
- `AIProofreadingPage.vue` - AI校对页面主组件
  - 负责整体页面布局和状态管理
  - 集成所有子组件
  - 处理路由参数和文档信息传递

**2. 核心功能组件**
- `DocumentInfoPanel.vue` - 文档信息展示组件
  - 显示文档基本信息（标题、作者、文件类型、大小等）
  - 提供文档内容预览功能
  - 支持文档元数据编辑

- `AIConfigPanel.vue` - AI配置选择组件
  - AI模型选择下拉框（DeepSeek、文心一言、豆包、通义千问、专业模型）
  - 提示词模板选择和自定义输入
  - 成本预算设置和质量要求配置

- `DocumentConverter.vue` - 文档格式转换组件
  - 支持docx、PDF、WPS等格式转换为Markdown
  - 显示转换进度和状态
  - 提供转换失败重试机制

- `DocumentSplitter.vue` - 文档智能拆分组件
  - 提供拆分策略选择（按章节、按字数、按段落）
  - 显示拆分预览和结果统计
  - 支持手动调整拆分参数

- `TaskListPanel.vue` - 多文档任务列表组件
  - 表格形式展示所有子文档
  - 每行包含：序号、标题、字数、状态、操作按钮
  - 支持批量操作和状态筛选

**3. 状态管理**
- `aiProofreadingStore.ts` - AI校对页面状态管理
  - 管理文档信息、AI配置、拆分结果等状态
  - 处理与AI校对引擎的交互
  - 管理校对进度和结果数据

**4. API接口扩展**
- 扩展现有的`preReview.ts` API模块
- 添加文档转换、智能拆分相关接口
- 集成AI校对引擎的ModuleAPI

**5. 类型定义**
- 扩展现有的类型定义
- 添加AI校对页面特有的接口和枚举
- 确保与AI校对引擎类型的兼容性

#### 实现内容
1. **API接口层**：在 `frontend/src/api/modules/preReview.ts` 中添加了 `aiProofreading` 方法
   - 接口路径：`POST /api/pre-review/ai-proofreading`
   - 参数：`{ documentId: string }`
   - 返回：AI校对结果对象

2. **Mock API层**：在 `frontend/src/mock/handlers/preReview.ts` 中添加了AI校对的Mock处理器
   - 模拟AI校对过程，包含拼写错误、语法错误检测
   - 返回详细的校对结果，包括错误类型、位置、置信度等
   - 模拟2秒处理时间

3. **状态管理层**：在 `frontend/src/features/content-review/stores/preReviewStore.ts` 中添加了 `aiProofreading` 方法
   - 处理加载状态管理
   - 错误处理和用户提示
   - 与API层的集成

4. **UI组件层**：在 `frontend/src/features/content-review/views/UnreviewedDocuments.vue` 中实现
   - 在操作列中添加了"AI校对"按钮（橙色warning类型）
   - 操作列宽度从200px调整为240px以容纳新按钮
   - 添加了 `handleAiProofreading` 方法处理用户点击
   - 包含确认对话框和成功提示

#### 技术特点
- **用户体验**：点击AI校对按钮会弹出确认对话框，确认后启动AI校对流程
- **状态反馈**：显示加载状态，完成后给出成功提示
- **错误处理**：完整的错误捕获和用户友好的错误提示
- **Mock数据**：提供真实的AI校对结果模拟，包含多种错误类型检测

#### 界面变化
- 操作列按钮顺序：查看详情 → AI校对 → 通过 → 拒绝 → 更多
- AI校对按钮使用橙色（warning）主题色，与其他操作按钮区分
- 保持了原有的响应式设计和样式一致性

## 界面布局优化

### 2024-06-28 合并el-card布局优化

#### 优化内容
将原来的三个独立el-card（搜索筛选区域、操作工具栏、文档表格）合并为一个统一的el-card，提升界面整体性和用户体验。

#### 具体变更
1. **HTML结构调整**：
   - 原来：`search-card` + `toolbar-card` + `table-card`
   - 现在：`document-management-card`（包含三个section）

2. **CSS样式优化**：
   - `.search-section`：搜索筛选区域，底部边框分隔
   - `.toolbar-section`：操作工具栏区域，底部边框分隔
   - `.table-section`：文档表格区域
   - 使用边框分隔代替卡片间距，视觉更统一

3. **布局层次**：
   ```
   document-management-card
   ├── search-section (搜索筛选)
   ├── toolbar-section (操作工具栏)
   └── table-section (文档表格 + 分页)
   ```

#### 用户体验提升
- 减少了视觉分割，界面更加整洁统一
- 保持了功能区域的逻辑分离
- 优化了间距和边框设计
- 响应式设计保持一致

## 问题修复记录

### 2024-06-28 API客户端导入问题修复

#### 问题描述
在合并el-card后，页面出现 `ReferenceError: apiClient is not defined` 错误，导致文档列表无法正常加载。

#### 问题原因
在 `frontend/src/api/modules/preReview.ts` 文件中缺少 `apiClient` 的导入语句，导致运行时找不到该对象。

#### 解决方案
在 `preReview.ts` 文件顶部添加正确的导入语句：
```typescript
import { apiClient } from '@/api'
```

#### 修复结果
- ✅ 页面能够正常加载文档列表
- ✅ 所有API调用功能恢复正常
- ✅ AI校对功能可以正常使用
- ✅ 合并后的el-card布局正常显示

## Mock API服务配置修复

### 2024-06-28 Mock API服务连接问题修复

#### 问题描述
页面出现"网络连接失败"和CORS错误，系统尝试连接到 `http://localhost:8000` 而不是使用Mock API服务。

#### 问题分析
1. **API基础URL配置错误**：环境变量中仍然配置了真实后端地址
2. **Mock服务拦截失败**：API客户端没有正确使用Mock服务
3. **应用初始化时序问题**：Mock服务可能在API调用之后才初始化

#### 解决方案
1. **修改环境配置**：
   ```bash
   # 清空API基础URL，让Mock服务接管
   VITE_API_BASE_URL=
   ```

2. **优化API客户端配置**：
   ```typescript
   // 在开发环境且启用Mock时，使用当前域名作为baseURL
   const isMockEnabled = import.meta.env.DEV && import.meta.env.VITE_ENABLE_MOCK === 'true'
   const baseURL = isMockEnabled
     ? window.location.origin
     : (import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000')
   ```

3. **改进应用启动流程**：
   ```typescript
   // 确保Mock服务在应用启动前完全初始化
   async function startApp() {
     if (import.meta.env.DEV && checkMockService()) {
       await initializeMockApi()
     }
     // 然后初始化Vue应用
   }
   ```

#### 修复效果
- ✅ Mock API服务正确拦截所有API请求
- ✅ 消除了CORS错误和网络连接失败
- ✅ 应用启动时序优化，确保Mock服务优先初始化
- ✅ 开发环境完全独立，无需真实后端服务

## Mock API服务深度调试

### 2024-06-28 Mock服务拦截问题分析

#### 问题现状
尽管进行了多项配置修改，Mock API服务仍然无法正确拦截请求：
1. **代理仍然生效**：请求被代理到 `http://localhost:8000/api/v1/`
2. **Mock服务未启动**：控制台没有Mock服务启动日志
3. **数据无法加载**：页面显示"暂无数据"

#### 已尝试的解决方案
1. **修改API客户端配置**：根据Mock启用状态动态选择baseURL
2. **优化应用启动流程**：确保Mock服务在Vue应用之前初始化
3. **调整Vite代理配置**：在Mock启用时禁用API代理
4. **环境变量配置**：确认 `VITE_ENABLE_MOCK=true` 设置正确

#### 根本问题分析
可能的原因：
1. **MSW Service Worker未注册**：浏览器中Service Worker可能未正确注册
2. **Mock处理器路径不匹配**：API路径与Mock处理器路径不一致
3. **初始化时序问题**：Mock服务初始化可能在API调用之后
4. **浏览器缓存问题**：Service Worker可能被缓存影响

#### 下一步调试方向
1. 检查浏览器开发者工具中的Service Worker状态
2. 验证Mock处理器的路径匹配
3. 添加更详细的Mock服务启动日志
4. 考虑使用其他Mock方案（如拦截器模式）

### 5. 状态值说明
文档状态字段 status 的可能值及其含义：

| 状态值 | 说明 |
|--------|------|
| draft | 草稿状态，文档已创建但尚未提交预审 |
| pending | 待预审状态，文档已提交等待预审，未预审文档模块主要显示此状态的文档 |
| reviewing | 预审中状态，文档正在被审核人员审核 |
| approved | 已通过预审状态，文档预审通过，可进入下一流程 |
| rejected | 预审拒绝状态，文档预审未通过，需要修改后重新提交 |
| published | 已发布状态，文档已完成全部流程并发布 |

未预审文档模块主要关注 status 为 pending 的文档，通过预审管理接口可以将其状态更改为 approved 或 rejected。
![alt text](image.png)
