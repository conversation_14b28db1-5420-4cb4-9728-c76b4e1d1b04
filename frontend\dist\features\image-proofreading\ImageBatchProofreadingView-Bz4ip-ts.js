import{Q as e,M as a}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as l,r as t,b as u,c as s,a as o,Q as r,H as n,K as i,I as c,ag as d,o as v,u as p,M as _,O as m}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as g}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const f={class:"image-batch-proofreading"},h={class:"content"},w={key:0,class:"upload-actions"},k={class:"progress-text"},y={class:"batch-actions"},b={key:0,class:"preview-content"},x=["src"],C={class:"dialog-footer"},R=g(l({__name:"ImageBatchProofreadingView",setup(l){const g=t([]),R=t([]),U=t(!1),V=t(!1),j=t(null),O=t(!1),L=t(""),z=t(null),T=u(()=>0===g.value.length?0:Math.round(R.value.length/g.value.length*100)),B=u(()=>U.value?"active":100===T.value?"success":"normal"),M=e=>e.raw?.type.startsWith("image/")?e.size&&e.size>10485760?(a.error("文件大小不能超过 10MB"),!1):void 0:(a.error("只能上传图片文件"),!1),I=e=>{const a=g.value.findIndex(a=>a.uid===e.uid);a>-1&&g.value.splice(a,1)},$=()=>{g.value=[],R.value=[]},F=async()=>{if(0!==g.value.length){U.value=!0,R.value=[];try{for(let e=0;e<g.value.length;e++){const a=g.value[e];await new Promise(e=>setTimeout(e,1e3));const l={name:a.name,size:a.size,status:"处理完成",ocrText:`这是文件 ${a.name} 的OCR识别结果示例文字...`,url:URL.createObjectURL(a.raw),originalFile:a};R.value.push(l)}a.success("批量处理完成")}catch{a.error("批量处理失败")}finally{U.value=!1}}else a.warning("请先上传图片文件")},E=()=>{z.value&&(z.value.ocrText=L.value,a.success("保存成功")),O.value=!1},K=()=>{if(0===R.value.length)return void a.warning("没有可导出的结果");const e=R.value.map(e=>`文件名: ${e.name}\n识别结果:\n${e.ocrText}\n\n`).join("---\n\n"),l=new Blob([e],{type:"text/plain"}),t=URL.createObjectURL(l),u=document.createElement("a");u.href=t,u.download="batch_ocr_results.txt",u.click(),URL.revokeObjectURL(t),a.success("导出成功")},P=()=>{0!==R.value.length?a.success("已提交审查，文件将转入待审查列表"):a.warning("没有可提交的结果")},Q=e=>{switch(e){case"处理完成":return"success";case"处理中":return"warning";case"处理失败":return"danger";default:return"info"}};return(a,l)=>{const t=d("el-icon"),u=d("el-upload"),H=d("el-button"),W=d("el-card"),q=d("el-progress"),A=d("el-table-column"),D=d("el-tag"),G=d("el-input"),J=d("el-table"),N=d("el-dialog");return v(),s("div",f,[l[15]||(l[15]=o("div",{class:"page-header"},[o("h1",null,"图片批量审校"),o("p",{class:"page-description"},"批量上传图片进行OCR识别和文字校对")],-1)),o("div",h,[r(W,{class:"upload-card",title:"批量上传图片"},{default:c(()=>[r(u,{class:"upload-demo",drag:"",action:"#",multiple:"",accept:"image/*","auto-upload":!1,"file-list":g.value,"on-change":M,"on-remove":I},{tip:c(()=>l[4]||(l[4]=[o("div",{class:"el-upload__tip"}," 支持 jpg/png/gif 格式，单个文件不超过 10MB，最多可上传 50 个文件 ",-1)])),default:c(()=>[r(t,{class:"el-icon--upload"},{default:c(()=>[r(p(e))]),_:1}),l[5]||(l[5]=o("div",{class:"el-upload__text"},[_("将图片拖拽到此处，或"),o("em",null,"点击批量上传")],-1))]),_:1,__:[5]},8,["file-list"]),g.value.length>0?(v(),s("div",w,[r(H,{type:"primary",onClick:F,loading:U.value},{default:c(()=>l[6]||(l[6]=[_(" 开始批量处理 ")])),_:1,__:[6]},8,["loading"]),r(H,{onClick:$},{default:c(()=>l[7]||(l[7]=[_("清空文件")])),_:1,__:[7]})])):i("",!0)]),_:1}),U.value||R.value.length>0?(v(),n(W,{key:0,title:"处理进度",class:"progress-card"},{default:c(()=>[r(q,{percentage:T.value,status:B.value,"stroke-width":8},null,8,["percentage","status"]),o("p",k," 已处理 "+m(R.value.length)+" / "+m(g.value.length)+" 个文件 ",1)]),_:1})):i("",!0),R.value.length>0?(v(),n(W,{key:1,title:"处理结果",class:"results-card"},{default:c(()=>[r(J,{data:R.value,style:{width:"100%"}},{default:c(()=>[r(A,{prop:"name",label:"文件名",width:"200"}),r(A,{prop:"size",label:"文件大小",width:"100"},{default:c(e=>{return[_(m((a=e.row.size,a<1024?a+" B":a<1048576?(a/1024).toFixed(1)+" KB":(a/1048576).toFixed(1)+" MB")),1)];var a}),_:1}),r(A,{prop:"status",label:"处理状态",width:"120"},{default:c(e=>[r(D,{type:Q(e.row.status)},{default:c(()=>[_(m(e.row.status),1)]),_:2},1032,["type"])]),_:1}),r(A,{prop:"ocrText",label:"识别文字","min-width":"300"},{default:c(e=>[r(G,{modelValue:e.row.ocrText,"onUpdate:modelValue":a=>e.row.ocrText=a,type:"textarea",rows:3,placeholder:"OCR识别结果...",readonly:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),r(A,{label:"操作",width:"200"},{default:c(e=>[r(H,{size:"small",onClick:a=>{return l=e.row,j.value=l,void(V.value=!0);var l}},{default:c(()=>l[8]||(l[8]=[_("预览")])),_:2,__:[8]},1032,["onClick"]),r(H,{size:"small",type:"primary",onClick:a=>{return l=e.row,z.value=l,L.value=l.ocrText,void(O.value=!0);var l}},{default:c(()=>l[9]||(l[9]=[_("编辑")])),_:2,__:[9]},1032,["onClick"]),r(H,{size:"small",onClick:a=>(e=>{const a=new Blob([e.ocrText],{type:"text/plain"}),l=URL.createObjectURL(a),t=document.createElement("a");t.href=l,t.download=`${e.name}_ocr_result.txt`,t.click(),URL.revokeObjectURL(l)})(e.row)},{default:c(()=>l[10]||(l[10]=[_("下载")])),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),o("div",y,[r(H,{type:"primary",onClick:K},{default:c(()=>l[11]||(l[11]=[_("导出全部结果")])),_:1,__:[11]}),r(H,{onClick:P},{default:c(()=>l[12]||(l[12]=[_("提交审查")])),_:1,__:[12]})])]),_:1})):i("",!0)]),r(N,{modelValue:V.value,"onUpdate:modelValue":l[0]||(l[0]=e=>V.value=e),title:"图片预览",width:"60%"},{default:c(()=>[j.value?(v(),s("div",b,[o("img",{src:j.value.url,alt:"预览图片",class:"preview-image"},null,8,x)])):i("",!0)]),_:1},8,["modelValue"]),r(N,{modelValue:O.value,"onUpdate:modelValue":l[3]||(l[3]=e=>O.value=e),title:"编辑OCR文字",width:"50%"},{footer:c(()=>[o("span",C,[r(H,{onClick:l[2]||(l[2]=e=>O.value=!1)},{default:c(()=>l[13]||(l[13]=[_("取消")])),_:1,__:[13]}),r(H,{type:"primary",onClick:E},{default:c(()=>l[14]||(l[14]=[_("保存")])),_:1,__:[14]})])]),default:c(()=>[r(G,{modelValue:L.value,"onUpdate:modelValue":l[1]||(l[1]=e=>L.value=e),type:"textarea",rows:10,placeholder:"请编辑OCR识别的文字..."},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-d3b0839b"]]);export{R as default};
