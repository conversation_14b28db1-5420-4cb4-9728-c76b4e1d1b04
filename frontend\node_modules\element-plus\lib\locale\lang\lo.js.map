{"version": 3, "file": "lo.js", "sources": ["../../../../../packages/locale/lang/lo.ts"], "sourcesContent": ["export default {\n  name: 'lo',\n  el: {\n    breadcrumb: {\n      label: 'ເມນູນຳທາງ',\n    },\n    colorpicker: {\n      confirm: 'ຕົກລົງ',\n      clear: 'ລ້າງ',\n      defaultLabel: 'ເຄື່ອງມືເລືອກສີ',\n      description: 'ສີປັດຈຸບັນແມ່ນ {color}. ກົດ enter ເພື່ອເລືອກສີໃໝ່.',\n      alphaLabel: 'ເລືອກຄ່າຄວາມໂປ່ງໃສ',\n    },\n    datepicker: {\n      now: 'ຕອນນີ້',\n      today: 'ມື້ນີ້',\n      cancel: 'ຍົກເລີກ',\n      clear: 'ລ້າງ',\n      confirm: 'ຕົກລົງ',\n      dateTablePrompt: 'ໃຊ້ປຸ່ມລູກສອນ ແລະ ປຸ່ມ enter ເພື່ອເລືອກວັນທີຂອງເດືອນ',\n      monthTablePrompt: 'ໃຊ້ປຸ່ມລູກສອນ ແລະ ປຸ່ມ enter ເພື່ອເລືອກເດືອນ',\n      yearTablePrompt: 'ໃຊ້ປຸ່ມລູກສອນ ແລະ ປຸ່ມ enter ເພື່ອເລືອກປີ',\n      selectedDate: 'ວັນທີທີ່ເລືອກ',\n      selectDate: 'ເລືອກວັນທີ',\n      selectTime: 'ເລືອກເວລາ',\n      startDate: 'ວັນທີເລີ່ມຕົ້ນ',\n      startTime: 'ເວລາເລີ່ມຕົ້ນ',\n      endDate: 'ວັນທີສິ້ນສຸດ',\n      endTime: 'ເວລາສິ້ນສຸດ',\n      prevYear: 'ປີກ່ອນໜ້າ',\n      nextYear: 'ປີຖັດໄປ',\n      prevMonth: 'ເດືອນກ່ອນໜ້າ',\n      nextMonth: 'ເດືອນຖັດໄປ',\n      year: 'ປີ',\n      month1: 'ມັງກອນ',\n      month2: 'ກຸມພາ',\n      month3: 'ມີນາ',\n      month4: 'ເມສາ',\n      month5: 'ພຶດສະພາ',\n      month6: 'ມິຖຸນາ',\n      month7: 'ກໍລະກົດ',\n      month8: 'ສິງຫາ',\n      month9: 'ກັນຍາ',\n      month10: 'ຕຸລາ',\n      month11: 'ພະຈິກ',\n      month12: 'ທັນວາ',\n      week: 'ອາທິດ',\n      weeks: {\n        sun: 'ອາ',\n        mon: 'ຈ',\n        tue: 'ອ',\n        wed: 'ພ',\n        thu: 'ພຫ',\n        fri: 'ສ',\n        sat: 'ເສົາ',\n      },\n      weeksFull: {\n        sun: 'ອາທິດ',\n        mon: 'ຈັນ',\n        tue: 'ອັງຄານ',\n        wed: 'ພຸດ',\n        thu: 'ພະຫັດ',\n        fri: 'ສຸກ',\n        sat: 'ເສົາ',\n      },\n      months: {\n        jan: 'ມັງກອນ',\n        feb: 'ກຸມພາ',\n        mar: 'ມີນາ',\n        apr: 'ເມສາ',\n        may: 'ພຶດສະພາ',\n        jun: 'ມິຖຸນາ',\n        jul: 'ກໍລະກົດ',\n        aug: 'ສິງຫາ',\n        sep: 'ກັນຍາ',\n        oct: 'ຕຸລາ',\n        nov: 'ພະຈິກ',\n        dec: 'ທັນວາ',\n      },\n    },\n    inputNumber: {\n      decrease: 'ຫຼຸດຈຳນວນ',\n      increase: 'ເພີ່ມຈຳນວນ',\n    },\n    select: {\n      loading: 'ກຳລັງໂຫຼດ',\n      noMatch: 'ບໍ່ພົບຂໍ້ມູນທີ່ກົງກັນ',\n      noData: 'ບໍ່ມີຂໍ້ມູນ',\n      placeholder: 'ເລືອກ',\n    },\n    mention: {\n      loading: 'ກຳລັງໂຫຼດ',\n    },\n    dropdown: {\n      toggleDropdown: 'ສະຫຼັບເມນູ',\n    },\n    cascader: {\n      noMatch: 'ບໍ່ພົບຂໍ້ມູນທີ່ກົງກັນ',\n      loading: 'ກຳລັງໂຫຼດ',\n      placeholder: 'ເລືອກ',\n      noData: 'ບໍ່ມີຂໍ້ມູນ',\n    },\n    pagination: {\n      goto: 'ໄປທີ່',\n      pagesize: '/ໜ້າ',\n      total: 'ທັງໝົດ {total}',\n      pageClassifier: '',\n      page: 'ໜ້າ',\n      prev: 'ຍ້ອນກັບ',\n      next: 'ຖັດໄປ',\n      currentPage: 'ໜ້າ {pager}',\n      prevPages: 'ຍ້ອນກັບ {pager} ໜ້າ',\n      nextPages: 'ຖັດໄປ {pager} ໜ້າ',\n      deprecationWarning:\n        'ພົບການໃຊ້ງານທີ່ບໍ່ຖືກຕ້ອງ, ກະລຸນາເບິ່ງເອກະສານການໃຊ້ງານ el-pagination ສຳລັບລາຍລະອຽດເພີ່ມເຕີມ',\n    },\n    dialog: {\n      close: 'ປິດກ່ອງຂໍ້ຄວາມນີ້',\n    },\n    drawer: {\n      close: 'ປິດກ່ອງຂໍ້ຄວາມນີ້',\n    },\n    messagebox: {\n      title: 'ຂໍ້ຄວາມ',\n      confirm: 'ຕົກລົງ',\n      cancel: 'ຍົກເລີກ',\n      error: 'ຂໍ້ມູນບໍ່ຖືກຕ້ອງ',\n      close: 'ປິດກ່ອງຂໍ້ຄວາມນີ້',\n    },\n    upload: {\n      deleteTip: 'ກົດປຸ່ມລົບເພື່ອລົບອອກ',\n      delete: 'ລົບ',\n      preview: 'ເບິ່ງຕົວຢ່າງ',\n      continue: 'ສືບຕໍ່',\n    },\n    slider: {\n      defaultLabel: 'ສະໄລເດີລະຫວ່າງ {min} ແລະ {max}',\n      defaultRangeStartLabel: 'ເລືອກຄ່າເລີ່ມຕົ້ນ',\n      defaultRangeEndLabel: 'ເລືອກຄ່າສິ້ນສຸດ',\n    },\n    table: {\n      emptyText: 'ບໍ່ມີຂໍ້ມູນ',\n      confirmFilter: 'ຢືນຢັນ',\n      resetFilter: 'ຄືນຄ່າໃໝ່',\n      clearFilter: 'ທັງໝົດ',\n      sumText: 'ຜົນລວມ',\n    },\n    tour: {\n      next: 'ຖັດໄປ',\n      previous: 'ຍ້ອນກັບ',\n      finish: 'ສຳເລັດ',\n    },\n    tree: {\n      emptyText: 'ບໍ່ມີຂໍ້ມູນ',\n    },\n    transfer: {\n      noMatch: 'ບໍ່ພົບຂໍ້ມູນທີ່ກົງກັນ',\n      noData: 'ບໍ່ມີຂໍ້ມູນ',\n      titles: ['ລາຍການທີ 1', 'ລາຍການທີ 2'],\n      filterPlaceholder: 'ປ້ອນຄຳຄົ້ນຫາ',\n      noCheckedFormat: '{total} ລາຍການ',\n      hasCheckedFormat: '{checked}/{total} ເລືອກແລ້ວ',\n    },\n    image: {\n      error: 'ເກີດຂໍ້ຜິດພາດ',\n    },\n    pageHeader: {\n      title: 'ກັບຄືນ',\n    },\n    popconfirm: {\n      confirmButtonText: 'ຕົກລົງ',\n      cancelButtonText: 'ຍົກເລີກ',\n    },\n    carousel: {\n      leftArrow: 'ລູກສອນເບື້ອງຊ້າຍຂອງ Carousel',\n      rightArrow: 'ລູກສອນເບື້ອງຂວາຂອງ Carousel',\n      indicator: 'Carousel ສະຫຼັບໄປລຳດັບທີ {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,wDAAwD;AACrE,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,KAAK,EAAE,0BAA0B;AACvC,MAAM,YAAY,EAAE,4FAA4F;AAChH,MAAM,WAAW,EAAE,oNAAoN;AACvO,MAAM,UAAU,EAAE,8GAA8G;AAChI,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,sCAAsC;AACjD,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,KAAK,EAAE,0BAA0B;AACvC,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,eAAe,EAAE,6QAA6Q;AACpS,MAAM,gBAAgB,EAAE,6NAA6N;AACrP,MAAM,eAAe,EAAE,2MAA2M;AAClO,MAAM,YAAY,EAAE,gFAAgF;AACpG,MAAM,UAAU,EAAE,8DAA8D;AAChF,MAAM,UAAU,EAAE,wDAAwD;AAC1E,MAAM,SAAS,EAAE,sFAAsF;AACvG,MAAM,SAAS,EAAE,gFAAgF;AACjG,MAAM,OAAO,EAAE,0EAA0E;AACzF,MAAM,OAAO,EAAE,oEAAoE;AACnF,MAAM,QAAQ,EAAE,wDAAwD;AACxE,MAAM,QAAQ,EAAE,4CAA4C;AAC5D,MAAM,SAAS,EAAE,0EAA0E;AAC3F,MAAM,SAAS,EAAE,8DAA8D;AAC/E,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,wDAAwD;AACxE,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,gIAAgI;AAC/I,MAAM,MAAM,EAAE,oEAAoE;AAClF,MAAM,WAAW,EAAE,gCAAgC;AACnD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,wDAAwD;AACvE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,8DAA8D;AACpF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gIAAgI;AAC/I,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,WAAW,EAAE,gCAAgC;AACnD,MAAM,MAAM,EAAE,oEAAoE;AAClF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,QAAQ,EAAE,qBAAqB;AACrC,MAAM,KAAK,EAAE,8CAA8C;AAC3D,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,WAAW,EAAE,4BAA4B;AAC/C,MAAM,SAAS,EAAE,uEAAuE;AACxF,MAAM,SAAS,EAAE,2DAA2D;AAC5E,MAAM,kBAAkB,EAAE,+cAA+c;AACze,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,wGAAwG;AACrH,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,wGAAwG;AACrH,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,4CAA4C;AACzD,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,KAAK,EAAE,kGAAkG;AAC/G,MAAM,KAAK,EAAE,wGAAwG;AACrH,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,gIAAgI;AACjJ,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,OAAO,EAAE,0EAA0E;AACzF,MAAM,QAAQ,EAAE,sCAAsC;AACtD,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,qHAAqH;AACzI,MAAM,sBAAsB,EAAE,wGAAwG;AACtI,MAAM,oBAAoB,EAAE,4FAA4F;AACxH,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,oEAAoE;AACrF,MAAM,aAAa,EAAE,sCAAsC;AAC3D,MAAM,WAAW,EAAE,wDAAwD;AAC3E,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,OAAO,EAAE,sCAAsC;AACrD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,QAAQ,EAAE,4CAA4C;AAC5D,MAAM,MAAM,EAAE,sCAAsC;AACpD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,oEAAoE;AACrF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gIAAgI;AAC/I,MAAM,MAAM,EAAE,oEAAoE;AAClF,MAAM,MAAM,EAAE,CAAC,oDAAoD,EAAE,oDAAoD,CAAC;AAC1H,MAAM,iBAAiB,EAAE,0EAA0E;AACnG,MAAM,eAAe,EAAE,8CAA8C;AACrE,MAAM,gBAAgB,EAAE,0EAA0E;AAClG,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,gFAAgF;AAC7F,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,sCAAsC;AACnD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,sCAAsC;AAC/D,MAAM,gBAAgB,EAAE,4CAA4C;AACpE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,6HAA6H;AAC9I,MAAM,UAAU,EAAE,uHAAuH;AACzI,MAAM,SAAS,EAAE,6GAA6G;AAC9H,KAAK;AACL,GAAG;AACH,CAAC;;;;"}