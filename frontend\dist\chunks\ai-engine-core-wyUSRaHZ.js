const e={},t=function(t,s,n){let r=Promise.resolve();if(s&&s.length>0){document.getElementsByTagName("link");const t=document.querySelector("meta[property=csp-nonce]"),n=t?.nonce||t?.getAttribute("nonce");r=Promise.allSettled(s.map(t=>{if((t=function(e){return"/"+e}(t))in e)return;e[t]=!0;const s=t.endsWith(".css"),r=s?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${t}"]${r}`))return;const o=document.createElement("link");return o.rel=s?"stylesheet":"modulepreload",s||(o.as="script"),o.crossOrigin="",o.href=t,n&&o.setAttribute("nonce",n),document.head.appendChild(o),s?new Promise((e,s)=>{o.addEventListener("load",e),o.addEventListener("error",()=>s(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function o(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then(e=>{for(const t of e||[])"rejected"===t.status&&o(t.reason);return t().catch(o)})};var s=(e=>(e.NETWORK="network",e.API="api",e.AUTH="auth",e.QUOTA="quota",e.TIMEOUT="timeout",e.PARSE="parse",e.CONFIG="config",e.UNKNOWN="unknown",e))(s||{}),n=(e=>(e.LOW="low",e.MEDIUM="medium",e.HIGH="high",e.CRITICAL="critical",e))(n||{});class r{config;modelConfig;requestPreprocessors=[];responsePostprocessors=[];dataMaskingConfig={enabled:!0,maskFields:["apiKey","content"],maskChar:"*",keepChars:4};requestLogs=[];maxLogRetention=1e3;constructor(e,t){this.config=e,this.modelConfig=t}async proofreadText(e){const t=Date.now();try{const s=this.buildAIRequest(e),n=await this.sendRequest(s);return this.parseResponse(n,e.content,t)}catch(s){throw this.handleError(s)}}async batchProofreadText(e){const t=[],s=this.chunkArray(e,3);for(const n of s){const e=n.map(e=>this.proofreadText(e)),r=await Promise.allSettled(e);for(const s of r)"fulfilled"===s.status&&t.push(s.value);s.indexOf(n)<s.length-1&&await this.delay(1e3)}return t}validateConfig(){const e=this.providerName||"Unknown Provider";if(!this.config.apiKey)throw new Error(`${e}: 缺少API密钥`);if(!this.config.apiEndpoint)throw new Error(`${e}: 缺少API端点`);if(!this.modelConfig.id)throw new Error(`${e}: 缺少模型ID`)}buildAIRequest(e){const t=this.buildSystemPrompt(e.promptTemplate,e.options),s=this.buildUserMessage(e.content,e.options);return{model:this.modelConfig.id,messages:[{role:"system",content:t},{role:"user",content:s}],max_tokens:Math.floor(.3*this.modelConfig.maxTokens),temperature:.1,stream:!1}}buildSystemPrompt(e,t){let s=e.content;if(e.variables&&e.variables.length>0)for(const n of e.variables){const e=t[n]||"";s=s.replace(new RegExp(`\\{\\{${n}\\}\\}`,"g"),e)}return s+="\n\n请按照以下JSON格式返回结果：\n",s+="{\n",s+='  "correctedContent": "校对后的文本",\n',s+='  "changes": [\n',s+="    {\n",s+='      "original": "原文",\n',s+='      "corrected": "修改后",\n',s+='      "reason": "修改原因",\n',s+='      "type": "修改类型(grammar/spelling/style/logic/punctuation/other)",\n',s+='      "severity": "严重程度(high/medium/low)",\n',s+='      "confidence": 0.95\n',s+="    }\n",s+="  ],\n",s+='  "suggestions": ["总体建议1", "总体建议2"],\n',s+='  "confidence": 0.95\n',s+="}",s}buildUserMessage(e,t){let s=`请校对以下${t.language||"中文"}文本：\n\n`;return s+=e,t.focus&&"all"!==t.focus&&(s+=`\n\n请重点关注：${t.focus}`),t.style&&(s+=`\n文档风格：${t.style}`),s}parseResponse(e,t,s){const n=Date.now()-s;try{const s=e.choices[0]?.message?.content;if(!s)throw new Error("AI响应为空");const r=this.parseJSONResponse(s),o=this.calculateCost(e.usage);return{chunkId:this.generateChunkId(),originalContent:t,correctedContent:r.correctedContent||t,changes:r.changes||[],suggestions:r.suggestions||[],confidence:r.confidence||.8,processingTime:n,cost:o,modelUsed:this.modelConfig.name,timestamp:Date.now()}}catch(r){return{chunkId:this.generateChunkId(),originalContent:t,correctedContent:t,changes:[],suggestions:["AI响应解析失败，请检查内容格式"],confidence:0,processingTime:n,cost:0,modelUsed:this.modelConfig.name,timestamp:Date.now(),error:r instanceof Error?r.message:"未知错误"}}}parseJSONResponse(e){const t=e.match(/\{[\s\S]*\}/);if(!t)throw new Error("响应中未找到JSON格式");return JSON.parse(t[0])}calculateCost(e){return((e.prompt_tokens||0)+(e.completion_tokens||0))*this.modelConfig.costPerToken}handleError(e){const t=`${this.providerName} 请求失败: ${e.message}`;return new Error(t)}chunkArray(e,t){const s=[];for(let n=0;n<e.length;n+=t)s.push(e.slice(n,n+t));return s}delay(e){return new Promise(t=>setTimeout(t,e))}generateChunkId(){return`chunk_${Date.now()}_${Math.random().toString(36).substring(2,8)}`}async validateConfigEnhanced(){const e=[],t=[];try{this.validateConfig();const s=await this.validateApiKey();s.valid||e.push(`API密钥验证失败: ${s.error}`);return await this.checkEndpointReachability()||e.push("API端点不可达"),this.modelConfig.maxTokens<=0&&e.push("模型最大Token数必须大于0"),this.modelConfig.costPerToken<0&&t.push("模型成本配置可能不正确"),{valid:0===e.length,errors:e,warnings:t,timestamp:Date.now()}}catch(s){return{valid:!1,errors:[s instanceof Error?s.message:"配置验证失败"],warnings:t,timestamp:Date.now()}}}async validateApiKey(){try{return!this.config.apiKey||this.config.apiKey.length<10?{valid:!1,error:"API密钥格式不正确"}:{valid:!0}}catch(e){return{valid:!1,error:e instanceof Error?e.message:"未知错误"}}}async checkEndpointReachability(){try{const e=new AbortController,t=setTimeout(()=>e.abort(),5e3),s=await fetch(this.apiEndpoint,{method:"HEAD",signal:e.signal});return clearTimeout(t),s.status<500}catch(e){return!1}}addRequestPreprocessor(e){this.requestPreprocessors.push(e),this.requestPreprocessors.sort((e,t)=>t.priority-e.priority)}addResponsePostprocessor(e){this.responsePostprocessors.push(e),this.responsePostprocessors.sort((e,t)=>t.priority-e.priority)}async executeRequestPreprocessing(e){let t=e;for(const n of this.requestPreprocessors)if(n.enabled)try{t=await n.process(t)}catch(s){}return t}async executeResponsePostprocessing(e,t){let s=e;for(const r of this.responsePostprocessors)if(r.enabled)try{s=await r.process(s,t)}catch(n){}return s}logRequest(e,t,s,n,r){const o={requestId:e,providerId:`${this.modelConfig.provider}-${this.modelConfig.id}`,timestamp:Date.now(),request:this.maskSensitiveData(t),response:s?this.maskSensitiveData(s):void 0,error:n,duration:r||0,tokenUsage:s?.usage?{input:s.usage.prompt_tokens||0,output:s.usage.completion_tokens||0,total:s.usage.total_tokens||0}:void 0,cost:s?this.calculateCost(s.usage):void 0};this.requestLogs.push(o),this.requestLogs.length>this.maxLogRetention&&(this.requestLogs=this.requestLogs.slice(-this.maxLogRetention))}maskSensitiveData(e){if(!this.dataMaskingConfig.enabled)return e;const t=JSON.parse(JSON.stringify(e));for(const s of this.dataMaskingConfig.maskFields)if(t[s]){const e=String(t[s]);if(e.length>2*this.dataMaskingConfig.keepChars){const n=e.substring(0,this.dataMaskingConfig.keepChars),r=e.substring(e.length-this.dataMaskingConfig.keepChars),o=this.dataMaskingConfig.maskChar.repeat(Math.max(4,e.length-2*this.dataMaskingConfig.keepChars));t[s]=`${n}${o}${r}`}}return t}getRequestLogs(e){const t=[...this.requestLogs].reverse();return e?t.slice(0,e):t}clearRequestLogs(){this.requestLogs=[]}handleErrorEnhanced(e){return{type:this.classifyError(e),code:this.extractErrorCode(e),message:e.message,details:e.stack,severity:this.assessErrorSeverity(e),retryable:this.isRetryableError(e),retryCount:0,timestamp:Date.now(),originalError:e}}classifyError(e){const t=e.message.toLowerCase();return t.includes("network")||t.includes("fetch")?s.NETWORK:t.includes("unauthorized")||t.includes("forbidden")?s.AUTH:t.includes("quota")||t.includes("rate limit")?s.QUOTA:t.includes("timeout")?s.TIMEOUT:t.includes("parse")||t.includes("json")?s.PARSE:t.includes("config")?s.CONFIG:s.UNKNOWN}extractErrorCode(e){const t=e.message.match(/(\d{3})/);return t?.[1]||"UNKNOWN"}assessErrorSeverity(e){switch(this.classifyError(e)){case s.AUTH:case s.CONFIG:return n.CRITICAL;case s.QUOTA:return n.HIGH;case s.TIMEOUT:case s.NETWORK:return n.MEDIUM;default:return n.LOW}}isRetryableError(e){const t=this.classifyError(e);return[s.NETWORK,s.TIMEOUT,s.QUOTA].includes(t)}}class o extends r{providerName="DeepSeek";apiEndpoint="https://api.deepseek.com/v1/chat/completions";constructor(e,t){super(e,t),e.apiEndpoint&&(this.apiEndpoint=e.apiEndpoint),this.validateConfig(),this.addRequestPreprocessor({process:async e=>({...e,top_p:.95,frequency_penalty:0,presence_penalty:0,chinese_optimization:!0}),priority:10,enabled:!0}),this.addResponsePostprocessor({process:async(e,t)=>(e.choices?.[0]?.message?.content&&(e.choices[0].message.content=this.optimizeChinesePunctuation(e.choices[0].message.content)),e),priority:10,enabled:!0})}async sendRequest(e){const t={model:e.model,messages:e.messages,max_tokens:e.max_tokens,temperature:e.temperature,stream:!1,top_p:.95,frequency_penalty:0,presence_penalty:0},s=await this.makeHttpRequest(t);return this.convertToStandardResponse(s)}async makeHttpRequest(e){const t=new AbortController,s=setTimeout(()=>t.abort(),this.config.timeout||3e4);try{const n=await fetch(this.apiEndpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`,"User-Agent":"AI-Proofreading-Engine/1.0.0"},body:JSON.stringify(e),signal:t.signal});if(clearTimeout(s),!n.ok){const e=await n.json().catch(()=>({}));throw new Error(`DeepSeek API错误 ${n.status}: ${e.error?.message||n.statusText}`)}return await n.json()}catch(n){if(clearTimeout(s),n instanceof Error){if("AbortError"===n.name)throw new Error("DeepSeek API请求超时");throw n}throw new Error("DeepSeek API请求失败")}}convertToStandardResponse(e){return{id:e.id,model:e.model,choices:e.choices.map(e=>({message:{role:e.message.role,content:e.message.content},finish_reason:e.finish_reason,index:e.index})),usage:{prompt_tokens:e.usage.prompt_tokens,completion_tokens:e.usage.completion_tokens,total_tokens:e.usage.total_tokens},created:e.created}}buildSystemPrompt(e,t){let s=super.buildSystemPrompt(e,t);return s+="\n\n注意事项：\n",s+="1. 请确保返回的JSON格式严格正确\n",s+="2. 修改建议应该具体明确\n",s+="3. 置信度应该基于修改的确定性\n",s+="4. 如果文本质量很好，可以返回较少的修改建议\n",s}parseJSONResponse(e){try{const t=e.match(/```json\s*([\s\S]*?)\s*```/)||e.match(/\{[\s\S]*\}/);if(!t)throw new Error("未找到JSON格式的响应");const s=t[1]||t[0],n=JSON.parse(s);return n.correctedContent||(n.correctedContent=n.corrected_content||""),n.changes||(n.changes=n.modifications||[]),n.suggestions||(n.suggestions=n.recommendations||[]),"number"!=typeof n.confidence&&(n.confidence=.8),n}catch(t){return this.extractBasicInfo(e)}}extractBasicInfo(e){return{correctedContent:e,changes:[],suggestions:["AI返回格式异常，请人工检查"],confidence:.3}}calculateCost(e){return(e.prompt_tokens||0)/1e3*.0014+(e.completion_tokens||0)/1e3*.0028}handleError(e){let t=e.message;return t.includes("401")?t="DeepSeek API密钥无效或已过期":t.includes("429")?t="DeepSeek API请求频率超限，请稍后重试":t.includes("500")?t="DeepSeek服务器内部错误，请稍后重试":t.includes("timeout")&&(t="DeepSeek API请求超时，请检查网络连接"),new Error(`${this.providerName}: ${t}`)}getModelInfo(){return`${this.providerName} - ${this.modelConfig.name} (${this.modelConfig.maxTokens} tokens)`}async batchProofreadText(e){const t=[],s=this.chunkArray(e,3);for(const n of s){const e=n.map(async e=>{try{return await this.proofreadText(e)}catch(t){return null}}),r=await Promise.allSettled(e);for(const s of r)"fulfilled"===s.status&&s.value&&t.push(s.value);s.indexOf(n)<s.length-1&&await this.delay(500)}return t}chunkArray(e,t){const s=[];for(let n=0;n<e.length;n+=t)s.push(e.slice(n,n+t));return s}delay(e){return new Promise(t=>setTimeout(t,e))}async checkAvailability(){try{const e={model:this.modelConfig.id,messages:[{role:"user",content:"测试连接"}],max_tokens:10};return await this.sendRequest(e),!0}catch(e){return!1}}optimizeChinesePunctuation(e){return e.replace(/，\s+/g,"，").replace(/。\s+/g,"。").replace(/；\s+/g,"；").replace(/：\s+/g,"：").replace(/？\s+/g,"？").replace(/！\s+/g,"！").replace(/,(\S)/g,"，$1").replace(/\.(\S)/g,"。$1").replace(/;(\S)/g,"；$1").replace(/:(\S)/g,"：$1").replace(/"([^"]*?)"/g,'"$1"').replace(/'([^']*?)'/g,"'$1'")}async validateApiKey(){try{if(!this.config.apiKey||!this.config.apiKey.startsWith("sk-"))return{valid:!1,error:'DeepSeek API密钥必须以"sk-"开头'};if(this.config.apiKey.length<20)return{valid:!1,error:"DeepSeek API密钥长度不足"};try{const e={model:this.modelConfig.id,messages:[{role:"user",content:"test"}],max_tokens:1};return await this.sendRequest(e),{valid:!0,permissions:["chat","completion"],quota:{remaining:-1,total:-1,resetTime:Date.now()+864e5}}}catch(e){const t=e instanceof Error?e.message:"未知错误";if(t.includes("401")||t.includes("unauthorized"))return{valid:!1,error:"API密钥无效或已过期"};throw e}}catch(e){return{valid:!1,error:e instanceof Error?e.message:"验证过程中发生错误"}}}handleErrorEnhanced(e){const t=super.handleErrorEnhanced(e),n=e.message.toLowerCase();return n.includes("deepseek")&&(n.includes("rate limit")||n.includes("429")?(t.type=s.QUOTA,t.message="DeepSeek API请求频率超限，建议等待1分钟后重试",t.retryable=!0):n.includes("context length")||n.includes("token")?(t.type=s.API,t.message="DeepSeek输入内容过长，请减少文本长度",t.retryable=!1):n.includes("model not found")&&(t.type=s.CONFIG,t.message="DeepSeek模型配置错误，请检查模型ID",t.retryable=!1)),t}getModelCapabilities(){return{maxTokens:this.modelConfig.maxTokens,supportedLanguages:["zh-CN","en-US","ja-JP"],specialFeatures:["32K上下文窗口","中文优化","代码理解","数学推理","函数调用支持"],costPerToken:this.modelConfig.costPerToken,contextWindow:32e3}}getCostOptimizationSuggestions(e){const t=[];e.prompt_tokens>2e4&&t.push("输入文本较长，建议分段处理以降低成本"),e.completion_tokens>5e3&&t.push("输出内容较多，可以通过调整提示词来控制输出长度");return(e.prompt_tokens+e.completion_tokens)*this.modelConfig.costPerToken>.1&&t.push("单次请求成本较高，建议优化提示词或使用更经济的模型"),t}}class i extends r{providerName="字节豆包";apiEndpoint="https://ark.cn-beijing.volces.com/api/v3/chat/completions";constructor(e,t){super(e,t),e.apiEndpoint&&(this.apiEndpoint=e.apiEndpoint),this.addRequestPreprocessor({process:async e=>({...e,temperature:Math.min(e.temperature||.1,.9),top_p:.9,frequency_penalty:0,presence_penalty:0,high_concurrency:!0,fast_response:!0}),priority:10,enabled:!0}),this.addResponsePostprocessor({process:async(e,t)=>(e.choices?.[0]?.message?.content&&(e.choices[0].message.content=this.optimizeResponseQuality(e.choices[0].message.content)),e),priority:10,enabled:!0})}async sendRequest(e){const t={model:e.model,messages:e.messages,max_tokens:e.max_tokens,temperature:e.temperature,stream:!1,top_p:.95,frequency_penalty:0,presence_penalty:0,stop:null},s=await this.makeHttpRequest(t);return this.convertToStandardResponse(s)}async makeHttpRequest(e){const t=new AbortController,s=setTimeout(()=>t.abort(),this.config.timeout||3e4);try{const n=await fetch(this.apiEndpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`,"User-Agent":"AI-Proofreading-Engine/1.0.0"},body:JSON.stringify(e),signal:t.signal});if(clearTimeout(s),!n.ok){const e=await n.json().catch(()=>({}));throw new Error(`豆包API错误 ${n.status}: ${e.error?.message||n.statusText}`)}return await n.json()}catch(n){if(clearTimeout(s),n instanceof Error){if("AbortError"===n.name)throw new Error("豆包API请求超时");throw n}throw new Error("豆包API请求失败")}}convertToStandardResponse(e){return{id:e.id,model:e.model,choices:e.choices.map(e=>({message:{role:e.message.role,content:e.message.content},finish_reason:e.finish_reason,index:e.index})),usage:{prompt_tokens:e.usage.prompt_tokens,completion_tokens:e.usage.completion_tokens,total_tokens:e.usage.total_tokens},created:e.created}}buildSystemPrompt(e,t){let s=super.buildSystemPrompt(e,t);return s+="\n\n豆包处理要求：\n",s+="1. 请快速准确地完成校对任务\n",s+="2. 返回的JSON格式必须严格正确\n",s+="3. 对于不确定的修改，请在原因中说明\n",s+="4. 优先处理明显的错误\n",s}parseJSONResponse(e){try{const t=e.match(/```json\s*([\s\S]*?)\s*```/)||e.match(/\{[\s\S]*\}/);if(!t)throw new Error("未找到JSON格式的响应");const s=t[1]||t[0],n=JSON.parse(s);return!n.correctedContent&&n.corrected_text&&(n.correctedContent=n.corrected_text),!n.changes&&n.modifications&&(n.changes=n.modifications),!n.suggestions&&n.advice&&(n.suggestions=n.advice),("number"!=typeof n.confidence||n.confidence<0||n.confidence>1)&&(n.confidence=.85),n}catch(t){return this.extractBasicInfo(e)}}extractBasicInfo(e){const t=e.match(/修改后[：:]\s*([\s\S]*?)(?:\n\n|$)/);return{correctedContent:t?t[1].trim():e,changes:[],suggestions:["豆包返回格式异常，请人工检查"],confidence:.4}}calculateCost(e){return((e.prompt_tokens||0)+(e.completion_tokens||0))/1e3*.005}handleError(e){let t=e.message;return t.includes("401")?t="豆包API密钥无效或已过期":t.includes("429")?t="豆包API请求频率超限，请稍后重试":t.includes("500")?t="豆包服务器内部错误，请稍后重试":t.includes("timeout")?t="豆包API请求超时，请检查网络连接":t.includes("model_not_found")&&(t="豆包模型不存在或不可用"),new Error(`${this.providerName}: ${t}`)}getModelInfo(){return`${this.providerName} - ${this.modelConfig.name} (快速响应)`}async checkAvailability(){try{const e={model:this.modelConfig.id,messages:[{role:"user",content:"测试连接"}],max_tokens:10};return await this.sendRequest(e),!0}catch(e){return!1}}async batchProofreadText(e){const t=Math.min(this.config.concurrencyLimit||5,8),s=this.chunkArray(e,t),n=[];for(const r of s){const e=r.map(e=>this.proofreadText(e)),t=await Promise.allSettled(e);for(const s of t)"fulfilled"===s.status&&n.push(s.value);s.indexOf(r)<s.length-1&&await this.delay(500)}return n}optimizeResponseQuality(e){return e.replace(/\n{3,}/g,"\n\n").replace(/\s{2,}/g," ").replace(/\s+([，。；：？！])/g,"$1").replace(/([，。；：？！])\s+/g,"$1 ").replace(/(\d+)\s*([%％])/g,"$1$2").replace(/(\d+)\s*(元|万|千|百)/g,"$1$2").trim()}async validateApiKey(){try{if(!this.config.apiKey)return{valid:!1,error:"缺少豆包API密钥"};if(this.config.apiKey.length<30)return{valid:!1,error:"豆包API密钥格式不正确，长度不足"};try{const e={model:this.modelConfig.id,messages:[{role:"user",content:"test"}],max_tokens:1};return await this.sendRequest(e),{valid:!0,permissions:["chat","completion","high_concurrency"],quota:{remaining:-1,total:-1,resetTime:Date.now()+864e5}}}catch(e){const t=e instanceof Error?e.message:"未知错误";if(t.includes("401")||t.includes("unauthorized"))return{valid:!1,error:"API密钥无效或已过期"};throw e}}catch(e){return{valid:!1,error:e instanceof Error?e.message:"验证过程中发生错误"}}}handleErrorEnhanced(e){const t=super.handleErrorEnhanced(e),s=e.message.toLowerCase();return(s.includes("doubao")||s.includes("bytedance"))&&(s.includes("rate limit")||s.includes("429")?(t.type=ErrorType.QUOTA,t.message="豆包API请求频率超限，建议稍后重试",t.retryable=!0):s.includes("context length")?(t.type=ErrorType.API,t.message="豆包输入内容过长，请减少文本长度",t.retryable=!1):s.includes("model not available")?(t.type=ErrorType.CONFIG,t.message="豆包模型暂时不可用，请稍后重试或更换模型",t.retryable=!0):s.includes("insufficient balance")&&(t.type=ErrorType.QUOTA,t.message="豆包账户余额不足",t.retryable=!1)),t}getModelCapabilities(){return{maxTokens:this.modelConfig.maxTokens,supportedLanguages:["zh-CN","en-US"],specialFeatures:["32K上下文窗口","高并发处理","快速响应","中英文双语","成本优化"],costPerToken:this.modelConfig.costPerToken,contextWindow:32e3}}getCostOptimizationSuggestions(e){const t=[];e.prompt_tokens>25e3&&t.push("输入文本较长，建议分段处理以提高处理效率"),e.completion_tokens>8e3&&t.push("输出内容较多，可以通过调整提示词来控制输出长度");return(e.prompt_tokens+e.completion_tokens)*this.modelConfig.costPerToken>.08&&t.push("单次请求成本较高，建议优化提示词"),t.push("豆包支持高并发处理，适合批量文档审校任务"),t.push("豆包响应速度快，适合实时交互场景"),t}getPerformanceOptimizationConfig(){return{concurrency:8,batchSize:20,retryDelay:500,timeout:15e3}}}class a extends r{providerName="阿里通义千问";apiEndpoint="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation";constructor(e,t){super(e,t),e.apiEndpoint&&(this.apiEndpoint=e.apiEndpoint)}async sendRequest(e){const t=this.convertToQwenRequest(e),s=await this.makeHttpRequest(t);return this.convertToStandardResponse(s)}convertToQwenRequest(e){return{model:e.model,input:{messages:e.messages.map(e=>({role:e.role,content:e.content}))},parameters:{max_tokens:e.max_tokens,temperature:e.temperature||.1,top_p:.95,top_k:50,repetition_penalty:1.1,stream:!1}}}async makeHttpRequest(e){const t=new AbortController,s=setTimeout(()=>t.abort(),this.config.timeout||3e4);try{const n=await fetch(this.apiEndpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`,"X-DashScope-SSE":"disable"},body:JSON.stringify(e),signal:t.signal});if(clearTimeout(s),!n.ok){const e=await n.json().catch(()=>({}));throw new Error(`通义千问API错误 ${n.status}: ${e.message||n.statusText}`)}const r=await n.json();if(r.code)throw new Error(`通义千问API错误 ${r.code}: ${r.message}`);return r}catch(n){if(clearTimeout(s),n instanceof Error){if("AbortError"===n.name)throw new Error("通义千问API请求超时");throw n}throw new Error("通义千问API请求失败")}}convertToStandardResponse(e){let t="",s="stop";return e.output.choices&&e.output.choices.length>0?(t=e.output.choices[0].message.content,s=e.output.choices[0].finish_reason):(t=e.output.text,s=e.output.finish_reason),{id:e.request_id,model:this.modelConfig.id,choices:[{message:{role:"assistant",content:t},finish_reason:s,index:0}],usage:{prompt_tokens:e.usage.input_tokens,completion_tokens:e.usage.output_tokens,total_tokens:e.usage.total_tokens},created:Date.now()}}buildSystemPrompt(e,t){let s=super.buildSystemPrompt(e,t);return s+="\n\n通义千问处理要求：\n",s+="1. 请提供专业准确的校对建议\n",s+="2. 确保返回的JSON格式完全正确\n",s+="3. 对于企业文档，请保持正式语调\n",s+="4. 注意术语的一致性和准确性\n",s}parseJSONResponse(e){try{const t=e.match(/```json\s*([\s\S]*?)\s*```/)||e.match(/\{[\s\S]*\}/);if(!t)throw new Error("未找到JSON格式的响应");const s=t[1]||t[0],n=JSON.parse(s);return this.normalizeResultFields(n),this.validateAndFixResult(n),n}catch(t){return this.extractBasicInfo(e)}}normalizeResultFields(e){e.correctedContent||(e.correctedContent=e.corrected_content||e.correctedText||e.corrected_text||""),e.changes||(e.changes=e.modifications||e.edits||[]),e.suggestions||(e.suggestions=e.recommendations||e.advice||[])}validateAndFixResult(e){("number"!=typeof e.confidence||e.confidence<0||e.confidence>1)&&(e.confidence=.88),Array.isArray(e.changes)||(e.changes=[]),Array.isArray(e.suggestions)||(e.suggestions=[]),e.changes=e.changes.filter(e=>e&&"object"==typeof e&&e.original&&e.corrected)}extractBasicInfo(e){const t=e.match(/(?:修改后|校对后)[：:]\s*([\s\S]*?)(?:\n\n|$)/);return{correctedContent:t?t[1].trim():e,changes:[],suggestions:["通义千问返回格式异常，请人工检查"],confidence:.5}}calculateCost(e){return((e.prompt_tokens||0)+(e.completion_tokens||0))/1e3*.006}handleError(e){let t=e.message;return t.includes("InvalidApiKey")?t="通义千问API密钥无效":t.includes("Throttling")?t="通义千问API请求频率超限，请稍后重试":t.includes("InternalError")?t="通义千问服务器内部错误，请稍后重试":t.includes("timeout")?t="通义千问API请求超时，请检查网络连接":t.includes("ModelNotFound")&&(t="通义千问模型不存在或不可用"),new Error(`${this.providerName}: ${t}`)}getModelInfo(){return`${this.providerName} - ${this.modelConfig.name} (企业级)`}async checkAvailability(){try{const e={model:this.modelConfig.id,messages:[{role:"user",content:"测试连接"}],max_tokens:10};return await this.sendRequest(e),!0}catch(e){return!1}}}class c extends r{providerName="百度文心一言";apiEndpoint="https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant";accessToken=null;tokenExpiry=0;constructor(e,t){super(e,t),e.apiEndpoint&&(this.apiEndpoint=e.apiEndpoint),this.addRequestPreprocessor({process:async e=>({...e,temperature:Math.min(e.temperature||.1,.95),top_p:.8,penalty_score:1,chinese_enhanced:!0}),priority:10,enabled:!0}),this.addResponsePostprocessor({process:async(e,t)=>(e.result&&(e.result=this.optimizeChineseExpression(e.result)),e),priority:10,enabled:!0})}async sendRequest(e){await this.ensureAccessToken();const t=this.convertToWenxinRequest(e),s=await this.makeHttpRequest(t);return this.convertToStandardResponse(s)}async ensureAccessToken(){const e=Date.now();this.accessToken&&e<this.tokenExpiry||await this.refreshAccessToken()}async refreshAccessToken(){const[e,t]=this.config.apiKey.split(":");if(!e||!t)throw new Error("文心一言API密钥格式错误，应为 client_id:client_secret");const s=new URLSearchParams({grant_type:"client_credentials",client_id:e,client_secret:t});try{const e=await fetch(`https://aip.baidubce.com/oauth/2.0/token?${s}`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"}});if(!e.ok)throw new Error(`获取访问令牌失败: ${e.status}`);const t=await e.json();if(t.error)throw new Error(`获取访问令牌失败: ${t.error_description}`);this.accessToken=t.access_token,this.tokenExpiry=Date.now()+1e3*t.expires_in-6e4}catch(n){throw new Error(`文心一言认证失败: ${n instanceof Error?n.message:"未知错误"}`)}}convertToWenxinRequest(e){const t=e.messages.filter(e=>"system"!==e.role),s=e.messages.find(e=>"system"===e.role);return s&&t.length>0&&"user"===t[0]?.role&&(t[0].content=`${s.content}\n\n${t[0].content}`),{messages:t.map(e=>({role:e.role,content:e.content})),temperature:e.temperature||.1,top_p:.95,penalty_score:1,stream:!1}}async makeHttpRequest(e){const t=new AbortController,s=setTimeout(()=>t.abort(),this.config.timeout||3e4);try{const n=`${this.apiEndpoint}?access_token=${this.accessToken}`,r=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),signal:t.signal});if(clearTimeout(s),!r.ok){const e=await r.json().catch(()=>({}));throw new Error(`文心一言API错误 ${r.status}: ${e.error_msg||r.statusText}`)}const o=await r.json();if(o.error_code)throw new Error(`文心一言API错误 ${o.error_code}: ${o.error_msg}`);return o}catch(n){if(clearTimeout(s),n instanceof Error){if("AbortError"===n.name)throw new Error("文心一言API请求超时");throw n}throw new Error("文心一言API请求失败")}}convertToStandardResponse(e){return{id:e.id,model:this.modelConfig.id,choices:[{message:{role:"assistant",content:e.result},finish_reason:e.is_truncated?"length":"stop",index:0}],usage:{prompt_tokens:e.usage.prompt_tokens,completion_tokens:e.usage.completion_tokens,total_tokens:e.usage.total_tokens},created:e.created}}buildSystemPrompt(e,t){let s=super.buildSystemPrompt(e,t);return s+="\n\n特别要求：\n",s+="1. 请特别注意中文语法和表达习惯\n",s+="2. 对于专业术语，请保持准确性\n",s+="3. 注意中文标点符号的正确使用\n",s+="4. 返回的JSON必须是有效格式，不要包含注释\n",s}calculateCost(e){return((e.prompt_tokens||0)+(e.completion_tokens||0))/1e3*.008}handleError(e){let t=e.message;return t.includes("110")?t="文心一言访问令牌无效":t.includes("111")?t="文心一言访问令牌已过期":t.includes("18")?t="文心一言请求频率超限":t.includes("336003")?t="文心一言服务内部错误":t.includes("timeout")&&(t="文心一言API请求超时"),new Error(`${this.providerName}: ${t}`)}async checkAvailability(){try{await this.ensureAccessToken();const e={model:this.modelConfig.id,messages:[{role:"user",content:"你好"}],max_tokens:10};return await this.sendRequest(e),!0}catch(e){return!1}}getModelInfo(){return`${this.providerName} - ${this.modelConfig.name} (中文优化)`}optimizeChineseExpression(e){return e.replace(/的话$/gm,"").replace(/的话，/g,"，").replace(/(\S)\1{2,}/g,"$1$1").replace(/呢$/gm,"").replace(/吧$/gm,"").replace(/咋样/g,"怎样").replace(/咋办/g,"怎么办").replace(/啥/g,"什么")}async validateApiKey(){try{if(!this.config.apiKey)return{valid:!1,error:"缺少文心一言API Key"};const t=this.config.extraConfig?.secretKey;if(!t)return{valid:!1,error:"缺少文心一言Secret Key，请在extraConfig中配置secretKey"};try{return await this.refreshAccessToken(),{valid:!0,permissions:["chat","completion","chinese_optimization"],quota:{remaining:-1,total:-1,resetTime:this.tokenExpiry}}}catch(e){return{valid:!1,error:"API密钥验证失败，请检查API Key和Secret Key是否正确"}}}catch(e){return{valid:!1,error:e instanceof Error?e.message:"验证过程中发生错误"}}}handleErrorEnhanced(e){const t=super.handleErrorEnhanced(e),n=e.message.toLowerCase();return(n.includes("wenxin")||n.includes("baidu"))&&(n.includes("access_token")?(t.type=s.AUTH,t.message="文心一言访问令牌无效，请检查API Key和Secret Key",t.retryable=!1):n.includes("qps limit")?(t.type=s.QUOTA,t.message="文心一言QPS限制，请降低请求频率",t.retryable=!0):n.includes("daily limit")?(t.type=s.QUOTA,t.message="文心一言日调用量已达上限",t.retryable=!1):n.includes("content filter")&&(t.type=s.API,t.message="内容被文心一言安全过滤器拦截，请修改输入内容",t.retryable=!1)),t}getModelCapabilities(){return{maxTokens:this.modelConfig.maxTokens,supportedLanguages:["zh-CN"],specialFeatures:["中文语言模型","企业级安全","内容安全过滤","中文语义理解","多轮对话支持"],costPerToken:this.modelConfig.costPerToken,contextWindow:8e3}}getCostOptimizationSuggestions(e){const t=[];e.prompt_tokens>6e3&&t.push("输入文本接近文心一言上下文限制，建议分段处理"),e.completion_tokens>2e3&&t.push("输出内容较长，可以通过更精确的提示词控制输出长度");return(e.prompt_tokens+e.completion_tokens)*this.modelConfig.costPerToken>.05&&t.push("单次请求成本较高，建议优化提示词或考虑使用更经济的模型"),t.push("文心一言对中文处理效果最佳，建议优先用于中文文档审校"),t}}export{i as D,a as T,c as W,t as _,o as a};
