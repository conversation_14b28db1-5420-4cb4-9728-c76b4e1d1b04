/**
 * Vue Router 路由配置
 * 配置AI智能审校系统的12大业务模块路由
 */

import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'

// 布局组件
const MainLayout = () => import('@/layouts/MainLayout.vue')
// const BlankLayout = () => import('@/layouts/BlankLayout.vue') // 暂时未使用

/**
 * 路由配置
 * 采用懒加载方式提高应用性能
 */
const routes: RouteRecordRaw[] = [
  // 登录页面（无布局）
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      layout: 'blank',
    },
  },

  // 测试页面（无布局）
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/TestView.vue'),
    meta: {
      title: '系统测试',
      requiresAuth: false,
      layout: 'blank',
    },
  },

  // wangEditor测试页面（无布局）
  {
    path: '/editor-test',
    name: 'EditorTest',
    component: () => import('@/components/WangEditorTest.vue'),
    meta: {
      title: 'wangEditor测试',
      requiresAuth: false,
      layout: 'blank',
    },
  },

  // ProofreadResultEditor测试页面（无布局）
  {
    path: '/test-proofread-editor',
    name: 'TestProofreadEditor',
    component: () => import('@/views/TestProofreadEditor.vue'),
    meta: {
      title: 'ProofreadResultEditor测试',
      requiresAuth: false,
      layout: 'blank',
    },
  },

  // OriginalTextEditor对比测试页面（无布局）
  {
    path: '/test-original-editor',
    name: 'TestOriginalEditor',
    component: () => import('@/views/TestOriginalEditor.vue'),
    meta: {
      title: 'OriginalTextEditor对比测试',
      requiresAuth: false,
      layout: 'blank',
    },
  },

  // 下拉菜单修复验证页面（无布局）
  {
    path: '/test-dropdown-fix',
    name: 'TestDropdownFix',
    component: () => import('@/views/TestDropdownFix.vue'),
    meta: {
      title: '下拉菜单修复验证',
      requiresAuth: false,
      layout: 'blank',
    },
  },

  // PDF上传功能测试页面（无布局）
  {
    path: '/test-pdf-upload',
    name: 'TestPDFUpload',
    component: () => import('@/views/TestPDFUpload.vue'),
    meta: {
      title: 'PDF上传功能测试',
      requiresAuth: false,
      layout: 'blank',
    },
  },

  // 主应用路由（带主布局）
  {
    path: '/',
    component: MainLayout,
    redirect: '/dashboard',
    children: [
      // 1. 校对大屏
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@features/dashboard/views/DashboardView.vue'),
        meta: {
          title: '校对大屏',
          icon: 'Monitor',
          requiresAuth: true,
        },
      },

      // 2. 内容预审管理
      {
        path: '/content-review',
        name: 'ContentReview',
        redirect: '/content-review/unreviewed',
        meta: {
          title: '内容预审管理',
          icon: 'DocumentChecked',
          requiresAuth: true,
        },
        children: [
          {
            path: 'unreviewed',
            name: 'UnreviewedDocuments',
            component: () => import('@features/content-review/views/UnreviewedDocuments.vue'),
            meta: { title: '未预审文档' },
          },
          {
            path: 'reviewed',
            name: 'ReviewedDocuments',
            component: () => import('@features/content-review/views/ReviewedDocuments.vue'),
            meta: { title: '已预审文档' },
          },
          {
            path: 'ai-proofreading/:documentId',
            name: 'AIProofreadingPage',
            component: () => import('@features/content-review/views/AIProofreadingPage.vue'),
            meta: { title: 'AI智能校对' },
          },
        ],
      },

      // 3. AI批量审校
      {
        path: '/ai-batch-proofreading',
        name: 'AIBatchProofreading',
        redirect: '/ai-batch-proofreading/batch-processing',
        meta: {
          title: 'AI批量审校',
          icon: 'Cpu',
          requiresAuth: true,
        },
        children: [
          {
            path: 'batch-processing',
            name: 'BatchProofreadingView',
            component: () =>
              import('@features/ai-batch-proofreading/views/BatchProofreadingView.vue'),
            meta: {
              title: '批量审校',
              description: '大模型批量审校功能，支持多文件同时处理',
            },
          },
          {
            path: 'unproofread',
            name: 'UnproofreadDocuments',
            component: () =>
              import('@features/ai-batch-proofreading/views/UnproofreadDocuments.vue'),
            meta: { title: '未校对文档' },
          },
          {
            path: 'pending',
            name: 'PendingProofread',
            component: () => import('@features/ai-batch-proofreading/views/PendingProofread.vue'),
            meta: { title: '待审校文档' },
          },
          {
            path: 'completed',
            name: 'CompletedProofread',
            component: () => import('@features/ai-batch-proofreading/views/CompletedProofread.vue'),
            meta: { title: '完成校对文档' },
          },
        ],
      },

      // 4. 在线AI审校
      {
        path: '/online-proofreading',
        name: 'OnlineProofreading',
        redirect: '/online-proofreading/editor',
        meta: {
          title: '在线AI审校',
          icon: 'Edit',
          requiresAuth: true,
        },
        children: [
          {
            path: 'editor',
            name: 'OnlineEditor',
            component: () => import('@features/online-proofreading/views/OnlineEditor.vue'),
            meta: { title: '在线审校' },
          },
          {
            path: 'history',
            name: 'ProofreadHistory',
            component: () => import('@features/online-proofreading/views/ProofreadHistory.vue'),
            meta: { title: '已在线审校文档' },
          },
        ],
      },

      // 5. 多媒体审校
      {
        path: '/multimedia',
        name: 'Multimedia',
        redirect: '/multimedia/image/batch',
        meta: {
          title: '多媒体审校',
          icon: 'Picture',
          requiresAuth: true,
        },
        children: [
          // 图片审校子模块
          {
            path: 'image',
            name: 'ImageProofreading',
            redirect: '/multimedia/image/batch',
            meta: { title: '图片审校' },
            children: [
              {
                path: 'batch',
                name: 'ImageBatchProofreading',
                component: () =>
                  import('@features/image-proofreading/views/ImageBatchProofreadingView.vue'),
                meta: { title: '批量审校' },
              },
              {
                path: 'pending',
                name: 'ImagePendingProofreading',
                component: () =>
                  import('@features/image-proofreading/views/ImagePendingProofreadingView.vue'),
                meta: { title: '待审查图片' },
              },
              {
                path: 'online',
                name: 'ImageOnlineProofreading',
                component: () =>
                  import('@features/image-proofreading/views/ImageOnlineProofreadingView.vue'),
                meta: { title: '在线审校' },
              },
              {
                path: 'completed',
                name: 'ImageCompletedProofreading',
                component: () =>
                  import('@features/image-proofreading/views/ImageCompletedProofreadingView.vue'),
                meta: { title: '已审校图片' },
              },
            ],
          },
          // 视频审校子模块
          {
            path: 'video',
            name: 'VideoProofreading',
            redirect: '/multimedia/video/batch',
            meta: { title: '视频审校' },
            children: [
              {
                path: 'batch',
                name: 'VideoBatchProofreading',
                component: () =>
                  import('@features/video-proofreading/views/VideoBatchProofreadingView.vue'),
                meta: { title: '批量审校' },
              },
              {
                path: 'pending',
                name: 'VideoPendingProofreading',
                component: () =>
                  import('@features/video-proofreading/views/VideoPendingProofreadingView.vue'),
                meta: { title: '待审查视频' },
              },
              {
                path: 'online',
                name: 'VideoOnlineProofreading',
                component: () =>
                  import('@features/video-proofreading/views/VideoOnlineProofreadingView.vue'),
                meta: { title: '在线审校' },
              },
              {
                path: 'completed',
                name: 'VideoCompletedProofreading',
                component: () =>
                  import('@features/video-proofreading/views/VideoCompletedProofreadingView.vue'),
                meta: { title: '已审校视频' },
              },
            ],
          },
          // 音频审校子模块
          {
            path: 'audio',
            name: 'AudioProofreading',
            redirect: '/multimedia/audio/batch',
            meta: { title: '音频审校' },
            children: [
              {
                path: 'batch',
                name: 'AudioBatchProofreading',
                component: () =>
                  import('@features/audio-proofreading/views/AudioBatchProofreadingView.vue'),
                meta: { title: '批量审校' },
              },
              {
                path: 'pending',
                name: 'AudioPendingProofreading',
                component: () =>
                  import('@features/audio-proofreading/views/AudioPendingProofreadingView.vue'),
                meta: { title: '待审查音频' },
              },
              {
                path: 'online',
                name: 'AudioOnlineProofreading',
                component: () =>
                  import('@features/audio-proofreading/views/AudioOnlineProofreadingView.vue'),
                meta: { title: '在线审校' },
              },
              {
                path: 'completed',
                name: 'AudioCompletedProofreading',
                component: () =>
                  import('@features/audio-proofreading/views/AudioCompletedProofreadingView.vue'),
                meta: { title: '已审校音频' },
              },
            ],
          },
        ],
      },

      // 6. 专业排版
      {
        path: '/professional-typesetting',
        name: 'ProfessionalTypesetting',
        redirect: '/professional-typesetting/unformatted',
        meta: {
          title: '专业排版',
          icon: 'Document',
          requiresAuth: true,
        },
        children: [
          {
            path: 'unformatted',
            name: 'UnformattedDocuments',
            component: () =>
              import('@features/professional-typesetting/views/UnformattedDocuments.vue'),
            meta: { title: '未排版文档' },
          },
          {
            path: 'chinese-formatting',
            name: 'ChineseFormatting',
            component: () =>
              import('@features/professional-typesetting/views/ChineseFormatting.vue'),
            meta: { title: '中文排版文档' },
          },
          {
            path: 'formatted',
            name: 'FormattedDocuments',
            component: () =>
              import('@features/professional-typesetting/views/FormattedDocuments.vue'),
            meta: { title: '已排版文档' },
          },
        ],
      },

      // 7. 专业查询
      {
        path: '/professional-query',
        name: 'ProfessionalQuery',
        redirect: '/professional-query/terminology',
        meta: {
          title: '专业查询',
          icon: 'Search',
          requiresAuth: true,
        },
        children: [
          {
            path: 'terminology',
            name: 'TerminologyQuery',
            component: () => import('@features/professional-query/views/TerminologyQuery.vue'),
            meta: { title: '术语查询' },
          },
          {
            path: 'standards',
            name: 'StandardsQuery',
            component: () => import('@features/professional-query/views/StandardsQuery.vue'),
            meta: { title: '标准查询' },
          },
          {
            path: 'classical-literature',
            name: 'ClassicalLiteratureQuery',
            component: () =>
              import('@features/professional-query/views/ClassicalLiteratureQuery.vue'),
            meta: { title: '古诗文查询' },
          },
          {
            path: 'legal-regulations',
            name: 'LegalRegulationsQuery',
            component: () => import('@features/professional-query/views/LegalRegulationsQuery.vue'),
            meta: { title: '法律法规查询' },
          },
          {
            path: 'important-speeches',
            name: 'ImportantSpeechesQuery',
            component: () =>
              import('@features/professional-query/views/ImportantSpeechesQuery.vue'),
            meta: { title: '重要讲话查询' },
          },
          {
            path: 'official-reports',
            name: 'OfficialReportsQuery',
            component: () => import('@features/professional-query/views/OfficialReportsQuery.vue'),
            meta: { title: '官方报道查询' },
          },
          {
            path: 'policy',
            name: 'PolicyQuery',
            component: () => import('@features/professional-query/views/PolicyQuery.vue'),
            meta: { title: '政策查询' },
          },
          {
            path: 'dictionary',
            name: 'DictionaryQuery',
            component: () => import('@features/professional-query/views/DictionaryQuery.vue'),
            meta: { title: '词典查询' },
          },
          {
            path: 'other',
            name: 'OtherQuery',
            component: () => import('@features/professional-query/views/OtherQuery.vue'),
            meta: { title: '其他查询' },
          },
        ],
      },

      // 8. 编辑文档库
      {
        path: '/document-library',
        name: 'DocumentLibrary',
        redirect: '/document-library/new-proofreading-comments',
        meta: {
          title: '编辑文档库',
          icon: 'Folder',
          requiresAuth: true,
        },
        children: [
          {
            path: 'new-proofreading-comments',
            name: 'NewProofreadingComments',
            component: () => import('@features/document-library/views/NewProofreadingComments.vue'),
            meta: { title: '新建校对意见' },
          },
          {
            path: 'existing-proofreading-comments',
            name: 'ExistingProofreadingComments',
            component: () =>
              import('@features/document-library/views/ExistingProofreadingComments.vue'),
            meta: { title: '已校对意见' },
          },
          {
            path: 'new-review-comments',
            name: 'NewReviewComments',
            component: () => import('@features/document-library/views/NewReviewComments.vue'),
            meta: { title: '新建审查意见' },
          },
          {
            path: 'existing-review-comments',
            name: 'ExistingReviewComments',
            component: () => import('@features/document-library/views/ExistingReviewComments.vue'),
            meta: { title: '已审查意见' },
          },
          {
            path: 'other-documents',
            name: 'OtherDocuments',
            component: () => import('@features/document-library/views/OtherDocuments.vue'),
            meta: { title: '其他文档' },
          },
        ],
      },

      // 9. 我的修改积累
      {
        path: '/modification-accumulation',
        name: 'ModificationAccumulation',
        redirect: '/modification-accumulation/add-case-set',
        meta: {
          title: '我的修改积累',
          icon: 'Collection',
          requiresAuth: true,
        },
        children: [
          {
            path: 'add-case-set',
            name: 'AddCaseSet',
            component: () => import('@features/modification-accumulation/views/AddCaseSet.vue'),
            meta: { title: '添加案例集' },
          },
          {
            path: 'review-case-set',
            name: 'ReviewCaseSet',
            component: () => import('@features/modification-accumulation/views/ReviewCaseSet.vue'),
            meta: { title: '审查案例集' },
          },
          {
            path: 'sync-case-set',
            name: 'SyncCaseSet',
            component: () => import('@features/modification-accumulation/views/SyncCaseSet.vue'),
            meta: { title: '同步案例集' },
          },
          {
            path: 'query-proofreading-cases',
            name: 'QueryProofreadingCases',
            component: () =>
              import('@features/modification-accumulation/views/QueryProofreadingCases.vue'),
            meta: { title: '查询校对案例集' },
          },
          {
            path: 'query-review-cases',
            name: 'QueryReviewCases',
            component: () =>
              import('@features/modification-accumulation/views/QueryReviewCases.vue'),
            meta: { title: '查询审查案例集' },
          },
        ],
      },

      // 10. 个人中心
      {
        path: '/user-center',
        name: 'UserCenter',
        redirect: '/user-center/profile',
        meta: {
          title: '个人中心',
          icon: 'User',
          requiresAuth: true,
        },
        children: [
          {
            path: 'profile',
            name: 'UserProfile',
            component: () => import('@features/user-center/views/UserProfile.vue'),
            meta: { title: '个人资料' },
          },
          {
            path: 'settings',
            name: 'UserSettings',
            component: () => import('@features/user-center/views/UserSettings.vue'),
            meta: { title: '账户设置' },
          },
          {
            path: 'operation-logs',
            name: 'OperationLogs',
            component: () => import('@features/user-center/views/OperationLogs.vue'),
            meta: { title: '操作日志' },
          },
          {
            path: 'preferences',
            name: 'SystemPreferences',
            component: () => import('@features/user-center/views/SystemPreferences.vue'),
            meta: { title: '系统偏好' },
          },
        ],
      },
    ],
  },

  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFoundView.vue'),
    meta: {
      title: '页面未找到',
      requiresAuth: false,
    },
  },
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  // 路由切换时滚动到顶部
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - AI智能审校系统`
  }

  // 检查认证状态
  const requiresAuth = to.meta?.requiresAuth !== false
  const isAuthenticated = localStorage.getItem('access_token')

  if (requiresAuth && !isAuthenticated) {
    // 需要认证但未登录，跳转到登录页
    next('/login')
  } else if (to.path === '/login' && isAuthenticated) {
    // 已登录用户访问登录页，跳转到首页
    next('/dashboard')
  } else {
    next()
  }
})

export default router
