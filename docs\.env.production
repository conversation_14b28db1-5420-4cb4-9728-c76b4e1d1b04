# 生产环境配置
NODE_ENV=production

# API配置
VITE_API_BASE_URL=https://api.proofreading.com/api/v1
VITE_USE_MOCK_API=false

# 应用配置
VITE_APP_TITLE=AI智能审校系统
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=基于AI的智能文档审校平台

# 功能开关
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_MOCK=false
VITE_ENABLE_CONSOLE_LOG=false

# 文件上传配置
VITE_UPLOAD_MAX_SIZE=50
VITE_UPLOAD_ALLOWED_TYPES=.doc,.docx,.pdf,.txt,.jpg,.jpeg,.png,.mp4,.mp3,.wav

# AI服务配置
VITE_AI_SERVICE_TIMEOUT=30000
VITE_AI_MAX_TEXT_LENGTH=100000
