<template>
  <div class="pdf-preview">
    <!-- PDF工具栏 -->
    <div class="pdf-toolbar">
      <div class="toolbar-left">
        <el-button-group size="small">
          <el-button @click="zoomOut" :disabled="currentZoom <= minZoom">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom"> {{ Math.round(currentZoom * 100) }}% </el-button>
          <el-button @click="zoomIn" :disabled="currentZoom >= maxZoom">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group size="small">
          <el-button @click="previousPage" :disabled="currentPage <= 1">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <el-input
            v-model.number="pageInput"
            size="small"
            style="width: 60px"
            @keyup.enter="goToPage"
            @blur="goToPage"
          />
          <span class="page-info">/ {{ totalPages }}</span>
          <el-button @click="nextPage" :disabled="currentPage >= totalPages">
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-right">
        <!-- 上传PDF按钮 -->
        <el-upload
          ref="uploadRef"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          accept=".pdf"
          action="#"
          :auto-upload="false"
          style="display: inline-block; margin-right: 8px"
        >
          <el-button size="small" type="primary">
            <el-icon><Upload /></el-icon>
            上传PDF
          </el-button>
        </el-upload>

        <el-button
          size="small"
          @click="toggleScrollSync"
          :type="scrollSync ? 'primary' : 'default'"
        >
          <el-icon><Connection /></el-icon>
          {{ scrollSync ? '同步开启' : '同步关闭' }}
        </el-button>

        <el-button size="small" @click="toggleFullscreen">
          <el-icon><FullScreen /></el-icon>
          全屏
        </el-button>
      </div>
    </div>

    <!-- PDF内容区域 -->
    <div
      ref="pdfContainer"
      class="pdf-container"
      :class="{ fullscreen: isFullscreen }"
      @scroll="handleScroll"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <el-loading-spinner />
        <p>正在加载PDF文档...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <el-icon class="error-icon"><Warning /></el-icon>
        <p>PDF加载失败</p>
        <p class="error-message">{{ error }}</p>
        <el-button @click="retryLoad">重新加载</el-button>
      </div>

      <!-- PDF页面渲染 -->
      <div v-else-if="pdfDocument" class="pdf-pages">
        <div
          v-for="pageNum in totalPages"
          :key="`page-${pageNum}`"
          :ref="(el) => setPageRef(el, pageNum)"
          class="pdf-page"
          :class="{ active: pageNum === currentPage }"
          @click="handlePageClick(pageNum, $event)"
        >
          <canvas :id="`pdf-page-${pageNum}`" class="pdf-canvas" />
          <div class="page-number">{{ pageNum }}</div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-icon class="empty-icon"><Document /></el-icon>
        <p>暂无PDF文档</p>
        <p class="empty-message">请上传PDF文档以开始预览</p>
      </div>
    </div>

    <!-- 缩略图侧边栏 -->
    <div
      v-if="showThumbnails && pdfDocument"
      class="thumbnails-sidebar"
      :class="{ show: thumbnailsVisible }"
    >
      <div class="thumbnails-header">
        <span>页面缩略图</span>
        <el-button size="small" text @click="toggleThumbnails">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      <div class="thumbnails-list">
        <div
          v-for="pageNum in totalPages"
          :key="`thumb-${pageNum}`"
          class="thumbnail-item"
          :class="{ active: pageNum === currentPage }"
          @click="goToPage(pageNum)"
        >
          <canvas :id="`pdf-thumb-${pageNum}`" class="thumbnail-canvas" />
          <span class="thumbnail-number">{{ pageNum }}</span>
        </div>
      </div>
    </div>

    <!-- 缩略图切换按钮 -->
    <el-button
      v-if="showThumbnails && pdfDocument"
      class="thumbnails-toggle"
      size="small"
      @click="toggleThumbnails"
    >
      <el-icon><Grid /></el-icon>
    </el-button>
  </div>
</template>

<script setup lang="ts">
/**
 * PDF预览组件
 *
 * 功能特性：
 * - PDF文档渲染和显示
 * - 缩放、翻页、跳转功能
 * - 滚动同步支持
 * - 缩略图导航
 * - 全屏预览
 * - 页面点击定位
 */

import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ZoomIn,
  ZoomOut,
  ArrowLeft,
  ArrowRight,
  Connection,
  FullScreen,
  Warning,
  Document,
  Close,
  Grid,
  Upload,
} from '@element-plus/icons-vue'

// 导入PDF.js
import * as pdfjsLib from 'pdfjs-dist'
import type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist'

// 配置PDF.js worker - 使用本地worker文件
pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.js',
  import.meta.url,
).toString()

// 组件属性
interface Props {
  /** PDF文件URL */
  pdfUrl?: string
  /** 当前页码 */
  currentPage?: number
  /** 是否启用滚动同步 */
  scrollSync?: boolean
  /** 默认缩放比例 */
  defaultZoom?: number
  /** 最小缩放比例 */
  minZoom?: number
  /** 最大缩放比例 */
  maxZoom?: number
  /** 是否显示缩略图 */
  showThumbnails?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  currentPage: 1,
  scrollSync: true,
  defaultZoom: 1.0,
  minZoom: 0.5,
  maxZoom: 3.0,
  showThumbnails: true,
})

// 组件事件
interface Emits {
  (e: 'page-change', page: number): void
  (e: 'position-change', position: number): void
  (e: 'zoom-change', zoom: number): void
  (e: 'click', page: number, position: { x: number; y: number }): void
  (e: 'pdf-upload', file: File): void
  (e: 'pdf-upload-success', url: string): void
  (e: 'pdf-upload-error', error: string): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const pdfContainer = ref<HTMLElement>()
const uploadRef = ref<InstanceType<(typeof import('element-plus'))['ElUpload']> | null>(null)
const pdfDocument = ref<PDFDocumentProxy | null>(null)
const loading = ref(false)
const error = ref('')
const currentZoom = ref(props.defaultZoom)
const pageInput = ref(props.currentPage)
const isFullscreen = ref(false)
const thumbnailsVisible = ref(false)
const pageRefs = ref<Map<number, HTMLElement>>(new Map())
const renderedPages = ref<Set<number>>(new Set())

// 计算属性
const totalPages = computed(() => {
  return pdfDocument.value?.numPages || 0
})

// 监听属性变化
watch(
  () => props.pdfUrl,
  (newUrl) => {
    if (newUrl) {
      loadPDF(newUrl)
    }
  },
  { immediate: true },
)

watch(
  () => props.currentPage,
  (newPage) => {
    pageInput.value = newPage
    scrollToPage(newPage)
    // 当页面变化时，渲染新的可见页面
    if (pdfDocument.value) {
      renderVisiblePages()
    }
  },
)

// 生命周期钩子
onMounted(() => {
  // 监听全屏变化
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})

/**
 * 加载PDF文档
 */
const loadPDF = async (url: string) => {
  if (!url) {
    pdfDocument.value = null
    return
  }

  try {
    loading.value = true
    error.value = ''
    renderedPages.value.clear()

    // 使用PDF.js加载PDF文档
    const loadingTask = pdfjsLib.getDocument(url)
    pdfDocument.value = await loadingTask.promise

    // 等待DOM更新后渲染页面
    await nextTick()
    await renderVisiblePages()

    ElMessage.success(`PDF加载成功，共${totalPages.value}页`)
  } catch (err) {
    console.error('PDF加载失败:', err)
    error.value = err instanceof Error ? err.message : '未知错误'
    ElMessage.error('PDF加载失败: ' + error.value)
    pdfDocument.value = null
  } finally {
    loading.value = false
  }
}

/**
 * 渲染可见页面（优化性能）
 */
const renderVisiblePages = async () => {
  if (!pdfDocument.value) return

  // 先渲染当前页面
  await renderPage(props.currentPage)

  // 然后渲染前后几页（预加载）
  const preloadRange = 2
  for (let i = -preloadRange; i <= preloadRange; i++) {
    const pageNum = props.currentPage + i
    if (pageNum >= 1 && pageNum <= totalPages.value && pageNum !== props.currentPage) {
      await renderPage(pageNum)
    }
  }

  // 渲染缩略图
  if (props.showThumbnails) {
    await renderAllThumbnails()
  }
}

/**
 * 渲染所有缩略图
 */
const renderAllThumbnails = async () => {
  if (!pdfDocument.value) return

  for (let pageNum = 1; pageNum <= totalPages.value; pageNum++) {
    await renderThumbnail(pageNum)
  }
}

/**
 * 渲染单个页面
 */
const renderPage = async (pageNum: number) => {
  if (!pdfDocument.value || renderedPages.value.has(pageNum)) return

  try {
    const canvas = document.getElementById(`pdf-page-${pageNum}`) as HTMLCanvasElement
    if (!canvas) return

    // 获取PDF页面
    const page: PDFPageProxy = await pdfDocument.value.getPage(pageNum)

    // 计算视口（考虑缩放）
    const viewport = page.getViewport({ scale: currentZoom.value })

    // 设置canvas尺寸
    const context = canvas.getContext('2d')
    if (!context) return

    canvas.height = viewport.height
    canvas.width = viewport.width

    // 清除之前的内容
    context.clearRect(0, 0, canvas.width, canvas.height)

    // 渲染PDF页面
    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    }

    await page.render(renderContext).promise
    renderedPages.value.add(pageNum)
  } catch (err) {
    console.error(`渲染页面 ${pageNum} 失败:`, err)

    // 如果渲染失败，显示错误信息
    const canvas = document.getElementById(`pdf-page-${pageNum}`) as HTMLCanvasElement
    if (canvas) {
      const context = canvas.getContext('2d')
      if (context) {
        canvas.width = 600
        canvas.height = 800
        context.fillStyle = '#f5f5f5'
        context.fillRect(0, 0, canvas.width, canvas.height)
        context.fillStyle = '#ff4757'
        context.font = '16px Arial'
        context.textAlign = 'center'
        context.fillText(`页面 ${pageNum} 加载失败`, canvas.width / 2, canvas.height / 2)
        context.fillText('请检查PDF文件', canvas.width / 2, canvas.height / 2 + 30)
      }
    }
  }
}

/**
 * 渲染缩略图
 */
const renderThumbnail = async (pageNum: number) => {
  if (!pdfDocument.value) return

  try {
    const canvas = document.getElementById(`pdf-thumb-${pageNum}`) as HTMLCanvasElement
    if (!canvas) return

    // 获取PDF页面
    const page: PDFPageProxy = await pdfDocument.value.getPage(pageNum)

    // 计算缩略图视口（固定宽度120px）
    const viewport = page.getViewport({ scale: 1.0 })
    const scale = 120 / viewport.width
    const scaledViewport = page.getViewport({ scale })

    // 设置canvas尺寸
    const context = canvas.getContext('2d')
    if (!context) return

    canvas.height = scaledViewport.height
    canvas.width = scaledViewport.width

    // 清除之前的内容
    context.clearRect(0, 0, canvas.width, canvas.height)

    // 渲染PDF页面缩略图
    const renderContext = {
      canvasContext: context,
      viewport: scaledViewport,
    }

    await page.render(renderContext).promise
  } catch (err) {
    console.error(`渲染缩略图 ${pageNum} 失败:`, err)

    // 如果渲染失败，显示占位符
    const canvas = document.getElementById(`pdf-thumb-${pageNum}`) as HTMLCanvasElement
    if (canvas) {
      const context = canvas.getContext('2d')
      if (context) {
        canvas.width = 120
        canvas.height = 160
        context.fillStyle = '#f0f0f0'
        context.fillRect(0, 0, canvas.width, canvas.height)
        context.fillStyle = '#999999'
        context.font = '12px Arial'
        context.textAlign = 'center'
        context.fillText(`${pageNum}`, canvas.width / 2, canvas.height / 2)
      }
    }
  }
}

/**
 * 设置页面引用
 */
const setPageRef = (el: HTMLElement | null, pageNum: number) => {
  if (el) {
    pageRefs.value.set(pageNum, el)
  }
}

/**
 * 缩放操作
 */
const zoomIn = () => {
  if (currentZoom.value < props.maxZoom) {
    currentZoom.value = Math.min(currentZoom.value + 0.25, props.maxZoom)
    emit('zoom-change', currentZoom.value)
    reRenderPages()
  }
}

const zoomOut = () => {
  if (currentZoom.value > props.minZoom) {
    currentZoom.value = Math.max(currentZoom.value - 0.25, props.minZoom)
    emit('zoom-change', currentZoom.value)
    reRenderPages()
  }
}

const resetZoom = () => {
  currentZoom.value = props.defaultZoom
  emit('zoom-change', currentZoom.value)
  reRenderPages()
}

/**
 * 重新渲染页面（缩放变化时）
 */
const reRenderPages = async () => {
  if (!pdfDocument.value) return

  // 清除已渲染页面标记，强制重新渲染
  renderedPages.value.clear()

  // 重新渲染可见页面
  await nextTick()
  await renderVisiblePages()
}

/**
 * 页面导航
 */
const previousPage = () => {
  if (props.currentPage > 1) {
    emit('page-change', props.currentPage - 1)
  }
}

const nextPage = () => {
  if (props.currentPage < totalPages.value) {
    emit('page-change', props.currentPage + 1)
  }
}

const goToPage = (page?: number) => {
  const targetPage = page || pageInput.value
  if (targetPage >= 1 && targetPage <= totalPages.value) {
    emit('page-change', targetPage)
  } else {
    pageInput.value = props.currentPage
  }
}

/**
 * 滚动到指定页面
 */
const scrollToPage = (pageNum: number) => {
  const pageElement = pageRefs.value.get(pageNum)
  if (pageElement && pdfContainer.value) {
    pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

/**
 * 处理滚动事件
 */
const handleScroll = () => {
  if (!props.scrollSync || !pdfContainer.value) return

  const container = pdfContainer.value
  const scrollTop = container.scrollTop
  const scrollHeight = container.scrollHeight - container.clientHeight
  const position = scrollHeight > 0 ? scrollTop / scrollHeight : 0

  emit('position-change', position)
}

/**
 * 处理页面点击
 */
const handlePageClick = (pageNum: number, event: MouseEvent) => {
  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const x = (event.clientX - rect.left) / rect.width
  const y = (event.clientY - rect.top) / rect.height

  emit('click', pageNum, { x, y })
}

/**
 * 处理PDF上传前的验证
 */
const handleBeforeUpload = (file: File) => {
  // 验证文件类型
  if (file.type !== 'application/pdf') {
    ElMessage.error('只能上传PDF格式的文件')
    return false
  }

  // 验证文件大小 (限制为50MB)
  const maxSize = 50 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过50MB')
    return false
  }

  // 触发上传事件
  emit('pdf-upload', file)

  // 创建本地URL用于预览
  const url = URL.createObjectURL(file)
  loadPDF(url)

  ElMessage.success('PDF文件上传成功')
  emit('pdf-upload-success', url)

  return false // 阻止自动上传
}

/**
 * 处理上传成功
 */
const handleUploadSuccess = (response: { url: string }) => {
  ElMessage.success('PDF上传成功')
  emit('pdf-upload-success', response.url)
}

/**
 * 处理上传错误
 */
const handleUploadError = (error: Error) => {
  ElMessage.error('PDF上传失败: ' + error.message)
  emit('pdf-upload-error', error.message)
}

/**
 * 切换滚动同步
 */
const toggleScrollSync = () => {
  // 这里应该通过emit通知父组件
  ElMessage.info(props.scrollSync ? '滚动同步已关闭' : '滚动同步已开启')
}

/**
 * 切换全屏
 */
const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    pdfContainer.value?.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

/**
 * 处理全屏变化
 */
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

/**
 * 切换缩略图
 */
const toggleThumbnails = () => {
  thumbnailsVisible.value = !thumbnailsVisible.value
}

/**
 * 重新加载
 */
const retryLoad = () => {
  if (props.pdfUrl) {
    loadPDF(props.pdfUrl)
  }
}
</script>

<style scoped>
.pdf-preview {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 上传按钮样式优化 */
.toolbar-right .el-upload {
  display: inline-block;
}

.toolbar-right .el-upload .el-button {
  margin: 0;
}

.page-info {
  font-size: 14px;
  color: #606266;
  margin: 0 4px;
}

.pdf-container {
  flex: 1;
  overflow: auto;
  position: relative;
  background: #e4e7ed;
}

.pdf-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #000;
}

.pdf-pages {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  gap: 20px;
}

.pdf-page {
  position: relative;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pdf-page:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.pdf-page.active {
  border: 2px solid #409eff;
}

.pdf-canvas {
  display: block;
  transform-origin: top left;
}

.page-number {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.error-icon,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-message,
.empty-message {
  font-size: 14px;
  margin-top: 8px;
}

.thumbnails-sidebar {
  position: absolute;
  top: 0;
  right: -200px;
  width: 200px;
  height: 100%;
  background: white;
  border-left: 1px solid #e4e7ed;
  transition: right 0.3s ease;
  z-index: 10;
}

.thumbnails-sidebar.show {
  right: 0;
}

.thumbnails-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
}

.thumbnails-list {
  padding: 12px;
  overflow-y: auto;
  height: calc(100% - 60px);
}

.thumbnail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.thumbnail-item:hover {
  background: #f5f7fa;
}

.thumbnail-item.active {
  background: #e6f7ff;
  border: 1px solid #409eff;
}

.thumbnail-canvas {
  border: 1px solid #e4e7ed;
  border-radius: 2px;
}

.thumbnail-number {
  font-size: 12px;
  margin-top: 4px;
  color: #606266;
}

.thumbnails-toggle {
  position: absolute;
  top: 60px;
  right: 8px;
  z-index: 11;
}
</style>
