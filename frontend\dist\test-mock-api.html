<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mock API 测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    .result {
      background: #f5f5f5;
      padding: 10px;
      margin: 10px 0;
      border-radius: 3px;
      white-space: pre-wrap;
    }

    .success {
      background: #d4edda;
      color: #155724;
    }

    .error {
      background: #f8d7da;
      color: #721c24;
    }

    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }

    button:hover {
      background: #0056b3;
    }
  </style>
</head>

<body>
  <h1>Mock API 测试页面</h1>

  <div class="test-section">
    <h2>环境检查</h2>
    <button onclick="checkEnvironment()">检查环境变量</button>
    <div id="env-result" class="result"></div>
  </div>

  <div class="test-section">
    <h2>Service Worker 检查</h2>
    <button onclick="checkServiceWorker()">检查 Service Worker</button>
    <div id="sw-result" class="result"></div>
  </div>

  <div class="test-section">
    <h2>Mock API 测试</h2>
    <button onclick="testMockApi()">测试未预审文档API</button>
    <div id="api-result" class="result"></div>
  </div>

  <div class="test-section">
    <h2>直接 Fetch 测试</h2>
    <button onclick="testDirectFetch()">直接 Fetch 请求</button>
    <div id="fetch-result" class="result"></div>
  </div>

  <script>
    function checkEnvironment () {
      const envResult = document.getElementById('env-result');
      const env = {
        DEV: import.meta?.env?.DEV,
        VITE_ENABLE_MOCK: import.meta?.env?.VITE_ENABLE_MOCK,
        VITE_ENABLE_CONSOLE_LOG: import.meta?.env?.VITE_ENABLE_CONSOLE_LOG,
        location: window.location.origin,
        userAgent: navigator.userAgent
      };
      envResult.textContent = JSON.stringify(env, null, 2);
      envResult.className = 'result success';
    }

    async function checkServiceWorker () {
      const swResult = document.getElementById('sw-result');
      try {
        if (!('serviceWorker' in navigator)) {
          throw new Error('浏览器不支持 Service Worker');
        }

        const registrations = await navigator.serviceWorker.getRegistrations();
        const swInfo = {
          supported: true,
          registrations: registrations.length,
          active: registrations.map(reg => ({
            scope: reg.scope,
            state: reg.active?.state,
            scriptURL: reg.active?.scriptURL
          }))
        };

        // 检查 mockServiceWorker.js 文件
        try {
          const swResponse = await fetch('/mockServiceWorker.js');
          swInfo.mockWorkerFile = {
            exists: swResponse.ok,
            status: swResponse.status,
            size: swResponse.headers.get('content-length')
          };
        } catch (e) {
          swInfo.mockWorkerFile = { error: e.message };
        }

        swResult.textContent = JSON.stringify(swInfo, null, 2);
        swResult.className = 'result success';
      } catch (error) {
        swResult.textContent = `错误: ${error.message}`;
        swResult.className = 'result error';
      }
    }

    async function testMockApi () {
      const apiResult = document.getElementById('api-result');
      try {
        console.log('🧪 开始测试 Mock API...');
        console.log('🧪 当前URL:', window.location.href);

        // 测试不同的URL格式
        const testUrls = [
          '/api/pre-review/pending?page=1&pageSize=10&status=pending',
          '/api/pre-review/pending?page=1&pageSize=10',
          '/api/pre-review/pending'
        ];

        const results = {};

        for (const url of testUrls) {
          console.log(`🧪 测试URL: ${url}`);

          try {
            const response = await fetch(url, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer dev-mock-token-12345'
              }
            });

            console.log(`🧪 ${url} 响应状态:`, response.status);

            const data = await response.json();
            console.log(`🧪 ${url} 响应数据:`, data);

            results[url] = {
              status: response.status,
              statusText: response.statusText,
              ok: response.ok,
              data: data
            };
          } catch (error) {
            console.error(`🧪 ${url} 测试失败:`, error);
            results[url] = { error: error.message };
          }
        }

        apiResult.textContent = JSON.stringify(results, null, 2);
        apiResult.className = 'result success';
      } catch (error) {
        console.error('🧪 API 测试失败:', error);
        apiResult.textContent = `错误: ${error.message}`;
        apiResult.className = 'result error';
      }
    }

    async function testDirectFetch () {
      const fetchResult = document.getElementById('fetch-result');
      try {
        console.log('🔗 开始直接 Fetch 测试...');

        // 测试多个端点
        const endpoints = [
          '/api/pre-review/pending',
          '/api/pre-review/statistics',
          '/api/auth/profile'
        ];

        const results = {};

        for (const endpoint of endpoints) {
          try {
            const response = await fetch(endpoint + '?test=1', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer dev-mock-token-12345'
              }
            });

            results[endpoint] = {
              status: response.status,
              statusText: response.statusText,
              ok: response.ok
            };

            if (response.ok) {
              const data = await response.json();
              results[endpoint].dataType = typeof data;
              results[endpoint].hasData = !!data;
            }
          } catch (e) {
            results[endpoint] = { error: e.message };
          }
        }

        fetchResult.textContent = JSON.stringify(results, null, 2);
        fetchResult.className = 'result success';
      } catch (error) {
        console.error('🔗 直接 Fetch 测试失败:', error);
        fetchResult.textContent = `错误: ${error.message}`;
        fetchResult.className = 'result error';
      }
    }

    // 页面加载时自动检查环境
    window.addEventListener('load', () => {
      console.log('🧪 Mock API 测试页面已加载');
      checkEnvironment();
    });
  </script>
</body>

</html>
