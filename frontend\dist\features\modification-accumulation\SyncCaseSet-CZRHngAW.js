import{d as s,c as a,a as e,Q as c,I as n,ag as t,o as l}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as d}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const o={class:"sync-case-set"},p={class:"content"},r=d(s({__name:"SyncCaseSet",setup:s=>(s,d)=>{const r=t("el-card");return l(),a("div",o,[d[1]||(d[1]=e("div",{class:"page-header"},[e("h1",null,"同步案例集"),e("p",{class:"page-description"},"同步和备份案例集数据")],-1)),e("div",p,[c(r,null,{default:n(()=>d[0]||(d[0]=[e("p",null,"同步案例集功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-17c6631d"]]);export{r as default};
