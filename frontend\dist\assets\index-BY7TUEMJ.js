const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["chunks/LoginView-J3tBBqaK.js","chunks/vue-vendor-BCsylZgc.js","chunks/ui-vendor-DZ6owSRu.js","chunks/utils-vendor-DYQz1-BF.js","chunks/_plugin-vue_export-helper-BCo6x5W8.js","assets/LoginView-DMNFpgq1.css","chunks/MainLayout-CNVL7J7k.js","assets/MainLayout-Ba74Jf4X.css","features/dashboard/DashboardView-DB2XhIGe.js","assets/DashboardView-DAnioPsE.css","features/content-review/UnreviewedDocuments-D6diQEQs.js","chunks/preReview-DUK05fM-.js","chunks/index-DU7Wk3Qr.js","chunks/useDocumentTable-DFS8VTHq.js","assets/UnreviewedDocuments-D_NrgN9F.css","features/content-review/ReviewedDocuments-CwESO2Vh.js","assets/ReviewedDocuments-CCaNypV3.css","features/content-review/AIProofreadingPage-CYOiczBF.js","chunks/ModuleAPI-p-N7PV56.js","chunks/ai-engine-storage-DTARYHrG.js","chunks/ai-engine-core-wyUSRaHZ.js","assets/AIProofreadingPage-CzkiCf_u.css","features/ai-batch-proofreading/BatchProofreadingView-D10QkGeD.js","assets/BatchProofreadingView-3tMEuqaP.css","features/ai-batch-proofreading/UnproofreadDocuments-_BYTAXhl.js","assets/UnproofreadDocuments-BAoBeRlk.css","features/ai-batch-proofreading/PendingProofread-f16B2ncW.js","assets/PendingProofread-CqkFlr_z.css","features/ai-batch-proofreading/CompletedProofread-B7a3FEgy.js","assets/CompletedProofread-Drxyv84R.css","features/online-proofreading/OnlineEditor-Bbon4Z3n.js","assets/OnlineEditor-D2azq5Fl.css","features/online-proofreading/ProofreadHistory-DXouy9dU.js","assets/ProofreadHistory-DjrqomQi.css","features/image-proofreading/ImageBatchProofreadingView-Bz4ip-ts.js","assets/ImageBatchProofreadingView-DQ2eI-tz.css","features/image-proofreading/ImagePendingProofreadingView-v6klFizq.js","assets/ImagePendingProofreadingView-DJpWByKf.css","features/image-proofreading/ImageOnlineProofreadingView-4iCCyEEg.js","assets/ImageOnlineProofreadingView-Z0JLubws.css","features/image-proofreading/ImageCompletedProofreadingView-BQkP7b5h.js","assets/ImageCompletedProofreadingView-CTuVRyEH.css","features/video-proofreading/VideoBatchProofreadingView-B9RfZjq3.js","assets/VideoBatchProofreadingView-BNjc8RTh.css","features/video-proofreading/VideoPendingProofreadingView-BijEaWmw.js","assets/VideoPendingProofreadingView-C9zqXU0C.css","features/video-proofreading/VideoOnlineProofreadingView-DMdtdNiY.js","assets/VideoOnlineProofreadingView-DArBu6wM.css","features/video-proofreading/VideoCompletedProofreadingView-D7mXinM1.js","assets/VideoCompletedProofreadingView-BJACg-Ed.css","features/audio-proofreading/AudioBatchProofreadingView-CeU4dxD6.js","assets/AudioBatchProofreadingView--y2d5l5c.css","features/audio-proofreading/AudioPendingProofreadingView-Df9y7W-I.js","assets/AudioPendingProofreadingView-EjaGtr07.css","features/audio-proofreading/AudioOnlineProofreadingView-QLvLGPjP.js","assets/AudioOnlineProofreadingView-CmFB8Wq-.css","features/audio-proofreading/AudioCompletedProofreadingView-D09s5WTj.js","assets/AudioCompletedProofreadingView-D3-N9XbE.css","features/professional-typesetting/UnformattedDocuments-BReEGCC4.js","assets/UnformattedDocuments-DJtiIwi-.css","features/professional-typesetting/ChineseFormatting-DDm5ekmN.js","assets/ChineseFormatting-IAbDQJOV.css","features/professional-typesetting/FormattedDocuments-DN1SK7Cu.js","assets/FormattedDocuments-DhrDTRfj.css","features/professional-query/TerminologyQuery-kj8H_Ewl.js","assets/TerminologyQuery-0m7b4fSM.css","features/professional-query/StandardsQuery-CvGAaUbf.js","assets/StandardsQuery-DNFQKDew.css","features/professional-query/ClassicalLiteratureQuery-Ck77ME3y.js","assets/ClassicalLiteratureQuery-Dm6gUFll.css","features/professional-query/LegalRegulationsQuery-DjiU1_Vi.js","assets/LegalRegulationsQuery-D5Rvtgr8.css","features/professional-query/ImportantSpeechesQuery-DG8FSCFF.js","assets/ImportantSpeechesQuery-6ltGZWzP.css","features/professional-query/OfficialReportsQuery-DU2YEYZ2.js","assets/OfficialReportsQuery-CGQyWgJm.css","features/professional-query/PolicyQuery-DF3Ltwwj.js","assets/PolicyQuery-BmxunS9a.css","features/professional-query/DictionaryQuery-CztCQCnh.js","assets/DictionaryQuery-DggRn7aZ.css","features/professional-query/OtherQuery-vCiOfs9G.js","assets/OtherQuery-B14BF0tD.css","features/document-library/NewProofreadingComments-cU3P4b5R.js","assets/NewProofreadingComments-BUSFLmjR.css","features/document-library/ExistingProofreadingComments-Cg24VGwK.js","assets/ExistingProofreadingComments-DYi0uKRv.css","features/document-library/NewReviewComments-Buwb9Ztb.js","assets/NewReviewComments-BO7DDY-I.css","features/document-library/ExistingReviewComments-ku1R1Qlz.js","assets/ExistingReviewComments-tNLSuJV1.css","features/document-library/OtherDocuments-DoY7iPDB.js","assets/OtherDocuments-DDyGreg5.css","features/modification-accumulation/AddCaseSet-176V3Jvh.js","assets/AddCaseSet-DKQlgiXg.css","features/modification-accumulation/ReviewCaseSet-DnXWNVVs.js","assets/ReviewCaseSet-CjS6M6gS.css","features/modification-accumulation/SyncCaseSet-CZRHngAW.js","assets/SyncCaseSet-9krNMZPj.css","features/modification-accumulation/QueryProofreadingCases-BHztVAYJ.js","assets/QueryProofreadingCases-DuEuBA_0.css","features/modification-accumulation/QueryReviewCases-B3zugyPA.js","assets/QueryReviewCases-DgdYb7du.css","features/user-center/UserProfile-X6ji4jrs.js","assets/UserProfile-xuIyC2oL.css","features/user-center/UserSettings-C4V6xp5s.js","assets/UserSettings-BnZhacgJ.css","features/user-center/OperationLogs-CfyDyYZf.js","assets/OperationLogs-15382iNB.css","features/user-center/SystemPreferences-DmUXyciJ.js","assets/SystemPreferences-CasALwoo.css","chunks/NotFoundView-B59dj_ab.js","assets/NotFoundView-CLmIWeAK.css"])))=>i.map(i=>d[i]);
import{d as e,m as t,c as o,Q as r,u as a,aw as n,o as i,ax as s,ay as m,at as c,az as l}from"../chunks/vue-vendor-BCsylZgc.js";import{i as p,E as d}from"../chunks/ui-vendor-DZ6owSRu.js";import{_ as u}from"../chunks/ai-engine-core-wyUSRaHZ.js";import"../chunks/utils-vendor-DYQz1-BF.js";function _(e,t){var o;return e="object"==typeof(o=e)&&null!==o?e:Object.create(null),new Proxy(e,{get:(e,o,r)=>"key"===o?Reflect.get(e,o,r):Reflect.get(e,o,r)||Reflect.get(t,o,r)})}function f(e,{storage:t,serializer:o,key:r,debug:a}){try{const a=null==t?void 0:t.getItem(r);a&&e.$patch(null==o?void 0:o.deserialize(a))}catch(n){}}function h(e,{storage:t,serializer:o,key:r,paths:a,debug:n}){try{const n=Array.isArray(a)?function(e,t){return t.reduce((t,o)=>{const r=o.split(".");return function(e,t,o){return t.slice(0,-1).reduce((e,t)=>/^(__proto__)$/.test(t)?{}:e[t]=e[t]||{},e)[t[t.length-1]]=o,e}(t,r,function(e,t){return t.reduce((e,t)=>null==e?void 0:e[t],e)}(e,r))},{})}(e,a):e;t.setItem(r,o.serialize(n))}catch(i){}}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var g=function(e={}){return t=>{const{auto:o=!1}=e,{options:{persist:r=o},store:a,pinia:n}=t;if(!r)return;if(!(a.$id in n.state.value)){const e=n._s.get(a.$id.replace("__hot:",""));return void(e&&Promise.resolve().then(()=>e.$persist()))}const i=(Array.isArray(r)?r.map(t=>_(t,e)):[_(r,e)]).map(function(e,t){return o=>{var r;try{const{storage:a=localStorage,beforeRestore:n,afterRestore:i,serializer:s={serialize:JSON.stringify,deserialize:JSON.parse},key:m=t.$id,paths:c=null,debug:l=!1}=o;return{storage:a,beforeRestore:n,afterRestore:i,serializer:s,key:(null!=(r=e.key)?r:e=>e)("string"==typeof m?m:m(t.$id)),paths:c,debug:l}}catch(a){return o.debug,null}}}(e,a)).filter(Boolean);a.$persist=()=>{i.forEach(e=>{h(a.$state,e)})},a.$hydrate=({runHooks:e=!0}={})=>{i.forEach(o=>{const{beforeRestore:r,afterRestore:n}=o;e&&(null==r||r(t)),f(a,o),e&&(null==n||n(t))})},i.forEach(e=>{const{beforeRestore:o,afterRestore:r}=e;null==o||o(t),f(a,e),null==r||r(t),a.$subscribe((t,o)=>{h(o,e)},{detached:!0})})}}(),P={name:"zh-cn",el:{breadcrumb:{label:"面包屑"},colorpicker:{confirm:"确定",clear:"清空",defaultLabel:"颜色选择器",description:"当前颜色 {color}，按 Enter 键选择新颜色",alphaLabel:"选择透明度的值"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",dateTablePrompt:"使用方向键与 Enter 键可选择日期",monthTablePrompt:"使用方向键与 Enter 键可选择月份",yearTablePrompt:"使用方向键与 Enter 键可选择年份",selectedDate:"已选日期",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},weeksFull:{sun:"星期日",mon:"星期一",tue:"星期二",wed:"星期三",thu:"星期四",fri:"星期五",sat:"星期六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},inputNumber:{decrease:"减少数值",increase:"增加数值"},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},dropdown:{toggleDropdown:"切换下拉选项"},mention:{loading:"加载中"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",page:"页",prev:"上一页",next:"下一页",currentPage:"第 {pager} 页",prevPages:"向前 {pager} 页",nextPages:"向后 {pager} 页",deprecationWarning:"你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"},dialog:{close:"关闭此对话框"},drawer:{close:"关闭此对话框"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!",close:"关闭此对话框"},upload:{deleteTip:"按 Delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},slider:{defaultLabel:"滑块介于 {min} 至 {max}",defaultRangeStartLabel:"选择起始值",defaultRangeEndLabel:"选择结束值"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tour:{next:"下一步",previous:"上一步",finish:"结束导览"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"},carousel:{leftArrow:"上一张幻灯片",rightArrow:"下一张幻灯片",indicator:"幻灯片切换至索引 {index}"}}};const E={id:"app",class:"app-container"},y=e({__name:"App",setup(e){t(()=>{document.title="AI智能审校系统",s()});const s=()=>{const e=localStorage.getItem("theme")||"light";document.documentElement.setAttribute("data-theme",e),"dark"===e&&document.documentElement.classList.add("dark")};return(e,t)=>(i(),o("div",E,[r(a(n))]))}}),A=[{path:"/login",name:"Login",component:()=>u(()=>import("../chunks/LoginView-J3tBBqaK.js"),__vite__mapDeps([0,1,2,3,4,5])),meta:{title:"用户登录",requiresAuth:!1,layout:"blank"}},{path:"/",component:()=>u(()=>import("../chunks/MainLayout-CNVL7J7k.js"),__vite__mapDeps([6,1,2,3,4,7])),redirect:"/dashboard",children:[{path:"/dashboard",name:"Dashboard",component:()=>u(()=>import("../features/dashboard/DashboardView-DB2XhIGe.js"),__vite__mapDeps([8,2,1,3,4,9])),meta:{title:"校对大屏",icon:"Monitor",requiresAuth:!0}},{path:"/content-review",name:"ContentReview",redirect:"/content-review/unreviewed",meta:{title:"内容预审管理",icon:"DocumentChecked",requiresAuth:!0},children:[{path:"unreviewed",name:"UnreviewedDocuments",component:()=>u(()=>import("../features/content-review/UnreviewedDocuments-D6diQEQs.js"),__vite__mapDeps([10,1,2,3,11,12,13,4,14])),meta:{title:"未预审文档"}},{path:"reviewed",name:"ReviewedDocuments",component:()=>u(()=>import("../features/content-review/ReviewedDocuments-CwESO2Vh.js"),__vite__mapDeps([15,1,2,3,11,12,13,4,16])),meta:{title:"已预审文档"}},{path:"ai-proofreading/:documentId",name:"AIProofreadingPage",component:()=>u(()=>import("../features/content-review/AIProofreadingPage-CYOiczBF.js"),__vite__mapDeps([17,1,2,3,4,11,12,18,19,20,21])),meta:{title:"AI智能校对"}}]},{path:"/ai-batch-proofreading",name:"AIBatchProofreading",redirect:"/ai-batch-proofreading/batch-processing",meta:{title:"AI批量审校",icon:"Cpu",requiresAuth:!0},children:[{path:"batch-processing",name:"BatchProofreadingView",component:()=>u(()=>import("../features/ai-batch-proofreading/BatchProofreadingView-D10QkGeD.js"),__vite__mapDeps([22,1,3,2,4,18,19,20,23])),meta:{title:"批量审校",description:"大模型批量审校功能，支持多文件同时处理"}},{path:"unproofread",name:"UnproofreadDocuments",component:()=>u(()=>import("../features/ai-batch-proofreading/UnproofreadDocuments-_BYTAXhl.js"),__vite__mapDeps([24,1,2,3,12,18,19,20,13,4,25])),meta:{title:"未校对文档"}},{path:"pending",name:"PendingProofread",component:()=>u(()=>import("../features/ai-batch-proofreading/PendingProofread-f16B2ncW.js"),__vite__mapDeps([26,1,4,27])),meta:{title:"待审校文档"}},{path:"completed",name:"CompletedProofread",component:()=>u(()=>import("../features/ai-batch-proofreading/CompletedProofread-B7a3FEgy.js"),__vite__mapDeps([28,1,4,29])),meta:{title:"完成校对文档"}}]},{path:"/online-proofreading",name:"OnlineProofreading",redirect:"/online-proofreading/editor",meta:{title:"在线AI审校",icon:"Edit",requiresAuth:!0},children:[{path:"editor",name:"OnlineEditor",component:()=>u(()=>import("../features/online-proofreading/OnlineEditor-Bbon4Z3n.js"),__vite__mapDeps([30,1,4,31])),meta:{title:"在线审校"}},{path:"history",name:"ProofreadHistory",component:()=>u(()=>import("../features/online-proofreading/ProofreadHistory-DXouy9dU.js"),__vite__mapDeps([32,1,4,33])),meta:{title:"已在线审校文档"}}]},{path:"/multimedia",name:"Multimedia",redirect:"/multimedia/image/batch",meta:{title:"多媒体审校",icon:"Picture",requiresAuth:!0},children:[{path:"image",name:"ImageProofreading",redirect:"/multimedia/image/batch",meta:{title:"图片审校"},children:[{path:"batch",name:"ImageBatchProofreading",component:()=>u(()=>import("../features/image-proofreading/ImageBatchProofreadingView-Bz4ip-ts.js"),__vite__mapDeps([34,2,1,3,4,35])),meta:{title:"批量审校"}},{path:"pending",name:"ImagePendingProofreading",component:()=>u(()=>import("../features/image-proofreading/ImagePendingProofreadingView-v6klFizq.js"),__vite__mapDeps([36,2,1,3,4,37])),meta:{title:"待审查图片"}},{path:"online",name:"ImageOnlineProofreading",component:()=>u(()=>import("../features/image-proofreading/ImageOnlineProofreadingView-4iCCyEEg.js"),__vite__mapDeps([38,2,1,3,4,39])),meta:{title:"在线审校"}},{path:"completed",name:"ImageCompletedProofreading",component:()=>u(()=>import("../features/image-proofreading/ImageCompletedProofreadingView-BQkP7b5h.js"),__vite__mapDeps([40,2,1,3,4,41])),meta:{title:"已审校图片"}}]},{path:"video",name:"VideoProofreading",redirect:"/multimedia/video/batch",meta:{title:"视频审校"},children:[{path:"batch",name:"VideoBatchProofreading",component:()=>u(()=>import("../features/video-proofreading/VideoBatchProofreadingView-B9RfZjq3.js"),__vite__mapDeps([42,2,1,3,4,43])),meta:{title:"批量审校"}},{path:"pending",name:"VideoPendingProofreading",component:()=>u(()=>import("../features/video-proofreading/VideoPendingProofreadingView-BijEaWmw.js"),__vite__mapDeps([44,2,1,3,4,45])),meta:{title:"待审查视频"}},{path:"online",name:"VideoOnlineProofreading",component:()=>u(()=>import("../features/video-proofreading/VideoOnlineProofreadingView-DMdtdNiY.js"),__vite__mapDeps([46,2,1,3,4,47])),meta:{title:"在线审校"}},{path:"completed",name:"VideoCompletedProofreading",component:()=>u(()=>import("../features/video-proofreading/VideoCompletedProofreadingView-D7mXinM1.js"),__vite__mapDeps([48,2,1,3,4,49])),meta:{title:"已审校视频"}}]},{path:"audio",name:"AudioProofreading",redirect:"/multimedia/audio/batch",meta:{title:"音频审校"},children:[{path:"batch",name:"AudioBatchProofreading",component:()=>u(()=>import("../features/audio-proofreading/AudioBatchProofreadingView-CeU4dxD6.js"),__vite__mapDeps([50,2,1,3,4,51])),meta:{title:"批量审校"}},{path:"pending",name:"AudioPendingProofreading",component:()=>u(()=>import("../features/audio-proofreading/AudioPendingProofreadingView-Df9y7W-I.js"),__vite__mapDeps([52,2,1,3,4,53])),meta:{title:"待审查音频"}},{path:"online",name:"AudioOnlineProofreading",component:()=>u(()=>import("../features/audio-proofreading/AudioOnlineProofreadingView-QLvLGPjP.js"),__vite__mapDeps([54,2,1,3,4,55])),meta:{title:"在线审校"}},{path:"completed",name:"AudioCompletedProofreading",component:()=>u(()=>import("../features/audio-proofreading/AudioCompletedProofreadingView-D09s5WTj.js"),__vite__mapDeps([56,2,1,3,4,57])),meta:{title:"已审校音频"}}]}]},{path:"/professional-typesetting",name:"ProfessionalTypesetting",redirect:"/professional-typesetting/unformatted",meta:{title:"专业排版",icon:"Document",requiresAuth:!0},children:[{path:"unformatted",name:"UnformattedDocuments",component:()=>u(()=>import("../features/professional-typesetting/UnformattedDocuments-BReEGCC4.js"),__vite__mapDeps([58,2,1,3,4,59])),meta:{title:"未排版文档"}},{path:"chinese-formatting",name:"ChineseFormatting",component:()=>u(()=>import("../features/professional-typesetting/ChineseFormatting-DDm5ekmN.js"),__vite__mapDeps([60,1,4,61])),meta:{title:"中文排版文档"}},{path:"formatted",name:"FormattedDocuments",component:()=>u(()=>import("../features/professional-typesetting/FormattedDocuments-DN1SK7Cu.js"),__vite__mapDeps([62,1,4,63])),meta:{title:"已排版文档"}}]},{path:"/professional-query",name:"ProfessionalQuery",redirect:"/professional-query/terminology",meta:{title:"专业查询",icon:"Search",requiresAuth:!0},children:[{path:"terminology",name:"TerminologyQuery",component:()=>u(()=>import("../features/professional-query/TerminologyQuery-kj8H_Ewl.js"),__vite__mapDeps([64,2,1,3,4,65])),meta:{title:"术语查询"}},{path:"standards",name:"StandardsQuery",component:()=>u(()=>import("../features/professional-query/StandardsQuery-CvGAaUbf.js"),__vite__mapDeps([66,1,4,67])),meta:{title:"标准查询"}},{path:"classical-literature",name:"ClassicalLiteratureQuery",component:()=>u(()=>import("../features/professional-query/ClassicalLiteratureQuery-Ck77ME3y.js"),__vite__mapDeps([68,1,4,69])),meta:{title:"古诗文查询"}},{path:"legal-regulations",name:"LegalRegulationsQuery",component:()=>u(()=>import("../features/professional-query/LegalRegulationsQuery-DjiU1_Vi.js"),__vite__mapDeps([70,1,4,71])),meta:{title:"法律法规查询"}},{path:"important-speeches",name:"ImportantSpeechesQuery",component:()=>u(()=>import("../features/professional-query/ImportantSpeechesQuery-DG8FSCFF.js"),__vite__mapDeps([72,1,4,73])),meta:{title:"重要讲话查询"}},{path:"official-reports",name:"OfficialReportsQuery",component:()=>u(()=>import("../features/professional-query/OfficialReportsQuery-DU2YEYZ2.js"),__vite__mapDeps([74,1,4,75])),meta:{title:"官方报道查询"}},{path:"policy",name:"PolicyQuery",component:()=>u(()=>import("../features/professional-query/PolicyQuery-DF3Ltwwj.js"),__vite__mapDeps([76,1,4,77])),meta:{title:"政策查询"}},{path:"dictionary",name:"DictionaryQuery",component:()=>u(()=>import("../features/professional-query/DictionaryQuery-CztCQCnh.js"),__vite__mapDeps([78,1,4,79])),meta:{title:"词典查询"}},{path:"other",name:"OtherQuery",component:()=>u(()=>import("../features/professional-query/OtherQuery-vCiOfs9G.js"),__vite__mapDeps([80,1,4,81])),meta:{title:"其他查询"}}]},{path:"/document-library",name:"DocumentLibrary",redirect:"/document-library/new-proofreading-comments",meta:{title:"编辑文档库",icon:"Folder",requiresAuth:!0},children:[{path:"new-proofreading-comments",name:"NewProofreadingComments",component:()=>u(()=>import("../features/document-library/NewProofreadingComments-cU3P4b5R.js"),__vite__mapDeps([82,1,4,83])),meta:{title:"新建校对意见"}},{path:"existing-proofreading-comments",name:"ExistingProofreadingComments",component:()=>u(()=>import("../features/document-library/ExistingProofreadingComments-Cg24VGwK.js"),__vite__mapDeps([84,1,4,85])),meta:{title:"已校对意见"}},{path:"new-review-comments",name:"NewReviewComments",component:()=>u(()=>import("../features/document-library/NewReviewComments-Buwb9Ztb.js"),__vite__mapDeps([86,1,4,87])),meta:{title:"新建审查意见"}},{path:"existing-review-comments",name:"ExistingReviewComments",component:()=>u(()=>import("../features/document-library/ExistingReviewComments-ku1R1Qlz.js"),__vite__mapDeps([88,1,4,89])),meta:{title:"已审查意见"}},{path:"other-documents",name:"OtherDocuments",component:()=>u(()=>import("../features/document-library/OtherDocuments-DoY7iPDB.js"),__vite__mapDeps([90,1,4,91])),meta:{title:"其他文档"}}]},{path:"/modification-accumulation",name:"ModificationAccumulation",redirect:"/modification-accumulation/add-case-set",meta:{title:"我的修改积累",icon:"Collection",requiresAuth:!0},children:[{path:"add-case-set",name:"AddCaseSet",component:()=>u(()=>import("../features/modification-accumulation/AddCaseSet-176V3Jvh.js"),__vite__mapDeps([92,1,4,93])),meta:{title:"添加案例集"}},{path:"review-case-set",name:"ReviewCaseSet",component:()=>u(()=>import("../features/modification-accumulation/ReviewCaseSet-DnXWNVVs.js"),__vite__mapDeps([94,1,4,95])),meta:{title:"审查案例集"}},{path:"sync-case-set",name:"SyncCaseSet",component:()=>u(()=>import("../features/modification-accumulation/SyncCaseSet-CZRHngAW.js"),__vite__mapDeps([96,1,4,97])),meta:{title:"同步案例集"}},{path:"query-proofreading-cases",name:"QueryProofreadingCases",component:()=>u(()=>import("../features/modification-accumulation/QueryProofreadingCases-BHztVAYJ.js"),__vite__mapDeps([98,1,4,99])),meta:{title:"查询校对案例集"}},{path:"query-review-cases",name:"QueryReviewCases",component:()=>u(()=>import("../features/modification-accumulation/QueryReviewCases-B3zugyPA.js"),__vite__mapDeps([100,1,4,101])),meta:{title:"查询审查案例集"}}]},{path:"/user-center",name:"UserCenter",redirect:"/user-center/profile",meta:{title:"个人中心",icon:"User",requiresAuth:!0},children:[{path:"profile",name:"UserProfile",component:()=>u(()=>import("../features/user-center/UserProfile-X6ji4jrs.js"),__vite__mapDeps([102,1,4,103])),meta:{title:"个人资料"}},{path:"settings",name:"UserSettings",component:()=>u(()=>import("../features/user-center/UserSettings-C4V6xp5s.js"),__vite__mapDeps([104,1,4,105])),meta:{title:"账户设置"}},{path:"operation-logs",name:"OperationLogs",component:()=>u(()=>import("../features/user-center/OperationLogs-CfyDyYZf.js"),__vite__mapDeps([106,1,4,107])),meta:{title:"操作日志"}},{path:"preferences",name:"SystemPreferences",component:()=>u(()=>import("../features/user-center/SystemPreferences-DmUXyciJ.js"),__vite__mapDeps([108,1,4,109])),meta:{title:"系统偏好"}}]}]},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>u(()=>import("../chunks/NotFoundView-B59dj_ab.js"),__vite__mapDeps([110,1,2,3,4,111])),meta:{title:"页面未找到",requiresAuth:!1}}],D=s({history:m("/"),routes:A,scrollBehavior:(e,t,o)=>o||{top:0}});D.beforeEach((e,t,o)=>{e.meta?.title&&(document.title=`${e.meta.title} - AI智能审校系统`);const r=!1!==e.meta?.requiresAuth,a=localStorage.getItem("access_token");r&&!a?o("/login"):"/login"===e.path&&a?o("/dashboard"):o()}),(()=>{const e=console.warn,t=console.error,o=console.log,r=["InstallTrigger","已弃用","deprecated","will be removed","commons.js","Documentation:","Found an issue?","Worker script URL:","Worker scope:","Client ID:","🍍"],a=e=>r.some(t=>e.includes(t));console.warn=(...t)=>{const o=t.join(" ");a(o)||e.apply(console,t)},console.error=(...e)=>{const o=e.join(" ");a(o)||t.apply(console,e)},console.log=(...e)=>{const t=e.join(" ");a(t)||o.apply(console,e)}})(),async function(){const e=c(y),t=l();t.use(g),e.use(t),e.use(D),e.use(p,{locale:P,size:"default"});for(const[o,r]of Object.entries(d))e.component(o,r);e.config.errorHandler=(e,t,o)=>{},e.config.warnHandler=(e,t,o)=>{},e.mount("#app")}();
