import{d as s,r as a,m as e,c as l,Q as t,I as d,ag as n,o as r,a as o,M as c}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as i}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const u={class:"online-editor"},p={class:"card-header"},_={class:"header-actions"},m={class:"content"},v=i(s({__name:"OnlineEditor",setup:s=>(a(!1),e(()=>{}),(s,a)=>{const e=n("el-button"),i=n("el-empty"),v=n("el-card");return r(),l("div",u,[t(v,null,{header:d(()=>[o("div",p,[a[2]||(a[2]=o("span",null,"在线编辑器",-1)),o("div",_,[t(e,{type:"primary",size:"small"},{default:d(()=>a[0]||(a[0]=[c(" 保存 ")])),_:1,__:[0]}),t(e,{type:"success",size:"small"},{default:d(()=>a[1]||(a[1]=[c(" 校对 ")])),_:1,__:[1]})])])]),default:d(()=>[o("div",m,[a[3]||(a[3]=o("p",null,"在线编辑器页面 - 开发中...",-1)),t(i,{description:"编辑器组件待集成"})])]),_:1})])})}),[["__scopeId","data-v-5a84d97d"]]);export{v as default};
