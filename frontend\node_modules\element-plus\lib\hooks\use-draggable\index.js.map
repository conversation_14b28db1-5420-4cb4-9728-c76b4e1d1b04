{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-draggable/index.ts"], "sourcesContent": ["import { onBeforeUnmount, onMounted, watchEffect } from 'vue'\nimport { addUnit } from '@element-plus/utils'\nimport type { ComputedRef, Ref } from 'vue'\n\nexport const useDraggable = (\n  targetRef: Ref<HTMLElement | undefined>,\n  dragRef: Ref<HTMLElement | undefined>,\n  draggable: ComputedRef<boolean>,\n  overflow?: ComputedRef<boolean>\n) => {\n  const transform = {\n    offsetX: 0,\n    offsetY: 0,\n  }\n\n  const adjustPosition = (moveX: number, moveY: number) => {\n    if (targetRef.value) {\n      const { offsetX, offsetY } = transform\n      const targetRect = targetRef.value.getBoundingClientRect()\n      const targetLeft = targetRect.left\n      const targetTop = targetRect.top\n      const targetWidth = targetRect.width\n      const targetHeight = targetRect.height\n\n      const clientWidth = document.documentElement.clientWidth\n      const clientHeight = document.documentElement.clientHeight\n\n      const minLeft = -targetLeft + offsetX\n      const minTop = -targetTop + offsetY\n      const maxLeft = clientWidth - targetLeft - targetWidth + offsetX\n      const maxTop =\n        clientHeight -\n        targetTop -\n        (targetHeight < clientHeight ? targetHeight : 0) +\n        offsetY\n\n      if (!overflow?.value) {\n        moveX = Math.min(Math.max(moveX, minLeft), maxLeft)\n        moveY = Math.min(Math.max(moveY, minTop), maxTop)\n      }\n\n      transform.offsetX = moveX\n      transform.offsetY = moveY\n\n      targetRef.value.style.transform = `translate(${addUnit(moveX)}, ${addUnit(\n        moveY\n      )})`\n    }\n  }\n\n  const onMousedown = (e: MouseEvent) => {\n    const downX = e.clientX\n    const downY = e.clientY\n    const { offsetX, offsetY } = transform\n\n    const onMousemove = (e: MouseEvent) => {\n      const moveX = offsetX + e.clientX - downX\n      const moveY = offsetY + e.clientY - downY\n\n      adjustPosition(moveX, moveY)\n    }\n\n    const onMouseup = () => {\n      document.removeEventListener('mousemove', onMousemove)\n      document.removeEventListener('mouseup', onMouseup)\n    }\n\n    document.addEventListener('mousemove', onMousemove)\n    document.addEventListener('mouseup', onMouseup)\n  }\n\n  const onDraggable = () => {\n    if (dragRef.value && targetRef.value) {\n      dragRef.value.addEventListener('mousedown', onMousedown)\n      window.addEventListener('resize', updatePosition)\n    }\n  }\n\n  const offDraggable = () => {\n    if (dragRef.value && targetRef.value) {\n      dragRef.value.removeEventListener('mousedown', onMousedown)\n      window.removeEventListener('resize', updatePosition)\n    }\n  }\n\n  const resetPosition = () => {\n    transform.offsetX = 0\n    transform.offsetY = 0\n\n    if (targetRef.value) {\n      targetRef.value.style.transform = ''\n    }\n  }\n\n  const updatePosition = () => {\n    const { offsetX, offsetY } = transform\n\n    adjustPosition(offsetX, offsetY)\n  }\n\n  onMounted(() => {\n    watchEffect(() => {\n      if (draggable.value) {\n        onDraggable()\n      } else {\n        offDraggable()\n      }\n    })\n  })\n\n  onBeforeUnmount(() => {\n    offDraggable()\n  })\n\n  return {\n    resetPosition,\n    updatePosition,\n  }\n}\n"], "names": ["addUnit", "onMounted", "watchEffect", "onBeforeUnmount"], "mappings": ";;;;;;;AAEY,MAAC,YAAY,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,KAAK;AACzE,EAAE,MAAM,SAAS,GAAG;AACpB,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,OAAO,EAAE,CAAC;AACd,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AAC3C,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;AACzB,MAAM,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;AAC7C,MAAM,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;AACjE,MAAM,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;AACzC,MAAM,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC;AACvC,MAAM,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC;AAC3C,MAAM,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC;AAC7C,MAAM,MAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC;AAC/D,MAAM,MAAM,YAAY,GAAG,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC;AACjE,MAAM,MAAM,OAAO,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC;AAC5C,MAAM,MAAM,MAAM,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC;AAC1C,MAAM,MAAM,OAAO,GAAG,WAAW,GAAG,UAAU,GAAG,WAAW,GAAG,OAAO,CAAC;AACvE,MAAM,MAAM,MAAM,GAAG,YAAY,GAAG,SAAS,IAAI,YAAY,GAAG,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;AAC3G,MAAM,IAAI,EAAE,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE;AACzD,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;AAC5D,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1D,OAAO;AACP,MAAM,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;AAChC,MAAM,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;AAChC,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,UAAU,EAAEA,aAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAEA,aAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1F,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK;AAC7B,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC;AAC5B,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC;AAC5B,IAAI,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;AAC3C,IAAI,MAAM,WAAW,GAAG,CAAC,EAAE,KAAK;AAChC,MAAM,MAAM,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC;AACjD,MAAM,MAAM,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC;AACjD,MAAM,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACnC,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,MAAM;AAC5B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC7D,MAAM,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACzD,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACxD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACpD,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,EAAE;AAC1C,MAAM,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC/D,MAAM,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AACxD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,EAAE;AAC1C,MAAM,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAClE,MAAM,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC;AAC1B,IAAI,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;AACzB,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;AAC3C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;AAC3C,IAAI,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACrC,GAAG,CAAC;AACJ,EAAEC,aAAS,CAAC,MAAM;AAClB,IAAIC,eAAW,CAAC,MAAM;AACtB,MAAM,IAAI,SAAS,CAAC,KAAK,EAAE;AAC3B,QAAQ,WAAW,EAAE,CAAC;AACtB,OAAO,MAAM;AACb,QAAQ,YAAY,EAAE,CAAC;AACvB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAEC,mBAAe,CAAC,MAAM;AACxB,IAAI,YAAY,EAAE,CAAC;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}