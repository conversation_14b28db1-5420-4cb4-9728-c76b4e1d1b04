import{Q as e,M as l}from"../../chunks/ui-vendor-DZ6owSRu.js";import{d as a,r as t,X as u,b as o,c as n,a as s,Q as d,H as i,K as r,I as c,ag as v,o as p,u as m,M as _,O as f}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as h}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";import"../../chunks/utils-vendor-DYQz1-BF.js";const g={class:"video-batch-proofreading"},b={class:"content"},w={key:0,class:"upload-actions"},x={class:"progress-text"},k={key:0,class:"current-processing"},y={class:"batch-actions"},V={key:0,class:"preview-content"},T=["src"],U={class:"video-info"},R={class:"subtitle-editor"},C={class:"timeline-editor"},L={class:"dialog-footer"},z=h(a({__name:"VideoBatchProofreadingView",setup(a){const h=t([]),z=t([]),j=t(!1),$=t(null),M=t(0),B=u({extractionMethod:"speech-to-text",language:"zh",quality:"standard",includeTimestamp:!0}),O=t(!1),S=t(null),I=t(!1),E=t("text"),F=t(""),q=t(null),A=t([]),K=o(()=>0===h.value.length?0:Math.round(z.value.length/h.value.length*100)),P=o(()=>j.value?"active":100===K.value?"success":"normal"),Q=e=>e.raw?.type.startsWith("video/")?e.size&&e.size>524288e3?(l.error("文件大小不能超过 500MB"),!1):void 0:(l.error("只能上传视频文件"),!1),G=e=>{const l=h.value.findIndex(l=>l.uid===e.uid);l>-1&&h.value.splice(l,1)},H=()=>{h.value=[],z.value=[]},W=async()=>{if(0!==h.value.length){j.value=!0,z.value=[];try{for(let e=0;e<h.value.length;e++){const l=h.value[e];$.value=l;for(let e=0;e<=100;e+=10)M.value=e,await new Promise(e=>setTimeout(e,200));const a={name:l.name,size:l.size,duration:"05:32",status:"处理完成",extractedText:`这是视频 ${l.name} 的字幕提取结果示例文字...\n\n[00:00:10] 欢迎观看本期视频\n[00:00:15] 今天我们来讲解AI智能审校系统\n[00:00:20] 首先介绍系统的主要功能...`,url:URL.createObjectURL(l.raw),originalFile:l,subtitles:[{startTime:"00:00:10",endTime:"00:00:15",text:"欢迎观看本期视频"},{startTime:"00:00:15",endTime:"00:00:20",text:"今天我们来讲解AI智能审校系统"},{startTime:"00:00:20",endTime:"00:00:25",text:"首先介绍系统的主要功能"}]};z.value.push(a),M.value=0}l.success("批量处理完成")}catch(e){l.error("批量处理失败")}finally{j.value=!1,$.value=null}}else l.warning("请先上传视频文件")},X=e=>{const l=e.target,a=Math.floor(l.duration),t=Math.floor(a/60),u=a%60;S.value&&(S.value.duration=`${t.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}`)},D=()=>{q.value&&(q.value.extractedText=F.value,l.success("保存成功")),I.value=!1},J=()=>{if(0===z.value.length)return void l.warning("没有可导出的结果");const e=z.value.map(e=>`文件名: ${e.name}\n时长: ${e.duration}\n字幕内容:\n${e.extractedText}\n\n`).join("---\n\n"),a=new Blob([e],{type:"text/plain"}),t=URL.createObjectURL(a),u=document.createElement("a");u.href=t,u.download="batch_subtitle_results.txt",u.click(),URL.revokeObjectURL(t),l.success("导出成功")},N=()=>{0!==z.value.length?l.success("已提交审查，文件将转入待审查列表"):l.warning("没有可提交的结果")},Y=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":e<1073741824?(e/1048576).toFixed(1)+" MB":(e/1073741824).toFixed(1)+" GB",Z=e=>{switch(e){case"处理完成":return"success";case"处理中":return"warning";case"处理失败":return"danger";default:return"info"}};return(a,t)=>{const u=v("el-icon"),o=v("el-upload"),ee=v("el-button"),le=v("el-card"),ae=v("el-option"),te=v("el-select"),ue=v("el-form-item"),oe=v("el-col"),ne=v("el-switch"),se=v("el-row"),de=v("el-progress"),ie=v("el-table-column"),re=v("el-tag"),ce=v("el-input"),ve=v("el-table"),pe=v("el-dialog"),me=v("el-tab-pane"),_e=v("el-tabs");return p(),n("div",g,[t[25]||(t[25]=s("div",{class:"page-header"},[s("h1",null,"视频批量审校"),s("p",{class:"page-description"},"批量上传视频进行字幕提取和文字校对")],-1)),s("div",b,[d(le,{class:"upload-card",title:"批量上传视频"},{default:c(()=>[d(o,{class:"upload-demo",drag:"",action:"#",multiple:"",accept:"video/*","auto-upload":!1,"file-list":h.value,"on-change":Q,"on-remove":G},{tip:c(()=>t[9]||(t[9]=[s("div",{class:"el-upload__tip"}," 支持 mp4/avi/mov/wmv 格式，单个文件不超过 500MB，最多可上传 20 个文件 ",-1)])),default:c(()=>[d(u,{class:"el-icon--upload"},{default:c(()=>[d(m(e))]),_:1}),t[10]||(t[10]=s("div",{class:"el-upload__text"},[_("将视频拖拽到此处，或"),s("em",null,"点击批量上传")],-1))]),_:1,__:[10]},8,["file-list"]),h.value.length>0?(p(),n("div",w,[d(ee,{type:"primary",onClick:W,loading:j.value},{default:c(()=>t[11]||(t[11]=[_(" 开始批量处理 ")])),_:1,__:[11]},8,["loading"]),d(ee,{onClick:H},{default:c(()=>t[12]||(t[12]=[_("清空文件")])),_:1,__:[12]})])):r("",!0)]),_:1}),h.value.length>0?(p(),i(le,{key:0,title:"处理设置",class:"settings-card"},{default:c(()=>[d(se,{gutter:20},{default:c(()=>[d(oe,{span:6},{default:c(()=>[d(ue,{label:"提取方式"},{default:c(()=>[d(te,{modelValue:B.extractionMethod,"onUpdate:modelValue":t[0]||(t[0]=e=>B.extractionMethod=e),placeholder:"选择提取方式"},{default:c(()=>[d(ae,{label:"语音转文字",value:"speech-to-text"}),d(ae,{label:"字幕文件提取",value:"subtitle-extract"}),d(ae,{label:"OCR识别",value:"ocr"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(oe,{span:6},{default:c(()=>[d(ue,{label:"语言设置"},{default:c(()=>[d(te,{modelValue:B.language,"onUpdate:modelValue":t[1]||(t[1]=e=>B.language=e),placeholder:"选择语言"},{default:c(()=>[d(ae,{label:"中文",value:"zh"}),d(ae,{label:"英文",value:"en"}),d(ae,{label:"中英混合",value:"zh-en"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(oe,{span:6},{default:c(()=>[d(ue,{label:"质量设置"},{default:c(()=>[d(te,{modelValue:B.quality,"onUpdate:modelValue":t[2]||(t[2]=e=>B.quality=e),placeholder:"选择质量"},{default:c(()=>[d(ae,{label:"快速",value:"fast"}),d(ae,{label:"标准",value:"standard"}),d(ae,{label:"高质量",value:"high"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(oe,{span:6},{default:c(()=>[d(ue,{label:"时间轴"},{default:c(()=>[d(ne,{modelValue:B.includeTimestamp,"onUpdate:modelValue":t[3]||(t[3]=e=>B.includeTimestamp=e),"active-text":"包含","inactive-text":"不包含"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})):r("",!0),j.value||z.value.length>0?(p(),i(le,{key:1,title:"处理进度",class:"progress-card"},{default:c(()=>[d(de,{percentage:K.value,status:P.value,"stroke-width":8},null,8,["percentage","status"]),s("p",x," 已处理 "+f(z.value.length)+" / "+f(h.value.length)+" 个文件 ",1),$.value?(p(),n("div",k,[s("p",null,"正在处理："+f($.value.name),1),d(de,{percentage:M.value,"stroke-width":6,"show-text":!1},null,8,["percentage"])])):r("",!0)]),_:1})):r("",!0),z.value.length>0?(p(),i(le,{key:2,title:"处理结果",class:"results-card"},{default:c(()=>[d(ve,{data:z.value,style:{width:"100%"}},{default:c(()=>[d(ie,{prop:"name",label:"文件名",width:"200"}),d(ie,{prop:"duration",label:"时长",width:"100"}),d(ie,{prop:"size",label:"文件大小",width:"100"},{default:c(e=>[_(f(Y(e.row.size)),1)]),_:1}),d(ie,{prop:"status",label:"处理状态",width:"120"},{default:c(e=>[d(re,{type:Z(e.row.status)},{default:c(()=>[_(f(e.row.status),1)]),_:2},1032,["type"])]),_:1}),d(ie,{prop:"extractedText",label:"提取文字","min-width":"300"},{default:c(e=>[d(ce,{modelValue:e.row.extractedText,"onUpdate:modelValue":l=>e.row.extractedText=l,type:"textarea",rows:3,placeholder:"字幕提取结果...",readonly:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),d(ie,{label:"操作",width:"250"},{default:c(e=>[d(ee,{size:"small",onClick:l=>{return a=e.row,S.value=a,void(O.value=!0);var a}},{default:c(()=>t[13]||(t[13]=[_("预览")])),_:2,__:[13]},1032,["onClick"]),d(ee,{size:"small",type:"primary",onClick:l=>{return a=e.row,q.value=a,F.value=a.extractedText,A.value=a.subtitles||[],void(I.value=!0);var a}},{default:c(()=>t[14]||(t[14]=[_("编辑")])),_:2,__:[14]},1032,["onClick"]),d(ee,{size:"small",onClick:l=>(e=>{const l=new Blob([e.extractedText],{type:"text/plain"}),a=URL.createObjectURL(l),t=document.createElement("a");t.href=a,t.download=`${e.name}_subtitle.txt`,t.click(),URL.revokeObjectURL(a)})(e.row)},{default:c(()=>t[15]||(t[15]=[_("下载")])),_:2,__:[15]},1032,["onClick"]),d(ee,{size:"small",onClick:a=>(e=>{if(!e.subtitles||0===e.subtitles.length)return void l.warning("没有时间轴信息，无法导出SRT文件");let a="";e.subtitles.forEach((e,l)=>{a+=`${l+1}\n`,a+=`${e.startTime} --\x3e ${e.endTime}\n`,a+=`${e.text}\n\n`});const t=new Blob([a],{type:"text/plain"}),u=URL.createObjectURL(t),o=document.createElement("a");o.href=u,o.download=`${e.name}_subtitle.srt`,o.click(),URL.revokeObjectURL(u),l.success("SRT文件导出成功")})(e.row)},{default:c(()=>t[16]||(t[16]=[_("导出SRT")])),_:2,__:[16]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),s("div",y,[d(ee,{type:"primary",onClick:J},{default:c(()=>t[17]||(t[17]=[_("导出全部结果")])),_:1,__:[17]}),d(ee,{onClick:N},{default:c(()=>t[18]||(t[18]=[_("提交审查")])),_:1,__:[18]})])]),_:1})):r("",!0)]),d(pe,{modelValue:O.value,"onUpdate:modelValue":t[4]||(t[4]=e=>O.value=e),title:"视频预览",width:"70%"},{default:c(()=>[S.value?(p(),n("div",V,[s("video",{src:S.value.url,controls:"",class:"preview-video",onLoadedmetadata:X},null,40,T),s("div",U,[s("p",null,[t[19]||(t[19]=s("strong",null,"文件名：",-1)),_(f(S.value.name),1)]),s("p",null,[t[20]||(t[20]=s("strong",null,"时长：",-1)),_(f(S.value.duration),1)]),s("p",null,[t[21]||(t[21]=s("strong",null,"文件大小：",-1)),_(f(Y(S.value.size)),1)])])])):r("",!0)]),_:1},8,["modelValue"]),d(pe,{modelValue:I.value,"onUpdate:modelValue":t[8]||(t[8]=e=>I.value=e),title:"编辑字幕文字",width:"60%"},{footer:c(()=>[s("span",L,[d(ee,{onClick:t[7]||(t[7]=e=>I.value=!1)},{default:c(()=>t[23]||(t[23]=[_("取消")])),_:1,__:[23]}),d(ee,{type:"primary",onClick:D},{default:c(()=>t[24]||(t[24]=[_("保存")])),_:1,__:[24]})])]),default:c(()=>[s("div",R,[d(_e,{modelValue:E.value,"onUpdate:modelValue":t[6]||(t[6]=e=>E.value=e)},{default:c(()=>[d(me,{label:"文字编辑",name:"text"},{default:c(()=>[d(ce,{modelValue:F.value,"onUpdate:modelValue":t[5]||(t[5]=e=>F.value=e),type:"textarea",rows:15,placeholder:"请编辑字幕文字..."},null,8,["modelValue"])]),_:1}),d(me,{label:"时间轴编辑",name:"timeline"},{default:c(()=>[s("div",C,[t[22]||(t[22]=s("p",null,"时间轴编辑功能（开发中）",-1)),d(ve,{data:A.value,style:{width:"100%"}},{default:c(()=>[d(ie,{prop:"startTime",label:"开始时间",width:"120"}),d(ie,{prop:"endTime",label:"结束时间",width:"120"}),d(ie,{prop:"text",label:"字幕内容"})]),_:1},8,["data"])])]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-1cf6c9f5"]]);export{z as default};
