{"$schema": "http://json.schemastore.org/web-types", "framework": "vue", "name": "element-plus", "version": "2.10.2", "js-types-syntax": "typescript", "description-markup": "markdown", "contributions": {"html": {"vue-components": [{"name": "el-affix", "source": {"symbol": "ElAffix"}, "description": "Fix the element to a specific visible area.", "doc-url": "https://element-plus.org/en-US/component/affix.html#affix", "props": [{"name": "offset", "description": "offset distance", "doc-url": "https://element-plus.org/en-US/component/affix.html#attributes", "type": ["number"], "default": "0"}, {"name": "position", "description": "position of affix", "doc-url": "https://element-plus.org/en-US/component/affix.html#attributes", "type": ["'top' | 'bottom'"], "default": "top"}, {"name": "target", "description": "target container (CSS selector)", "doc-url": "https://element-plus.org/en-US/component/affix.html#attributes", "type": ["string"]}, {"name": "z-index", "description": "`z-index` of affix", "doc-url": "https://element-plus.org/en-US/component/affix.html#attributes", "type": ["number"], "default": "100"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/affix.html#slots"}], "js": {"events": [{"name": "change", "description": "triggers when fixed state changed", "doc-url": "https://element-plus.org/en-US/component/affix.html#events"}, {"name": "scroll", "description": "triggers when scrolling", "doc-url": "https://element-plus.org/en-US/component/affix.html#events"}]}}, {"name": "el-alert", "source": {"symbol": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Displays important alert messages.", "doc-url": "https://element-plus.org/en-US/component/alert.html#alert", "props": [{"name": "title", "description": "alert title.", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["string"]}, {"name": "type", "description": "alert type.", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["'primary'  | 'success' | 'warning' | 'info' | 'error'"], "default": "info"}, {"name": "description", "description": "descriptive text.", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["string"]}, {"name": "closable", "description": "whether alert can be dismissed.", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "center", "description": "whether content is placed in the center.", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "close-text", "description": "customized close button text.", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["string"]}, {"name": "show-icon", "description": "whether a type icon is displayed.", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "effect", "description": "theme style.", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["'light' | 'dark'"], "default": "light"}, {"name": "show-after", "description": "delay of appearance, in millisecond", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["number"], "default": "0"}, {"name": "hide-after", "description": "delay of disappear, in millisecond", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["number"], "default": "200"}, {"name": "auto-close", "description": "timeout in milliseconds to hide alert", "doc-url": "https://element-plus.org/en-US/component/alert.html#attributes", "type": ["number"], "default": "0"}], "slots": [{"name": "default", "description": "content of the alert description.", "doc-url": "https://element-plus.org/en-US/component/alert.html#slots"}, {"name": "title", "description": "content of the alert title.", "doc-url": "https://element-plus.org/en-US/component/alert.html#slots"}, {"name": "icon", "description": "content of the alert icon.", "doc-url": "https://element-plus.org/en-US/component/alert.html#slots"}], "js": {"events": [{"name": "open", "description": "trigger when alert is opened.", "doc-url": "https://element-plus.org/en-US/component/alert.html#events"}, {"name": "close", "description": "trigger when alert is closed.", "doc-url": "https://element-plus.org/en-US/component/alert.html#events"}]}}, {"name": "el-anchor", "source": {"symbol": "ElAnchor"}, "description": "Through the anchor point, you can quickly find the position of the information content on the current page.", "doc-url": "https://element-plus.org/en-US/component/anchor.html#anchor", "slots": [{"name": "default", "description": "AnchorLink component list", "doc-url": "https://element-plus.org/en-US/component/anchor.html#anchor-slots"}], "js": {"events": [{"name": "change", "description": "callback when the step changes", "doc-url": "https://element-plus.org/en-US/component/anchor.html#anchor-events"}, {"name": "click", "description": "Triggered when the user clicks on the link", "doc-url": "https://element-plus.org/en-US/component/anchor.html#anchor-events"}]}}, {"name": "el-anchor-link", "source": {"symbol": "ElAnchorLink"}, "doc-url": "https://element-plus.org/en-US/component/anchor.html#anchorlink", "slots": [{"name": "default", "description": "the content of the anchor link.", "doc-url": "https://element-plus.org/en-US/component/anchor.html#anchorlink-slots"}, {"name": "sub-link", "description": "slots for child links.", "doc-url": "https://element-plus.org/en-US/component/anchor.html#anchorlink-slots"}]}, {"name": "el-autocomplete", "source": {"symbol": "ElAutocomplete"}, "description": "Get some recommended tips based on the current input.", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#autocomplete", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["string"]}, {"name": "placeholder", "description": "the placeholder of Autocomplete", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["string"]}, {"name": "clearable", "description": "whether to show clear button", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "whether Autocomplete is disabled", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "value-key", "description": "key name of the input suggestion object for display", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["string"], "default": "value"}, {"name": "debounce", "description": "debounce delay when typing, in milliseconds", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["number"], "default": "300"}, {"name": "placement", "description": "placement of the popup menu", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["'top' | 'top- start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end'"], "default": "bottom-start"}, {"name": "fetch-suggestions", "description": "a method to fetch input suggestions. When suggestions are ready, invoke `callback(data:[])` to return them to Autocomplete", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["Array", "(queryString: string, callback: callbackfn) => void"]}, {"name": "trigger-on-focus", "description": "whether show suggestions when input focus", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "select-when-unmatched", "description": "whether to emit a `select` event on enter when there is no autocomplete match", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "name", "description": "same as `name` in native input", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["string"]}, {"name": "aria-label", "description": "native `aria-label` attribute", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["string"]}, {"name": "hide-loading", "description": "whether to hide the loading icon in remote search", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "popper-class", "description": "custom class name for autocomplete's dropdown", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["string"]}, {"name": "teleported", "description": "whether select dropdown is teleported to the body", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "append-to", "description": "which select dropdown appends to", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": [{"name": "CSSSelector", "source": {"symbol": "CSSSelector"}}, "HTMLElement"]}, {"name": "highlight-first-item", "description": "whether to highlight first item in remote search suggestions by default", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "fit-input-width", "description": "whether the width of the dropdown is the same as the input", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "popper-append-to-body", "description": "whether to append the dropdown to body. If the positioning of the dropdown is wrong, you can try to set this prop to false", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "custom content for input suggestions", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#slots", "type": "{ item: Record<string, any> }"}, {"name": "prefix", "description": "content as Input prefix", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#slots"}, {"name": "suffix", "description": "content as Input suffix", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#slots"}, {"name": "prepend", "description": "content to prepend before Input", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#slots"}, {"name": "append", "description": "content to append after Input", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#slots"}, {"name": "loading", "description": "override loading content", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#slots"}], "js": {"events": [{"name": "blur", "description": "triggers when Input blurs", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#events"}, {"name": "focus", "description": "triggers when Input focuses", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#events"}, {"name": "input", "description": "triggers when the Input value change", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#events"}, {"name": "clear", "description": "triggers when the Input is cleared by clicking the clear button", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#events"}, {"name": "select", "description": "triggers when a suggestion is clicked", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#events"}, {"name": "change", "description": "triggers when the icon inside Input value change", "doc-url": "https://element-plus.org/en-US/component/autocomplete.html#events"}]}}, {"name": "el-avatar", "source": {"symbol": "El<PERSON><PERSON><PERSON>"}, "description": "Avatars can be used to represent people or objects. It supports images, Icons, or characters.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#avatar", "props": [{"name": "icon", "description": "representation type to icon, more info on icon component.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "size", "description": "avatar size.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#attributes", "type": ["number", "'large' | 'default' | 'small'"], "default": "default"}, {"name": "shape", "description": "avatar shape.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#attributes", "type": ["'circle' | 'square'"], "default": "circle"}, {"name": "src", "description": "the source of the image for an image avatar.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#attributes", "type": ["string"]}, {"name": "src-set", "description": "native attribute `srcset` of image avatar.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#attributes", "type": ["string"]}, {"name": "alt", "description": "native attribute `alt` of image avatar.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#attributes", "type": ["string"]}, {"name": "fit", "description": "set how the image fit its container for an image avatar.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#attributes", "type": ["'fill' | 'contain' | 'cover' | 'none' | 'scale-down'"], "default": "cover"}], "slots": [{"name": "default", "description": "customize avatar content.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#slots"}], "js": {"events": [{"name": "error", "description": "trigger when image load error.", "doc-url": "https://element-plus.org/en-US/component/avatar.html#events"}]}}, {"name": "el-backtop", "source": {"symbol": "ElBacktop"}, "description": "A button to back to top.", "doc-url": "https://element-plus.org/en-US/component/backtop.html#backtop", "props": [{"name": "target", "description": "the target to trigger scroll.", "doc-url": "https://element-plus.org/en-US/component/backtop.html#attributes", "type": ["string"]}, {"name": "visibility-height", "description": "the button will not show until the scroll height reaches this value.", "doc-url": "https://element-plus.org/en-US/component/backtop.html#attributes", "type": ["number"], "default": "200"}, {"name": "right", "description": "right distance.", "doc-url": "https://element-plus.org/en-US/component/backtop.html#attributes", "type": ["number"], "default": "40"}, {"name": "bottom", "description": "bottom distance.", "doc-url": "https://element-plus.org/en-US/component/backtop.html#attributes", "type": ["number"], "default": "40"}], "slots": [{"name": "default", "description": "customize default content.", "doc-url": "https://element-plus.org/en-US/component/backtop.html#slots"}], "js": {"events": [{"name": "click", "description": "triggers when click.", "doc-url": "https://element-plus.org/en-US/component/backtop.html#events"}]}}, {"name": "el-badge", "source": {"symbol": "ElBadge"}, "description": "A number or status mark on buttons and icons.", "doc-url": "https://element-plus.org/en-US/component/badge.html#badge", "props": [{"name": "value", "description": "display value.", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": ["string", "number"], "default": "''"}, {"name": "max", "description": "maximum value, shows `{max}+` when exceeded. Only works if value is a number.", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": ["number"], "default": "99"}, {"name": "is-dot", "description": "if a little dot is displayed.", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hidden", "description": "hidden badge.", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "type", "description": "badge type.", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": ["'primary' | 'success' | 'warning' | 'danger' | 'info'"], "default": "danger"}, {"name": "show-zero", "description": "Whether to show badge when value is zero.", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "color", "description": "background color of the dot", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": ["string"]}, {"name": "offset", "description": "offset of badge", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": ["[ `number` , `number` ]"]}, {"name": "badge-style", "description": "custom style of badge", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": [{"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}]}, {"name": "badge-class", "description": "custom class of badge", "doc-url": "https://element-plus.org/en-US/component/badge.html#attributes", "type": ["string"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/badge.html#slots"}, {"name": "content", "description": "customize badge content", "doc-url": "https://element-plus.org/en-US/component/badge.html#slots", "type": "{ value: string }"}]}, {"name": "el-breadcrumb", "source": {"symbol": "ElBreadcrumb"}, "description": "Displays the location of the current page, making it easier to browser back.", "doc-url": "https://element-plus.org/en-US/component/breadcrumb.html#breadcrumb", "props": [{"name": "separator", "description": "separator character", "doc-url": "https://element-plus.org/en-US/component/breadcrumb.html#breadcrumb-attributes", "type": ["string"], "default": "/"}, {"name": "separator-icon", "description": "icon component of icon separator", "doc-url": "https://element-plus.org/en-US/component/breadcrumb.html#breadcrumb-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/breadcrumb.html#breadcrumb-slots"}]}, {"name": "el-breadcrumb-item", "source": {"symbol": "ElBreadcrumbItem"}, "doc-url": "https://element-plus.org/en-US/component/breadcrumb.html#breadcrumbitem", "props": [{"name": "to", "description": "target route of the link, same as `to` of `vue-router`", "doc-url": "https://element-plus.org/en-US/component/breadcrumb.html#breadcrumbitem-attributes", "type": ["string", {"name": "RouteLocationRaw", "source": {"symbol": "RouteLocationRaw"}}], "default": "''"}, {"name": "replace", "description": "if `true`, the navigation will not leave a history record", "doc-url": "https://element-plus.org/en-US/component/breadcrumb.html#breadcrumbitem-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/breadcrumb.html#breadcrumbitem-slots"}]}, {"name": "el-button", "source": {"symbol": "ElButton"}, "description": "Commonly used button.", "doc-url": "https://element-plus.org/en-US/component/button.html#button", "props": [{"name": "size", "description": "button size", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "type", "description": "button type, when setting `color`, the latter prevails", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info' | '' | 'text'"]}, {"name": "plain", "description": "determine whether it's a plain button", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "text", "description": "determine whether it's a text button", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "bg", "description": "determine whether the text button background color is always on", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "link", "description": "determine whether it's a link button", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "round", "description": "determine whether it's a round button", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "circle", "description": "determine whether it's a circle button", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "loading", "description": "determine whether it's loading", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "loading-icon", "description": "customize loading icon component", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "Loading"}, {"name": "disabled", "description": "disable the button", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "icon", "description": "icon component", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "autofocus", "description": "same as native button's `autofocus`", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "native-type", "description": "same as native button's `type`", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["'button' | 'submit' | 'reset'"], "default": "button"}, {"name": "auto-insert-space", "description": "automatically insert a space between two chinese characters(this will only take effect when the text length is 2 and all characters are in Chinese.)", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "color", "description": "custom button color, automatically calculate `hover` and `active` color", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["string"]}, {"name": "dark", "description": "dark mode, which automatically converts `color` to dark mode colors", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["boolean"], "default": "false"}, {"name": "tag", "description": "custom element tag", "doc-url": "https://element-plus.org/en-US/component/button.html#button-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "button"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/button.html#button-slots"}, {"name": "loading", "description": "customize loading component", "doc-url": "https://element-plus.org/en-US/component/button.html#button-slots"}, {"name": "icon", "description": "customize icon component", "doc-url": "https://element-plus.org/en-US/component/button.html#button-slots"}]}, {"name": "el-button-group", "source": {"symbol": "ElButtonGroup"}, "doc-url": "https://element-plus.org/en-US/component/button.html#buttongroup", "props": [{"name": "size", "description": "control the size of buttons in this button-group", "doc-url": "https://element-plus.org/en-US/component/button.html#buttongroup-attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "type", "description": "control the type of buttons in this button-group", "doc-url": "https://element-plus.org/en-US/component/button.html#buttongroup-attributes", "type": ["'primary' | 'success' | 'warning' | 'danger' | 'info'"]}], "slots": [{"name": "default", "description": "customize button group content", "doc-url": "https://element-plus.org/en-US/component/button.html#buttongroup-slots"}]}, {"name": "el-calendar", "source": {"symbol": "ElCalendar"}, "description": "Display date.", "doc-url": "https://element-plus.org/en-US/component/calendar.html#calendar", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/calendar.html#attributes", "type": ["Date"]}, {"name": "range", "description": "time range, including start time and end time. Start time must be start day of week, end time must be end day of week, the time span cannot exceed two months.", "doc-url": "https://element-plus.org/en-US/component/calendar.html#attributes", "type": ["[Date, Date]"]}], "slots": [{"name": "date-cell", "description": "`type` indicates which month the date belongs, optional values are prev-month, current-month, next-month; `isSelected` indicates whether the date is selected; `day` is the formatted date in the format `YYYY-MM-DD`; `date` is date the cell represents", "doc-url": "https://element-plus.org/en-US/component/calendar.html#slots", "type": "{ data: { type: 'prev-month' | 'current-month' | 'next-month', isSelected: boolean, day: string, date: Date } }"}, {"name": "header", "description": "content of the Calendar header", "doc-url": "https://element-plus.org/en-US/component/calendar.html#slots", "type": "{ date: string }"}]}, {"name": "el-card", "source": {"symbol": "ElCard"}, "description": "Integrate information in a card container.", "doc-url": "https://element-plus.org/en-US/component/card.html#card", "props": [{"name": "header", "description": "title of the card. Also accepts a DOM passed by `slot#header`", "doc-url": "https://element-plus.org/en-US/component/card.html#attributes", "type": ["string"]}, {"name": "footer", "description": "footer of the card. Also accepts a DOM passed by `slot#footer`", "doc-url": "https://element-plus.org/en-US/component/card.html#attributes", "type": ["string"]}, {"name": "body-style", "description": "CSS style of card body", "doc-url": "https://element-plus.org/en-US/component/card.html#attributes", "type": [{"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}]}, {"name": "header-class", "description": "custom class name of card header", "doc-url": "https://element-plus.org/en-US/component/card.html#attributes", "type": ["string"]}, {"name": "body-class", "description": "custom class name of card body", "doc-url": "https://element-plus.org/en-US/component/card.html#attributes", "type": ["string"]}, {"name": "footer-class", "description": "custom class name of card footer", "doc-url": "https://element-plus.org/en-US/component/card.html#attributes", "type": ["string"]}, {"name": "shadow", "description": "when to show card shadows", "doc-url": "https://element-plus.org/en-US/component/card.html#attributes", "type": [{"name": "always", "source": {"symbol": "always"}}, {"name": "never", "source": {"symbol": "never"}}, {"name": "hover", "source": {"symbol": "hover"}}], "default": "always"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/card.html#slots"}, {"name": "header", "description": "content of the Card header", "doc-url": "https://element-plus.org/en-US/component/card.html#slots"}, {"name": "footer", "description": "content of the Card footer", "doc-url": "https://element-plus.org/en-US/component/card.html#slots"}]}, {"name": "el-carousel", "source": {"symbol": "ElCarousel"}, "description": "Loop a series of images or texts in a limited space", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel", "props": [{"name": "height", "description": "height of the carousel", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["string"], "default": "''"}, {"name": "initial-index", "description": "index of the initially active slide (starting from 0)", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["number"], "default": "0"}, {"name": "trigger", "description": "how indicators are triggered", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["'hover' | 'click'"], "default": "hover"}, {"name": "autoplay", "description": "whether automatically loop the slides", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["boolean"], "default": "true"}, {"name": "interval", "description": "interval of the auto loop, in milliseconds", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["number"], "default": "3000"}, {"name": "indicator-position", "description": "position of the indicators", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["'' | 'none' | 'outside'"], "default": "''"}, {"name": "arrow", "description": "when arrows are shown", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["'always' | 'hover' | 'never'"], "default": "hover"}, {"name": "type", "description": "type of the Carousel", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["'' | 'card'"], "default": "''"}, {"name": "card-scale", "description": "when type is card, scaled size of secondary cards", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["number"], "default": "0.83"}, {"name": "loop", "description": "display the items in loop", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["boolean"], "default": "true"}, {"name": "direction", "description": "display direction", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["'horizontal' | 'vertical'"], "default": "horizontal"}, {"name": "pause-on-hover", "description": "pause autoplay when hover", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["boolean"], "default": "true"}, {"name": "motion-blur", "description": "infuse dynamism and smoothness into the carousel", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-slots"}], "js": {"events": [{"name": "change", "description": "triggers when the active slide switches, it has two parameters, the one is the index of the new active slide, and other is index of the old active slide", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-events"}]}}, {"name": "el-carousel-item", "source": {"symbol": "ElCarouselItem"}, "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-item", "props": [{"name": "name", "description": "name of the item, can be used in `setActiveItem`", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-item-attributes", "type": ["string"], "default": "''"}, {"name": "label", "description": "text content for the corresponding indicator", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-item-attributes", "type": ["string", "number"], "default": "''"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/carousel.html#carousel-item-slots"}]}, {"name": "el-cascader", "source": {"symbol": "ElCascader"}, "description": "If the options have a clear hierarchical structure, Cascader can be used to view and select them.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["string", "number", "string[]", "number[]", {"name": "any", "source": {"symbol": "any"}}]}, {"name": "options", "description": "data of the options, the key of `value` and `label` can be customize by `CascaderProps`.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["Record<string, unknown>[]"]}, {"name": "props", "description": "configuration options, see the following `CascaderProps` table.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": [{"name": "CascaderProps", "source": {"symbol": "CascaderProps"}}]}, {"name": "size", "description": "size of input", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "placeholder", "description": "placeholder of input", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["string"]}, {"name": "disabled", "description": "whether <PERSON><PERSON> is disabled", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"]}, {"name": "clearable", "description": "whether selected value can be cleared", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"]}, {"name": "show-all-levels", "description": "whether to display all levels of the selected value in the input", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"], "default": "true"}, {"name": "collapse-tags", "description": "whether to collapse tags in multiple selection mode", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"]}, {"name": "collapse-tags-tooltip", "description": "whether show all selected tags when mouse hover text of collapse-tags. To use this, `collapse-tags` must be true", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"], "default": "false"}, {"name": "max-collapse-tags-tooltip-height", "description": "max height of collapse-tags tooltip.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["string", "number"]}, {"name": "separator", "description": "option label separator", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["string"], "default": "' / '"}, {"name": "filterable", "description": "whether the options can be searched", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"]}, {"name": "filter-method", "description": "customize search logic, the first parameter is `node`, the second is `keyword`, and need return a boolean value indicating whether it hits.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["(node: <PERSON>r<PERSON><PERSON>, keyword: string) => boolean"]}, {"name": "debounce", "description": "debounce delay when typing filter keyword, in milliseconds", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["number"], "default": "300"}, {"name": "before-filter", "description": "hook function before filtering with the value to be filtered as its parameter. If `false` is returned or a `Promise` is returned and then is rejected, filtering will be aborted", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["(value: string) => boolean"]}, {"name": "popper-class", "description": "custom class name for <PERSON><PERSON>'s dropdown", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["string"], "default": "''"}, {"name": "teleported", "description": "whether cascader popup is teleported", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"], "default": "true"}, {"name": "tag-type", "description": "tag type", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["'success' | 'info' | 'warning' | 'danger'"], "default": "info"}, {"name": "tag-effect", "description": "tag effect", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["'light' | 'dark' | 'plain'"], "default": "light"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"], "default": "true"}, {"name": "max-collapse-tags", "description": "The max tags number to be shown. To use this, `collpase-tags` must be true", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["number"], "default": "1"}, {"name": "empty-values", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["array"]}, {"name": "value-on-clear", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["string", "number", "boolean", "Function"]}, {"name": "persistent", "description": "when dropdown is inactive and `persistent` is `false`, dropdown will be destroyed", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"], "default": "true"}, {"name": "fallback-placements", "description": "list of possible positions for Tooltip [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements)", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": [{"name": "Placement[]", "source": {"symbol": "Placement"}}]}, {"name": "placement", "description": "position of dropdown", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'"], "default": "bottom-start"}, {"name": "popper-append-to-body", "description": "whether to append the popper menu to body. If the positioning of the popper is wrong, you can try to set this prop to false", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-attributes", "type": ["boolean"], "default": "true"}], "slots": [{"name": "default", "description": "the custom content of cascader node, which are current Node object and node data respectively.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-slots"}, {"name": "empty", "description": "content when there is no matched options.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-slots"}, {"name": "prefix", "description": "content as Input prefix", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-slots"}, {"name": "suggestion-item", "description": "custom content for suggestion item when searching", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-slots"}], "js": {"events": [{"name": "change", "description": "triggers when the binding value changes", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-events"}, {"name": "expand-change", "description": "triggers when expand option changes", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-events"}, {"name": "blur", "description": "triggers when <PERSON><PERSON> blurs", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-events"}, {"name": "focus", "description": "triggers when <PERSON><PERSON> focuses", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-events"}, {"name": "clear", "description": "triggers when the clear icon is clicked in a clearable Select", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-events"}, {"name": "visible-change", "description": "triggers when the dropdown appears/disappears", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-events"}, {"name": "remove-tag", "description": "triggers when remove tag in multiple selection mode", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascader-events"}]}}, {"name": "el-cascader-panel", "source": {"symbol": "ElCascaderPanel"}, "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascaderpanel", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascaderpanel-attributes", "type": ["string", "number", "string[]", "number[]", {"name": "any", "source": {"symbol": "any"}}]}, {"name": "options", "description": "data of the options, the key of `value` and `label` can be customize by `CascaderProps`.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascaderpanel-attributes", "type": ["Record<string, unknown>[]"]}, {"name": "props", "description": "configuration options, see the following `CascaderProps` table.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascaderpanel-attributes", "type": [{"name": "CascaderProps", "source": {"symbol": "CascaderProps"}}]}], "slots": [{"name": "default", "description": "the custom content of cascader node, which are current Node object and node data respectively.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascaderpanel-slots"}, {"name": "empty", "description": "the content of the panel when there is no data.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascaderpanel-slots"}], "js": {"events": [{"name": "change", "description": "triggers when the binding value changes", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascaderpanel-events"}, {"name": "expand-change", "description": "triggers when expand option changes", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascaderpanel-events"}, {"name": "close", "description": "close panel event, provided to <PERSON><PERSON> to put away the panel judgment.", "doc-url": "https://element-plus.org/en-US/component/cascader.html#cascaderpanel-events"}]}}, {"name": "el-checkbox", "source": {"symbol": "ElCheckbox"}, "description": "A group of options for multiple choices.", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string", "number", "boolean"]}, {"name": "value", "description": "value of the Checkbox when used inside a `checkbox-group`", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string", "number", "boolean", "object"]}, {"name": "label", "description": "label of the Checkbox when used inside a `checkbox-group`. If there's no value, `label` will act as `value`", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string", "number", "boolean", "object"]}, {"name": "true-value", "description": "value of the Checkbox if it's checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string", "number"]}, {"name": "false-value", "description": "value of the Checkbox if it's not checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string", "number"]}, {"name": "disabled", "description": "whether the Checkbox is disabled", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["boolean"], "default": "false"}, {"name": "border", "description": "whether to add a border around Checkbox", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "size of the Checkbox", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "name", "description": "native 'name' attribute", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string"]}, {"name": "checked", "description": "if the Checkbox is checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["boolean"], "default": "false"}, {"name": "indeterminate", "description": "Set indeterminate state, only responsible for style control", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["boolean"], "default": "false"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["boolean"], "default": "true"}, {"name": "tabindex", "description": "input tabindex", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string", "number"]}, {"name": "id", "description": "input id", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string"]}, {"name": "aria-controls", "description": "same as [aria-controls](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-controls), takes effect when `indeterminate` is `true`", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string"]}, {"name": "true-label", "description": "value of the Checkbox if it's checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string", "number"]}, {"name": "false-label", "description": "value of the Checkbox if it's not checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string", "number"]}, {"name": "controls", "description": "same as [aria-controls](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-controls), takes effect when `indeterminate` is `true`", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-attributes", "type": ["string"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-slots"}], "js": {"events": [{"name": "change", "description": "triggers when the binding value changes", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkbox-events"}]}}, {"name": "el-checkbox-group", "source": {"symbol": "ElCheckboxGroup"}, "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["string[]", "number[]"], "default": "[]"}, {"name": "size", "description": "size of checkbox", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "disabled", "description": "whether the nesting checkboxes are disabled", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["boolean"], "default": "false"}, {"name": "min", "description": "minimum number of checkbox checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["number"]}, {"name": "max", "description": "maximum number of checkbox checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["number"]}, {"name": "aria-label", "description": "native `aria-label` attribute", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["string"]}, {"name": "text-color", "description": "font color when button is active", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["string"], "default": "#ffffff"}, {"name": "fill", "description": "border and background color when button is active", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["string"], "default": "#409eff"}, {"name": "tag", "description": "element tag of the checkbox group", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["string"], "default": "div"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["boolean"], "default": "true"}, {"name": "label", "description": "native `aria-label` attribute", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-attributes", "type": ["string"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-slots"}], "js": {"events": [{"name": "change", "description": "triggers when the binding value changes", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxgroup-events"}]}}, {"name": "el-checkbox-button", "source": {"symbol": "ElCheckboxButton"}, "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton", "props": [{"name": "value", "description": "value of the checkbox when used inside a `checkbox-group`", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes", "type": ["string", "number", "boolean", "object"]}, {"name": "label", "description": "label of the checkbox when used inside a `checkbox-group`. If there's no value, `label` will act as `value`", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes", "type": ["string", "number", "boolean", "object"]}, {"name": "true-value", "description": "value of the checkbox if it's checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes", "type": ["string", "number"]}, {"name": "false-value", "description": "value of the checkbox if it's not checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes", "type": ["string", "number"]}, {"name": "disabled", "description": "whether the checkbox is disabled", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes", "type": ["boolean"], "default": "false"}, {"name": "name", "description": "native 'name' attribute", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes", "type": ["string"]}, {"name": "checked", "description": "if the checkbox is checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes", "type": ["boolean"], "default": "false"}, {"name": "true-label", "description": "value of the checkbox if it's checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes", "type": ["string", "number"]}, {"name": "false-label", "description": "value of the checkbox if it's not checked", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-attributes", "type": ["string", "number"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/checkbox.html#checkboxbutton-slots"}]}, {"name": "el-collapse", "source": {"symbol": "ElCollapse"}, "description": "Use Collapse to store contents.", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse", "props": [{"name": "model-value", "description": "currently active panel, the type is `string` in accordion mode, otherwise it is `array`", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-attributes", "type": ["string", "array"], "default": "[]"}, {"name": "accordion", "description": "whether to activate accordion mode", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-attributes", "type": ["boolean"], "default": "false"}, {"name": "expand-icon-position", "description": "set expand icon position", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-attributes", "type": ["'left' | 'right'"], "default": "right"}, {"name": "before-collapse", "description": "before-collapse hook before the collapse state changes. If `false` is returned or a `Promise` is returned and then is rejected, will stop collapsing", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-attributes", "type": ["() => Promise<boolean>", {"name": "boolen", "source": {"symbol": "boolen"}}]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-slots"}], "js": {"events": [{"name": "change", "description": "triggers when active panels change, the parameter type is `string` in accordion mode, otherwise it is `array`", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-events"}]}}, {"name": "el-collapse-item", "source": {"symbol": "ElCollapseItem"}, "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-item", "props": [{"name": "name", "description": "unique identification of the panel", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-item-attributes", "type": ["string", "number"]}, {"name": "title", "description": "title of the panel", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-item-attributes", "type": ["string"], "default": "''"}, {"name": "icon", "description": "icon of the collapse item", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-item-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "ArrowRight"}, {"name": "disabled", "description": "disable the collapse item", "doc-url": "https://element-plus.org/en-US/component/collapse.html#collapse-item-attributes", "type": ["boolean"], "default": "false"}]}, {"name": "el-color-picker", "source": {"symbol": "ElColorPicker"}, "description": "ColorPicker is a color selector supporting multiple color formats.", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#colorpicker", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["string"]}, {"name": "disabled", "description": "whether to disable the ColorPicker", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "size of ColorPicker", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "show-alpha", "description": "whether to display the alpha slider", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "color-format", "description": "color format of v-model", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["'hsl' | 'hsv' | 'hex' | 'rgb' | 'hex'  | 'rgb'"]}, {"name": "popper-class", "description": "custom class name for ColorPicker's dropdown", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["string"]}, {"name": "predefine", "description": "predefined color options", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["string[]"]}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "tabindex", "description": "ColorPicker tabindex", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["string", "number"], "default": "0"}, {"name": "aria-label", "description": "ColorPicker aria-label", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["string"]}, {"name": "id", "description": "ColorPicker id", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["string"]}, {"name": "teleported", "description": "whether color-picker popper is teleported to the body", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "label", "description": "ColorPicker aria-label", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#attributes", "type": ["string"]}], "js": {"events": [{"name": "change", "description": "triggers when input value changes", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#events"}, {"name": "active-change", "description": "triggers when the current active color changes", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#events"}, {"name": "focus", "description": "triggers when Component focuses", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#events"}, {"name": "blur", "description": "triggers when Component blurs", "doc-url": "https://element-plus.org/en-US/component/color-picker.html#events"}]}}, {"name": "el-config-provider", "source": {"symbol": "ElConfigProvider"}, "description": "Config Provider is used for providing global configurations, which enables your entire application to access these configurations everywhere.", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider", "props": [{"name": "locale", "description": "Locale Object", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["{name: string, el: TranslatePair}"], "default": "[en](https://github.com/element-plus/element-plus/blob/dev/packages/locale/lang/en.ts)"}, {"name": "size", "description": "global component size", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["'large' | 'default' | 'small'"], "default": "default"}, {"name": "z-index", "description": "global Initial zIndex", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["number"]}, {"name": "namespace", "description": "global component className prefix (cooperated with [$namespace](https://github.com/element-plus/element-plus/blob/dev/packages/theme-chalk/src/mixins/config.scss#L1))", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["string"], "default": "el"}, {"name": "button", "description": "button related configuration, [see the following table](#button-attribute)", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["{autoInsertSpace?: boolean, type?: string, plain?: boolean, round?: boolean}"], "default": "see the following table"}, {"name": "link", "description": "link related configuration, [see the following table](#link-attribute)", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["{type?: string, underline?: boolean | string}"], "default": "see the following table"}, {"name": "message", "description": "message related configuration, [see the following table](#message-attribute)", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["{max?: number}"], "default": "see the following table"}, {"name": "experimental-features", "description": "features at experimental stage to be added, all features are default to be set to false", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["object"]}, {"name": "empty-values", "description": "global empty values of components", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["array"]}, {"name": "value-on-clear", "description": "global clear return value", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-attributes", "type": ["string", "number", "boolean", "Function"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/config-provider.html#config-provider-slots"}]}, {"name": "el-container", "source": {"symbol": "ElC<PERSON><PERSON>"}, "description": "Container components for scaffolding basic structure of the page:", "doc-url": "https://element-plus.org/en-US/component/container.html#container", "props": [{"name": "direction", "description": "layout direction for child elements", "doc-url": "https://element-plus.org/en-US/component/container.html#container-attributes", "type": ["'horizontal' | 'vertical'"], "default": "vertical when nested with `el-header` or `el-footer`; horizontal otherwise"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/container.html#container-slots"}]}, {"name": "el-header", "source": {"symbol": "<PERSON><PERSON><PERSON><PERSON>"}, "doc-url": "https://element-plus.org/en-US/component/container.html#header", "props": [{"name": "height", "description": "height of the header", "doc-url": "https://element-plus.org/en-US/component/container.html#header-attributes", "type": ["string"], "default": "60px"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/container.html#header-slots"}]}, {"name": "el-aside", "source": {"symbol": "ElAside"}, "doc-url": "https://element-plus.org/en-US/component/container.html#aside", "props": [{"name": "width", "description": "width of the side section", "doc-url": "https://element-plus.org/en-US/component/container.html#aside-attributes", "type": ["string"], "default": "300px"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/container.html#aside-slots"}]}, {"name": "el-main", "source": {"symbol": "<PERSON><PERSON><PERSON>"}, "doc-url": "https://element-plus.org/en-US/component/container.html#main", "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/container.html#main-slots"}]}, {"name": "el-footer", "source": {"symbol": "<PERSON><PERSON><PERSON><PERSON>"}, "doc-url": "https://element-plus.org/en-US/component/container.html#footer", "props": [{"name": "height", "description": "height of the footer", "doc-url": "https://element-plus.org/en-US/component/container.html#footer-attributes", "type": ["string"], "default": "60px"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/container.html#footer-slots"}]}, {"name": "el-date-picker", "source": {"symbol": "ElDatePicker"}, "description": "Use Date Picker for date input.", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#datepicker", "props": [{"name": "model-value", "description": "binding value, if it is an array, the length should be 2", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["number", "string", "Date", "[Date, Date] | [string, string]"], "default": "''"}, {"name": "readonly", "description": "whether DatePicker is read only", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "whether <PERSON><PERSON><PERSON> is disabled", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "size of Input", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["'' | 'large' | 'default' | 'small'"]}, {"name": "editable", "description": "whether the input is editable", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "clearable", "description": "whether to show clear button", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "placeholder", "description": "placeholder in non-range mode", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string"], "default": "''"}, {"name": "start-placeholder", "description": "placeholder for the start date in range mode", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string"]}, {"name": "end-placeholder", "description": "placeholder for the end date in range mode", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string"]}, {"name": "type", "description": "type of the picker", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["'year' | 'years' |'month' | 'months' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange' | 'yearrange'"], "default": "date"}, {"name": "format", "description": "format of the displayed value in the input box", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": [{"name": "string see", "source": {"symbol": "string see"}}], "default": "YYYY-MM-DD"}, {"name": "popper-class", "description": "custom class name for DatePicker's dropdown", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string"]}, {"name": "popper-options", "description": "Customized popper option see more at [popper.js](https://popper.js.org/docs/v2/)", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["Partial<PopperOptions>"], "default": "{}"}, {"name": "range-separator", "description": "range separator", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string"], "default": "'-'"}, {"name": "default-value", "description": "optional, default date of the calendar", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["Date", "[Date, Date]"]}, {"name": "default-time", "description": "optional, the time value to use when selecting date range", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["Date", "[Date, Date]"]}, {"name": "value-format", "description": "optional, format of binding value. If not specified, the binding value will be a Date object", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": [{"name": "string see", "source": {"symbol": "string see"}}]}, {"name": "id", "description": "same as `id` in native input", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string", "[string, string]"]}, {"name": "name", "description": "same as `name` in native input", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string", "[string, string]"], "default": "''"}, {"name": "unlink-panels", "description": "unlink two date-panels in range-picker", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "prefix-icon", "description": "custom prefix icon component. By default, if the value of `type` is `TimeLikeType`, the value is `Clock`, else is `Calendar`", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "''"}, {"name": "clear-icon", "description": "custom clear icon component", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "CircleClose"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "disabled-date", "description": "a function determining if a date is disabled with that date as its parameter. Should return a Boolean", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["(data: Date) => boolean"]}, {"name": "shortcuts", "description": "an object array to set shortcut options", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["Array<{ text: string, value: Date | Function }>"], "default": "[]"}, {"name": "cell-class-name", "description": "set custom className", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["(data: Date) => string"]}, {"name": "teleported", "description": "whether date-picker dropdown is teleported to the body", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "empty-values", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["array"]}, {"name": "value-on-clear", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": ["string", "number", "boolean", "Function"]}, {"name": "fallback-placements", "description": "list of possible positions for Tooltip [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements)", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": [{"name": "Placement[]", "source": {"symbol": "Placement"}}]}, {"name": "placement", "description": "position of dropdown", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#attributes", "type": [{"name": "Placement", "source": {"symbol": "Placement"}}], "default": "bottom"}], "slots": [{"name": "default", "description": "custom cell content", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#slots"}, {"name": "range-separator", "description": "custom range separator content", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#slots"}, {"name": "prev-month", "description": "prev month icon", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#slots"}, {"name": "next-month", "description": "next month icon", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#slots"}, {"name": "prev-year", "description": "prev year icon", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#slots"}, {"name": "next-year", "description": "next year icon", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#slots"}], "js": {"events": [{"name": "change", "description": "triggers when user confirms the value", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#events"}, {"name": "blur", "description": "triggers when Input blurs", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#events"}, {"name": "focus", "description": "triggers when Input focuses", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#events"}, {"name": "clear", "description": "triggers when the clear icon is clicked in a clearable DatePicker", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#events"}, {"name": "calendar-change", "description": "triggers when the calendar selected date is changed.", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#events"}, {"name": "panel-change", "description": "triggers when the navigation button click.", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#events"}, {"name": "visible-change", "description": "triggers when the DatePicker's dropdown appears/disappears", "doc-url": "https://element-plus.org/en-US/component/date-picker.html#events"}]}}, {"name": "el-descriptions", "source": {"symbol": "ElDescriptions"}, "description": "Display multiple fields in list form.", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions", "props": [{"name": "border", "description": "with or without border", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes", "type": ["boolean"], "default": "false"}, {"name": "column", "description": "numbers of `Descriptions Item` in one line", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes", "type": ["number"], "default": "3"}, {"name": "direction", "description": "direction of list", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes", "type": ["'vertical' | 'horizontal'"], "default": "horizontal"}, {"name": "size", "description": "size of list", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes", "type": ["'' | 'large' | 'default' | 'small'"]}, {"name": "title", "description": "title text, display on the top left", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes", "type": ["string"], "default": "''"}, {"name": "extra", "description": "extra text, display on the top right", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes", "type": ["string"], "default": "''"}, {"name": "label-width", "description": "label width of every column", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-attributes", "type": ["string", "number"], "default": "''"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-slots"}, {"name": "title", "description": "custom title, display on the top left", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-slots"}, {"name": "extra", "description": "custom extra area, display on the top right", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptions-slots"}]}, {"name": "el-descriptions-item", "source": {"symbol": "ElDescriptionsItem"}, "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem", "props": [{"name": "label", "description": "label text", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["string"], "default": "''"}, {"name": "span", "description": "colspan of column", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["number"], "default": "1"}, {"name": "rowspan", "description": "the number of rows a cell should span", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["number"], "default": "1"}, {"name": "width", "description": "column width, the width of the same column in different rows is set by the max value (If no `border`, width contains label and content)", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["string", "number"], "default": "''"}, {"name": "min-width", "description": "column minimum width, columns with `width` has a fixed width, while columns with `min-width` has a width that is distributed in proportion (If no`border`, width contains label and content)", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["string", "number"], "default": "''"}, {"name": "label-width", "description": "column label width, if not set, it will be the same as the width of the column. Higher priority than the `label-width` of `Descriptions`", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["string", "number"], "default": "''"}, {"name": "align", "description": "column content alignment (If no `border`, effective for both label and content)", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["'left' | 'center' | 'right'"], "default": "left"}, {"name": "label-align", "description": "column label alignment, if omitted, the value of the above `align` attribute will be applied (If no `border`, please use `align` attribute)", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["'left' | 'center' | 'right'"]}, {"name": "class-name", "description": "column content custom class name", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["string"], "default": "''"}, {"name": "label-class-name", "description": "column label custom class name", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-attributes", "type": ["string"], "default": "''"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-slots"}, {"name": "label", "description": "custom label", "doc-url": "https://element-plus.org/en-US/component/descriptions.html#descriptionsitem-slots"}]}, {"name": "el-dialog", "source": {"symbol": "ElDialog"}, "description": "Informs users while preserving the current page state.", "doc-url": "https://element-plus.org/en-US/component/dialog.html#dialog", "props": [{"name": "model-value", "description": "visibility of Dialog", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"]}, {"name": "title", "description": "title of Dialog. Can also be passed with a named slot (see the following table)", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string"], "default": "''"}, {"name": "width", "description": "width of Dialog, default is 50%", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string", "number"], "default": "''"}, {"name": "fullscreen", "description": "whether the Dialog takes up full screen", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "top", "description": "value for `margin-top` of Dialog CSS, default is 15vh", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string"], "default": "''"}, {"name": "modal", "description": "whether a mask is displayed", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "modal-class", "description": "custom class names for mask", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string"]}, {"name": "header-class", "description": "custom class names for header wrapper", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string"]}, {"name": "body-class", "description": "custom class names for body wrapper", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string"]}, {"name": "footer-class", "description": "custom class names for footer wrapper", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string"]}, {"name": "append-to-body", "description": "whether to append Dialog itself to body. A nested Dialog should have this attribute set to `true`", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "append-to", "description": "which element the Dialog appends to. <PERSON> override `append-to-body`", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": [{"name": "CSSSelector", "source": {"symbol": "CSSSelector"}}, "HTMLElement"], "default": "body"}, {"name": "lock-scroll", "description": "whether scroll of body is disabled while <PERSON><PERSON> is displayed", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "open-delay", "description": "the Time(milliseconds) before open", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["number"], "default": "0"}, {"name": "close-delay", "description": "the Time(milliseconds) before close", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["number"], "default": "0"}, {"name": "close-on-click-modal", "description": "whether the Dialog can be closed by clicking the mask", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "close-on-press-escape", "description": "whether the Dialog can be closed by pressing ESC", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "show-close", "description": "whether to show a close button", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "before-close", "description": "callback before Dialog closes, and it will prevent Dialog from closing, use done to close the dialog", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["(done: DoneFn) => void"]}, {"name": "draggable", "description": "enable dragging feature for Dialog", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "overflow", "description": "draggable Dialog can overflow the viewport", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "center", "description": "whether to align the header and footer in center", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "align-center", "description": "whether to align the dialog both horizontally and vertically", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "destroy-on-close", "description": "destroy elements in Dialog when closed", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "close-icon", "description": "custom close icon, default is Close", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "z-index", "description": "same as z-index in native CSS, z-order of dialog", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["number"]}, {"name": "header-aria-level", "description": "header's `aria-level` attribute", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string"], "default": "2"}, {"name": "custom-class", "description": "custom class names for Dialog", "doc-url": "https://element-plus.org/en-US/component/dialog.html#attributes", "type": ["string"], "default": "''"}], "slots": [{"name": "default", "description": "default content of Dialog", "doc-url": "https://element-plus.org/en-US/component/dialog.html#slots"}, {"name": "header", "description": "content of the Dialog header; Replacing this removes the title, but does not remove the close button.", "doc-url": "https://element-plus.org/en-US/component/dialog.html#slots"}, {"name": "footer", "description": "content of the Dialog footer", "doc-url": "https://element-plus.org/en-US/component/dialog.html#slots"}, {"name": "title", "description": "works the same as the header slot. Use that instead.", "doc-url": "https://element-plus.org/en-US/component/dialog.html#slots"}], "js": {"events": [{"name": "open", "description": "triggers when the Dialog opens", "doc-url": "https://element-plus.org/en-US/component/dialog.html#events"}, {"name": "opened", "description": "triggers when the Dialog opening animation ends", "doc-url": "https://element-plus.org/en-US/component/dialog.html#events"}, {"name": "close", "description": "triggers when the Dialog closes", "doc-url": "https://element-plus.org/en-US/component/dialog.html#events"}, {"name": "closed", "description": "triggers when the Dialog closing animation ends", "doc-url": "https://element-plus.org/en-US/component/dialog.html#events"}, {"name": "open-auto-focus", "description": "triggers after Dialog opens and content focused", "doc-url": "https://element-plus.org/en-US/component/dialog.html#events"}, {"name": "close-auto-focus", "description": "triggers after Dialog closed and content focused", "doc-url": "https://element-plus.org/en-US/component/dialog.html#events"}]}}, {"name": "el-divider", "source": {"symbol": "ElDivider"}, "description": "The dividing line that separates the content.", "doc-url": "https://element-plus.org/en-US/component/divider.html#divider", "props": [{"name": "direction", "description": "Set divider's direction", "doc-url": "https://element-plus.org/en-US/component/divider.html#attributes", "type": ["'horizontal' | 'vertical'"], "default": "horizontal"}, {"name": "border-style", "description": "Set the style of divider", "doc-url": "https://element-plus.org/en-US/component/divider.html#attributes", "type": ["'none' | 'solid' | 'hidden' | 'dashed' | ..."], "default": "solid"}, {"name": "content-position", "description": "the position of the customized content on the divider line", "doc-url": "https://element-plus.org/en-US/component/divider.html#attributes", "type": ["'left' | 'right' | 'center'"], "default": "center"}], "slots": [{"name": "default", "description": "customized content on the divider line", "doc-url": "https://element-plus.org/en-US/component/divider.html#slots"}]}, {"name": "el-drawer", "source": {"symbol": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Sometimes, `Dialog` does not always satisfy our requirements, let's say you have a massive form, or you need space to display something like `terms & conditions`, `Drawer` has almost identical API with `Dialog`, but it introduces different user experience.", "doc-url": "https://element-plus.org/en-US/component/drawer.html#drawer", "props": [{"name": "model-value", "description": "Should Drawer be displayed", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "append-to-body", "description": "Controls should Drawer be inserted to DocumentBody Element, nested Drawer must assign this param to **true**", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "append-to", "description": "which element the Drawer appends to. <PERSON> override `append-to-body`", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": [{"name": "CSSSelector", "source": {"symbol": "CSSSelector"}}, "HTMLElement"], "default": "body"}, {"name": "lock-scroll", "description": "whether scroll of body is disabled while <PERSON><PERSON> is displayed", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "before-close", "description": "If set, closing procedure will be halted", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["(done: (cancel?: boolean) => void) => void"]}, {"name": "close-on-click-modal", "description": "whether the Drawer can be closed by clicking the mask", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "close-on-press-escape", "description": "Indicates whether Drawer can be closed by pressing ESC", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "open-delay", "description": "Time(milliseconds) before open", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["number"], "default": "0"}, {"name": "close-delay", "description": "Time(milliseconds) before close", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["number"], "default": "0"}, {"name": "destroy-on-close", "description": "Indicates whether children should be destroyed after <PERSON><PERSON> closed", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "modal", "description": "Should show shadowing layer", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "direction", "description": "Drawer's opening direction", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["'rtl' | 'ltr' | 'ttb' | 'btt'"], "default": "rtl"}, {"name": "show-close", "description": "Should show close button at the top right of Drawer", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "size", "description": "Drawer's size, if Drawer is horizontal mode, it effects the width property, otherwise it effects the height property, when size is `number` type, it describes the size by unit of pixels; when size is `string` type, it should be used with `x%` notation, other wise it will be interpreted to pixel unit", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["number", "string"], "default": "30%"}, {"name": "title", "description": "Drawer's title, can also be set by named slot, detailed descriptions can be found in the slot form", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["string"]}, {"name": "with-header", "description": "Flag that controls the header section's existance, default to true, when withHeader set to false, both `title attribute` and `title slot` won't work", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "modal-class", "description": "Extra class names for shadowing layer", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["string"]}, {"name": "header-class", "description": "custom class names for header wrapper", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["string"]}, {"name": "body-class", "description": "custom class names for body wrapper", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["string"]}, {"name": "footer-class", "description": "custom class names for footer wrapper", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["string"]}, {"name": "z-index", "description": "set z-index", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["number"]}, {"name": "header-aria-level", "description": "header's `aria-level` attribute", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["string"], "default": "2"}, {"name": "custom-class", "description": "Extra class names for Drawer", "doc-url": "https://element-plus.org/en-US/component/drawer.html#attributes", "type": ["string"]}], "slots": [{"name": "default", "description": "Drawer's Content", "doc-url": "https://element-plus.org/en-US/component/drawer.html#slots"}, {"name": "header", "description": "Drawer header section; Replacing this removes the title, but does not remove the close button.", "doc-url": "https://element-plus.org/en-US/component/drawer.html#slots"}, {"name": "footer", "description": "Drawer footer Section", "doc-url": "https://element-plus.org/en-US/component/drawer.html#slots"}, {"name": "title", "description": "Works the same as the header slot. Use that instead.", "doc-url": "https://element-plus.org/en-US/component/drawer.html#slots"}], "js": {"events": [{"name": "open", "description": "Triggered before Drawer opening animation begins", "doc-url": "https://element-plus.org/en-US/component/drawer.html#events"}, {"name": "opened", "description": "Triggered after <PERSON>er opening animation ended", "doc-url": "https://element-plus.org/en-US/component/drawer.html#events"}, {"name": "close", "description": "Triggered before Drawer closing animation begins", "doc-url": "https://element-plus.org/en-US/component/drawer.html#events"}, {"name": "closed", "description": "Triggered after <PERSON><PERSON> closing animation ended", "doc-url": "https://element-plus.org/en-US/component/drawer.html#events"}, {"name": "open-auto-focus", "description": "triggers after Drawer opens and content focused", "doc-url": "https://element-plus.org/en-US/component/drawer.html#events"}, {"name": "close-auto-focus", "description": "triggers after <PERSON><PERSON> closed and content focused", "doc-url": "https://element-plus.org/en-US/component/drawer.html#events"}]}}, {"name": "el-dropdown", "source": {"symbol": "ElDropdown"}, "description": "Toggleable menu for displaying lists of links and actions.", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown", "props": [{"name": "type", "description": "menu button type, refer to `Button` Component, only works when `split-button` is true", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["'' | 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text'"], "default": "''"}, {"name": "size", "description": "menu size, also works on the split button", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["'' | 'large' | 'default' | 'small'"], "default": "''"}, {"name": "button-props", "description": "props for the button component, refer to [<PERSON>ton Attributes](./button.html#button-attributes)", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["object"]}, {"name": "max-height", "description": "the max height of menu", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["string", "number"], "default": "''"}, {"name": "split-button", "description": "whether a button group is displayed", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "whether to disable", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["boolean"], "default": "false"}, {"name": "placement", "description": "placement of pop menu", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end'"], "default": "bottom"}, {"name": "trigger", "description": "how to trigger", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["'hover' | 'click' | 'contextmenu'"], "default": "hover"}, {"name": "trigger-keys", "description": "specify which keys on the keyboard can trigger when pressed", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["string[]"], "default": "['Enter', '<PERSON>', 'ArrowDown', 'NumpadEnter']"}, {"name": "hide-on-click", "description": "whether to hide menu after clicking menu-item", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["boolean"], "default": "true"}, {"name": "show-timeout", "description": "delay time before show a dropdown (only works when trigger is `hover`)", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["number"], "default": "150"}, {"name": "hide-timeout", "description": "delay time before hide a dropdown (only works when trigger is `hover`)", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["number"], "default": "150"}, {"name": "role", "description": "the ARIA role attribute for the dropdown menu. Depending on the use case, you may want to change this to 'navigation'", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["'dialog' | 'grid' | 'group' | 'listbox' | 'menu' | 'navigation' | 'tooltip' | 'tree'"], "default": "menu"}, {"name": "tabindex", "description": "[tabindex](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex) of Dropdown", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["number", "string"], "default": "0"}, {"name": "popper-class", "description": "custom class name for Dropdown's dropdown", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["string"], "default": "''"}, {"name": "popper-options", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["object"], "default": "{modifiers: [{name: 'computeStyles',options: {gpuAcceleration: false}}]}"}, {"name": "teleported", "description": "whether the dropdown popup is teleported to the body", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["boolean"], "default": "true"}, {"name": "persistent", "description": "when dropdown inactive and `persistent` is `false` , dropdown menu will be destroyed", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-attributes", "type": ["boolean"], "default": "true"}], "slots": [{"name": "default", "description": "content of Dropdown. Notice: Must be a valid html dom element (ex. `<span>, <button> etc.`) or `el-component`, to attach the trigger listener", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-slots"}, {"name": "dropdown", "description": "content of the Dropdown Menu, usually a `<el-dropdown-menu>` element", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-slots"}], "js": {"events": [{"name": "click", "description": "if `split-button` is `true`, triggers when left button is clicked", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-events"}, {"name": "command", "description": "triggers when a dropdown item is clicked, the parameters is the command dispatched from the dropdown item", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-events"}, {"name": "visible-change", "description": "triggers when the dropdown appears/disappears, the param is true when it appears, and false otherwise", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-events"}]}}, {"name": "el-dropdown-menu", "source": {"symbol": "ElDropdownMenu"}, "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-menu", "slots": [{"name": "default", "description": "content of Dropdown Menu", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-menu-slots"}]}, {"name": "el-dropdown-item", "source": {"symbol": "ElDropdownItem"}, "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-item", "props": [{"name": "command", "description": "a command to be dispatched to <PERSON><PERSON>'s `command` callback", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-item-attributes", "type": ["string", "number", "object"]}, {"name": "disabled", "description": "whether the item is disabled", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-item-attributes", "type": ["boolean"], "default": "false"}, {"name": "divided", "description": "whether a divider is displayed", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-item-attributes", "type": ["boolean"], "default": "false"}, {"name": "icon", "description": "custom icon", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-item-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}], "slots": [{"name": "default", "description": "customize of Dropdown Item", "doc-url": "https://element-plus.org/en-US/component/dropdown.html#dropdown-item-slots"}]}, {"name": "el-empty", "source": {"symbol": "ElEmpty"}, "description": "Placeholder hints for empty states.", "doc-url": "https://element-plus.org/en-US/component/empty.html#empty", "props": [{"name": "image", "description": "image URL of empty", "doc-url": "https://element-plus.org/en-US/component/empty.html#attributes", "type": ["string"], "default": "''"}, {"name": "image-size", "description": "image size (width) of empty", "doc-url": "https://element-plus.org/en-US/component/empty.html#attributes", "type": ["number"]}, {"name": "description", "description": "description of empty", "doc-url": "https://element-plus.org/en-US/component/empty.html#attributes", "type": ["string"], "default": "''"}], "slots": [{"name": "default", "description": "content as bottom content", "doc-url": "https://element-plus.org/en-US/component/empty.html#slots"}, {"name": "image", "description": "content as image", "doc-url": "https://element-plus.org/en-US/component/empty.html#slots"}, {"name": "description", "description": "content as description", "doc-url": "https://element-plus.org/en-US/component/empty.html#slots"}]}, {"name": "el-form", "source": {"symbol": "ElForm"}, "description": "Form consists of `input`, `radio`, `select`, `checkbox` and so on. With form, you can collect, verify and submit data.", "doc-url": "https://element-plus.org/en-US/component/form.html#form", "props": [{"name": "model", "description": "Data of form component.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["Record<string, any>"]}, {"name": "rules", "description": "Validation rules of form.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": [{"name": "FormRules", "source": {"symbol": "FormRules"}}]}, {"name": "inline", "description": "Whether the form is inline.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["boolean"], "default": "false"}, {"name": "label-position", "description": "Position of label. If set to `'left'` or `'right'`, `label-width` prop is also required.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["'left' | 'right' | 'top'"], "default": "right"}, {"name": "label-width", "description": "Width of label, e.g. `'50px'`. All its direct child form items will inherit this value. `auto` is supported.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["string", "number"], "default": "''"}, {"name": "label-suffix", "description": "Suffix of the label.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["string"], "default": "''"}, {"name": "hide-required-asterisk", "description": "Whether to hide required fields should have a red asterisk (star) beside their labels.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["boolean"], "default": "false"}, {"name": "require-asterisk-position", "description": "Position of asterisk.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["'left' | 'right'"], "default": "left"}, {"name": "show-message", "description": "Whether to show the error message.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["boolean"], "default": "true"}, {"name": "inline-message", "description": "Whether to display the error message inline with the form item.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["boolean"], "default": "false"}, {"name": "status-icon", "description": "Whether to display an icon indicating the validation result.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["boolean"], "default": "false"}, {"name": "validate-on-rule-change", "description": "Whether to trigger validation when the `rules` prop is changed.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["boolean"], "default": "true"}, {"name": "size", "description": "Control the size of components in this form.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["'' | 'large' | 'default' | 'small'"]}, {"name": "disabled", "description": "Whether to disable all components in this form. If set to `true`, it will override the `disabled` prop of the inner component.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["boolean"], "default": "false"}, {"name": "scroll-to-error", "description": "When validation fails, scroll to the first error form entry.", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["boolean"], "default": "false"}, {"name": "scroll-into-view-options", "description": "When validation fails, it scrolls to the first error item based on the scrollIntoView option. [scrollIntoView](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView).", "doc-url": "https://element-plus.org/en-US/component/form.html#form-attributes", "type": ["Record<string, any>", "boolean"], "default": "true"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/form.html#form-slots"}], "js": {"events": [{"name": "validate", "description": "triggers after a form item is validated", "doc-url": "https://element-plus.org/en-US/component/form.html#form-events"}]}}, {"name": "el-form-item", "source": {"symbol": "ElFormItem"}, "doc-url": "https://element-plus.org/en-US/component/form.html#formitem", "props": [{"name": "prop", "description": "A key of `model`. It could be a path of the property (e.g `a.b.0` or `['a', 'b', '0']`). In the use of `validate` and `resetFields` method, the attribute is required.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["string", "string&#91;&#93;"]}, {"name": "label", "description": "Label text.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["string"]}, {"name": "label-position", "description": "Position of item label. If set to `'left'` or `'right'`, `label-width` prop is also required. Default extend `label-postion` of `form`.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["'left' | 'right' | 'top'"], "default": "''"}, {"name": "label-width", "description": "Width of label, e.g. `'50px'`. `'auto'` is supported.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["string", "number"], "default": "''"}, {"name": "required", "description": "Whether the field is required or not, will be determined by validation rules if omitted.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["boolean"]}, {"name": "rules", "description": "Validation rules of form, see the [following table](#formitemrule), more advanced usage at [async-validator](https://github.com/yiminghe/async-validator).", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": [{"name": "Arrayable<FormItemRule>", "source": {"symbol": "Arrayable"}}]}, {"name": "error", "description": "Field error message, set its value and the field will validate error and show this message immediately.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["string"]}, {"name": "show-message", "description": "Whether to show the error message.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["boolean"], "default": "true"}, {"name": "inline-message", "description": "Inline style validate message.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["string", "boolean"], "default": "''"}, {"name": "size", "description": "Control the size of components in this form-item.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["'' | 'large' | 'default' | 'small'"]}, {"name": "for", "description": "Same as for in native label.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["string"]}, {"name": "validate-status", "description": "Validation state of formItem.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-attributes", "type": ["'' | 'error' | 'validating' | 'success'"]}], "slots": [{"name": "default", "description": "Content of Form Item.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-slots"}, {"name": "label", "description": "Custom content to display on label.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-slots", "type": "{ label: string }"}, {"name": "error", "description": "Custom content to display validation message.", "doc-url": "https://element-plus.org/en-US/component/form.html#formitem-slots", "type": "{ error: string }"}]}, {"name": "el-icon", "source": {"symbol": "ElIcon"}, "description": "Element Plus provides a set of common icons.", "doc-url": "https://element-plus.org/en-US/component/icon.html#icon", "props": [{"name": "color", "description": "SVG tag's fill attribute", "doc-url": "https://element-plus.org/en-US/component/icon.html#attributes", "type": ["string"], "default": "inherit from color"}, {"name": "size", "description": "SVG icon size, size x size", "doc-url": "https://element-plus.org/en-US/component/icon.html#attributes", "type": ["number", "string"], "default": "inherit from font size"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/icon.html#slots"}]}, {"name": "el-image", "source": {"symbol": "ElImage"}, "description": "Besides the native features of img, support lazy load, custom placeholder and load failure, etc.", "doc-url": "https://element-plus.org/en-US/component/image.html#image", "props": [{"name": "src", "description": "image source, same as native.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["string"], "default": "''"}, {"name": "fit", "description": "indicate how the image should be resized to fit its container, same as [object-fit](https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit).", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["'' | 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'"], "default": "''"}, {"name": "hide-on-click-modal", "description": "when enabling preview, use this flag to control whether clicking on backdrop can exit preview mode.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["boolean"], "default": "false"}, {"name": "loading", "description": "Indicates how the browser should load the image, same as [native](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attr-loading).", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["'eager' | 'lazy'"]}, {"name": "lazy", "description": "whether to use lazy load.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["boolean"], "default": "false"}, {"name": "scroll-container", "description": "the container to add scroll listener when using lazy load. By default, the container to add scroll listener when using lazy load.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["string", "HTMLElement"]}, {"name": "alt", "description": "native attribute `alt`.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["string"]}, {"name": "referrerpolicy", "description": "native attribute [referrerPolicy](https://developer.mozilla.org/en-US/docs/Web/API/HTMLImageElement/referrerPolicy).", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["string"]}, {"name": "crossorigin", "description": "native attribute [crossorigin](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/crossorigin).", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["'' | 'anonymous' | 'use-credentials'"]}, {"name": "preview-src-list", "description": "allow big image preview.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["string[]"], "default": "[]"}, {"name": "z-index", "description": "set image preview z-index.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["number"]}, {"name": "initial-index", "description": "initial preview image index, less than the length of `url-list`.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["number"], "default": "0"}, {"name": "close-on-press-escape", "description": "whether the image-viewer can be closed by pressing ESC.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["boolean"], "default": "true"}, {"name": "preview-teleported", "description": "whether to append image-viewer to body. A nested parent element attribute transform should have this attribute set to `true`.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["boolean"], "default": "false"}, {"name": "infinite", "description": "whether the viewer preview is infinite.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["boolean"], "default": "true"}, {"name": "zoom-rate", "description": "the zoom rate of the image viewer zoom event.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["number"], "default": "1.2"}, {"name": "min-scale", "description": "the min scale of the image viewer zoom event.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["number"], "default": "0.2"}, {"name": "max-scale", "description": "the max scale of the image viewer zoom event.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["number"], "default": "7"}, {"name": "show-progress", "description": "whether to display the preview image progress content.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "placeholder", "description": "custom placeholder content when image hasn't loaded yet.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-slots"}, {"name": "error", "description": "custom image load failed content.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-slots"}, {"name": "viewer", "description": "custom content when image preview.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-slots"}, {"name": "progress", "description": "custom progress content when image preview. (Priority is higher than `show-progress` prop)", "doc-url": "https://element-plus.org/en-US/component/image.html#image-slots", "type": "{ activeIndex: number, total: number }"}, {"name": "toolbar", "description": "custom toolbar content when image preview.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-slots", "type": "{actions: (action: ImageViewerAction, options?: ImageViewerActionOptions ) => void, prev: ()=> void, next: () => void,reset: () => void, activeIndex: number }, setActiveItem: (index: number) => void"}], "js": {"events": [{"name": "load", "description": "same as native load.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-events"}, {"name": "error", "description": "same as native error.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-events"}, {"name": "switch", "description": "trigger when switching images.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-events"}, {"name": "close", "description": "trigger when clicking on close button or when `hide-on-click-modal` enabled clicking on backdrop.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-events"}, {"name": "show", "description": "trigger when the viewer displays", "doc-url": "https://element-plus.org/en-US/component/image.html#image-events"}]}}, {"name": "el-image-viewer", "source": {"symbol": "ElImageViewer"}, "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer", "props": [{"name": "url-list", "description": "preview link list.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["string[]"], "default": "[]"}, {"name": "z-index", "description": "preview backdrop z-index.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["number", "string"]}, {"name": "initial-index", "description": "the initial preview image index, less than or equal to the length of `url-list`.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["number"], "default": "0"}, {"name": "infinite", "description": "whether preview is infinite.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["boolean"], "default": "true"}, {"name": "hide-on-click-modal", "description": "whether user can emit close event when clicking backdrop.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["boolean"], "default": "false"}, {"name": "teleported", "description": "whether to append image itself to body. A nested parent element attribute transform should have this attribute set to `true`.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["boolean"], "default": "false"}, {"name": "zoom-rate", "description": "the zoom rate of the image viewer zoom event.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["number"], "default": "1.2"}, {"name": "min-scale", "description": "the min scale of the image viewer zoom event.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["number"], "default": "0.2"}, {"name": "max-scale", "description": "the max scale of the image viewer zoom event.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["number"], "default": "7"}, {"name": "close-on-press-escape", "description": "whether the image-viewer can be closed by pressing ESC.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["boolean"], "default": "true"}, {"name": "show-progress", "description": "whether to display the preview image progress content", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "close", "description": "trigger when clicking on close button or when `hide-on-click-modal` enabled clicking on backdrop.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-events"}, {"name": "switch", "description": "trigger when switching images.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-events"}, {"name": "rotate", "description": "trigger when rotating images.", "doc-url": "https://element-plus.org/en-US/component/image.html#image-viewer-events"}]}}, {"name": "el-input-number", "source": {"symbol": "ElInputNumber"}, "description": "Input numerical values with a customizable range.", "doc-url": "https://element-plus.org/en-US/component/input-number.html#input-number", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["number", "null"]}, {"name": "min", "description": "the minimum allowed value", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["number"], "default": "-Infinity"}, {"name": "max", "description": "the maximum allowed value", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["number"], "default": "Infinity"}, {"name": "step", "description": "incremental step", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["number"], "default": "1"}, {"name": "step-strictly", "description": "whether input value can only be multiple of step", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "precision", "description": "precision of input value", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["number"]}, {"name": "size", "description": "size of the component", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["'large' | 'default' | 'small'"], "default": "default"}, {"name": "readonly", "description": "same as `readonly` in native input", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "whether the component is disabled", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "controls", "description": "whether to enable the control buttons", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "controls-position", "description": "position of the control buttons", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["'' | 'right'"]}, {"name": "name", "description": "same as `name` in native input", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["string"]}, {"name": "aria-label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["string"]}, {"name": "placeholder", "description": "same as `placeholder` in native input", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["string"]}, {"name": "id", "description": "same as `id` in native input", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["string"]}, {"name": "value-on-clear", "description": "value should be set when input box is cleared", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["number", "null", "'min' | 'max'"]}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/input-number.html#attributes", "type": ["string"]}], "slots": [{"name": "decrease-icon", "description": "custom input box button decrease icon", "doc-url": "https://element-plus.org/en-US/component/input-number.html#slots"}, {"name": "increase-icon", "description": "custom input box button increase icon", "doc-url": "https://element-plus.org/en-US/component/input-number.html#slots"}, {"name": "prefix", "description": "content as Input prefix", "doc-url": "https://element-plus.org/en-US/component/input-number.html#slots"}, {"name": "suffix", "description": "content as Input suffix", "doc-url": "https://element-plus.org/en-US/component/input-number.html#slots"}], "js": {"events": [{"name": "change", "description": "triggers when the value changes", "doc-url": "https://element-plus.org/en-US/component/input-number.html#events"}, {"name": "blur", "description": "triggers when Input blurs", "doc-url": "https://element-plus.org/en-US/component/input-number.html#events"}, {"name": "focus", "description": "triggers when Input focuses", "doc-url": "https://element-plus.org/en-US/component/input-number.html#events"}]}}, {"name": "el-input-tag", "source": {"symbol": "ElInputTag"}, "description": "The InputTag component allows users to add content as tags.", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#inputtag", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["string[]"]}, {"name": "max", "description": "max number tags that can be enter", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["number"]}, {"name": "tag-type", "description": "tag type", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["'' | 'success' | 'info' | 'warning' | 'danger'"], "default": "info"}, {"name": "tag-effect", "description": "tag effect", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["'' | 'light' | 'dark' | 'plain'"], "default": "light"}, {"name": "trigger", "description": "the key to trigger input tag", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["'Enter' | 'Space'"], "default": "Enter"}, {"name": "draggable", "description": "whether tags can be dragged", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "delimiter", "description": "add a tag when a delimiter is matched", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["string", {"name": "regex", "source": {"symbol": "regex"}}]}, {"name": "size", "description": "input box size", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "save-on-blur", "description": "whether to save the input value when the input loses focus", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "clearable", "description": "whether to show clear button", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "whether to disable input-tag", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "readonly", "description": "same as `readonly` in native input", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "autofocus", "description": "same as `autofocus` in native input", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "id", "description": "same as `id` in native input", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["string"]}, {"name": "tabindex", "description": "same as `tabindex` in native input", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["string", "number"]}, {"name": "maxlength", "description": "same as `maxlength` in native input", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["string", "number"]}, {"name": "minlength", "description": "same as `minlength` in native input", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["string", "number"]}, {"name": "placeholder", "description": "placeholder of input", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["string"]}, {"name": "autocomplete", "description": "same as `autocomplete` in native input", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["string"], "default": "off"}, {"name": "aria-label", "description": "native `aria-label` attribute", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#attributes", "type": ["string"]}], "slots": [{"name": "tag", "description": "content as tag", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#slots", "type": "{ value: string, index: number }"}, {"name": "prefix", "description": "content as InputTag prefix", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#slots"}, {"name": "suffix", "description": "content as InputTag suffix", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#slots"}], "js": {"events": [{"name": "change", "description": "triggers when the modelValue change", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#events"}, {"name": "input", "description": "triggers when the input value change", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#events"}, {"name": "add-tag", "description": "triggers when a tag is added", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#events"}, {"name": "remove-tag", "description": "triggers when a tag is removed", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#events"}, {"name": "focus", "description": "triggers when InputTag focuses", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#events"}, {"name": "blur", "description": "triggers when InputTag blurs", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#events"}, {"name": "clear", "description": "triggers when the clear icon is clicked", "doc-url": "https://element-plus.org/en-US/component/input-tag.html#events"}]}}, {"name": "el-input", "source": {"symbol": "ElInput"}, "description": "Input data using mouse or keyboard.", "doc-url": "https://element-plus.org/en-US/component/input.html#input", "props": [{"name": "type", "description": "type of input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["'text' | 'textarea' | 'password' | 'button' | 'checkbox' | 'file' | 'number' | 'radio' | ..."], "default": "text"}, {"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string", "number"]}, {"name": "maxlength", "description": "same as `maxlength` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string", "number"]}, {"name": "minlength", "description": "same as `minlength` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string", "number"]}, {"name": "show-word-limit", "description": "whether show word count, only works when `type` is 'text' or 'textarea'", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "placeholder", "description": "placeholder of Input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string"]}, {"name": "clearable", "description": "whether to show clear button, only works when `type` is not 'textarea'", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "formatter", "description": "specifies the format of the value presented input.(only works when `type` is 'text')", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["(value: string | number) => string"]}, {"name": "parser", "description": "specifies the value extracted from formatter input.(only works when `type` is 'text')", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["(value: string) => string"]}, {"name": "show-password", "description": "whether to show toggleable password input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "whether Input is disabled", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "size of Input, works when `type` is not 'textarea'", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "prefix-icon", "description": "prefix icon component", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "suffix-icon", "description": "suffix icon component", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "rows", "description": "number of rows of textarea, only works when `type` is 'textarea'", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["number"], "default": "2"}, {"name": "autosize", "description": "whether textarea has an adaptive height, only works when `type` is 'textarea'. Can accept an object, e.g. `{ minRows: 2, maxRows: 6 }`", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["boolean", "{ minRows?: number, maxRows?: number }"], "default": "false"}, {"name": "autocomplete", "description": "same as `autocomplete` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string"], "default": "off"}, {"name": "name", "description": "same as `name` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string"]}, {"name": "readonly", "description": "same as `readonly` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "max", "description": "same as `max` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes"}, {"name": "min", "description": "same as `min` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes"}, {"name": "step", "description": "same as `step` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes"}, {"name": "resize", "description": "control the resizability", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["'none' | 'both' | 'horizontal' | 'vertical'"]}, {"name": "autofocus", "description": "same as `autofocus` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "form", "description": "same as `form` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string"]}, {"name": "aria-label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string"]}, {"name": "tabindex", "description": "input tabindex", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string", "number"]}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "input-style", "description": "the style of the input element or textarea element", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string", {"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}, {"name": "CSSProperties[]", "source": {"symbol": "CSSProperties", "module": "vue"}}, "string[]"], "default": "{}"}, {"name": "label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/input.html#attributes", "type": ["string"]}], "slots": [{"name": "prefix", "description": "content as Input prefix, only works when `type` is not 'textarea'", "doc-url": "https://element-plus.org/en-US/component/input.html#slots"}, {"name": "suffix", "description": "content as Input suffix, only works when `type` is not 'textarea'", "doc-url": "https://element-plus.org/en-US/component/input.html#slots"}, {"name": "prepend", "description": "content to prepend before Input, only works when `type` is not 'textarea'", "doc-url": "https://element-plus.org/en-US/component/input.html#slots"}, {"name": "append", "description": "content to append after Input, only works when `type` is not 'textarea'", "doc-url": "https://element-plus.org/en-US/component/input.html#slots"}], "js": {"events": [{"name": "blur", "description": "triggers when Input blurs", "doc-url": "https://element-plus.org/en-US/component/input.html#events"}, {"name": "focus", "description": "triggers when Input focuses", "doc-url": "https://element-plus.org/en-US/component/input.html#events"}, {"name": "change", "description": "triggers when the input box loses focus or the user presses Enter, only if the modelValue has changed", "doc-url": "https://element-plus.org/en-US/component/input.html#events"}, {"name": "input", "description": "triggers when the Input value change", "doc-url": "https://element-plus.org/en-US/component/input.html#events"}, {"name": "clear", "description": "triggers when the Input is cleared by clicking the clear button", "doc-url": "https://element-plus.org/en-US/component/input.html#events"}]}}, {"name": "el-row", "source": {"symbol": "ElRow"}, "doc-url": "https://element-plus.org/en-US/component/layout.html#row", "props": [{"name": "gutter", "description": "grid spacing", "doc-url": "https://element-plus.org/en-US/component/layout.html#row-attributes", "type": ["number"], "default": "0"}, {"name": "justify", "description": "horizontal alignment of flex layout", "doc-url": "https://element-plus.org/en-US/component/layout.html#row-attributes", "type": ["'start' | 'end' | 'center' | 'space-around' | 'space-between' | 'space-evenly'"], "default": "start"}, {"name": "align", "description": "vertical alignment of flex layout", "doc-url": "https://element-plus.org/en-US/component/layout.html#row-attributes", "type": ["'top' | 'middle' | 'bottom'"]}, {"name": "tag", "description": "custom element tag", "doc-url": "https://element-plus.org/en-US/component/layout.html#row-attributes", "type": ["string"], "default": "div"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/layout.html#row-slots"}]}, {"name": "el-col", "source": {"symbol": "ElCol"}, "doc-url": "https://element-plus.org/en-US/component/layout.html#col", "props": [{"name": "span", "description": "number of column the grid spans", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["number"], "default": "24"}, {"name": "offset", "description": "number of spacing on the left side of the grid", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["number"], "default": "0"}, {"name": "push", "description": "number of columns that grid moves to the right", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["number"], "default": "0"}, {"name": "pull", "description": "number of columns that grid moves to the left", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["number"], "default": "0"}, {"name": "xs", "description": "`<768px` Responsive columns or column props object", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["number", "{span?: number, offset?: number, pull?: number, push?: number}"]}, {"name": "sm", "description": "`≥768px` Responsive columns or column props object", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["number", "{span?: number, offset?: number, pull?: number, push?: number}"]}, {"name": "md", "description": "`≥992px` Responsive columns or column props object", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["number", "{span?: number, offset?: number, pull?: number, push?: number}"]}, {"name": "lg", "description": "`≥1200px` Responsive columns or column props object", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["number", "{span?: number, offset?: number, pull?: number, push?: number}"]}, {"name": "xl", "description": "`≥1920px` Responsive columns or column props object", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["number", "{span?: number, offset?: number, pull?: number, push?: number}"]}, {"name": "tag", "description": "custom element tag", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-attributes", "type": ["string"], "default": "div"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/layout.html#col-slots"}]}, {"name": "el-link", "source": {"symbol": "ElLink"}, "description": "Text hyperlink", "doc-url": "https://element-plus.org/en-US/component/link.html#link", "props": [{"name": "type", "description": "type", "doc-url": "https://element-plus.org/en-US/component/link.html#attributes", "type": ["'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'"], "default": "default"}, {"name": "underline", "description": "when underlines should appear", "doc-url": "https://element-plus.org/en-US/component/link.html#attributes", "type": ["'always' | 'hover' | 'never'", "boolean"], "default": "hover"}, {"name": "disabled", "description": "whether the component is disabled", "doc-url": "https://element-plus.org/en-US/component/link.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "href", "description": "same as native hyperlink's `href`", "doc-url": "https://element-plus.org/en-US/component/link.html#attributes", "type": ["string"]}, {"name": "target", "description": "same as native hyperlink's `target`", "doc-url": "https://element-plus.org/en-US/component/link.html#attributes", "type": ["'_blank' | '_parent' | '_self' | '_top'"], "default": "\\_self"}, {"name": "icon", "description": "icon component", "doc-url": "https://element-plus.org/en-US/component/link.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/link.html#slots"}, {"name": "icon", "description": "customize icon component", "doc-url": "https://element-plus.org/en-US/component/link.html#slots"}]}, {"name": "el-mention", "source": {"symbol": "ElMention"}, "description": "Used to mention someone or something in an input.", "doc-url": "https://element-plus.org/en-US/component/mention.html#mention", "props": [{"name": "options", "description": "mention options list", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": [{"name": "MentionOption[]", "source": {"symbol": "MentionOption"}}], "default": "[]"}, {"name": "prefix", "description": "prefix character to trigger mentions. The string length must be exactly 1", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["string", "string[]"], "default": "'@'"}, {"name": "split", "description": "character to split mentions. The string length must be exactly 1", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["string"], "default": "' '"}, {"name": "filter-option", "description": "customize filter option logic", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": [{"name": "false", "source": {"symbol": "false"}}, "(pattern: string, option: MentionOption) => boolean"]}, {"name": "placement", "description": "set popup placement", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["'bottom' | 'top'"], "default": "'bottom'"}, {"name": "show-arrow", "description": "whether the dropdown panel has an arrow", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "offset", "description": "offset of the dropdown panel", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["number"], "default": "0"}, {"name": "whole", "description": "when backspace is pressed to delete, whether the mention content is deleted as a whole", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "check-is-whole", "description": "when backspace is pressed to delete, check if the mention is a whole", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["(pattern: string, prefix: string) => boolean"]}, {"name": "loading", "description": "whether the dropdown panel of mentions is in a loading state", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "model-value", "description": "input value", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["string"]}, {"name": "popper-class", "description": "custom class name for dropdown panel", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": ["string"]}, {"name": "popper-options", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes", "type": [{"name": "object refer to", "source": {"symbol": "object refer to"}}]}, {"name": "[input props]", "doc-url": "https://element-plus.org/en-US/component/mention.html#attributes"}], "slots": [{"name": "label", "description": "content as option label", "doc-url": "https://element-plus.org/en-US/component/mention.html#slots", "type": "{ item: MentionOption, index: number }"}, {"name": "loading", "description": "content as option loading", "doc-url": "https://element-plus.org/en-US/component/mention.html#slots"}, {"name": "header", "description": "content at the top of the dropdown", "doc-url": "https://element-plus.org/en-US/component/mention.html#slots"}, {"name": "footer", "description": "content at the bottom of the dropdown", "doc-url": "https://element-plus.org/en-US/component/mention.html#slots"}, {"name": "[input slots]", "doc-url": "https://element-plus.org/en-US/component/mention.html#slots"}], "js": {"events": [{"name": "search", "description": "trigger when prefix hit", "doc-url": "https://element-plus.org/en-US/component/mention.html#events"}, {"name": "select", "description": "trigger when user select the option", "doc-url": "https://element-plus.org/en-US/component/mention.html#events"}, {"name": "[input events]", "doc-url": "https://element-plus.org/en-US/component/mention.html#events"}]}}, {"name": "el-menu", "source": {"symbol": "ElMenu"}, "description": "Menu that provides navigation for your website.", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu", "props": [{"name": "mode", "description": "menu display mode", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["'horizontal' | 'vertical'"], "default": "vertical"}, {"name": "collapse", "description": "whether the menu is collapsed (available only in vertical mode)", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["boolean"], "default": "false"}, {"name": "ellipsis", "description": "whether the menu is ellipsis (available only in horizontal mode)", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["boolean"], "default": "true"}, {"name": "ellipsis-icon", "description": "custom ellipsis icon (available only in horizontal mode and ellipsis is true)", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "popper-offset", "description": "offset of the popper (effective for all submenus)", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["number"], "default": "6"}, {"name": "default-active", "description": "index of active menu on page load", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["string"], "default": "''"}, {"name": "default-openeds", "description": "array that contains indexes of currently active sub-menus", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["string[]"], "default": "[]"}, {"name": "unique-opened", "description": "whether only one sub-menu can be active", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["boolean"], "default": "false"}, {"name": "menu-trigger", "description": "how sub-menus are triggered, only works when `mode` is 'horizontal'", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["'hover' | 'click'"], "default": "hover"}, {"name": "router", "description": "whether `vue-router` mode is activated. If true, index will be used as 'path' to activate the route action. Use with `default-active` to set the active item on load.", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["boolean"], "default": "false"}, {"name": "collapse-transition", "description": "whether to enable the collapse transition", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["boolean"], "default": "true"}, {"name": "popper-effect", "description": "Tooltip theme, built-in theme: `dark` / `light` when menu is collapsed", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["'dark' | 'light'", "string"], "default": "dark"}, {"name": "close-on-click-outside", "description": "optional, whether menu is collapsed when clicking outside", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["boolean"], "default": "false"}, {"name": "popper-class", "description": "custom class name for all popup menus", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["string"]}, {"name": "show-timeout", "description": "control timeout for all menus before showing", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["number"], "default": "300"}, {"name": "hide-timeout", "description": "control timeout for all menus before hiding", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["number"], "default": "300"}, {"name": "background-color", "description": "background color of Menu (hex format) (use `--el-menu-bg-color` in a style class instead)", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["string"], "default": "#ffffff"}, {"name": "text-color", "description": "text color of Menu (hex format) ( use `--el-menu-text-color` in a style class instead)", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["string"], "default": "#303133"}, {"name": "active-text-color", "description": "text color of currently active menu item (hex format) ( use `--el-menu-active-color` in a style class instead)", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["string"], "default": "#409eff"}, {"name": "persistent", "description": "when menu inactive and `persistent` is `false` , dropdown menu will be destroyed", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-attributes", "type": ["boolean"], "default": "true"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-slots"}], "js": {"events": [{"name": "select", "description": "callback function when menu is activated", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-events"}, {"name": "open", "description": "callback function when sub-menu expands", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-events"}, {"name": "close", "description": "callback function when sub-menu collapses", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-events"}]}}, {"name": "el-sub-menu", "source": {"symbol": "ElSubMenu"}, "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu", "props": [{"name": "index", "description": "unique identification", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["string"]}, {"name": "popper-class", "description": "custom class name for the popup menu", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["string"]}, {"name": "show-timeout", "description": "timeout before showing a sub-menu(inherit `show-timeout` of the menu by default.)", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["number"]}, {"name": "hide-timeout", "description": "timeout before hiding a sub-menu(inherit `hide-timeout` of the menu by default.)", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["number"]}, {"name": "disabled", "description": "whether the sub-menu is disabled", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["boolean"], "default": "false"}, {"name": "teleported", "description": "whether popup menu is teleported to the body, the default is true for the level one SubMenu, false for other SubMenus", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["boolean"], "default": "undefined"}, {"name": "popper-offset", "description": "offset of the popper (overrides the `popper` of menu)", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["number"]}, {"name": "expand-close-icon", "description": "Icon when menu are expanded and submenu are closed, `expand-close-icon` and `expand-open-icon` need to be passed together to take effect", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "expand-open-icon", "description": "Icon when menu are expanded and submenu are opened, `expand-open-icon` and `expand-close-icon` need to be passed together to take effect", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "collapse-close-icon", "description": "Icon when menu are collapsed and submenu are closed, `collapse-close-icon` and `collapse-open-icon` need to be passed together to take effect", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "collapse-open-icon", "description": "Icon when menu are collapsed and submenu are opened, `collapse-open-icon` and `collapse-close-icon` need to be passed together to take effect", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-slots"}, {"name": "title", "description": "customize title content", "doc-url": "https://element-plus.org/en-US/component/menu.html#submenu-slots"}]}, {"name": "el-menu-item", "source": {"symbol": "ElMenuItem"}, "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item", "props": [{"name": "index", "description": "unique identification", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-attributes", "type": ["string"]}, {"name": "route", "description": "Vue Router Route Location Parameters", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-attributes", "type": ["string", "object"]}, {"name": "disabled", "description": "whether disabled", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-slots"}, {"name": "title", "description": "customize title content", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-slots"}], "js": {"events": [{"name": "click", "description": "callback function when menu-item is clicked, the param is menu-item instance", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-events"}]}}, {"name": "el-menu-item-group", "source": {"symbol": "ElMenuItemGroup"}, "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-group", "props": [{"name": "title", "description": "group title", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-group-attributes", "type": ["string"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-group-slots"}, {"name": "title", "description": "customize group title", "doc-url": "https://element-plus.org/en-US/component/menu.html#menu-item-group-slots"}]}, {"name": "el-page-header", "source": {"symbol": "ElPageHeader"}, "description": "If path of the page is simple, it is recommended to use PageHeader instead of the Breadcrumb.", "doc-url": "https://element-plus.org/en-US/component/page-header.html#page-header", "props": [{"name": "icon", "description": "icon component of page header", "doc-url": "https://element-plus.org/en-US/component/page-header.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "Back"}, {"name": "title", "description": "main title of page header, default is Back that built-in a11y", "doc-url": "https://element-plus.org/en-US/component/page-header.html#attributes", "type": ["string"], "default": "''"}, {"name": "content", "description": "content of page header", "doc-url": "https://element-plus.org/en-US/component/page-header.html#attributes", "type": ["string"], "default": "''"}], "slots": [{"name": "icon", "description": "content as icon", "doc-url": "https://element-plus.org/en-US/component/page-header.html#slots"}, {"name": "title", "description": "content as title", "doc-url": "https://element-plus.org/en-US/component/page-header.html#slots"}, {"name": "content", "description": "content", "doc-url": "https://element-plus.org/en-US/component/page-header.html#slots"}, {"name": "extra", "description": "extra", "doc-url": "https://element-plus.org/en-US/component/page-header.html#slots"}, {"name": "breadcrumb", "description": "content as breadcrumb", "doc-url": "https://element-plus.org/en-US/component/page-header.html#slots"}, {"name": "default", "description": "main content", "doc-url": "https://element-plus.org/en-US/component/page-header.html#slots"}], "js": {"events": [{"name": "back", "description": "triggers when right side is clicked", "doc-url": "https://element-plus.org/en-US/component/page-header.html#events"}]}}, {"name": "el-pagination", "source": {"symbol": "ElPagination"}, "description": "If you have too much data to display in one page, use pagination.", "doc-url": "https://element-plus.org/en-US/component/pagination.html#pagination", "props": [{"name": "size", "description": "pagination size", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["'large' | 'default' | 'small'"], "default": "'default'"}, {"name": "background", "description": "whether the buttons have a background color", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "page-size", "description": "item count of each page", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["number"]}, {"name": "default-page-size", "description": "default initial value of page size, not setting is the same as setting 10", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["number"]}, {"name": "total", "description": "total item count", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["number"]}, {"name": "page-count", "description": "total page count. Set either `total` or `page-count` and pages will be displayed; if you need `page-sizes`, `total` is required", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["number"]}, {"name": "pager-count", "description": "number of pagers. Pagination collapses when the total page count exceeds this value", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["5", "7", "9", "11", "13", "15", "17", "19", "21"], "default": "7"}, {"name": "current-page", "description": "current page number", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["number"]}, {"name": "default-current-page", "description": "default initial value of current-page, not setting is the same as setting 1", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["number"]}, {"name": "layout", "description": "layout of Pagination, elements separated with a comma", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["string"], "default": "prev, pager, next, jumper, ->, total"}, {"name": "page-sizes", "description": "options of item count per page", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["number[]"], "default": "[10, 20, 30, 40, 50, 100]"}, {"name": "append-size-to", "description": "which element the size dropdown appends to", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["string"]}, {"name": "popper-class", "description": "custom class name for the page size Select's dropdown", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["string"], "default": "''"}, {"name": "prev-text", "description": "text for the prev button", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["string"], "default": "''"}, {"name": "prev-icon", "description": "icon for the prev button, has a lower priority than `prev-text`", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "ArrowLeft"}, {"name": "next-text", "description": "text for the next button", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["string"], "default": "''"}, {"name": "next-icon", "description": "icon for the next button, has a lower priority than `next-text`", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "ArrowRight"}, {"name": "disabled", "description": "whether Pagination is disabled", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "teleported", "description": "whether Pagination select dropdown is teleported to the body", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "hide-on-single-page", "description": "whether to hide when there's only one page", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "small", "description": "whether to use small pagination", "doc-url": "https://element-plus.org/en-US/component/pagination.html#attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "custom content. To use this, you need to declare `slot` in `layout`", "doc-url": "https://element-plus.org/en-US/component/pagination.html#slots"}], "js": {"events": [{"name": "size-change", "description": "triggers when `page-size` changes", "doc-url": "https://element-plus.org/en-US/component/pagination.html#events"}, {"name": "current-change", "description": "triggers when `current-page` changes", "doc-url": "https://element-plus.org/en-US/component/pagination.html#events"}, {"name": "change", "description": "triggers when `current-page` or `page-size` changes", "doc-url": "https://element-plus.org/en-US/component/pagination.html#events"}, {"name": "prev-click", "description": "triggers when the prev button is clicked and current page changes", "doc-url": "https://element-plus.org/en-US/component/pagination.html#events"}, {"name": "next-click", "description": "triggers when the next button is clicked and current page changes", "doc-url": "https://element-plus.org/en-US/component/pagination.html#events"}]}}, {"name": "el-popconfirm", "source": {"symbol": "ElPopconfirm"}, "description": "A simple confirmation dialog of an element click action.", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#popconfirm", "props": [{"name": "title", "description": "Title", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["string"]}, {"name": "confirm-button-text", "description": "Confirm button text", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["string"]}, {"name": "cancel-button-text", "description": "Cancel button text", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["string"]}, {"name": "confirm-button-type", "description": "Confirm button type", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'"], "default": "primary"}, {"name": "cancel-button-type", "description": "Cancel button type", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'"], "default": "text"}, {"name": "icon", "description": "Icon Component", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "QuestionFilled"}, {"name": "icon-color", "description": "Icon color", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["string"], "default": "#f90"}, {"name": "hide-icon", "description": "is hide Icon", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hide-after", "description": "delay of disappear, in millisecond", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["number"], "default": "200"}, {"name": "teleported", "description": "whether popconfirm is teleported to the body", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "persistent", "description": "when popconfirm inactive and `persistent` is `false` , popconfirm will be destroyed", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "width", "description": "popconfirm width, min width 150px", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#attributes", "type": ["string", "number"], "default": "150"}], "slots": [{"name": "reference", "description": "HTML element that triggers Popconfirm", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#slots"}, {"name": "actions", "description": "content of the Popconfirm footer", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#slots", "type": "{ confirm: (e: <PERSON><PERSON><PERSON>) => void, cancel: (e: <PERSON>Event) => void }"}], "js": {"events": [{"name": "confirm", "description": "triggers when click confirm button", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#events"}, {"name": "cancel", "description": "triggers when click cancel button", "doc-url": "https://element-plus.org/en-US/component/popconfirm.html#events"}]}}, {"name": "el-popover", "source": {"symbol": "ElPopover"}, "description": "", "doc-url": "https://element-plus.org/en-US/component/popover.html#popover", "props": [{"name": "trigger", "description": "how the popover is triggered", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["'click' | 'focus' | 'hover' | 'contextmenu'"], "default": "hover"}, {"name": "trigger-keys", "description": "When you click the mouse to focus on the trigger element, you can define a set of keyboard codes to control the display of popover through the keyboard", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["Array"], "default": "['Enter','Space']"}, {"name": "title", "description": "popover title", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["string"]}, {"name": "effect", "description": "Tooltip theme, built-in theme: `dark` / `light`", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["'dark' | 'light'", "string"], "default": "light"}, {"name": "content", "description": "popover content, can be replaced with a default `slot`", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["string"], "default": "''"}, {"name": "width", "description": "popover width", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["string", "number"], "default": "150"}, {"name": "placement", "description": "popover placement", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'"], "default": "bottom"}, {"name": "disabled", "description": "whether <PERSON><PERSON> is disabled", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "visible", "description": "whether popover is visible", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["boolean", "null"], "default": "null"}, {"name": "offset", "description": "popover offset, `Popover` is built with `Tooltip`, offset of `Popover` is `undefined`, but offset of `Tooltip` is 12", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["number"], "default": "undefined"}, {"name": "transition", "description": "popover transition animation, the default is el-fade-in-linear", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["string"]}, {"name": "show-arrow", "description": "whether a tooltip arrow is displayed or not. For more info, please refer to [ElPopper](https://github.com/element-plus/element-plus/tree/dev/packages/components/popper)", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "popper-options", "description": "parameters for [popper.js](https://popper.js.org/docs/v2/)", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["object"], "default": "{modifiers: [{name: 'computeStyles',options: {gpuAcceleration: false}}]}"}, {"name": "popper-class", "description": "custom class name for popover", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["string"]}, {"name": "popper-style", "description": "custom style for popover", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["string", "object"]}, {"name": "show-after", "description": "delay of appearance, in millisecond", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["number"], "default": "0"}, {"name": "hide-after", "description": "delay of disappear, in millisecond", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["number"], "default": "200"}, {"name": "auto-close", "description": "timeout in milliseconds to hide tooltip", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["number"], "default": "0"}, {"name": "tabindex", "description": "[tabindex](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex) of Popover", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["number", "string"], "default": "0"}, {"name": "teleported", "description": "whether popover dropdown is teleported to the body", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "append-to", "description": "which element the popover CONTENT appends to", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": [{"name": "CSSSelector", "source": {"symbol": "CSSSelector"}}, "HTMLElement"], "default": "body"}, {"name": "persistent", "description": "when popover inactive and `persistent` is `false` , popover will be destroyed", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "virtual-triggering", "description": "Indicates whether virtual triggering is enabled", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["boolean"]}, {"name": "virtual-ref", "description": "Indicates the reference element to which the popover is attached", "doc-url": "https://element-plus.org/en-US/component/popover.html#attributes", "type": ["HTMLElement"]}], "slots": [{"name": "default", "description": "text content of popover", "doc-url": "https://element-plus.org/en-US/component/popover.html#slots"}, {"name": "reference", "description": "HTML element that triggers popover", "doc-url": "https://element-plus.org/en-US/component/popover.html#slots"}], "js": {"events": [{"name": "show", "description": "triggers when popover shows", "doc-url": "https://element-plus.org/en-US/component/popover.html#events"}, {"name": "before-enter", "description": "triggers when the entering transition before", "doc-url": "https://element-plus.org/en-US/component/popover.html#events"}, {"name": "after-enter", "description": "triggers when the entering transition ends", "doc-url": "https://element-plus.org/en-US/component/popover.html#events"}, {"name": "hide", "description": "triggers when popover hides", "doc-url": "https://element-plus.org/en-US/component/popover.html#events"}, {"name": "before-leave", "description": "triggers when the leaving transition before", "doc-url": "https://element-plus.org/en-US/component/popover.html#events"}, {"name": "after-leave", "description": "triggers when the leaving transition ends", "doc-url": "https://element-plus.org/en-US/component/popover.html#events"}]}}, {"name": "el-progress", "source": {"symbol": "ElProgress"}, "description": "Progress is used to show the progress of current operation, and inform the user the current status.", "doc-url": "https://element-plus.org/en-US/component/progress.html#progress", "props": [{"name": "percentage", "description": "percentage", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "default": "0"}, {"name": "type", "description": "the type of progress bar", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["'line' | 'circle' | 'dashboard'"], "default": "line"}, {"name": "stroke-width", "description": "the width of progress bar", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["number"], "default": "6"}, {"name": "text-inside", "description": "whether to place the percentage inside progress bar, only works when `type` is 'line'", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "status", "description": "the current status of progress bar", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["'success' | 'exception' | 'warning'"]}, {"name": "indeterminate", "description": "set indeterminate progress", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "duration", "description": "control the animation duration of indeterminate progress or striped flow progress", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["number"], "default": "3"}, {"name": "color", "description": "background color of progress bar. Overrides `status` prop", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["string", "(percentage: number) => string", "{ color: string; percentage: number }[]"], "default": "''"}, {"name": "width", "description": "the canvas width of circle progress bar", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["number"], "default": "126"}, {"name": "show-text", "description": "whether to show percentage", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "stroke-linecap", "description": "circle/dashboard type shape at the end path", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["'butt' | 'round' | 'square'"], "default": "round"}, {"name": "format", "description": "custom text format", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["(percentage: number) => string"]}, {"name": "striped", "description": "stripe over the progress bar's color", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "striped-flow", "description": "get the stripes to flow", "doc-url": "https://element-plus.org/en-US/component/progress.html#attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "Customized content", "doc-url": "https://element-plus.org/en-US/component/progress.html#slots", "type": "{ percentage: number }"}]}, {"name": "el-radio", "source": {"symbol": "ElRadio"}, "description": "Single selection among multiple options.", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio-attributes", "type": ["string", "number", "boolean"]}, {"name": "value", "description": "the value of Radio", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio-attributes", "type": ["string", "number", "boolean"]}, {"name": "label", "description": "the label of Radio. If there's no `value`, `label` will act as `value`", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio-attributes", "type": ["string", "number", "boolean"]}, {"name": "disabled", "description": "whether Radio is disabled", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio-attributes", "type": ["boolean"], "default": "false"}, {"name": "border", "description": "whether to add a border around Radio", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio-attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "size of the Radio", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio-attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "name", "description": "native `name` attribute", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio-attributes", "type": ["string"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio-slots"}], "js": {"events": [{"name": "change", "description": "triggers when the bound value changes", "doc-url": "https://element-plus.org/en-US/component/radio.html#radio-events"}]}}, {"name": "el-radio-group", "source": {"symbol": "ElRadioGroup"}, "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["string", "number", "boolean"]}, {"name": "size", "description": "the size of radio buttons or bordered radios", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["string"], "default": "default"}, {"name": "disabled", "description": "whether the nesting radios are disabled", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["boolean"], "default": "false"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["boolean"], "default": "true"}, {"name": "text-color", "description": "font color when button is active", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["string"], "default": "#ffffff"}, {"name": "fill", "description": "border and background color when button is active", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["string"], "default": "#409eff"}, {"name": "aria-label", "description": "same as `aria-label` in RadioGroup", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["string"]}, {"name": "name", "description": "native `name` attribute", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["string"]}, {"name": "id", "description": "native `id` attribute", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["string"]}, {"name": "label", "description": "same as `aria-label` in RadioGroup", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-attributes", "type": ["string"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-slots"}], "js": {"events": [{"name": "change", "description": "triggers when the bound value changes", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiogroup-events"}]}}, {"name": "el-radio-button", "source": {"symbol": "ElRadioButton"}, "doc-url": "https://element-plus.org/en-US/component/radio.html#radiobutton", "props": [{"name": "value", "description": "the value of Radio", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiobutton-attributes", "type": ["string", "number", "boolean"]}, {"name": "label", "description": "the label of Radio. If there's no `value`, `label` will act as `value`", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiobutton-attributes", "type": ["string", "number", "boolean"]}, {"name": "disabled", "description": "whether Radio is disabled", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiobutton-attributes", "type": ["boolean"], "default": "false"}, {"name": "name", "description": "native 'name' attribute", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiobutton-attributes", "type": ["string"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/radio.html#radiobutton-slots"}]}, {"name": "el-rate", "source": {"symbol": "ElRate"}, "description": "Used for rating", "doc-url": "https://element-plus.org/en-US/component/rate.html#rate", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["number"], "default": "0"}, {"name": "max", "description": "max rating score", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["number"], "default": "5"}, {"name": "size", "description": "size of Rate", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "disabled", "description": "whether Rate is read-only", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "allow-half", "description": "whether picking half start is allowed", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "low-threshold", "description": "threshold value between low and medium level. The value itself will be included in low level", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["number"], "default": "2"}, {"name": "high-threshold", "description": "threshold value between medium and high level. The value itself will be included in high level", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["number"], "default": "4"}, {"name": "colors", "description": "colors for icons. If array, it should have 3 elements, each of which corresponds with a score level, else if object, the key should be threshold value between two levels, and the value should be corresponding color", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string[]", "Record<number, string>"], "default": "['#f7ba2a', '#f7ba2a', '#f7ba2a']"}, {"name": "void-color", "description": "color of unselected icons", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string"], "default": "#c6d1de"}, {"name": "disabled-void-color", "description": "color of unselected read-only icons", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string"], "default": "#eff2f7"}, {"name": "icons", "description": "icon components. If array, it should have 3 elements, each of which corresponds with a score level, else if object, the key should be threshold value between two levels, and the value should be corresponding icon component", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string[]", {"name": "Component[]", "source": {"symbol": "Component", "module": "vue"}}, "Record<number, string | Component>"], "default": "[Star<PERSON><PERSON>d, StarFilled, StarFilled]"}, {"name": "void-icon", "description": "component of unselected icons", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "Star"}, {"name": "disabled-void-icon", "description": "component of unselected read-only icons", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "StarFilled"}, {"name": "show-text", "description": "whether to display texts", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-score", "description": "whether to display current score. show-score and show-text cannot be true at the same time", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "text-color", "description": "color of texts", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string"], "default": "''"}, {"name": "texts", "description": "text array", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string[]"], "default": "['Extremely bad', 'Disappointed', 'Fair', 'Satisfied', 'Surprise']"}, {"name": "score-template", "description": "score template", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string"], "default": "{value}"}, {"name": "clearable", "description": "whether value can be reset to `0`", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "id", "description": "native `id` attribute", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string"]}, {"name": "aria-label", "description": "same as `aria-label` in Rate", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string"]}, {"name": "label", "description": "same as `aria-label` in Rate", "doc-url": "https://element-plus.org/en-US/component/rate.html#attributes", "type": ["string"]}], "js": {"events": [{"name": "change", "description": "Triggers when rate value is changed", "doc-url": "https://element-plus.org/en-US/component/rate.html#events"}]}}, {"name": "el-result", "source": {"symbol": "ElResult"}, "description": "Used to give feedback on the result of user's operation or access exception.", "doc-url": "https://element-plus.org/en-US/component/result.html#result", "props": [{"name": "title", "description": "title of result", "doc-url": "https://element-plus.org/en-US/component/result.html#attributes", "type": ["string"], "default": "''"}, {"name": "sub-title", "description": "sub title of result", "doc-url": "https://element-plus.org/en-US/component/result.html#attributes", "type": ["string"], "default": "''"}, {"name": "icon", "description": "icon type of result", "doc-url": "https://element-plus.org/en-US/component/result.html#attributes", "type": ["'primary'  | 'success' | 'warning' | 'info' | 'error'"], "default": "info"}], "slots": [{"name": "icon", "description": "content as result icon", "doc-url": "https://element-plus.org/en-US/component/result.html#slots"}, {"name": "title", "description": "content as result title", "doc-url": "https://element-plus.org/en-US/component/result.html#slots"}, {"name": "sub-title", "description": "content as result sub title", "doc-url": "https://element-plus.org/en-US/component/result.html#slots"}, {"name": "extra", "description": "content as result extra area", "doc-url": "https://element-plus.org/en-US/component/result.html#slots"}]}, {"name": "el-scrollbar", "source": {"symbol": "ElScrollbar"}, "description": "Used to replace the browser's native scrollbar.", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#scrollbar", "props": [{"name": "height", "description": "height of scrollbar", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string", "number"]}, {"name": "max-height", "description": "max height of scrollbar", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string", "number"]}, {"name": "native", "description": "whether to use the native scrollbar style", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "wrap-style", "description": "style of wrap container", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string", {"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}, {"name": "CSSProperties[]", "source": {"symbol": "CSSProperties", "module": "vue"}}, "string[]"]}, {"name": "wrap-class", "description": "class of wrap container", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string"]}, {"name": "view-style", "description": "style of view", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string", {"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}, {"name": "CSSProperties[]", "source": {"symbol": "CSSProperties", "module": "vue"}}, "string[]"]}, {"name": "view-class", "description": "class of view", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string"]}, {"name": "noresize", "description": "do not respond to container size changes, if the container size does not change, it is better to set it to optimize performance", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "tag", "description": "element tag of the view", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string"], "default": "div"}, {"name": "always", "description": "always show scrollbar", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "min-size", "description": "minimum size of scrollbar", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["number"], "default": "20"}, {"name": "id", "description": "id of view", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string"]}, {"name": "role", "description": "role of view", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string"]}, {"name": "aria-label", "description": "aria-label of view", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["string"]}, {"name": "aria-orientation", "description": "aria-orientation of view", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["'horizontal' | 'vertical'"]}, {"name": "tabindex", "description": "tabindex of wrap container", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#attributes", "type": ["number", "string"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#slots"}], "js": {"events": [{"name": "scroll", "description": "triggers when scrolling, return distance of scrolling", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#events"}, {"name": "end-reached", "description": "triggers when the end of a scroll is triggered", "doc-url": "https://element-plus.org/en-US/component/scrollbar.html#events"}]}}, {"name": "el-segmented", "source": {"symbol": "ElSegmented"}, "description": "Display multiple options and allow users to select a single option.", "doc-url": "https://element-plus.org/en-US/component/segmented.html#segmented", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["string", "number", "boolean"]}, {"name": "options", "description": "data of the options", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": [{"name": "Option[]", "source": {"symbol": "Option"}}], "default": "[]"}, {"name": "props", "description": "configuration options, see the following table", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["object"]}, {"name": "size", "description": "size of component", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["'' | 'large' | 'default' | 'small'"], "default": "''"}, {"name": "block", "description": "fit width of parent content", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["boolean"]}, {"name": "disabled", "description": "whether segmented is disabled", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "name", "description": "native `name` attribute", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["string"]}, {"name": "id", "description": "native `id` attribute", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["string"]}, {"name": "aria-label", "description": "native `aria-label` attribute", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["string"]}, {"name": "direction", "description": "display direction", "doc-url": "https://element-plus.org/en-US/component/segmented.html#attributes", "type": ["'horizontal' | 'vertical'"], "default": "horizontal"}], "slots": [{"name": "default", "description": "option renderer", "doc-url": "https://element-plus.org/en-US/component/segmented.html#slots", "type": "{ item: Option }"}], "js": {"events": [{"name": "change", "description": "triggers when the selected value changes, the param is current selected value", "doc-url": "https://element-plus.org/en-US/component/segmented.html#events"}]}}, {"name": "el-virtualized-select", "source": {"symbol": "ElVirtualizedSelect"}, "description": ":::tip", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#virtualized-select", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string", "number", "boolean", "object", "array"]}, {"name": "options", "description": "data of the options, the key of `value` and `label` can be customize by `props`", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["array"]}, {"name": "props", "description": "configuration options, see the following table", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["object"]}, {"name": "multiple", "description": "is multiple", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "is disabled", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "value-key", "description": "unique identity key name for value, required when value is an object", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string"], "default": "value"}, {"name": "size", "description": "size of component", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["'' | 'large' | 'default' | 'small'"], "default": "''"}, {"name": "clearable", "description": "whether select can be cleared", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "clear-icon", "description": "custom clear icon", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "CircleClose"}, {"name": "collapse-tags", "description": "whether to collapse tags to a text when multiple selecting", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "multiple-limit", "description": "maximum number of options user can select when multiple is true. No limit when set to 0", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["number"], "default": "0"}, {"name": "name", "description": "the name attribute of select input", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string"]}, {"name": "effect", "description": "tooltip theme, built-in theme: `dark` / `light`", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["'dark' | 'light'", "string"], "default": "light"}, {"name": "autocomplete", "description": "autocomplete of select input", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string"], "default": "off"}, {"name": "placeholder", "description": "placeholder", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string"], "default": "Please select"}, {"name": "filterable", "description": "is filterable", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "allow-create", "description": "whether creating new items is allowed. To use this, `filterable` must be true", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "filter-method", "description": "custom filter method", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["() => void"]}, {"name": "loading", "description": "whether Select is loading data from server", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "loading-text", "description": "displayed text while loading data from server, default is 'Loading'", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string"]}, {"name": "reserve-keyword", "description": "whether reserve the keyword after select filtered option.", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "default-first-option", "description": "select first matching option on enter key. Use with `filterable` or `remote`", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "no-match-text", "description": "displayed text when no data matches the filtering query, you can also use slot `empty`, default is 'No matching data'", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string"]}, {"name": "no-data-text", "description": "displayed text when there is no options, you can also use slot empty", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string"], "default": "No Data"}, {"name": "popper-class", "description": "custom class name for Select's dropdown", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string"], "default": "''"}, {"name": "teleported", "description": "whether select dropdown is teleported, if `true` it will be teleported to where `append-to` sets", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "append-to", "description": "which element the select dropdown appends to", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": [{"name": "CSSSelector", "source": {"symbol": "CSSSelector"}}, "HTMLElement"]}, {"name": "persistent", "description": "when select dropdown is inactive and `persistent` is `false`, select dropdown will be destroyed", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "popper-options", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": [{"name": "objectrefer to  doc", "source": {"symbol": "objectrefer to  doc"}}], "default": "{}"}, {"name": "automatic-dropdown", "description": "for non-filterable Select, this prop decides if the option menu pops up when the input is focused", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "fit-input-width", "description": "whether the width of the dropdown is the same as the input, if the value is `number`, then the width is fixed", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean", "number"], "default": "true"}, {"name": "suffix-icon", "description": "custom suffix icon component", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "ArrowDown"}, {"name": "height", "description": "The height of the dropdown panel, 34px for each item", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["number"], "default": "274"}, {"name": "item-height", "description": "The height of the dropdown item", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["number"], "default": "34"}, {"name": "scrollbar-always-on", "description": "Controls whether the scrollbar is always displayed", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "remote", "description": "whether search data from server", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "remote-method", "description": "function that gets called when the input value changes. Its parameter is the current input value. To use this, `filterable` must be true", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["(keyword: string) => void"]}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "offset", "description": "offset of the dropdown", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["number"], "default": "12"}, {"name": "show-arrow", "description": "whether the dropdown has an arrow", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "placement", "description": "position of dropdown", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'"], "default": "bottom-start"}, {"name": "fallback-placements", "description": "list of possible positions for dropdown [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements)", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": [{"name": "Placement[]", "source": {"symbol": "Placement"}}], "default": "['bottom-start', 'top-start', 'right', 'left']"}, {"name": "collapse-tags-tooltip", "description": "whether show all selected tags when mouse hover text of collapse-tags. To use this, `collapse-tags` must be true", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "max-collapse-tags", "description": "The max tags number to be shown. To use this, `collapse-tags` must be true", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["number"], "default": "1"}, {"name": "tag-type", "description": "tag type", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["'' | 'success' | 'info' | 'warning' | 'danger'"], "default": "info"}, {"name": "tag-effect", "description": "tag effect", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["'' | 'light' | 'dark' | 'plain'"], "default": "light"}, {"name": "aria-label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string"]}, {"name": "empty-values", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["array"]}, {"name": "value-on-clear", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string", "number", "boolean", "Function"]}, {"name": "popper-append-to-body", "description": "whether to append the popper menu to body. If the positioning of the popper is wrong, you can try to set this prop to false", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "tabindex", "description": "tabindex for input", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#attributes", "type": ["string", "number"]}], "slots": [{"name": "default", "description": "Option renderer", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#slots"}, {"name": "header", "description": "content at the top of the dropdown", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#slots"}, {"name": "footer", "description": "content at the bottom of the dropdown", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#slots"}, {"name": "empty", "description": "content when options is empty", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#slots"}, {"name": "prefix", "description": "prefix content of input", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#slots"}, {"name": "tag", "description": "content as Select tag", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#slots"}, {"name": "loading", "description": "content as Select loading", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#slots"}, {"name": "label", "description": "content as Select label", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#slots"}], "js": {"events": [{"name": "change", "description": "triggers when the selected value changes, the param is current selected value", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#events"}, {"name": "visible-change", "description": "triggers when the dropdown appears/disappears, the param will be true when it appears, and false otherwise", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#events"}, {"name": "remove-tag", "description": "triggers when a tag is removed in multiple mode, the param is removed tag value", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#events"}, {"name": "clear", "description": "triggers when the clear icon is clicked in a clearable Select", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#events"}, {"name": "blur", "description": "triggers when Input blurs", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#events"}, {"name": "focus", "description": "triggers when Input focuses", "doc-url": "https://element-plus.org/en-US/component/select-v2.html#events"}]}}, {"name": "el-select", "source": {"symbol": "ElSelect"}, "description": "When there are plenty of options, use a drop-down menu to display and select desired ones.", "doc-url": "https://element-plus.org/en-US/component/select.html#select", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string", "number", "boolean", "object", "array"]}, {"name": "multiple", "description": "whether multiple-select is activated", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "whether Select is disabled", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "value-key", "description": "unique identity key name for value, required when value is an object", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string"], "default": "value"}, {"name": "size", "description": "size of Input", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["'' | 'large' | 'default' | 'small'"]}, {"name": "clearable", "description": "whether select can be cleared", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "collapse-tags", "description": "whether to collapse tags to a text when multiple selecting", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "collapse-tags-tooltip", "description": "whether show all selected tags when mouse hover text of collapse-tags. To use this, `collapse-tags` must be true", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "multiple-limit", "description": "maximum number of options user can select when `multiple` is `true`. No limit when set to 0", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["number"], "default": "0"}, {"name": "name", "description": "the name attribute of select input", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string"]}, {"name": "effect", "description": "tooltip theme, built-in theme: `dark` / `light`", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["'dark' | 'light'", "string"], "default": "light"}, {"name": "autocomplete", "description": "the autocomplete attribute of select input", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string"], "default": "off"}, {"name": "placeholder", "description": "placeholder, default is 'Select'", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string"]}, {"name": "filterable", "description": "whether Select is filterable", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "allow-create", "description": "whether creating new items is allowed. To use this, `filterable` must be true", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "filter-method", "description": "custom filter method", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["() => void"]}, {"name": "remote", "description": "whether options are loaded from server", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "remote-method", "description": "custom remote search method", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["() => void"]}, {"name": "remote-show-suffix", "description": "in remote search method show suffix icon", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "loading", "description": "whether Select is loading data from server", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "loading-text", "description": "displayed text while loading data from server, default is 'Loading'", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string"]}, {"name": "no-match-text", "description": "displayed text when no data matches the filtering query, you can also use slot `empty`, default is 'No matching data'", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string"]}, {"name": "no-data-text", "description": "displayed text when there is no options, you can also use slot `empty`, default is 'No data'", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string"]}, {"name": "popper-class", "description": "custom class name for Select's dropdown", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string"], "default": "''"}, {"name": "reserve-keyword", "description": "when `multiple` and `filterable` is true, whether to reserve current keyword after selecting an option", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "true"}, {"name": "default-first-option", "description": "select first matching option on enter key. Use with `filterable` or `remote`", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "teleported", "description": "whether select dropdown is teleported, if `true` it will be teleported to where `append-to` sets", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "true"}, {"name": "append-to", "description": "which element the select dropdown appends to", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": [{"name": "CSSSelector", "source": {"symbol": "CSSSelector"}}, "HTMLElement"]}, {"name": "persistent", "description": "when select dropdown is inactive and `persistent` is `false`, select dropdown will be destroyed", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "true"}, {"name": "automatic-dropdown", "description": "for non-filterable Select, this prop decides if the option menu pops up when the input is focused", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "clear-icon", "description": "custom clear icon component", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "CircleClose"}, {"name": "fit-input-width", "description": "whether the width of the dropdown is the same as the input", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "false"}, {"name": "suffix-icon", "description": "custom suffix icon component", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "ArrowDown"}, {"name": "tag-type", "description": "tag type", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["'' | 'success' | 'info' | 'warning' | 'danger'"], "default": "info"}, {"name": "tag-effect", "description": "tag effect", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["'' | 'light' | 'dark' | 'plain'"], "default": "light"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "true"}, {"name": "offset", "description": "offset of the dropdown", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["number"], "default": "12"}, {"name": "show-arrow", "description": "whether the dropdown has an arrow", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "true"}, {"name": "placement", "description": "position of dropdown", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'"], "default": "bottom-start"}, {"name": "fallback-placements", "description": "list of possible positions for dropdown [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements)", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": [{"name": "Placement[]", "source": {"symbol": "Placement"}}], "default": "['bottom-start', 'top-start', 'right', 'left']"}, {"name": "max-collapse-tags", "description": "the max tags number to be shown. To use this, `collapse-tags` must be true", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["number"], "default": "1"}, {"name": "popper-options", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": [{"name": "objectrefer to  doc", "source": {"symbol": "objectrefer to  doc"}}], "default": "{}"}, {"name": "aria-label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string"]}, {"name": "empty-values", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["array"]}, {"name": "value-on-clear", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string", "number", "boolean", "Function"]}, {"name": "suffix-transition", "description": "animation when dropdown appears/disappears icon", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["boolean"], "default": "true"}, {"name": "tabindex", "description": "tabindex for input", "doc-url": "https://element-plus.org/en-US/component/select.html#select-attributes", "type": ["string", "number"]}], "slots": [{"name": "default", "description": "option component list", "doc-url": "https://element-plus.org/en-US/component/select.html#select-slots"}, {"name": "header", "description": "content at the top of the dropdown", "doc-url": "https://element-plus.org/en-US/component/select.html#select-slots"}, {"name": "footer", "description": "content at the bottom of the dropdown", "doc-url": "https://element-plus.org/en-US/component/select.html#select-slots"}, {"name": "prefix", "description": "content as Select prefix", "doc-url": "https://element-plus.org/en-US/component/select.html#select-slots"}, {"name": "empty", "description": "content when there is no options", "doc-url": "https://element-plus.org/en-US/component/select.html#select-slots"}, {"name": "tag", "description": "content as Select tag", "doc-url": "https://element-plus.org/en-US/component/select.html#select-slots"}, {"name": "loading", "description": "content as Select loading", "doc-url": "https://element-plus.org/en-US/component/select.html#select-slots"}, {"name": "label", "description": "content as Select label", "doc-url": "https://element-plus.org/en-US/component/select.html#select-slots"}], "js": {"events": [{"name": "change", "description": "triggers when the selected value changes", "doc-url": "https://element-plus.org/en-US/component/select.html#select-events"}, {"name": "visible-change", "description": "triggers when the dropdown appears/disappears", "doc-url": "https://element-plus.org/en-US/component/select.html#select-events"}, {"name": "remove-tag", "description": "triggers when a tag is removed in multiple mode", "doc-url": "https://element-plus.org/en-US/component/select.html#select-events"}, {"name": "clear", "description": "triggers when the clear icon is clicked in a clearable Select", "doc-url": "https://element-plus.org/en-US/component/select.html#select-events"}, {"name": "blur", "description": "triggers when Input blurs", "doc-url": "https://element-plus.org/en-US/component/select.html#select-events"}, {"name": "focus", "description": "triggers when Input focuses", "doc-url": "https://element-plus.org/en-US/component/select.html#select-events"}, {"name": "popup-scroll", "description": "triggers when dropdown scrolls", "doc-url": "https://element-plus.org/en-US/component/select.html#select-events"}]}}, {"name": "el-option-group", "source": {"symbol": "ElOptionGroup"}, "doc-url": "https://element-plus.org/en-US/component/select.html#option-group", "props": [{"name": "label", "description": "name of the group", "doc-url": "https://element-plus.org/en-US/component/select.html#option-group-attributes", "type": ["string"]}, {"name": "disabled", "description": "whether to disable all options in this group", "doc-url": "https://element-plus.org/en-US/component/select.html#option-group-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/select.html#option-group-slots"}]}, {"name": "el-option", "source": {"symbol": "ElOption"}, "doc-url": "https://element-plus.org/en-US/component/select.html#option", "props": [{"name": "value", "description": "value of option", "doc-url": "https://element-plus.org/en-US/component/select.html#option-attributes", "type": ["string", "number", "boolean", "object"]}, {"name": "label", "description": "label of option, same as `value` if omitted", "doc-url": "https://element-plus.org/en-US/component/select.html#option-attributes", "type": ["string", "number"]}, {"name": "disabled", "description": "whether option is disabled", "doc-url": "https://element-plus.org/en-US/component/select.html#option-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/select.html#option-slots"}]}, {"name": "el-skeleton", "source": {"symbol": "ElSkeleton"}, "description": "When loading data, and you need a rich experience for visual and interactions for your end users, you can choose `skeleton`.", "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeleton", "props": [{"name": "animated", "description": "whether showing the animation", "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes", "type": ["boolean"], "default": "false"}, {"name": "count", "description": "how many fake items to render to the DOM", "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes", "type": ["number"], "default": "1"}, {"name": "loading", "description": "whether showing the real DOM", "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes", "type": ["boolean"], "default": "false"}, {"name": "rows", "description": "numbers of the row, only useful when no template slot were given", "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes", "type": ["number"], "default": "3"}, {"name": "throttle", "description": "rendering delay in milliseconds. Numbers represent delayed display, and can also be set to delay hide, for example `{ leading: 500, trailing: 500 }`. When needing to control the initial value of loading, you can set `{ initVal: true }`", "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeleton-attributes", "type": ["number", "{ leading?: number, trailing?: number, initVal?: boolean }"], "default": "0"}], "slots": [{"name": "default", "description": "real rendering DOM", "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeleton-slots"}, {"name": "template", "description": "content as rendering skeleton template", "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeleton-slots"}]}, {"name": "el-skeleton-item", "source": {"symbol": "ElSkeletonItem"}, "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeletonitem", "props": [{"name": "variant", "description": "the current rendering skeleton type", "doc-url": "https://element-plus.org/en-US/component/skeleton.html#skeletonitem-attributes", "type": ["'p' | 'text' | 'h1' | 'h3' | 'caption' | 'button' | 'image' | 'circle' | 'rect'"], "default": "text"}]}, {"name": "el-slider", "source": {"symbol": "ElSlider"}, "description": "Drag the slider within a fixed range.", "doc-url": "https://element-plus.org/en-US/component/slider.html#slider", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["number", "number[]"], "default": "0"}, {"name": "min", "description": "minimum value", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["number"], "default": "0"}, {"name": "max", "description": "maximum value", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["number"], "default": "100"}, {"name": "disabled", "description": "whether <PERSON><PERSON><PERSON> is disabled", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "step", "description": "step size", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["number"], "default": "1"}, {"name": "show-input", "description": "whether to display an input box, works when `range` is false", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-input-controls", "description": "whether to display control buttons when `show-input` is true", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "size", "description": "size of the slider wrapper, will not work in vertical mode", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["'' | 'large' | 'default' | 'small'"], "default": "default"}, {"name": "input-size", "description": "size of the input box, when set `size`, the default is the value of `size`", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["'' | 'large' | 'default' | 'small'"], "default": "default"}, {"name": "show-stops", "description": "whether to display breakpoints", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-tooltip", "description": "whether to display tooltip value", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "format-tooltip", "description": "format to display tooltip value", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["(value: number) => number", "string"]}, {"name": "range", "description": "whether to select a range", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "vertical", "description": "vertical mode", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "height", "description": "slider height, required in vertical mode", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["string"]}, {"name": "aria-label", "description": "native `aria-label` attribute", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["string"]}, {"name": "range-start-label", "description": "when `range` is true, screen reader label for the start of the range", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["string"]}, {"name": "range-end-label", "description": "when `range` is true, screen reader label for the end of the range", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["string"]}, {"name": "format-value-text", "description": "format to display the `aria-valuenow` attribute for screen readers", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["(value: number) => string"]}, {"name": "debounce", "description": "debounce delay when typing, in milliseconds, works when `show-input` is true", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["number"], "default": "300"}, {"name": "tooltip-class", "description": "custom class name for the tooltip", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["string"]}, {"name": "placement", "description": "position of Toolt<PERSON>", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'"], "default": "top"}, {"name": "marks", "description": "marks, type of key must be `number` and must in closed interval `[min, max]`, each mark can custom style", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": [{"name": "SliderMarks", "source": {"symbol": "SliderMarks"}}]}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "persistent", "description": "when slider tooltip inactive and `persistent` is `false` , popconfirm will be destroyed. `persistent` always be `false` when `show-tooltip ` is `false`", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "label", "description": "native `aria-label` attribute", "doc-url": "https://element-plus.org/en-US/component/slider.html#attributes", "type": ["string"]}], "js": {"events": [{"name": "change", "description": "triggers when the value changes (if the mouse is being dragged, this event only fires when the mouse is released)", "doc-url": "https://element-plus.org/en-US/component/slider.html#events"}, {"name": "input", "description": "triggers when the data changes (It'll be emitted in real time during sliding)", "doc-url": "https://element-plus.org/en-US/component/slider.html#events"}]}}, {"name": "el-space", "source": {"symbol": "ElSpace"}, "description": "Even though we have [<PERSON><PERSON><PERSON>]", "doc-url": "https://element-plus.org/en-US/component/space.html#space", "props": [{"name": "alignment", "description": "Controls the alignment of items", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["'center' | 'normal' | 'stretch' | ..."], "default": "center"}, {"name": "class", "description": "className", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["string", "object", "array"]}, {"name": "direction", "description": "Placement direction", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["'vertical' | 'horizontal'"], "default": "horizontal"}, {"name": "prefix-cls", "description": "Prefix for space-items", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["string"]}, {"name": "style", "description": "Extra style rules", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["string", {"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}, {"name": "CSSProperties[]", "source": {"symbol": "CSSProperties", "module": "vue"}}, "string[]"]}, {"name": "spacer", "description": "Spacer", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["string", "number", {"name": "VNode", "source": {"symbol": "VNode", "module": "vue"}}]}, {"name": "size", "description": "Spacing size", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["'default' | 'small' | 'large'", "number", "[number, number]"], "default": "small"}, {"name": "wrap", "description": "Auto wrapping", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "fill", "description": "Whether to fill the container", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "fill-ratio", "description": "Ratio of fill", "doc-url": "https://element-plus.org/en-US/component/space.html#attributes", "type": ["number"], "default": "100"}]}, {"name": "el-splitter", "source": {"symbol": "ElSplitter"}, "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitter", "props": [{"name": "layout", "description": "Layout direction of the splitter", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitter-attributes", "type": ["'horizontal' | 'vertical'"], "default": "horizontal"}], "js": {"events": [{"name": "resize-start", "description": "Triggered when starting to resize a panel, `index` is the drag bar index", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitter-events"}, {"name": "resize", "description": "Triggered while resizing a panel, `index` is the drag bar index", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitter-events"}, {"name": "resize-end", "description": "Triggered when panel resizing ends, `index` is the drag bar index", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitter-events"}]}}, {"name": "el-splitter-panel", "source": {"symbol": "ElSplitterPanel"}, "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel", "props": [{"name": "size", "description": "Size of the panel (in pixels or percentage)", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel-attributes", "type": ["string", "number"]}, {"name": "min", "description": "Minimum size of the panel (in pixels or percentage)", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel-attributes", "type": ["string", "number"]}, {"name": "max", "description": "Maximum size of the panel (in pixels or percentage)", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel-attributes", "type": ["string", "number"]}, {"name": "resizable", "description": "Whether the panel can be resized", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel-attributes", "type": ["boolean"], "default": "true"}, {"name": "collapsible", "description": "Whether the panel can be collapsed", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "Default content of the panel", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel-slots"}, {"name": "start-collapsible", "description": "Custom content for the start collapsible button", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel-slots"}, {"name": "end-collapsible", "description": "Custom content for the end collapsible button", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel-slots"}], "js": {"events": [{"name": "update:size", "description": "Triggered when panel size changes", "doc-url": "https://element-plus.org/en-US/component/splitter.html#splitterpanel-events"}]}}, {"name": "el-statistic", "source": {"symbol": "ElStatistic"}, "description": "Display statistics.", "doc-url": "https://element-plus.org/en-US/component/statistic.html#statistic", "slots": [{"name": "prefix", "description": "Numeric prefix", "doc-url": "https://element-plus.org/en-US/component/statistic.html#statistic-slots"}, {"name": "suffix", "description": "Suffixes for numeric values", "doc-url": "https://element-plus.org/en-US/component/statistic.html#statistic-slots"}, {"name": "title", "description": "Numeric titles", "doc-url": "https://element-plus.org/en-US/component/statistic.html#statistic-slots"}]}, {"name": "el-countdown", "source": {"symbol": "ElCountdown"}, "description": ":::demo Countdown component, support to add other components control countdown.", "doc-url": "https://element-plus.org/en-US/component/statistic.html#countdown", "slots": [{"name": "prefix", "description": "countdown value prefix", "doc-url": "https://element-plus.org/en-US/component/statistic.html#countdown-slots"}, {"name": "suffix", "description": "countdown value suffix", "doc-url": "https://element-plus.org/en-US/component/statistic.html#countdown-slots"}, {"name": "title", "description": "countdown title", "doc-url": "https://element-plus.org/en-US/component/statistic.html#countdown-slots"}]}, {"name": "el-steps", "source": {"symbol": "ElSteps"}, "description": "Guide the user to complete tasks in accordance with the process. Its steps can be set according to the actual application scenario and the number of the steps can't be less than 2.", "doc-url": "https://element-plus.org/en-US/component/steps.html#steps", "props": [{"name": "space", "description": "the spacing of each step, will be responsive if omitted. Supports percentage.", "doc-url": "https://element-plus.org/en-US/component/steps.html#steps-attributes", "type": ["number", "string"], "default": "''"}, {"name": "direction", "description": "display direction", "doc-url": "https://element-plus.org/en-US/component/steps.html#steps-attributes", "type": ["'vertical' | 'horizontal'"], "default": "horizontal"}, {"name": "active", "description": "current activation step", "doc-url": "https://element-plus.org/en-US/component/steps.html#steps-attributes", "type": ["number"], "default": "0"}, {"name": "process-status", "description": "status of current step", "doc-url": "https://element-plus.org/en-US/component/steps.html#steps-attributes", "type": ["'wait' | 'process' | 'finish' | 'error' | 'success'"], "default": "process"}, {"name": "finish-status", "description": "status of end step", "doc-url": "https://element-plus.org/en-US/component/steps.html#steps-attributes", "type": ["'wait' | 'process' | 'finish' | 'error' | 'success'"], "default": "finish"}, {"name": "align-center", "description": "center title and description", "doc-url": "https://element-plus.org/en-US/component/steps.html#steps-attributes", "type": ["boolean"]}, {"name": "simple", "description": "whether to apply simple theme", "doc-url": "https://element-plus.org/en-US/component/steps.html#steps-attributes", "type": ["boolean"]}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/steps.html#steps-slots"}]}, {"name": "el-step", "source": {"symbol": "ElStep"}, "doc-url": "https://element-plus.org/en-US/component/steps.html#step", "props": [{"name": "title", "description": "step title", "doc-url": "https://element-plus.org/en-US/component/steps.html#step-attributes", "type": ["string"], "default": "''"}, {"name": "description", "description": "step description", "doc-url": "https://element-plus.org/en-US/component/steps.html#step-attributes", "type": ["string"], "default": "''"}, {"name": "icon", "description": "step custom icon. Icons can be passed via named slot as well", "doc-url": "https://element-plus.org/en-US/component/steps.html#step-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "status", "description": "current status. It will be automatically set by Steps if not configured.", "doc-url": "https://element-plus.org/en-US/component/steps.html#step-attributes", "type": ["'' | 'wait' | 'process' | 'finish' | 'error' | 'success'"], "default": "''"}], "slots": [{"name": "icon", "description": "custom icon", "doc-url": "https://element-plus.org/en-US/component/steps.html#step-slots"}, {"name": "title", "description": "step title", "doc-url": "https://element-plus.org/en-US/component/steps.html#step-slots"}, {"name": "description", "description": "step description", "doc-url": "https://element-plus.org/en-US/component/steps.html#step-slots"}]}, {"name": "el-switch", "source": {"symbol": "ElSwitch"}, "description": "Switch is used for switching between two opposing states.", "doc-url": "https://element-plus.org/en-US/component/switch.html#switch", "slots": [{"name": "active-action", "description": "customize active action", "doc-url": "https://element-plus.org/en-US/component/switch.html#switch-slots"}, {"name": "inactive-action", "description": "customize inactive action", "doc-url": "https://element-plus.org/en-US/component/switch.html#switch-slots"}]}, {"name": "el-switch", "source": {"symbol": "ElSwitch"}, "description": "Switch is used for switching between two opposing states.", "doc-url": "https://element-plus.org/en-US/component/switch.html#switch", "props": [{"name": "model-value", "description": "binding value, it should be equivalent to either `active-value` or `inactive-value`, by default it's `boolean` type", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["boolean", "string", "number"], "default": "false"}, {"name": "disabled", "description": "whether <PERSON><PERSON> is disabled", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "loading", "description": "whether Switch is in loading state", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "size of Switch", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["'' | 'large' | 'default' | 'small'"], "default": "''"}, {"name": "width", "description": "width of Switch", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["number", "string"], "default": "''"}, {"name": "inline-prompt", "description": "whether icon or text is displayed inside dot, only the first character will be rendered for text", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "active-icon", "description": "component of the icon displayed when in `on` state, overrides `active-text`", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "inactive-icon", "description": "component of the icon displayed when in `off` state, overrides `inactive-text`", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "active-action-icon", "description": "component of the icon displayed in action when in `on` state", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "inactive-action-icon", "description": "component of the icon displayed in action when in `off` state", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "active-text", "description": "text displayed when in `on` state", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string"], "default": "''"}, {"name": "inactive-text", "description": "text displayed when in `off` state", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string"], "default": "''"}, {"name": "active-value", "description": "switch value when in `on` state", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["boolean", "string", "number"], "default": "true"}, {"name": "inactive-value", "description": "switch value when in `off` state", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["boolean", "string", "number"], "default": "false"}, {"name": "name", "description": "input name of Switch", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string"], "default": "''"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "before-change", "description": "before-change hook before the switch state changes. If `false` is returned or a `Promise` is returned and then is rejected, will stop switching", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["() => Promise<boolean>", {"name": "boolen", "source": {"symbol": "boolen"}}]}, {"name": "id", "description": "id for input", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string"]}, {"name": "tabindex", "description": "tabindex for input", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string", "number"]}, {"name": "aria-label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string"]}, {"name": "active-color", "description": "background color when in `on` state ( use CSS var `--el-switch-on-color` instead )", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string"], "default": "''"}, {"name": "inactive-color", "description": "background color when in `off` state ( use CSS var `--el-switch-off-color` instead )", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string"], "default": "''"}, {"name": "border-color", "description": "border color of the switch ( use CSS var `--el-switch-border-color` instead )", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string"], "default": "''"}, {"name": "label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/switch.html#attributes", "type": ["string"]}], "js": {"events": [{"name": "change", "description": "triggers when value changes", "doc-url": "https://element-plus.org/en-US/component/switch.html#events"}]}}, {"name": "el-table-v2", "source": {"symbol": "ElTableV2"}, "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2", "props": [{"name": "cache", "description": "Number of rows rendered in advance to boost the performance", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"], "default": "2"}, {"name": "estimated-row-height", "description": "The estimated row height for rendering dynamic height rows", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"]}, {"name": "header-class", "description": "Customized class name passed to header wrapper", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes"}, {"name": "header-props", "description": "Customized props name passed to header component", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes"}, {"name": "header-cell-props", "description": "Customized props name passed to header cell component", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes"}, {"name": "header-height", "description": "The height of the header is set by `height`. If given an array, it renders header rows equal to its length", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": [{"name": "number`", "source": {"symbol": "number`"}}, {"name": "`number[]", "source": {"symbol": "`number"}}], "default": "50"}, {"name": "footer-height", "description": "The height of the footer element, when provided, will be part to the calculation of the table's height.", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"], "default": "0"}, {"name": "row-class", "description": "Customized class name passed to row wrapper", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes"}, {"name": "row-key", "description": "The key of each row, if not provided, will be the index of the row", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": [{"name": "string`", "source": {"symbol": "string`"}}, {"name": "`Symbol`", "source": {"symbol": "`Symbol`"}}, {"name": "`number", "source": {"symbol": "`number"}}], "default": "id"}, {"name": "row-props", "description": "Customized props name passed to row component", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes"}, {"name": "row-height", "description": "The height of each row, used for calculating the total height of the table", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"], "default": "50"}, {"name": "row-event-handlers", "description": "A collection of handlers attached to each row", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes"}, {"name": "cell-props", "description": "extra props passed to each cell (except header cells)", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes"}, {"name": "columns", "description": "An array of column definitions.", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["[Column[]]"]}, {"name": "data", "description": "An array of data to be rendered in the table.", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["[Data[]]"], "default": "[]"}, {"name": "data-getter", "description": "A method to customize data fetch from the data source.", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes"}, {"name": "fixed-data", "description": "Data for rendering rows above the main content and below the header", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes"}, {"name": "expand-column-key", "description": "The column key indicates which row is expandable", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["string"]}, {"name": "expanded-row-keys", "description": "An array of keys for expanded rows, can be used with `v-model`", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["[KeyType[]]"]}, {"name": "default-expanded-row-keys", "description": "An array of keys for default expanded rows, **NON REACTIVE**", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["[KeyType[]]"]}, {"name": "class", "description": "Class name for the virtual table, will be applied to all three tables (left, right, main)", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": [{"name": "string`", "source": {"symbol": "string`"}}, {"name": "`array`", "source": {"symbol": "`array`"}}, {"name": "`object", "source": {"symbol": "`object"}}]}, {"name": "fixed", "description": "Flag indicates the table column's width to be fixed or flexible.", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["boolean"], "default": "false"}, {"name": "width", "description": "Width of the table", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"]}, {"name": "height", "description": "Height of the table", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"]}, {"name": "max-height", "description": "Maximum height of the table", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"]}, {"name": "indent-size", "description": "horizontal indentation of tree table", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"], "default": "12"}, {"name": "h-scrollbar-size", "description": "Indicates the horizontal scrollbar's size for the table, used to prevent the horizontal and vertical scrollbar to collapse", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"], "default": "6"}, {"name": "v-scrollbar-size", "description": "Indicates the vertical scrollbar's size for the table, used to prevent the horizontal and vertical scrollbar to collapse", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["number"], "default": "6"}, {"name": "scrollbar-always-on", "description": "If true, the scrollbar will always be shown instead of when mouse is placed above the table", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "type": ["boolean"], "default": "false"}, {"name": "sort-by", "description": "Sort indicator", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "default": "{}"}, {"name": "sort-state", "description": "Multiple sort indicator", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-attributes", "default": "undefined"}], "slots": [{"name": "cell", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-slots"}, {"name": "header", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-slots"}, {"name": "header-cell", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-slots"}, {"name": "row", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-slots"}, {"name": "footer", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-slots"}, {"name": "empty", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-slots"}, {"name": "overlay", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-slots"}], "js": {"events": [{"name": "column-sort", "description": "Invoked when column sorted", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-events"}, {"name": "expanded-rows-change", "description": "Invoked when expanded rows changed", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-events"}, {"name": "end-reached", "description": "Invoked when the end of the table is reached. The callback contain the remain distance, it is the usually the scrollbar height.", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-events"}, {"name": "scroll", "description": "Invoked after scrolling", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-events"}, {"name": "rows-rendered", "description": "Invoked when rows are rendered", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-events"}, {"name": "row-expand", "description": "Invoked when expand/collapse the tree node by clicking the arrow icon", "doc-url": "https://element-plus.org/en-US/component/table-v2.html#tablev2-events"}]}}, {"name": "el-table", "source": {"symbol": "ElTable"}, "description": "Display multiple data with similar format. You can sort, filter, compare your data in a table.", "doc-url": "https://element-plus.org/en-US/component/table.html#table", "props": [{"name": "data", "description": "table data", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": [{"name": "any[]", "source": {"symbol": "any"}}], "default": "[]"}, {"name": "height", "description": "table's height. By default it has an `auto` height. If its value is a number, the height is measured in pixels; if its value is a string, the value will be assigned to element's style.height, the height is affected by external styles", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["string", "number"]}, {"name": "max-height", "description": "table's max-height. The legal value is a number or the height in px", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["string", "number"]}, {"name": "stripe", "description": "whether Table is striped", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "false"}, {"name": "border", "description": "whether Table has vertical border", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "size of Table", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["'' | 'large' | 'default' | 'small'"]}, {"name": "fit", "description": "whether width of column automatically fits its container", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "true"}, {"name": "show-header", "description": "whether Table header is visible", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "true"}, {"name": "highlight-current-row", "description": "whether current row is highlighted", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "false"}, {"name": "current-row-key", "description": "key of current row, a set only prop", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["string", "number"]}, {"name": "row-class-name", "description": "function that returns custom class names for a row, or a string assigning class names for every row", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, rowIndex: number }) => string", "string"]}, {"name": "row-style", "description": "function that returns custom style for a row, or an object assigning custom style for every row", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, rowIndex: number }) => CSSProperties", {"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}]}, {"name": "cell-class-name", "description": "function that returns custom class names for a cell, or a string assigning class names for every cell", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => string", "string"]}, {"name": "cell-style", "description": "function that returns custom style for a cell, or an object assigning custom style for every cell", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => CSSProperties", {"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}]}, {"name": "header-row-class-name", "description": "function that returns custom class names for a row in table header, or a string assigning class names for every row in table header", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, rowIndex: number }) => string", "string"]}, {"name": "header-row-style", "description": "function that returns custom style for a row in table header, or an object assigning custom style for every row in table header", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, rowIndex: number }) => CSSProperties", {"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}]}, {"name": "header-cell-class-name", "description": "function that returns custom class names for a cell in table header, or a string assigning class names for every cell in table header", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => string", "string"]}, {"name": "header-cell-style", "description": "function that returns custom style for a cell in table header, or an object assigning custom style for every cell in table header", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => CSSProperties", {"name": "CSSProperties", "source": {"symbol": "CSSProperties", "module": "vue"}}]}, {"name": "row-key", "description": "key of row data, used for optimizing rendering. Required if `reserve-selection` is on or display tree data. When its type is String, multi-level access is supported, e.g. `user.info.id`, but `user.info[0].id` is not supported, in which case `Function` should be used", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(row: any) => string", "string"]}, {"name": "empty-text", "description": "displayed text when data is empty. You can customize this area with `#empty`", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["string"], "default": "No Data"}, {"name": "default-expand-all", "description": "whether expand all rows by default, works when the table has a column type=\"expand\" or contains tree structure data", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "false"}, {"name": "expand-row-keys", "description": "set expanded rows by this prop, prop's value is the keys of expand rows, you should set row-key before using this prop", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["string[]"]}, {"name": "default-sort", "description": "set the default sort column and order. property `prop` is used to set default sort column, property `order` is used to set default sort order", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": [{"name": "Sort", "source": {"symbol": "Sort"}}], "default": "if `prop` is set, and `order` is not set, then `order` is default to ascending"}, {"name": "tooltip-effect", "description": "the `effect` of the overflow tooltip", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["'dark' | 'light'"], "default": "dark"}, {"name": "tooltip-options", "description": "the options for the overflow tooltip, [see the following tooltip component](tooltip.html#attributes)", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["Pick<ElTooltipProps, 'effect' | 'enterable' | 'hideAfter' | 'offset' | 'placement' | 'popperClass' | 'popperOptions' | 'showAfter' | 'showArrow'>"], "default": "^[object]`{ enterable: true, placement: 'top', showArrow: true, hideAfter: 200, popperOptions: { strategy: 'fixed' } }`"}, {"name": "append-filter-panel-to", "description": "which element the filter panels appends to", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["string"]}, {"name": "show-summary", "description": "whether to display a summary row", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "false"}, {"name": "sum-text", "description": "displayed text for the first column of summary row", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["string"], "default": "Sum"}, {"name": "summary-method", "description": "custom summary method", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": [{"name": "(data: { columns: any[], data: any[] }) => []", "source": {"symbol": "(data: { columns: any[], data: any[] }) => "}}]}, {"name": "span-method", "description": "method that returns rowspan and colspan", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, column: any, rowIndex: number, columnIndex: number }) => number[] | { rowspan: number, colspan: number }", {"name": "void", "source": {"symbol": "void"}}]}, {"name": "select-on-indeterminate", "description": "controls the behavior of master checkbox in multi-select tables when only some rows are selected (but not all). If true, all rows will be selected, else deselected", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "true"}, {"name": "indent", "description": "horizontal indentation of tree data", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["number"], "default": "16"}, {"name": "lazy", "description": "whether to lazy loading data", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "false"}, {"name": "load", "description": "method for loading child row data, only works when `lazy` is true", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(row: any, treeNode: TreeNode, resolve: (data: any[]) => void) => void"]}, {"name": "tree-props", "description": "configuration for rendering nested data", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["{ has<PERSON><PERSON><PERSON>n?: string, children?: string, checkStrictly?: boolean }"], "default": "^[object]`{ hasChildren: 'hasChildren', children: 'children', checkStrictly: false }`"}, {"name": "table-layout", "description": "sets the algorithm used to lay out table cells, rows, and columns", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["'fixed' | 'auto'"], "default": "fixed"}, {"name": "scrollbar-always-on", "description": "always show scrollbar", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "false"}, {"name": "show-overflow-tooltip", "description": "whether to hide extra content and show them in a tooltip when hovering on the cell.It will affect all the table columns, refer to table [tooltip-options](#table-attributes)", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"]}, {"name": "flexible", "description": "ensure main axis minimum-size doesn't follow the content", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "false"}, {"name": "scrollbar-tabindex", "description": "body scrollbar's wrap container tabindex", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["string", "number"]}, {"name": "allow-drag-last-column", "description": "whether to allow drag the last column", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "true"}, {"name": "tooltip-formatter", "description": "customize tooltip content when using `show-overflow-tooltip`", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["(data: { row: any, column: any, cellValue: any }) => VNode", "string"]}, {"name": "preserve-expanded-content", "description": "whether to preserve expanded row content in DOM when collapsed", "doc-url": "https://element-plus.org/en-US/component/table.html#table-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/table.html#table-slots"}, {"name": "append", "description": "Contents to be inserted after the last row. You may need this slot if you want to implement infinite scroll for the table. This slot will be displayed above the summary row if there is one.", "doc-url": "https://element-plus.org/en-US/component/table.html#table-slots"}, {"name": "empty", "description": "you can customize content when data is empty.", "doc-url": "https://element-plus.org/en-US/component/table.html#table-slots"}], "js": {"events": [{"name": "select", "description": "triggers when user clicks the checkbox in a row", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "select-all", "description": "triggers when user clicks the checkbox in table header", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "selection-change", "description": "triggers when selection changes", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "cell-mouse-enter", "description": "triggers when hovering into a cell", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "cell-mouse-leave", "description": "triggers when hovering out of a cell", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "cell-click", "description": "triggers when clicking a cell", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "cell-dblclick", "description": "triggers when double clicking a cell", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "cell-contextmenu", "description": "triggers when user right clicks on a cell", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "row-click", "description": "triggers when clicking a row", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "row-contextmenu", "description": "triggers when user right clicks on a row", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "row-dblclick", "description": "triggers when double clicking a row", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "header-click", "description": "triggers when clicking a column header", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "header-contextmenu", "description": "triggers when user right clicks on a column header", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "sort-change", "description": "triggers when Table's sorting changes", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "filter-change", "description": "column's key. If you need to use the filter-change event, this attribute is mandatory to identify which column is being filtered", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "current-change", "description": "triggers when current row changes", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "header-dragend", "description": "triggers after changing a column's width by dragging the column header's border", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "expand-change", "description": "triggers when user expands or collapses a row (for expandable table, second param is expandedRows; for tree Table, second param is expanded)", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}, {"name": "scroll", "description": "Invoked after scrolled", "doc-url": "https://element-plus.org/en-US/component/table.html#table-events"}]}}, {"name": "el-table-column", "source": {"symbol": "ElTableColumn"}, "doc-url": "https://element-plus.org/en-US/component/table.html#table-column", "props": [{"name": "type", "description": "type of the column. If set to `selection`, the column will display checkbox. If set to `index`, the column will display index of the row (staring from 1). If set to `expand`, the column will display expand icon", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["'default' | 'selection' | 'index' | 'expand'"], "default": "default"}, {"name": "index", "description": "customize indices for each row, works on columns with `type=index`", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["number", "(index: number) => number"]}, {"name": "label", "description": "column label", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["string"]}, {"name": "column-key", "description": "column's key. If you need to use the filter-change event, you need this attribute to identify which column is being filtered", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["string"]}, {"name": "prop", "description": "field name. You can also use its alias: `property`", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["string"]}, {"name": "width", "description": "column width", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["string", "number"], "default": "''"}, {"name": "min-width", "description": "column minimum width. Columns with `width` has a fixed width, while columns with `min-width` has a width that is distributed in proportion", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["string", "number"], "default": "''"}, {"name": "fixed", "description": "whether column is fixed at left / right. Will be fixed at left if `true`", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["'left' | 'right'", "boolean"], "default": "false"}, {"name": "render-header", "description": "render function for table header of this column", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["(data: { column: any, $index: number }) => void"]}, {"name": "sortable", "description": "whether column can be sorted. Remote sorting can be done by setting this attribute to 'custom' and listening to the `sort-change` event of Table", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["boolean", "string"], "default": "false"}, {"name": "sort-method", "description": "sorting method, works when `sortable` is `true`. Should return a number, just like <PERSON><PERSON><PERSON>.sort", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": [{"name": "<T = any>(a: T, b: T) => number", "source": {"symbol": "<T = any>(a: T, b: T) => number"}}]}, {"name": "sort-by", "description": "specify which property to sort by, works when `sortable` is `true` and `sort-method` is `undefined`. If set to an Array, the column will sequentially sort by the next property if the previous one is equal", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["(row: any, index: number) => string", "string", "string[]"]}, {"name": "sort-orders", "description": "the order of the sorting strategies used when sorting the data, works when `sortable` is `true`. Accepts an array, as the user clicks on the header, the column is sorted in order of the elements in the array", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["[]"], "default": "['ascending', 'descending', null]"}, {"name": "resizable", "description": "whether column width can be resized, works when `border` of `el-table` is `true`", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["boolean"], "default": "true"}, {"name": "formatter", "description": "function that formats cell content", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["(row: any, column: any, cellValue: any, index: number) => VNode", "string"]}, {"name": "show-overflow-tooltip", "description": "whether to hide extra content and show them in a tooltip when hovering on the cell", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["boolean"], "default": "undefined"}, {"name": "align", "description": "alignment", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["'left' | 'center' | 'right'"], "default": "left"}, {"name": "header-align", "description": "alignment of the table header. If omitted, the value of the above `align` attribute will be applied", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["'left' | 'center' | 'right'"], "default": "left"}, {"name": "class-name", "description": "class name of cells in the column", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["string"]}, {"name": "label-class-name", "description": "class name of the label of this column", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["string"]}, {"name": "selectable", "description": "function that determines if a certain row can be selected, works when `type` is 'selection'", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["(row: any, index: number) => boolean"]}, {"name": "reserve-selection", "description": "whether to reserve selection after data refreshing, works when `type` is 'selection'. Note that `row-key` is required for this to work", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["boolean"], "default": "false"}, {"name": "filters", "description": "an array of data filtering options. For each element in this array, `text` and `value` are required", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["Array<{text: string, value: string}>"]}, {"name": "filter-placement", "description": "placement for the filter dropdown", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'"]}, {"name": "filter-class-name", "description": "className for the filter dropdown", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["string"]}, {"name": "filter-multiple", "description": "whether data filtering supports multiple options", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["boolean"], "default": "true"}, {"name": "filter-method", "description": "data filtering method. If `filter-multiple` is on, this method will be called multiple times for each row, and a row will display if one of the calls returns `true`", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["(value: any, row: any, column: any) => void"]}, {"name": "filtered-value", "description": "filter value for selected data, might be useful when table header is rendered with `render-header`", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["string[]"]}, {"name": "tooltip-formatter", "description": "customize tooltip content when using `show-overflow-tooltip`", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-attributes", "type": ["(data: { row: any, column: any, cellValue: any }) => VNode", "string"]}], "slots": [{"name": "default", "description": "Custom content for table columns", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-slots", "type": "{ row: any, column: any, $index: number }"}, {"name": "header", "description": "Custom content for table header", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-slots", "type": "{ column: any, $index: number }"}, {"name": "filter-icon", "description": "Custom content for filter icon", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-slots", "type": "{ filterOpened: boolean }"}, {"name": "expand", "description": "Custom content for expand columns", "doc-url": "https://element-plus.org/en-US/component/table.html#table-column-slots", "type": "{ expanded: boolean }"}]}, {"name": "el-tabs", "source": {"symbol": "ElTabs"}, "description": "Divide data collections which are related yet belong to different types.", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs", "props": [{"name": "model-value", "description": "binding value, name of the selected tab, the default value is the name of first tab", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-attributes", "type": ["string", "number"]}, {"name": "type", "description": "type of Tab", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-attributes", "type": ["'' | 'card' | 'border-card'"], "default": "''"}, {"name": "closable", "description": "whether Tab is closable", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-attributes", "type": ["boolean"], "default": "false"}, {"name": "addable", "description": "whether Tab is addable", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-attributes", "type": ["boolean"], "default": "false"}, {"name": "editable", "description": "whether Tab is addable and closable", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-attributes", "type": ["boolean"], "default": "false"}, {"name": "tab-position", "description": "position of tabs", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-attributes", "type": ["'top' | 'right' | 'bottom' | 'left'"], "default": "top"}, {"name": "stretch", "description": "whether width of tab automatically fits its container", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-attributes", "type": ["boolean"], "default": "false"}, {"name": "before-leave", "description": "hook function before switching tab. If `false` is returned or a `Promise` is returned and then is rejected, switching will be prevented", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-attributes", "type": ["(activeName: TabPaneName, oldActiveName: TabPaneName) => Awaitable<void | boolean>"], "default": "() => true"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-slots"}, {"name": "add-icon", "description": "customize add button icon", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-slots"}, {"name": "add-icon", "description": "customize add button icon", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-slots"}], "js": {"events": [{"name": "tab-click", "description": "triggers when a tab is clicked", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-events"}, {"name": "tab-change", "description": "triggers when `activeName` is changed", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-events"}, {"name": "tab-remove", "description": "triggers when tab-remove button is clicked", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-events"}, {"name": "tab-add", "description": "triggers when tab-add button is clicked", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-events"}, {"name": "edit", "description": "triggers when tab-add button or tab-remove is clicked", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tabs-events"}]}}, {"name": "el-tab-pane", "source": {"symbol": "ElTabPane"}, "doc-url": "https://element-plus.org/en-US/component/tabs.html#tab-pane", "props": [{"name": "label", "description": "title of the tab", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes", "type": ["string"], "default": "''"}, {"name": "disabled", "description": "whether <PERSON><PERSON> is disabled", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes", "type": ["boolean"], "default": "false"}, {"name": "name", "description": "identifier corresponding to the name of Tabs, representing the alias of the tab-pane, the default is ordinal number of the tab-pane in the sequence, e.g. the first tab-pane is '0'", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes", "type": ["string", "number"]}, {"name": "closable", "description": "whether Tab is closable", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes", "type": ["boolean"], "default": "false"}, {"name": "lazy", "description": "whether Tab is lazily rendered", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tab-pane-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "Tab-pane's content", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tab-pane-slots"}, {"name": "label", "description": "Tab-pane's label", "doc-url": "https://element-plus.org/en-US/component/tabs.html#tab-pane-slots"}]}, {"name": "el-tag", "source": {"symbol": "ElTag"}, "description": "Used for marking and selection.", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag", "props": [{"name": "type", "description": "type of Tag", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-attributes", "type": ["'primary' | 'success' | 'info' | 'warning' | 'danger'"], "default": "primary"}, {"name": "closable", "description": "whether Tag can be removed", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-attributes", "type": ["boolean"], "default": "false"}, {"name": "disable-transitions", "description": "whether to disable animations", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-attributes", "type": ["boolean"], "default": "false"}, {"name": "hit", "description": "whether Tag has a highlighted border", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-attributes", "type": ["boolean"], "default": "false"}, {"name": "color", "description": "background color of the Tag", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-attributes", "type": ["string"]}, {"name": "size", "description": "size of Tag", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "effect", "description": "theme of Tag", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-attributes", "type": ["'dark' | 'light' | 'plain'"], "default": "light"}, {"name": "round", "description": "whether Tag is rounded", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-slots"}], "js": {"events": [{"name": "click", "description": "triggers when Tag is clicked", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-events"}, {"name": "close", "description": "triggers when Tag is removed", "doc-url": "https://element-plus.org/en-US/component/tag.html#tag-events"}]}}, {"name": "el-check-tag", "source": {"symbol": "ElCheckTag"}, "doc-url": "https://element-plus.org/en-US/component/tag.html#checktag", "props": [{"name": "checked", "description": "is checked", "doc-url": "https://element-plus.org/en-US/component/tag.html#checktag-attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "whether the check-tag is disabled", "doc-url": "https://element-plus.org/en-US/component/tag.html#checktag-attributes", "type": ["boolean"], "default": "false"}, {"name": "type", "description": "type of CheckTag", "doc-url": "https://element-plus.org/en-US/component/tag.html#checktag-attributes", "type": ["'primary' | 'success' | 'info' | 'warning' | 'danger'"], "default": "primary"}], "slots": [{"name": "default", "description": "customize default content", "doc-url": "https://element-plus.org/en-US/component/tag.html#checktag-slots"}], "js": {"events": [{"name": "change", "description": "triggers when Check Tag is clicked", "doc-url": "https://element-plus.org/en-US/component/tag.html#checktag-events"}]}}, {"name": "el-text", "source": {"symbol": "ElText"}, "description": "Used for text.", "doc-url": "https://element-plus.org/en-US/component/text.html#text", "props": [{"name": "type", "description": "text type", "doc-url": "https://element-plus.org/en-US/component/text.html#attributes", "type": ["'primary' | 'success' | 'warning' | 'danger' | 'info'"]}, {"name": "size", "description": "text size", "doc-url": "https://element-plus.org/en-US/component/text.html#attributes", "type": ["'large' | 'default' | 'small'"], "default": "default"}, {"name": "truncated", "description": "render ellipsis", "doc-url": "https://element-plus.org/en-US/component/text.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "line-clamp", "description": "maximum lines", "doc-url": "https://element-plus.org/en-US/component/text.html#attributes", "type": ["string", "number"]}, {"name": "tag", "description": "custom element tag", "doc-url": "https://element-plus.org/en-US/component/text.html#attributes", "type": ["string"], "default": "span"}], "slots": [{"name": "default", "description": "default content", "doc-url": "https://element-plus.org/en-US/component/text.html#slots"}]}, {"name": "el-time-picker", "source": {"symbol": "ElTimePicker"}, "description": "Use Time Picker for time input.", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#timepicker", "props": [{"name": "model-value", "description": "binding value, if it is an array, the length should be 2", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["number", "string", "Date", "[Date, Date] | [number, number] | [string, string]"], "default": "''"}, {"name": "readonly", "description": "whether TimePicker is read only", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "whether Time<PERSON><PERSON> is disabled", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "editable", "description": "whether the input is editable", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "clearable", "description": "whether to show clear button", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "size", "description": "size of Input", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["'large' | 'default' | 'small'"]}, {"name": "placeholder", "description": "placeholder in non-range mode", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string"], "default": "''"}, {"name": "start-placeholder", "description": "placeholder for the start time in range mode", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string"]}, {"name": "end-placeholder", "description": "placeholder for the end time in range mode", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string"]}, {"name": "is-range", "description": "whether to pick a time range", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "arrow-control", "description": "whether to pick time using arrow buttons", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "popper-class", "description": "custom class name for TimePicker's dropdown", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string"], "default": "''"}, {"name": "range-separator", "description": "range separator", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string"], "default": "'-'"}, {"name": "format", "description": "format of the displayed value in the input box", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": [{"name": "string see", "source": {"symbol": "string see"}}]}, {"name": "default-value", "description": "optional, default date of the calendar", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["Date", "[Date, Date]"]}, {"name": "value-format", "description": "optional, format of binding value. If not specified, the binding value will be a Date object", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": [{"name": "string see", "source": {"symbol": "string see"}}]}, {"name": "id", "description": "same as `id` in native input", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string", "[string, string]"]}, {"name": "name", "description": "same as `name` in native input", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string"], "default": "''"}, {"name": "aria-label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string"]}, {"name": "prefix-icon", "description": "Custom prefix icon component", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "Clock"}, {"name": "clear-icon", "description": "Custom clear icon component", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "CircleClose"}, {"name": "disabled-hours", "description": "To specify the array of hours that cannot be selected", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["(role: string, comparingDate?: Dayjs) => number[]"]}, {"name": "disabled-minutes", "description": "To specify the array of minutes that cannot be selected", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["(hour: number, role: string, comparingDate?: Dayjs) => number[]"]}, {"name": "disabled-seconds", "description": "To specify the array of seconds that cannot be selected", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["(hour: number, minute: number, role: string, comparingDate?: Dayjs) => number[]"]}, {"name": "teleported", "description": "whether time-picker dropdown is teleported to the body", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "tabindex", "description": "input tabindex", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string", "number"], "default": "0"}, {"name": "empty-values", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["array"]}, {"name": "value-on-clear", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string", "number", "boolean", "Function"]}, {"name": "label", "description": "same as `aria-label` in native input", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#attributes", "type": ["string"]}], "js": {"events": [{"name": "change", "description": "triggers when user confirms the value", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#events"}, {"name": "blur", "description": "triggers when Input blurs", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#events"}, {"name": "focus", "description": "triggers when Input focuses", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#events"}, {"name": "clear", "description": "triggers when the clear icon is clicked in a clearable TimePicker", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#events"}, {"name": "visible-change", "description": "triggers when the TimePicker's dropdown appears/disappears", "doc-url": "https://element-plus.org/en-US/component/time-picker.html#events"}]}}, {"name": "el-time-select", "source": {"symbol": "ElTimeSelect"}, "description": "Use Time Select for time input.", "doc-url": "https://element-plus.org/en-US/component/time-select.html#timeselect", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string"]}, {"name": "disabled", "description": "whether TimeSelect is disabled", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "editable", "description": "whether the input is editable", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "clearable", "description": "whether to show clear button", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "include-end-time", "description": "whether `end` is included in options", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "size of Input", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["'large' | 'default' | 'small'"], "default": "default"}, {"name": "placeholder", "description": "placeholder in non-range mode", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string"]}, {"name": "name", "description": "same as `name` in native input", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string"]}, {"name": "effect", "description": "Tooltip theme, built-in theme: `dark` / `light`", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string", "'dark' | 'light'"], "default": "light"}, {"name": "prefix-icon", "description": "custom prefix icon component", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "Clock"}, {"name": "clear-icon", "description": "custom clear icon component", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}], "default": "CircleClose"}, {"name": "start", "description": "start time", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string"], "default": "09:00"}, {"name": "end", "description": "end time", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string"], "default": "18:00"}, {"name": "step", "description": "time step", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string"], "default": "00:30"}, {"name": "min-time", "description": "minimum time, any time before this time will be disabled", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string"]}, {"name": "max-time", "description": "maximum time, any time after this time will be disabled", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string"]}, {"name": "format", "description": "set format of time", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": [{"name": "string see", "source": {"symbol": "string see"}}], "default": "HH:mm"}, {"name": "empty-values", "description": "empty values of component, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["array"]}, {"name": "value-on-clear", "description": "clear return value, [see config-provider](/en-US/component/config-provider#empty-values-configurations)", "doc-url": "https://element-plus.org/en-US/component/time-select.html#attributes", "type": ["string", "number", "boolean", "Function"]}], "js": {"events": [{"name": "change", "description": "triggers when user confirms the value", "doc-url": "https://element-plus.org/en-US/component/time-select.html#events"}, {"name": "blur", "description": "triggers when Input blurs", "doc-url": "https://element-plus.org/en-US/component/time-select.html#events"}, {"name": "focus", "description": "triggers when Input focuses", "doc-url": "https://element-plus.org/en-US/component/time-select.html#events"}, {"name": "clear", "description": "triggers when the clear icon is clicked in a clearable TimeSelect", "doc-url": "https://element-plus.org/en-US/component/time-select.html#events"}]}}, {"name": "el-timeline", "source": {"symbol": "ElTimeline"}, "description": "Visually display timeline.", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline", "slots": [{"name": "default", "description": "customize default content for timeline", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-slots"}]}, {"name": "el-timeline-item", "source": {"symbol": "ElTimelineItem"}, "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item", "props": [{"name": "timestamp", "description": "timestamp content", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes", "type": ["string"], "default": "''"}, {"name": "hide-timestamp", "description": "whether to show timestamp", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes", "type": ["boolean"], "default": "false"}, {"name": "center", "description": "whether vertically centered", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes", "type": ["boolean"], "default": "false"}, {"name": "placement", "description": "position of timestamp", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes", "type": ["'top' | 'bottom'"], "default": "bottom"}, {"name": "type", "description": "node type", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes", "type": ["'primary' | 'success' | 'warning' | 'danger' | 'info'"], "default": "''"}, {"name": "color", "description": "background color of node", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes", "type": ["'hsl' | 'hsv' | 'hex' | 'rgb'"], "default": "''"}, {"name": "size", "description": "node size", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes", "type": ["'normal' | 'large'"], "default": "normal"}, {"name": "icon", "description": "icon component", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "hollow", "description": "icon is hollow", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-attributes", "type": ["boolean"], "default": "false"}], "slots": [{"name": "default", "description": "customize default content for timeline item", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-slots"}, {"name": "dot", "description": "customize defined node for timeline item", "doc-url": "https://element-plus.org/en-US/component/timeline.html#timeline-item-slots"}]}, {"name": "el-tooltip", "source": {"symbol": "ElTooltip"}, "description": "Display prompt information for mouse hover.", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#tooltip", "props": [{"name": "append-to", "description": "which element the tooltip CONTENT appends to", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": [{"name": "CSSSelector", "source": {"symbol": "CSSSelector"}}, "HTMLElement"]}, {"name": "effect", "description": "Tooltip theme, built-in theme: `dark` / `light`", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["'dark' | 'light'"], "default": "dark"}, {"name": "content", "description": "display content, can be overridden by `slot#content`", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["string"], "default": "''"}, {"name": "raw-content", "description": "whether `content` is treated as HTML string", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "placement", "description": "position of Toolt<PERSON>", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end' | 'right' | 'right-start' | 'right-end'"], "default": "bottom"}, {"name": "fallback-placements", "description": "list of possible positions for Tooltip [popper.js](https://popper.js.org/docs/v2/modifiers/flip/#fallbackplacements)", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": [{"name": "Placement[]", "source": {"symbol": "Placement"}}]}, {"name": "visible", "description": "visibility of Tooltip", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["boolean"]}, {"name": "disabled", "description": "whether <PERSON><PERSON><PERSON> is disabled", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["boolean"]}, {"name": "offset", "description": "offset of the Tooltip", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["number"], "default": "12"}, {"name": "transition", "description": "animation name", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["string"]}, {"name": "popper-options", "description": "[popper.js](https://popper.js.org/docs/v2/) parameters", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": [{"name": "objectrefer to  doc", "source": {"symbol": "objectrefer to  doc"}}], "default": "{}"}, {"name": "arrow-offset", "description": "Controls the offset (padding) of the tooltip’s arrow relative to the popper.", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["number"], "default": "5"}, {"name": "show-after", "description": "delay of appearance, in millisecond", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["number"], "default": "0"}, {"name": "show-arrow", "description": "whether the tooltip content has an arrow", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "hide-after", "description": "delay of disappear, in millisecond", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["number"], "default": "200"}, {"name": "auto-close", "description": "timeout in milliseconds to hide tooltip", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["number"], "default": "0"}, {"name": "popper-class", "description": "custom class name for <PERSON><PERSON><PERSON>'s popper", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["string"]}, {"name": "enterable", "description": "whether the mouse can enter the tooltip", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "teleported", "description": "whether tooltip content is teleported, if `true` it will be teleported to where `append-to` sets", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "trigger", "description": "How should the tooltip be triggered (to show)", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["'hover' | 'click' | 'focus' | 'contextmenu'"], "default": "hover"}, {"name": "virtual-triggering", "description": "Indicates whether virtual triggering is enabled", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["boolean"]}, {"name": "virtual-ref", "description": "Indicates the reference element to which the tooltip is attached", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["HTMLElement"]}, {"name": "trigger-keys", "description": "When you click the mouse to focus on the trigger element, you can define a set of keyboard codes to control the display of tooltip through the keyboard", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["Array"], "default": "['Enter','Space']"}, {"name": "persistent", "description": "when tooltip inactive and `persistent` is `false` , popconfirm will be destroyed", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["boolean"]}, {"name": "aria-label", "description": "same as `aria-label`", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#attributes", "type": ["string"]}], "slots": [{"name": "default", "description": "Tooltip triggering & reference element", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#slots"}, {"name": "content", "description": "customize content", "doc-url": "https://element-plus.org/en-US/component/tooltip.html#slots"}]}, {"name": "el-tour", "source": {"symbol": "ElTour"}, "description": "A popup component for guiding users through a product. Use when you want to guide users through a product.", "doc-url": "https://element-plus.org/en-US/component/tour.html#tour"}, {"name": "el-tour-step", "source": {"symbol": "ElTourStep"}, "doc-url": "https://element-plus.org/en-US/component/tour.html#tourstep"}, {"name": "el-transfer", "source": {"symbol": "ElTransfer"}, "description": "", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer", "props": [{"name": "model-value", "description": "binding value", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["Array<string | number>"], "default": "[]"}, {"name": "data", "description": "data source", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["Record<string, any>[]"], "default": "[]"}, {"name": "filterable", "description": "whether Transfer is filterable", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["boolean"], "default": "false"}, {"name": "filter-placeholder", "description": "placeholder for the filter input", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["string"]}, {"name": "filter-method", "description": "custom filter method", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["(query: string, item: Record<string, any>) => boolean"]}, {"name": "target-order", "description": "order strategy for elements in the target list. If set to `original`, the elements will keep the same order as the data source. If set to `push`, the newly added elements will be pushed to the bottom. If set to `unshift`, the newly added elements will be inserted on the top", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["'original' | 'push' | 'unshift'"], "default": "original"}, {"name": "titles", "description": "custom list titles", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["[string, string]"], "default": "[]"}, {"name": "button-texts", "description": "custom button texts", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["[string, string]"], "default": "[]"}, {"name": "render-content", "description": "custom render function for data items", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": [{"name": "renderContent", "source": {"symbol": "renderContent"}}]}, {"name": "format", "description": "texts for checking status in list header", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": [{"name": "TransferFormat", "source": {"symbol": "TransferFormat"}}], "default": "{}"}, {"name": "props", "description": "prop aliases for data source", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": [{"name": "TransferPropsAlias", "source": {"symbol": "TransferPropsAlias"}}]}, {"name": "left-default-checked", "description": "key array of initially checked data items of the left list", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["Array<string | number>"], "default": "[]"}, {"name": "right-default-checked", "description": "key array of initially checked data items of the right list", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["Array<string | number>"], "default": "[]"}, {"name": "validate-event", "description": "whether to trigger form validation", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-attributes", "type": ["boolean"], "default": "true"}], "slots": [{"name": "default", "description": "Custom content for data items. The scope parameter is `{ option }`", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-slots"}, {"name": "left-footer", "description": "content of left list footer", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-slots"}, {"name": "right-footer", "description": "content of right list footer", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-slots"}, {"name": "left-empty", "description": "content when left panel is empty or when no data matches the filter", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-slots"}, {"name": "right-empty", "description": "content when right panel is empty or when no data matches the filter", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-slots"}], "js": {"events": [{"name": "change", "description": "triggers when data items change in the right list", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-events"}, {"name": "left-check-change", "description": "triggers when end user changes the checked state of any data item in the left list", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-events"}, {"name": "right-check-change", "description": "triggers when end user changes the checked state of any data item in the right list", "doc-url": "https://element-plus.org/en-US/component/transfer.html#transfer-events"}]}}, {"name": "el-own", "source": {"symbol": "ElOwn"}, "doc-url": "https://element-plus.org/en-US/component/tree-select.html#own", "props": [{"name": "cache-data", "description": "The cached data of the lazy node, the structure is the same as the data, used to get the label of the unloaded data", "doc-url": "https://element-plus.org/en-US/component/tree-select.html#own-attributes", "type": [{"name": "CacheOption[]", "source": {"symbol": "CacheOption"}}], "default": "[]"}]}, {"name": "el-tree-v2", "source": {"symbol": "ElTreeV2"}, "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2", "props": [{"name": "data", "description": "tree data", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["array"]}, {"name": "empty-text", "description": "text displayed when data is void", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["string"]}, {"name": "props", "description": "configuration options, see the following table", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["object"]}, {"name": "highlight-current", "description": "whether current node is highlighted", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["boolean"], "default": "false"}, {"name": "expand-on-click-node", "description": "whether to expand or collapse node when clicking on the node, if false, then expand or collapse node only when clicking on the arrow icon.", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["boolean"], "default": "true"}, {"name": "check-on-click-node", "description": "whether to check or uncheck node when clicking on the node, if false, the node can only be checked or unchecked by clicking on the checkbox.", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["boolean"], "default": "false"}, {"name": "check-on-click-leaf", "description": "whether to check or uncheck node when clicking on leaf node (last children).", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["boolean"], "default": "true"}, {"name": "default-expanded-keys", "description": "array of keys of initially expanded nodes", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["array"]}, {"name": "show-checkbox", "description": "whether node is selectable", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["boolean"], "default": "false"}, {"name": "check-strictly", "description": "whether checked state of a node not affects its father and child nodes when `show-checkbox` is `true`", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["boolean"], "default": "false"}, {"name": "default-checked-keys", "description": "array of keys of initially checked nodes", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["array"]}, {"name": "current-node-key", "description": "key of initially selected node", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["string", "number"]}, {"name": "filter-method", "description": "this function will be executed on each node when use filter method. if return `false`, tree node will be hidden.", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["Function"]}, {"name": "indent", "description": "horizontal indentation of nodes in adjacent levels in pixels", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["number"], "default": "16"}, {"name": "icon", "description": "custom tree node icon", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "item-size", "description": "custom tree node height", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-attributes", "type": ["number"], "default": "26"}], "slots": [{"name": "default", "description": "Custom content for tree nodes. The scope parameter is `{ node: TreeNode, data: TreeNodeData }`", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-slots"}, {"name": "empty", "description": "empty you can customize content when data is empty.", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-slots"}], "js": {"events": [{"name": "node-click", "description": "triggers when a node is clicked", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-events"}, {"name": "node-drop", "description": "triggers when drag someting and drop on a node", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-events"}, {"name": "node-contextmenu", "description": "triggers when a node is clicked by right button", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-events"}, {"name": "check-change", "description": "triggers when the selected state of the node changes", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-events"}, {"name": "check", "description": "triggers after clicking the checkbox of a node", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-events"}, {"name": "current-change", "description": "triggers when current node changes", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-events"}, {"name": "node-expand", "description": "triggers when current node open", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-events"}, {"name": "node-collapse", "description": "triggers when current node close", "doc-url": "https://element-plus.org/en-US/component/tree-v2.html#treev2-events"}]}}, {"name": "el-tree", "source": {"symbol": "ElTree"}, "description": "Display a set of data with hierarchies.", "doc-url": "https://element-plus.org/en-US/component/tree.html#tree", "props": [{"name": "data", "description": "tree data", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["Array<{[key: string]: any}>"]}, {"name": "empty-text", "description": "text displayed when data is void", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["string"]}, {"name": "node-key", "description": "unique identity key name for nodes, its value should be unique across the whole tree", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["string"]}, {"name": "props", "description": "configuration options, see the following table", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["[props]"]}, {"name": "render-after-expand", "description": "whether to render child nodes only after a parent node is expanded for the first time", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "load", "description": "method for loading subtree data, only works when `lazy` is true", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["(node, resolve, reject) => void"]}, {"name": "render-content", "description": "render function for tree node", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["(h, { node, data, store }) => void"]}, {"name": "highlight-current", "description": "whether current node is highlighted", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "default-expand-all", "description": "whether to expand all nodes by default", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "expand-on-click-node", "description": "whether to expand or collapse node when clicking on the node, if false, then expand or collapse node only when clicking on the arrow icon.", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "check-on-click-node", "description": "whether to check or uncheck node when clicking on the node, if false, the node can only be checked or unchecked by clicking on the checkbox.", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "check-on-click-leaf", "description": "whether to check or uncheck node when clicking on leaf node (last children).", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "auto-expand-parent", "description": "whether to expand father node when a child node is expanded", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "default-expanded-keys", "description": "array of keys of initially expanded nodes", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["Array<string | number>"]}, {"name": "show-checkbox", "description": "whether node is selectable", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "check-strictly", "description": "whether checked state of a node not affects its father and child nodes when `show-checkbox` is `true`", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "default-checked-keys", "description": "array of keys of initially checked nodes", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["Array<string | number>"]}, {"name": "current-node-key", "description": "key of initially selected node", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["string", "number"]}, {"name": "filter-node-method", "description": "this function will be executed on each node when use filter method. if return `false`, tree node will be hidden.", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["(value, data, node) => boolean"]}, {"name": "accordion", "description": "whether only one node among the same level can be expanded at one time", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "indent", "description": "horizontal indentation of nodes in adjacent levels in pixels", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["number"], "default": "18"}, {"name": "icon", "description": "custom tree node icon component", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["string", {"name": "Component", "source": {"symbol": "Component", "module": "vue"}}]}, {"name": "lazy", "description": "whether to lazy load leaf node, used with `load` attribute", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "draggable", "description": "whether enable tree nodes drag and drop", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "allow-drag", "description": "this function will be executed before dragging a node. If `false` is returned, the node can not be dragged", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["(node) => boolean"]}, {"name": "allow-drop", "description": "this function will be executed before the dragging node is dropped. If `false` is returned, the dragging node can not be dropped at the target node. `type` has three possible values: 'prev' (inserting the dragging node before the target node), 'inner' (inserting the dragging node to the target node) and 'next' (inserting the dragging node after the target node)", "doc-url": "https://element-plus.org/en-US/component/tree.html#attributes", "type": ["(draggingNode, dropNode, type) => boolean"]}], "slots": [{"name": "default", "description": "Custom content for tree nodes. The scope parameter is `{ node, data }`", "doc-url": "https://element-plus.org/en-US/component/tree.html#slots"}, {"name": "empty", "description": "empty you can customize content when data is empty.", "doc-url": "https://element-plus.org/en-US/component/tree.html#slots"}], "js": {"events": [{"name": "node-click", "description": "triggers when a node is clicked", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "node-contextmenu", "description": "triggers when a node is clicked by right button", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "check-change", "description": "triggers when the selected state of the node changes", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "check", "description": "triggers after clicking the checkbox of a node", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "current-change", "description": "triggers when current node changes", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "node-expand", "description": "triggers when current node open", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "node-collapse", "description": "triggers when current node close", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "node-drag-start", "description": "triggers when dragging starts", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "node-drag-enter", "description": "triggers when the dragging node enters another node", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "node-drag-leave", "description": "triggers when the dragging node leaves a node", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "node-drag-over", "description": "triggers when dragging over a node (like mouseover event)", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "node-drag-end", "description": "triggers when dragging ends", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}, {"name": "node-drop", "description": "triggers after the dragging node is dropped", "doc-url": "https://element-plus.org/en-US/component/tree.html#events"}]}}, {"name": "el-upload", "source": {"symbol": "ElUpload"}, "description": "Upload files by clicking or drag-and-drop.", "doc-url": "https://element-plus.org/en-US/component/upload.html#upload", "props": [{"name": "action", "description": "request URL.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["string"], "default": "#"}, {"name": "headers", "description": "request headers.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": [{"name": "Headers", "source": {"symbol": "Headers"}}, "Record<string, any>"]}, {"name": "method", "description": "set upload request method.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["string"], "default": "post"}, {"name": "multiple", "description": "whether uploading multiple files is permitted.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "data", "description": "additions options of request. support `Awaitable` data and `Function` since v2.3.13.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["Record<string, any>", {"name": "Awaitable<Record<string, any>>", "source": {"symbol": "Awaitable"}}, "(rawFile: UploadRawFile) => Awaitable<Record<string, any>>"], "default": "{}"}, {"name": "name", "description": "key name for uploaded file.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["string"], "default": "file"}, {"name": "with-credentials", "description": "whether cookies are sent.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-file-list", "description": "whether to show the uploaded file list.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "drag", "description": "whether to activate drag and drop mode.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "accept", "description": "accepted [file types](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#attr-accept), will not work when `thumbnail-mode === true`.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["string"], "default": "''"}, {"name": "crossorigin", "description": "native attribute [crossorigin](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/crossorigin).", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["'' | 'anonymous' | 'use-credentials'"]}, {"name": "on-preview", "description": "hook function when clicking the uploaded files.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(uploadFile: UploadFile) => void"]}, {"name": "on-remove", "description": "hook function when files are removed.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(uploadFile: UploadFile, uploadFiles: UploadFiles) => void"]}, {"name": "on-success", "description": "hook function when uploaded successfully.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) => void"]}, {"name": "on-error", "description": "hook function when some errors occurs.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(error: Error, uploadFile: UploadFile, uploadFiles: UploadFiles) => void"]}, {"name": "on-progress", "description": "hook function when some progress occurs.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(evt: UploadProgressEvent, uploadFile: UploadFile, uploadFiles: UploadFiles) => void"]}, {"name": "on-change", "description": "hook function when select file or upload file success or upload file fail.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(uploadFile: UploadFile, uploadFiles: UploadFiles) => void"]}, {"name": "on-exceed", "description": "hook function when limit is exceeded.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(files: File[], uploadFiles: UploadUserFile[]) => void"]}, {"name": "before-upload", "description": "hook function before uploading with the file to be uploaded as its parameter. If `false` is returned or a `Promise` is returned and then is rejected, uploading will be aborted.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(rawFile: UploadRawFile) => Awaitable<void | undefined | null | boolean | File | Blob>"]}, {"name": "before-remove", "description": "hook function before removing a file with the file and file list as its parameters. If `false` is returned or a `Promise` is returned and then is rejected, removing will be aborted.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(uploadFile: UploadFile, uploadFiles: UploadFiles) => Awaitable<boolean>"]}, {"name": "file-list", "description": "default uploaded files.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": [{"name": "UploadUserFile[]", "source": {"symbol": "UploadUserFile"}}], "default": "[]"}, {"name": "list-type", "description": "type of file list.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["'text' | 'picture' | 'picture-card'"], "default": "text"}, {"name": "auto-upload", "description": "whether to auto upload file.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "http-request", "description": "override default xhr behavior, allowing you to implement your own upload-file's request.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["(options: UploadRequestOptions) => XMLHttpRequest", "Promise<unknown>"], "default": "ajaxUpload [see](https://github.com/element-plus/element-plus/blob/dev/packages/components/upload/src/ajax.ts#L55)"}, {"name": "disabled", "description": "whether to disable upload.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "limit", "description": "maximum number of uploads allowed.", "doc-url": "https://element-plus.org/en-US/component/upload.html#attributes", "type": ["number"]}], "slots": [{"name": "default", "description": "customize default content.", "doc-url": "https://element-plus.org/en-US/component/upload.html#slots"}, {"name": "trigger", "description": "content which triggers file dialog.", "doc-url": "https://element-plus.org/en-US/component/upload.html#slots"}, {"name": "tip", "description": "content of tips.", "doc-url": "https://element-plus.org/en-US/component/upload.html#slots"}, {"name": "file", "description": "content of thumbnail template.", "doc-url": "https://element-plus.org/en-US/component/upload.html#slots", "type": "{ file: UploadFile, index: number }"}]}, {"name": "el-watermark", "source": {"symbol": "ElWatermark"}, "description": "Add specific text or patterns to the page.", "doc-url": "https://element-plus.org/en-US/component/watermark.html#watermark", "props": [{"name": "width", "description": "The width of the watermark, the default value of `content` is its own width", "doc-url": "https://element-plus.org/en-US/component/watermark.html#attributes", "type": ["number"], "default": "120"}, {"name": "height", "description": "The height of the watermark, the default value of `content` is its own height", "doc-url": "https://element-plus.org/en-US/component/watermark.html#attributes", "type": ["number"], "default": "64"}, {"name": "rotate", "description": "When the watermark is drawn, the rotation Angle, unit `°`", "doc-url": "https://element-plus.org/en-US/component/watermark.html#attributes", "type": ["number"], "default": "-22"}, {"name": "z-index", "description": "The z-index of the appended watermark element", "doc-url": "https://element-plus.org/en-US/component/watermark.html#attributes", "type": ["number"], "default": "9"}, {"name": "image", "description": "Image source, it is recommended to export 2x or 3x image, high priority", "doc-url": "https://element-plus.org/en-US/component/watermark.html#attributes", "type": ["string"]}, {"name": "content", "description": "Watermark text content", "doc-url": "https://element-plus.org/en-US/component/watermark.html#attributes", "type": ["string", "string[]"]}, {"name": "font", "description": "Text style", "doc-url": "https://element-plus.org/en-US/component/watermark.html#attributes", "type": ["[Font]"], "default": "[Font](#font)"}, {"name": "gap", "description": "The spacing between watermarks", "doc-url": "https://element-plus.org/en-US/component/watermark.html#attributes", "type": ["[number, number]"], "default": "\\[100, 100\\]"}, {"name": "offset", "description": "The offset of the watermark from the upper left corner of the container. The default is `gap/2`", "doc-url": "https://element-plus.org/en-US/component/watermark.html#attributes", "type": ["[number, number]"], "default": "\\[gap\\[0\\]/2, gap\\[1\\]/2\\]"}], "slots": [{"name": "default", "description": "container for adding watermark", "doc-url": "https://element-plus.org/en-US/component/watermark.html#slots"}]}], "attributes": [{"name": "v-infinite-scroll", "source": {"symbol": "ElInfiniteScroll"}, "description": "Load more data while reach bottom of the page", "doc-url": "https://element-plus.org/en-US/component/infinite-scroll.html#directives", "type": ["Function"]}, {"name": "infinite-scroll-disabled", "source": {"symbol": "ElInfiniteScroll"}, "description": "is disabled", "doc-url": "https://element-plus.org/en-US/component/infinite-scroll.html#directives", "type": ["boolean"]}, {"name": "infinite-scroll-delay", "source": {"symbol": "ElInfiniteScroll"}, "description": "throttle delay (ms)", "doc-url": "https://element-plus.org/en-US/component/infinite-scroll.html#directives", "type": ["number"]}, {"name": "infinite-scroll-distance", "source": {"symbol": "ElInfiniteScroll"}, "description": "trigger distance (px)", "doc-url": "https://element-plus.org/en-US/component/infinite-scroll.html#directives", "type": ["number"]}, {"name": "infinite-scroll-immediate", "source": {"symbol": "ElInfiniteScroll"}, "description": "Whether to execute the loading method immediately, in case the content cannot be filled up in the initial state.", "doc-url": "https://element-plus.org/en-US/component/infinite-scroll.html#directives", "type": ["boolean"]}, {"name": "v-loading", "source": {"symbol": "ElLoading"}, "description": "show animation while loading data", "doc-url": "https://element-plus.org/en-US/component/loading.html#directives", "type": ["boolean", {"name": "LoadingOptions", "source": {"symbol": "LoadingOptions"}}]}, {"name": "element-loading-text", "source": {"symbol": "ElLoading"}, "description": "loading text that displays under the spinner", "doc-url": "https://element-plus.org/en-US/component/loading.html#directives", "type": ["string"]}, {"name": "element-loading-spinner", "source": {"symbol": "ElLoading"}, "description": "icon of the custom spinner", "doc-url": "https://element-plus.org/en-US/component/loading.html#directives", "type": ["string"]}, {"name": "element-loading-svg", "source": {"symbol": "ElLoading"}, "description": "icon of the custom spinner (same as element-loading-spinner)", "doc-url": "https://element-plus.org/en-US/component/loading.html#directives", "type": ["string"]}, {"name": "element-loading-svg-view-box", "source": {"symbol": "ElLoading"}, "description": "sets the viewBox attribute for loading svg element", "doc-url": "https://element-plus.org/en-US/component/loading.html#directives", "type": ["string"]}, {"name": "element-loading-background", "source": {"symbol": "ElLoading"}, "description": "background color of the mask", "doc-url": "https://element-plus.org/en-US/component/loading.html#directives", "type": ["string"]}, {"name": "element-loading-custom-class", "source": {"symbol": "ElLoading"}, "description": "custom class name for loading", "doc-url": "https://element-plus.org/en-US/component/loading.html#directives", "type": ["string"]}]}}}