{"version": 3, "file": "auto-resizer.js", "sources": ["../../../../../../packages/components/table-v2/src/auto-resizer.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\ntype AutoResizeHandler = (event: { height: number; width: number }) => void\n\nexport const autoResizerProps = buildProps({\n  disableWidth: <PERSON>olean,\n  disableHeight: <PERSON>olean,\n  onResize: {\n    type: definePropType<AutoResizeHandler>(Function),\n  },\n} as const)\n\nexport type AutoResizerProps = ExtractPropTypes<typeof autoResizerProps>\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,gBAAgB,GAAGA,kBAAU,CAAC;AAC3C,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAEC,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,CAAC;;;;"}