import{M as e,S as t}from"./ai-engine-storage-DTARYHrG.js";import{T as a,D as s,W as n,a as r}from"./ai-engine-core-wyUSRaHZ.js";class o{configCache=new Map;cacheExpiry=3e5;apiBasePath="/api/config";async getModuleConfig(e){const t=`module_config_${e}`,a=this.getCachedConfig(t);if(a)return a;try{const a=await fetch(`${this.apiBasePath}/modules/${e}`,{headers:{Authorization:`Bearer ${this.getAuthToken()}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`获取模块配置失败: ${a.status} ${a.statusText}`);const s=await a.json();return this.validateModuleConfig(s).valid,this.setCachedConfig(t,s),s}catch(s){return this.getDefaultModuleConfig(e)}}async getAIConfig(e){const t=`ai_config_${e}`,a=this.getCachedConfig(t);if(a)return a;try{const a=await fetch(`${this.apiBasePath}/ai/${e}`,{headers:{Authorization:`Bearer ${this.getAuthToken()}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`获取AI配置失败: ${a.status} ${a.statusText}`);const s=await a.json();return this.validateAIConfig(s).valid,this.setCachedConfig(t,s),s}catch(s){throw new Error(`无法获取AI配置: ${s instanceof Error?s.message:"未知错误"}`)}}async getPromptTemplate(e,t){const a=`prompt_template_${e}_${t}`,s=this.getCachedConfig(a);if(s)return s;try{const s=await fetch(`${this.apiBasePath}/prompts/${e}?moduleType=${t}`,{headers:{Authorization:`Bearer ${this.getAuthToken()}`,"Content-Type":"application/json"}});if(!s.ok)throw new Error(`获取提示词模板失败: ${s.status} ${s.statusText}`);const n=await s.json();return this.validatePromptTemplate(n).valid,this.setCachedConfig(a,n),n}catch(n){return this.getDefaultPromptTemplate(t)}}async getAvailableModels(e){const t=`available_models_${e}`,a=this.getCachedConfig(t);if(a)return a;try{const a=await fetch(`${this.apiBasePath}/models?moduleType=${e}`,{headers:{Authorization:`Bearer ${this.getAuthToken()}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`获取模型列表失败: ${a.status} ${a.statusText}`);const s=(await a.json()).filter(e=>e.available);return this.setCachedConfig(t,s),s}catch(s){return this.getDefaultModels()}}async getAvailablePrompts(e){const t=`available_prompts_${e}`,a=this.getCachedConfig(t);if(a)return a;try{const a=await fetch(`${this.apiBasePath}/prompts?moduleType=${e}`,{headers:{Authorization:`Bearer ${this.getAuthToken()}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`获取提示词列表失败: ${a.status} ${a.statusText}`);const s=await a.json();return this.setCachedConfig(t,s),s}catch(s){return this.getDefaultPrompts(e)}}async getGlobalConfig(){const e="global_config",t=this.getCachedConfig(e);if(t)return t;try{const t=await fetch(`${this.apiBasePath}/global`,{headers:{Authorization:`Bearer ${this.getAuthToken()}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`获取全局配置失败: ${t.status} ${t.statusText}`);const a=await t.json();return this.setCachedConfig(e,a),a}catch(a){return this.getDefaultGlobalConfig()}}refreshCache(e){if(e)for(const t of this.configCache.keys())t.startsWith(e)&&this.configCache.delete(t);else this.configCache.clear()}cleanupExpiredCache(){const e=Date.now();for(const[t,a]of this.configCache.entries())e>a.expiry&&this.configCache.delete(t)}getCachedConfig(e){const t=this.configCache.get(e);if(!t)return null;return Date.now()>t.expiry?(this.configCache.delete(e),null):t.data}setCachedConfig(e,t){const a=Date.now();this.configCache.set(e,{data:t,timestamp:a,expiry:a+this.cacheExpiry})}getAuthToken(){return localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")||""}validateModuleConfig(e){const t=[],a=[];return e.moduleType||t.push("缺少模块类型"),e.name||t.push("缺少模块名称"),e.settings||a.push("缺少模块设置"),{valid:0===t.length,errors:t,warnings:a,timestamp:Date.now()}}validateAIConfig(e){const t=[],a=[];return e.apiKey||t.push("缺少API密钥"),e.apiEndpoint||t.push("缺少API端点"),(!e.timeout||e.timeout<=0)&&a.push("超时时间设置不合理"),{valid:0===t.length,errors:t,warnings:a,timestamp:Date.now()}}validatePromptTemplate(e){const t=[];return e.id||t.push("缺少模板ID"),e.template||t.push("缺少模板内容"),e.moduleType||t.push("缺少适用模块类型"),{valid:0===t.length,errors:t,warnings:[],timestamp:Date.now()}}getDefaultModuleConfig(e){return{moduleType:e,name:this.getModuleDisplayName(e),description:`${this.getModuleDisplayName(e)}的默认配置`,settings:{defaultChunkSize:3e3,defaultOverlapSize:300,supportedFormats:["txt","docx","pdf","md"],maxFileSize:104857600,defaultProcessingOptions:{focus:"all",style:"formal",language:"zh-CN"}},enabled:!0,version:"1.0.0"}}getDefaultPromptTemplate(t){const a={[e.PRE_REVIEW]:{template:"你是一个专业的文本预审助手。请仔细审查以下文本，重点关注内容的合规性、准确性和完整性。",description:"内容预审默认模板"},[e.BATCH_PROOFREADING]:{template:"你是一个专业的文本校对助手。请仔细校对以下文本，纠正语法错误、拼写错误，并提供改进建议。",description:"批量审校默认模板"},[e.ONLINE_PROOFREADING]:{template:"你是一个专业的在线校对助手。请快速准确地校对以下文本，提供实时的修改建议。",description:"在线审校默认模板"},[e.TYPESETTING]:{template:"你是一个专业的排版助手。请分析以下文本的排版格式，提供专业的排版建议和优化方案。",description:"专业排版默认模板"}},s=a[t]||a[e.BATCH_PROOFREADING];return{id:`default_${t}`,name:"默认模板",moduleType:t,category:"general",template:s.template,variables:[],description:s.description,version:"1.0.0",createdAt:Date.now(),updatedAt:Date.now()}}getDefaultModels(){return[{id:"deepseek-chat",name:"DeepSeek Chat",provider:"deepseek",maxTokens:32e3,costPerToken:.0014,capabilities:["text","reasoning"],supportedLanguages:["zh-CN","en-US"],apiEndpoint:"https://api.deepseek.com/v1/chat/completions",version:"v1",available:!0},{id:"ernie-bot-turbo",name:"文心一言 Turbo",provider:"baidu",maxTokens:8e3,costPerToken:.008,capabilities:["text","chinese"],supportedLanguages:["zh-CN"],apiEndpoint:"https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant",version:"v1",available:!0},{id:"doubao-pro",name:"豆包 Pro",provider:"doubao",maxTokens:16e3,costPerToken:.005,capabilities:["text","fast"],supportedLanguages:["zh-CN","en-US"],apiEndpoint:"https://ark.cn-beijing.volces.com/api/v3/chat/completions",version:"v3",available:!0},{id:"qwen-turbo",name:"通义千问 Turbo",provider:"qwen",maxTokens:8e3,costPerToken:.006,capabilities:["text","enterprise"],supportedLanguages:["zh-CN","en-US"],apiEndpoint:"https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",version:"v1",available:!0}]}getDefaultPrompts(e){return[this.getDefaultPromptTemplate(e),{id:`detailed_${e}`,name:"详细模板",moduleType:e,category:"detailed",template:"你是一个资深的文本编辑专家。请对以下文本进行详细的校对和分析，包括语法、拼写、逻辑、风格等各个方面。",variables:[],description:"详细校对模板",version:"1.0.0",createdAt:Date.now(),updatedAt:Date.now()}]}getDefaultGlobalConfig(){return{modules:{},providers:{},cost:{defaultBudget:100,budgetWarningThreshold:80,budgetStopThreshold:95,costPrecision:4,currency:"CNY",trackingInterval:1e3},cache:{configCacheExpiry:3e5,resultCacheExpiry:864e5,maxCacheSize:100,cleanupInterval:36e5,enabled:!0,strategy:"lru"},storage:{type:"indexeddb",databaseName:"ai_proofreading_engine",databaseVersion:1,maxStorageSize:500,autoCleanup:!0,dataRetentionTime:6048e5,compression:!0},network:{timeout:3e4,maxRetries:3,retryInterval:1e3,retryStrategy:"exponential",concurrencyLimit:5,enableRequestCache:!0,requestCacheTime:3e5},security:{encryptApiKeys:!0,encryptionAlgorithm:"AES-256-GCM",validateSSL:!0,allowedDomains:[],requestSigning:!1,dataMasking:!0},logging:{level:"info",enableConsole:!0,enableFile:!1,maxFileSize:10,retentionDays:7,logApiCalls:!0,logPerformance:!0},performance:{enableMonitoring:!0,samplingRate:.1,memoryWarningThreshold:500,cpuWarningThreshold:80,enableOptimization:!0,batchSize:10},userPreferences:{defaultLanguage:"zh-CN",defaultAIModel:"deepseek-chat",defaultPromptTemplate:"default",defaultProcessingOptions:{focus:"all",style:"formal",language:"zh-CN"},theme:"auto",notifications:{desktop:!0,sound:!1,types:["completion","error"]},autoSaveInterval:3e4},version:"1.0.0",lastUpdated:Date.now()}}getModuleDisplayName(t){return{[e.PRE_REVIEW]:"内容预审",[e.BATCH_PROOFREADING]:"AI批量审校",[e.ONLINE_PROOFREADING]:"在线AI审校",[e.IMAGE_PROOFREADING]:"图片审校",[e.VIDEO_PROOFREADING]:"视频审校",[e.AUDIO_PROOFREADING]:"音频审校",[e.TYPESETTING]:"专业排版",[e.PROFESSIONAL_QUERY]:"专业查询"}[t]||"未知模块"}}class i{configManager;sessionManager;providerCache=new Map;progressCallbacks=new Map;batchCallbacks=new Map;constructor(){this.configManager=new o,this.sessionManager=new t}async initialize(e,t){try{await this.validateUserSelections(t);const a=await this.configManager.getAIConfig(t.aiModel.provider);return await this.sessionManager.createSession({moduleType:e,aiModel:t.aiModel,promptTemplate:t.promptTemplate,aiConfig:a,budget:t.costBudget,timestamp:Date.now()})}catch(a){throw new Error(`初始化审校模块失败: ${a instanceof Error?a.message:"未知错误"}`)}}async startProofreading(e,t,a){try{const s=await this.sessionManager.getSession(e);if(!s)throw new Error(`会话 ${e} 不存在`);await this.sessionManager.updateSessionStatus(e,"processing");const n=await this.processContent(t);t instanceof File&&(s.fileName=t.name,s.fileSize=t.size);const r=await this.createChunks(n,s.userSelections.aiModel.maxTokens);await this.sessionManager.saveChunks(e,r);const o=await this.getAIProvider(s.userSelections.aiModel);await this.processChunks(e,r,o,s.userSelections.promptTemplate,a),await this.mergeResults(e),await this.generateReport(e),await this.sessionManager.updateSessionStatus(e,"completed");const i=this.progressCallbacks.get(e);if(i?.onComplete){const t=await this.sessionManager.getResult(e);i.onComplete(e,t)}}catch(s){await this.sessionManager.updateSessionStatus(e,"error"),await this.sessionManager.saveError(e,s);const t=this.progressCallbacks.get(e);throw t?.onError&&t.onError({id:this.generateErrorId(),type:"processing",message:s instanceof Error?s.message:"处理失败",timestamp:Date.now(),retryable:!0}),s}}async getProgress(e){return await this.sessionManager.getProgress(e)}async getResult(e){return await this.sessionManager.getResult(e)}async getReport(e){return await this.sessionManager.getReport(e)}async pause(e){await this.sessionManager.updateSessionStatus(e,"paused")}async resume(e){await this.sessionManager.updateSessionStatus(e,"processing")}async cancel(e){await this.sessionManager.updateSessionStatus(e,"cancelled")}async cleanup(e){this.progressCallbacks.delete(e),await this.sessionManager.cleanupSession(e)}async getSessionInfo(e){const t=await this.sessionManager.getSession(e);if(!t)throw new Error(`会话 ${e} 不存在`);return t}async getActiveSessions(e){return await this.sessionManager.getActiveSessions(e)}async createBatch(e,t,a,s){return this.generateBatchId()}async startBatchProcessing(e,t){throw new Error("批次处理功能正在开发中")}async getBatchProgress(e){throw new Error("批次进度功能正在开发中")}async getBatchResults(e){throw new Error("批次结果功能正在开发中")}async pauseBatch(e){throw new Error("批次暂停功能正在开发中")}async resumeBatch(e){throw new Error("批次恢复功能正在开发中")}async cancelBatch(e){throw new Error("批次取消功能正在开发中")}async cleanupBatch(e){throw new Error("批次清理功能正在开发中")}async getActiveBatches(e){return[]}async retryFailedFiles(e,t){throw new Error("失败文件重试功能正在开发中")}async exportBatchResults(e,t){throw new Error("批次结果导出功能正在开发中")}setProgressCallback(e,t){this.progressCallbacks.set(e,t)}setBatchCallback(e,t){this.batchCallbacks.set(e,t)}async validateUserSelections(e){if(!e.aiModel)throw new Error("未选择AI模型");if(!e.promptTemplate)throw new Error("未选择提示词模板");if(!e.costBudget||e.costBudget<=0)throw new Error("成本预算设置无效")}async processContent(e){return"string"==typeof e?e:new Promise((t,a)=>{const s=new FileReader;s.onload=e=>{const s=e.target?.result;"string"==typeof s?t(s):a(new Error("文件读取失败"))},s.onerror=()=>a(new Error("文件读取错误")),s.readAsText(e,"utf-8")})}async createChunks(e,t){const a=[],s=Math.floor(.7*t),n=e.split(/\n\s*\n/);let r="",o=0,i=0;for(let c=0;c<n.length;c++){const e=n[c],t=r+(r?"\n\n":"")+e;if(t.length>s&&r){const t=i+r.length;a.push({id:`chunk_${o}`,content:r,startIndex:i,endIndex:t,type:"paragraph",size:r.length,estimatedTokens:Math.ceil(r.length/4)});const s=r.slice(-300);r=s+"\n\n"+e,i=t-300,o++}else r=t}if(r){const e=i+r.length;a.push({id:`chunk_${o}`,content:r,startIndex:i,endIndex:e,type:"paragraph",size:r.length,estimatedTokens:Math.ceil(r.length/4)})}return a}async getAIProvider(e){const t=`${e.provider}_${e.id}`;if(this.providerCache.has(t))return this.providerCache.get(t);const o=await this.configManager.getAIConfig(e.provider);let i;switch(e.provider){case"deepseek":i=new r(o,e);break;case"baidu":i=new n(o,e);break;case"doubao":i=new s(o,e);break;case"qwen":i=new a(o,e);break;default:throw new Error(`不支持的AI提供商: ${e.provider}`)}return this.providerCache.set(t,i),i}async processChunks(e,t,a,s,n){const r=t.length;let o=0;for(const c of t)try{await this.updateProgress(e,{type:"proofread",progress:Math.round(o/r*100),currentItem:`处理分块 ${o+1}/${r}`,timestamp:Date.now()});const t=await a.proofreadText({content:c.content,promptTemplate:s,options:n});await this.sessionManager.saveChunkResult(e,c.id,t),o++;const i=this.progressCallbacks.get(e);i?.onChunkComplete&&i.onChunkComplete(c.id,t)}catch(i){await this.sessionManager.addError(e,{id:this.generateErrorId(),type:"processing",message:i instanceof Error?i.message:"分块处理失败",chunkId:c.id,timestamp:Date.now(),retryable:!0})}}async updateProgress(e,t){await this.sessionManager.updateProgress(e,t);const a=this.progressCallbacks.get(e);if(a?.onProgress){const t=await this.sessionManager.getProgress(e);a.onProgress(t)}}async mergeResults(e){}async generateReport(e){}generateErrorId(){return`error_${Date.now()}_${Math.random().toString(36).substr(2,6)}`}generateBatchId(){return`batch_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}}const c=new i;export{i as M,c as a};
