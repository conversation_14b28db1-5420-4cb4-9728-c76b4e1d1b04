import{d as s,c as a,a as e,Q as t,I as n,ag as l,o as r}from"../../chunks/vue-vendor-BCsylZgc.js";import{_ as c}from"../../chunks/_plugin-vue_export-helper-BCo6x5W8.js";const d={class:"user-settings"},o={class:"content"},p=c(s({__name:"UserSettings",setup:s=>(s,c)=>{const p=l("el-card");return r(),a("div",d,[c[1]||(c[1]=e("div",{class:"page-header"},[e("h1",null,"账户设置"),e("p",{class:"page-description"},"管理账户安全和偏好设置")],-1)),e("div",o,[t(p,null,{default:n(()=>c[0]||(c[0]=[e("p",null,"账户设置功能开发中...",-1)])),_:1,__:[0]})])])}}),[["__scopeId","data-v-8d7ff883"]]);export{p as default};
